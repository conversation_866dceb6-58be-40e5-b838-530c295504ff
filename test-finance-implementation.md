# Finance Module Testing Guide

## Prerequisites
1. Backend server running on port 3000
2. Frontend server running on port 3002
3. PostgreSQL database with migrations applied
4. A registered church and admin user

## Testing Steps

### 1. Start the Servers
```bash
# Terminal 1 - Backend
cd backend
bun run dev

# Terminal 2 - Frontend  
cd frontend
bun run dev
```

### 2. Access the Finance Module
1. Go to `http://localhost:3002`
2. Login with your church admin account
3. Navigate to `http://localhost:3002/[your-church-slug]/finances`

### 3. Test Finance Features

#### A. Record a Finance Entry
1. Click "Record Finance" button
2. Fill in the form:
   - Amount: 100.00
   - Currency: ZMW (or your preferred currency)
   - Type: Tithe/Offering/Donation
   - Method: Cash/Bank Transfer/etc.
   - Add notes if needed
3. Submit the form
4. Verify the entry appears in the finances list

#### B. Create a Finance Project
1. Go to "Projects" tab
2. Click "Add Project"
3. Fill in project details:
   - Name: "New Church Building"
   - Description: "Fundraising for new building"
   - Target Amount: 50000
   - Currency: ZMW
   - Status: Active
4. Submit and verify project appears with progress bar

#### C. Add Payment Methods
1. Go to "Payment Methods" tab
2. Click "Add Payment Method"
3. Test different types:
   - Mobile Money (Airtel/MTN/Zamtel)
   - Bank Card (Visa/Mastercard)
   - Bank Transfer
   - Cash
4. Set one as default
5. Verify all methods display correctly

#### D. Create Finance Categories
1. Go to "Categories" tab
2. Click "Add Category"
3. Create categories like:
   - "Building Fund"
   - "Mission Support"
   - "Youth Ministry"
4. Verify categories are saved

#### E. View Dashboard
1. Go to `http://localhost:3002/[your-church-slug]/finances/dashboard`
2. Verify statistics display correctly:
   - Total finances
   - Number of records
   - Average finance amount
   - Recent activity list

### 4. Test API Endpoints Directly

You can test the API endpoints using curl or a tool like Postman:

```bash
# Get currencies (public endpoint)
curl http://localhost:3000/api/currencies

# Other endpoints require authentication token
# First login to get a token, then use it in Authorization header
```

## Expected Results

✅ **Success Indicators:**
- All forms submit without errors
- Data appears in lists immediately after creation
- Dashboard shows correct statistics
- Payment methods display with proper icons/badges
- Projects show progress bars
- No console errors in browser dev tools

❌ **Failure Indicators:**
- Forms show validation errors
- API calls return 404 or 500 errors
- Data doesn't persist after page refresh
- Console shows network or JavaScript errors

## Troubleshooting

### Common Issues:
1. **"Failed to load data" errors:** Check backend server is running and database is connected
2. **404 errors:** Verify routes are properly registered
3. **Validation errors:** Check form data matches backend schema requirements
4. **Permission errors:** Ensure user has proper role (Super Admin/Pastor/Elder)

### Debug Steps:
1. Check browser console for JavaScript errors
2. Check backend logs for API errors
3. Verify database tables exist: `finances`, `finance_projects`, `finance_categories`, `payment_methods`
4. Test API endpoints directly with curl