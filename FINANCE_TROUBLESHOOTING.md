# Finance Module Troubleshooting Guide

## Current Issue
Getting "Validation failed" error when trying to record a finance entry.

## Recent Fixes Applied

### 1. Fixed Form Validation Schema
- ✅ Aligned frontend validation with backend requirements
- ✅ Added proper enum validation for `type` and `method` fields
- ✅ Fixed UUID validation for optional fields (`eventId`, `projectId`)
- ✅ Added proper string transformations for empty values

### 2. Added Access Control
- ✅ Added role-based access control to finance routes
- ✅ Required roles: Super <PERSON><PERSON>, Pastor, Elder, Deacon

### 3. Enhanced Error Handling
- ✅ Added detailed validation error logging in backend
- ✅ Improved error messages in frontend form

## Step-by-Step Testing

### Step 1: Verify Backend Server
```bash
cd backend
bun run dev
```
Should show:
```
🚀 Server running on port 3000
📚 API Documentation: http://localhost:3000/api-docs
```

### Step 2: Verify Frontend Server
```bash
cd frontend
bun run dev
```
Should show:
```
▲ Next.js 15.4.5
- Local: http://localhost:3002
```

### Step 3: Check User Permissions
1. Login to your church admin account
2. Verify your role is one of: Super <PERSON><PERSON>, Pastor, Elder, or Deacon
3. Check browser console for any authentication errors

### Step 4: Test Finance Form
1. Go to `/[church-slug]/finances`
2. Click "Record Finance"
3. Fill in the form with these exact values:
   - **Amount**: 100.50
   - **Currency**: ZMW
   - **Type**: Offering
   - **Method**: Cash
   - **Notes**: Test entry
4. Submit the form

### Step 5: Check Browser Console
Open browser developer tools (F12) and look for:
- "Submitting finance data:" - shows what the form is sending
- Any error messages with validation details

## Expected Valid Data Format

The form should send data in this format:
```json
{
  "amount": 100.5,
  "currency": "ZMW",
  "type": "offering",
  "method": "cash",
  "isAnonymous": false,
  "notes": "Test entry"
}
```

## Common Issues & Solutions

### Issue 1: "Validation failed" Error
**Cause**: Data format doesn't match backend schema
**Solution**: Check browser console for exact validation error

### Issue 2: "Access denied" Error  
**Cause**: User doesn't have required permissions
**Solution**: Ensure user role is Super Admin, Pastor, Elder, or Deacon

### Issue 3: "Church context required" Error
**Cause**: Church slug is invalid or user not associated with church
**Solution**: Verify church slug in URL and user membership

### Issue 4: Network/Connection Errors
**Cause**: Backend server not running or connection issues
**Solution**: Restart backend server and check network connectivity

## Debug Commands

### Test Backend Directly
```bash
# Test health endpoint
curl http://localhost:3000/health

# Test currencies endpoint (no auth required)
curl http://localhost:3000/api/currencies
```

### Check Database Tables
```bash
cd backend
bun run db:studio
```
Verify these tables exist:
- `finances`
- `finance_projects` 
- `finance_categories`
- `payment_methods`

## If Issue Persists

1. **Clear Browser Cache**: Hard refresh (Ctrl+F5) or clear browser cache
2. **Check Network Tab**: Look for failed API requests in browser dev tools
3. **Restart Both Servers**: Stop and restart both backend and frontend
4. **Check Database Connection**: Ensure PostgreSQL is running and accessible
5. **Verify Environment Variables**: Check `.env` files in both backend and frontend

## Contact Information

If you continue to experience issues, please provide:
1. Exact error message from browser console
2. Network tab showing the failed request
3. Your user role and church slug
4. Backend server logs (if accessible)

The finance module should now be working correctly with the fixes applied.