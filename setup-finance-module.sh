#!/bin/bash

echo "🏦 Setting up Finance Module..."

# Check if we're in the right directory
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

echo "📦 Installing dependencies..."

# Install backend dependencies
cd backend
echo "Installing backend dependencies..."
bun install

# Install frontend dependencies  
cd ../frontend
echo "Installing frontend dependencies..."
bun install

cd ..

echo "🗄️ Setting up database..."

# Generate and apply migrations
cd backend
echo "Generating database migrations..."
bun run db:generate

echo "Applying database schema..."
bun run db:push

echo "✅ Finance module setup complete!"
echo ""
echo "🚀 Next steps:"
echo "1. Start the backend server:"
echo "   cd backend && bun run dev"
echo ""
echo "2. In another terminal, start the frontend:"
echo "   cd frontend && bun run dev"
echo ""
echo "3. Open http://localhost:3002 and test the finance features"
echo ""
echo "📋 See test-finance-implementation.md for detailed testing instructions"