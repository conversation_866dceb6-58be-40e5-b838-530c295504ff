#!/usr/bin/env node

// Simple test to validate finance data structure
const testData = {
  amount: 100.50,
  currency: "ZMW",
  type: "offering",
  method: "cash",
  isAnonymous: false,
  notes: "Test finance entry"
};

console.log('Testing finance data structure:');
console.log(JSON.stringify(testData, null, 2));

// Test with curl if backend is running
const { spawn } = require('child_process');

const curlCommand = [
  'curl',
  '-X', 'POST',
  'http://localhost:3000/api/churches/test-church/finances',
  '-H', 'Content-Type: application/json',
  '-H', 'Authorization: Bearer test-token',
  '-d', JSON.stringify(testData),
  '-v'
];

console.log('\nTesting with curl command:');
console.log(curlCommand.join(' '));

const curl = spawn('curl', curlCommand.slice(1));

curl.stdout.on('data', (data) => {
  console.log('Response:', data.toString());
});

curl.stderr.on('data', (data) => {
  console.log('Error/Headers:', data.toString());
});

curl.on('close', (code) => {
  console.log(`\nCurl process exited with code ${code}`);
  
  if (code === 0) {
    console.log('✅ Request completed successfully');
  } else {
    console.log('❌ Request failed');
    console.log('\nTroubleshooting:');
    console.log('1. Make sure backend server is running: cd backend && bun run dev');
    console.log('2. Check if you have a valid authentication token');
    console.log('3. Verify the church slug exists');
  }
});