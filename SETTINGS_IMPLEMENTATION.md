# Church Settings Page Implementation

## Overview
I've implemented a comprehensive church settings page that allows church administrators to manage their church profile and various settings. The implementation follows the established patterns in the codebase and provides a clean, tabbed interface for different setting categories.

## Features Implemented

### 1. Church Profile Management
- **Location**: `/[slug]/settings` (Profile tab)
- **Features**:
  - Edit church name, description, contact information
  - Display read-only church code and URL
  - Form validation with Zod schemas
  - Real-time form state management

### 2. Settings Categories
The settings are organized into 6 main tabs:

#### Profile Tab
- Church name, description, address
- Contact information (email, phone, website)
- Church code display (read-only)
- Church URL display

#### General Tab
- Member registration settings
- Email verification requirements
- Timezone and language preferences

#### Theme Tab
- Primary and secondary color customization
- Logo URL configuration
- Color picker integration

#### Features Tab
- Toggle various church features on/off:
  - Events management
  - Donations tracking
  - Messaging system
  - Calendar view
  - Online giving
  - Member directory
  - Event RSVP
  - Recurring events

#### Events Tab
- Default event type configuration
- Event approval settings
- Member event creation permissions
- Event image requirements
- Monthly event limits

#### Donations Tab
- Currency settings
- Anonymous donation options
- Receipt number requirements
- Tax deductible settings
- Event donation linking
- Recurring donation support
- Minimum donation amounts

## Technical Implementation

### Backend Integration
- Uses existing API endpoints:
  - `GET /api/churches/{slug}` - Church basic info
  - `GET /api/churches/{slug}/settings` - Church settings
  - `PUT /api/churches/{slug}` - Update church profile
  - `PUT /api/churches/{slug}/settings` - Update church settings

### Frontend Architecture
- **Main Page**: `frontend/src/app/[slug]/settings/page.tsx`
- **Profile Form**: `frontend/src/components/forms/church-profile-form.tsx`
- **Settings Form**: `frontend/src/components/forms/church-settings-form.tsx`
- **Validation**: Extended `frontend/src/lib/validations.ts`

### Key Components Used
- **UI Components**: shadcn/ui (Card, Tabs, Switch, Select, Input, etc.)
- **Form Handling**: react-hook-form with Zod validation
- **State Management**: React hooks with API integration
- **Notifications**: Sonner toast notifications
- **Icons**: Lucide React icons

### Permission System
- Requires `MANAGE_CHURCH_SETTINGS` permission
- Available to Super Admin and Pastor roles
- Proper access control with user-friendly error messages

### Data Flow
1. **Load**: Fetches church data and settings on page load
2. **Edit**: Form state management with real-time validation
3. **Save**: Optimistic updates with error handling
4. **Refresh**: Refetches data after successful updates

## File Structure
```
frontend/src/
├── app/[slug]/settings/
│   └── page.tsx                    # Main settings page
├── components/forms/
│   ├── church-profile-form.tsx     # Church profile form
│   └── church-settings-form.tsx    # Settings configuration form
└── lib/
    └── validations.ts              # Extended with settings schemas
```

## Usage
1. Navigate to `/{church-slug}/settings`
2. Use tabs to switch between different setting categories
3. Make changes in any tab
4. Click "Save Changes" to persist updates
5. Toast notifications confirm success/failure

## Security & Validation
- All forms use Zod schemas for validation
- Server-side validation on all endpoints
- Permission-based access control
- CSRF protection through existing middleware
- Input sanitization and type safety

## Responsive Design
- Mobile-friendly tabbed interface
- Responsive grid layouts
- Proper spacing and typography
- Accessible form controls

## Error Handling
- Network error handling with retry logic
- Form validation errors with clear messages
- Permission-based access control
- Loading states and user feedback

## Future Enhancements
- File upload for church logo
- Advanced theme customization
- Bulk settings import/export
- Settings history/audit log
- Advanced permission granularity