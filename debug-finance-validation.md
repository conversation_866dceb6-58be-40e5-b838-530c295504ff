# Debug Finance Validation Issue

## Problem
Getting "Validation failed" error when trying to record a finance entry.

## Backend Validation Schema (Expected)
From `backend/src/types/finance.ts`:

```typescript
export const createFinanceSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3).default('ZMW'),
  type: z.enum(['tithe', 'donation', 'offering', 'event_donation', 'building_fund', 'mission_support', 'special_collection', 'other']).default('offering'),
  method: z.enum(['cash', 'check', 'credit_card', 'debit_card', 'bank_transfer', 'mobile_payment', 'online', 'other']).default('cash'),
  status: z.enum(['pending', 'completed', 'failed', 'refunded', 'cancelled']).default('completed'),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  projectId: z.string().uuid().optional(),
  donorName: z.string().max(255).optional(),
  donorEmail: z.string().email().optional(),
  donorPhone: z.string().max(20).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  receiptNumber: z.string().max(100).optional(),
  taxDeductible: z.boolean().default(true),
  isAnonymous: z.boolean().default(false),
  transactionId: z.string().max(255).optional(),
  metadata: z.record(z.any()).optional(),
  recordedAt: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
    message: 'Invalid finance record date format',
  }),
});
```

## Frontend Form Schema (What's being sent)
From `frontend/src/components/forms/FinanceForm.tsx`:

```typescript
const financeSchema = z.object({
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  currency: z.string().min(1, 'Currency is required'),
  type: z.string().min(1, 'Finance type is required'),
  method: z.string().min(1, 'Payment method is required'),
  isAnonymous: z.boolean().default(false),
  notes: z.string().optional(),
  eventId: z.string().optional().transform(val => val === '' ? undefined : val),
  projectId: z.string().optional().transform(val => val === '' ? undefined : val),
})
```

## Issues Identified

### 1. Type Validation Mismatch
- **Backend**: Expects `type` to be one of specific enum values
- **Frontend**: Accepts any string, but form provides correct enum values

### 2. Method Validation Mismatch  
- **Backend**: Expects `method` to be one of specific enum values
- **Frontend**: Accepts any string, but form provides correct enum values

### 3. UUID Validation
- **Backend**: Validates `eventId` and `projectId` as UUIDs if provided
- **Frontend**: Sends empty strings which need to be converted to undefined

## Fixes Applied

1. **Fixed payment method selection**: Changed form to only use enum values instead of payment method IDs
2. **Fixed empty string handling**: Transform empty strings to undefined for optional UUID fields
3. **Added debugging**: Added console logs to see exact data being sent
4. **Improved error handling**: Show more detailed error messages

## Testing Steps

1. Open browser console
2. Try to record a finance entry
3. Check console logs for:
   - "Form data being submitted:" - shows what form sends
   - "API createFinance called with data:" - shows what API receives
   - Any validation error details

## Expected Valid Data Format

```json
{
  "amount": 100.50,
  "currency": "ZMW",
  "type": "offering",
  "method": "cash",
  "isAnonymous": false,
  "notes": "Sunday offering",
  "eventId": undefined,  // or valid UUID
  "projectId": undefined // or valid UUID
}
```

## Next Steps

If the issue persists:
1. Check the exact error message in browser console
2. Verify the backend server is running and accessible
3. Check if the user has proper permissions (Super Admin/Pastor/Elder)
4. Verify the church context is properly set