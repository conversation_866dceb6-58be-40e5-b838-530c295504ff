# Church Management System - Email Setup Guide

This guide will help you set up Google SMTP for your Church Management System to send automated emails.

## 🚀 Quick Setup

### 1. Gmail Configuration

1. **Enable 2-Factor Authentication** on your Gmail account
2. **Generate an App Password**:
   - Go to Google Account settings
   - Security → 2-Step Verification → App passwords
   - Generate a new app password for "Mail"
   - Copy the 16-character password (no spaces)

### 2. Environment Variables

Add these variables to your `backend/.env` file:

```env
# Email Configuration (Google SMTP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Church Name
FRONTEND_URL=http://localhost:3002
```

### 3. Test Your Setup

Run the mail system test:

```bash
cd backend
bun run test:mail
```

## 📧 Email Features

The system automatically sends emails for:

### 1. Welcome Emails
- **When**: New members are created without a password
- **Contains**: Welcome message, temporary password, login instructions
- **Triggered by**: `POST /api/churches/{slug}/members`

### 2. Password Reset Emails
- **When**: User requests password reset
- **Contains**: Secure reset link (expires in 1 hour)
- **Triggered by**: `POST /api/auth/forgot-password`

### 3. Event Notifications
- **When**: New events are created (manual trigger)
- **Contains**: Event details, date, location
- **Usage**: Call `MailService.sendEventNotification()`

### 4. Donation Receipts
- **When**: Donations are recorded (manual trigger)
- **Contains**: Receipt details, amount, tax information
- **Usage**: Call `MailService.sendDonationReceipt()`

### 5. Bulk Announcements
- **When**: Church admins send announcements
- **Contains**: Custom HTML content
- **Triggered by**: `POST /api/churches/{slug}/mail/bulk`

## 🛠 API Endpoints

### Test Email Configuration
```http
GET /api/mail/status
POST /api/mail/test
```

### Send Individual Email
```http
POST /api/mail/send
Content-Type: application/json

{
  "to": "<EMAIL>",
  "subject": "Test Subject",
  "html": "<h1>Hello World</h1>"
}
```

### Send Bulk Email (Church-scoped)
```http
POST /api/churches/{slug}/mail/bulk
Content-Type: application/json

{
  "recipients": ["<EMAIL>", "<EMAIL>"],
  "subject": "Church Announcement",
  "html": "<h1>Important Update</h1><p>Message content...</p>"
}
```

## 🔧 Advanced Configuration

### Custom SMTP Provider

To use a different email provider, update these environment variables:

```env
# Example: Outlook/Hotmail
SMTP_HOST=smtp-mail.outlook.com
SMTP_PORT=587
SMTP_SECURE=false

# Example: Custom SMTP
SMTP_HOST=mail.yourchurch.org
SMTP_PORT=465
SMTP_SECURE=true
```

### Email Templates

All email templates are in `backend/src/services/mailService.ts`. You can customize:

- **Welcome Email**: `sendWelcomeEmail()` method
- **Password Reset**: `sendPasswordResetEmail()` method
- **Event Notifications**: `sendEventNotification()` method
- **Donation Receipts**: `sendDonationReceipt()` method

### Rate Limiting

The system includes built-in rate limiting for bulk emails:
- **Batch Size**: 10 emails per batch
- **Delay**: 1 second between batches
- **Purpose**: Prevents SMTP server overload

## 🚨 Troubleshooting

### Common Issues

1. **"Authentication failed"**
   - Ensure 2FA is enabled on Gmail
   - Use App Password, not regular password
   - Check SMTP_USER matches the Gmail account

2. **"Connection timeout"**
   - Check firewall settings
   - Verify SMTP_HOST and SMTP_PORT
   - Try SMTP_SECURE=true with port 465

3. **"Email not received"**
   - Check spam/junk folders
   - Verify recipient email address
   - Check Gmail sent items

### Debug Mode

Enable detailed logging by setting:

```env
NODE_ENV=development
```

This will:
- Show email tokens in API responses
- Log detailed SMTP connection info
- Display email sending status

### Test Commands

```bash
# Test SMTP connection only
cd backend
bun run src/config/mail.ts

# Test full mail system
bun run test:mail

# Test with custom recipient
SMTP_USER=<EMAIL> bun run test:mail
```

## 🔒 Security Best Practices

1. **Never commit credentials** to version control
2. **Use App Passwords** instead of regular passwords
3. **Rotate credentials** regularly
4. **Monitor email usage** for suspicious activity
5. **Use environment-specific configs** for dev/staging/prod

## 📊 Monitoring

The system logs all email activities:

- ✅ Successful sends
- ❌ Failed attempts
- 🔍 SMTP connection status
- 📈 Bulk email statistics

Check your server logs for email-related entries.

## 🎯 Next Steps

1. **Set up your Gmail credentials** in `.env`
2. **Run the test script** to verify setup
3. **Create a test member** to see welcome emails
4. **Test password reset** functionality
5. **Customize email templates** for your church branding

## 📞 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review server logs for error details
3. Test with the provided test script
4. Verify Gmail App Password setup

---

**Happy emailing! 📧✨**