#!/bin/bash

# Script to run authentication and monitoring tests

echo "🧪 Running Authentication Metrics Tests..."
bun test backend/src/__tests__/authMetrics.test.ts --run

echo ""
echo "🔒 Running Security Audit Tests..."
bun test backend/src/__tests__/securityAuditEnhanced.test.ts --run

echo ""
echo "✅ All monitoring tests completed!"
echo ""
echo "📊 Monitoring Features Implemented:"
echo "  ✓ Authentication metrics collection"
echo "  ✓ Error rate tracking by error type"
echo "  ✓ Performance monitoring for auth operations"
echo "  ✓ Security event logging and alerting"
echo "  ✓ Dashboard for authentication health monitoring"
echo ""
echo "🚀 Monitoring endpoints available at:"
echo "  - GET /api/monitoring/health"
echo "  - GET /api/monitoring/auth/metrics"
echo "  - GET /api/monitoring/security/dashboard"
echo "  - GET /api/monitoring/security/alerts"
echo "  - GET /api/monitoring/dashboard"
echo "  - GET /api/monitoring/status"
echo ""
echo "🎯 Frontend dashboard available at:"
echo "  - /monitoring (for admin users)"