#!/usr/bin/env bun
import { db } from './src/db';
import { members, churches, roles } from './src/db/schema';
import { generateEmailVerificationToken, hashPassword } from './src/utils/auth';
import { generateUniqueUserId } from './src/utils/userIdGenerator';
import { eq } from 'drizzle-orm';

async function createTestMember() {
  console.log('🧪 Creating test member for email verification...\n');

  try {
    // Find the first church
    const church = await db.query.churches.findFirst();
    if (!church) {
      console.log('❌ No church found. Please create a church first.');
      process.exit(1);
    }

    // Find a role for the church
    const role = await db.query.roles.findFirst({
      where: eq(roles.churchId, church.id)
    });
    if (!role) {
      console.log('❌ No role found for church. Please create a role first.');
      process.exit(1);
    }

    // Generate test data
    const userId = await generateUniqueUserId();
    const tempPassword = 'TestPassword123!';
    const hashedPassword = await hashPassword(tempPassword);
    const emailVerificationToken = generateEmailVerificationToken();

    console.log('📝 Generated test data:', {
      userId,
      tempPassword,
      tokenLength: emailVerificationToken.length,
      token: emailVerificationToken
    });

    // Create test member
    const [newMember] = await db.insert(members).values({
      userId,
      churchId: church.id,
      roleId: role.id,
      firstName: 'Test',
      lastName: 'User',
      email: `test${Date.now()}@example.com`,
      phone: '+1234567890',
      password: hashedPassword,
      emailVerificationToken,
      isEmailVerified: false,
      status: 'active'
    }).returning();

    console.log('✅ Test member created:', {
      id: newMember.id,
      email: newMember.email,
      hasToken: !!newMember.emailVerificationToken,
      tokenMatches: newMember.emailVerificationToken === emailVerificationToken
    });

    console.log('\n🔗 Test verification URL:');
    console.log(`http://localhost:3002/verify-email/${emailVerificationToken}`);

    console.log('\n🧪 You can now test the verification process!');

  } catch (error) {
    console.error('❌ Error creating test member:', error);
  }

  process.exit(0);
}

createTestMember();