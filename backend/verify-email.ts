import { db } from './src/db/index';
import { members } from './src/db/schema/members';
import { eq } from 'drizzle-orm';

async function verifyEmail() {
  try {
    // Update the user's email verification status
    await db.update(members)
      .set({ isEmailVerified: true })
      .where(eq(members.userId, '8781858321'));

    console.log('Email verified for user ID: 8781858321');
  } catch (error) {
    console.error('Error verifying email:', error);
  }
}

verifyEmail();