import { db } from './src/db/index';
import { members } from './src/db/schema/members';
import { eq } from 'drizzle-orm';

async function updateUserIds() {
  try {
    console.log('Starting user ID migration...');

    // Get all users
    const allUsers = await db.select({ 
      id: members.id, 
      userId: members.userId 
    }).from(members);
    
    console.log(`Found ${allUsers.length} users to update`);

    for (const user of allUsers) {
      // Generate a random 10-digit number
      let newUserId: string;
      let attempts = 0;
      
      do {
        const randomNumber = Math.floor(1000000000 + Math.random() * 9000000000);
        newUserId = randomNumber.toString();
        
        // Check if this ID already exists
        const existing = await db.select({ id: members.id })
          .from(members)
          .where(eq(members.userId, newUserId))
          .limit(1);
          
        if (existing.length === 0) break;
        
        attempts++;
      } while (attempts < 10);
      
      if (attempts >= 10) {
        console.error(`Failed to generate unique user ID for user: ${user.id}`);
        continue;
      }
      
      // Update the user ID
      await db.update(members)
        .set({ userId: newUserId })
        .where(eq(members.id, user.id));
        
      console.log(`Updated user ${user.id}: ${user.userId} -> ${newUserId}`);
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
  }
}

updateUserIds();