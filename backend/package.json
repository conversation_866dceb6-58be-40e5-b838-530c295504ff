{"name": "backend", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun --watch run index.ts", "start": "bun run index.ts", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:mail": "bun run src/utils/testMail.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "devDependencies": {"@electric-sql/pglite": "^0.3.7", "@types/bun": "latest", "@types/jest": "^30.0.0", "@types/supertest": "^6.0.3", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "@vitest/ui": "^3.2.4", "eslint": "^9.32.0", "jest": "^30.0.5", "supertest": "^7.1.4", "ts-jest": "^29.4.0", "vitest": "^3.2.4"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "@types/bcryptjs": "^3.0.0", "@types/cookie-parser": "^1.4.9", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.5", "bcryptjs": "^3.0.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "nodemailer": "^7.0.5", "postgres": "^3.4.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "zod": "^4.0.14"}}