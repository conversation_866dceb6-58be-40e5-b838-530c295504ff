import type { Request, Response, NextFunction } from 'express';
import { db } from '../db';
import { churches, members } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { AppError } from '../utils/errorHandler';
import { AuthErrorCode, type AuthenticatedRequest } from './auth';

export interface TenantRequest extends AuthenticatedRequest {
  church?: {
    id: string;
    name: string;
    slug: string;
    settings: any;
  };
}

// Audit logging function for tenant operations
const auditLog = (event: string, details: any, req: Request) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
    ...details
  };
  
  console.log('[TENANT_AUDIT]', JSON.stringify(logEntry));
};

export const validateTenant = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { slug } = req.params;
    
    if (!slug) {
      auditLog('TENANT_VALIDATION_FAILED', {
        reason: 'Missing church slug',
        path: req.path
      }, req);
      
      throw new AppError('Church slug is required', 400);
    }

    // Validate slug format (basic validation)
    if (!/^[a-z0-9-]+$/.test(slug)) {
      auditLog('TENANT_VALIDATION_FAILED', {
        reason: 'Invalid slug format',
        slug,
        path: req.path
      }, req);
      
      throw new AppError('Invalid church slug format', 400);
    }

    const church = await db.query.churches.findFirst({
      where: and(eq(churches.slug, slug), eq(churches.isActive, true)),
      columns: {
        id: true,
        name: true,
        slug: true,
        settings: true,
      }
    });

    if (!church) {
      auditLog('TENANT_VALIDATION_FAILED', {
        reason: 'Church not found or inactive',
        slug,
        path: req.path
      }, req);
      
      throw new AppError('Church not found or inactive', 404);
    }

    auditLog('TENANT_VALIDATION_SUCCESS', {
      churchId: church.id,
      churchSlug: church.slug,
      path: req.path
    }, req);

    (req as TenantRequest).church = church;
    next();
  } catch (error) {
    next(error);
  }
};

export const requireTenantAccess = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const tenantReq = req as TenantRequest;
    
    if (!tenantReq.church) {
      auditLog('TENANT_ACCESS_FAILED', {
        reason: 'Church context not found',
        userId: req.user?.id || null
      }, req);
      
      throw new AppError('Church context not found', 400);
    }

    if (!req.user) {
      auditLog('TENANT_ACCESS_FAILED', {
        reason: 'Authentication required',
        churchId: tenantReq.church.id
      }, req);
      
      throw new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING);
    }

    if (req.user.churchId !== tenantReq.church.id) {
      auditLog('TENANT_ACCESS_FAILED', {
        reason: 'Cross-church access denied',
        userId: req.user.id,
        userChurchId: req.user.churchId,
        requestedChurchId: tenantReq.church.id,
        churchSlug: tenantReq.church.slug
      }, req);
      
      throw new AppError(
        'Access denied. You can only access resources from your own church.', 
        403, 
        AuthErrorCode.CHURCH_ACCESS_DENIED
      );
    }

    auditLog('TENANT_ACCESS_SUCCESS', {
      userId: req.user.id,
      churchId: tenantReq.church.id,
      churchSlug: tenantReq.church.slug
    }, req);

    next();
  } catch (error) {
    next(error);
  }
};

export const requireSuperAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      auditLog('SUPER_ADMIN_ACCESS_FAILED', {
        reason: 'Authentication required'
      }, req);
      
      throw new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING);
    }

    const userWithRole = await db.query.members.findFirst({
      where: eq(members.id, req.user.id),
      with: {
        role: true,
      },
    });

    if (!userWithRole?.role) {
      auditLog('SUPER_ADMIN_ACCESS_FAILED', {
        reason: 'User has no role assigned',
        userId: req.user.id
      }, req);
      
      throw new AppError('No role assigned to user', 403, AuthErrorCode.INSUFFICIENT_PERMISSIONS);
    }

    if (userWithRole.role.name !== 'Super Admin') {
      auditLog('SUPER_ADMIN_ACCESS_FAILED', {
        reason: 'Insufficient role for super admin access',
        userId: req.user.id,
        userRole: userWithRole.role.name
      }, req);
      
      throw new AppError('Super Admin access required', 403, AuthErrorCode.INSUFFICIENT_PERMISSIONS);
    }

    auditLog('SUPER_ADMIN_ACCESS_SUCCESS', {
      userId: req.user.id,
      userRole: userWithRole.role.name
    }, req);

    next();
  } catch (error) {
    next(error);
  }
};

export const checkChurchAccess = (allowedRoles: string[] = []) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      const tenantReq = req as TenantRequest;
      
      if (!tenantReq.church || !req.user) {
        auditLog('CHURCH_ACCESS_FAILED', {
          reason: 'Invalid request context',
          hasChurch: !!tenantReq.church,
          hasUser: !!req.user
        }, req);
        
        throw new AppError('Invalid request context', 400);
      }

      if (req.user.churchId !== tenantReq.church.id) {
        auditLog('CHURCH_ACCESS_FAILED', {
          reason: 'Cross-church access denied',
          userId: req.user.id,
          userChurchId: req.user.churchId,
          requestedChurchId: tenantReq.church.id,
          allowedRoles
        }, req);
        
        throw new AppError(
          'Access denied to this church', 
          403, 
          AuthErrorCode.CHURCH_ACCESS_DENIED
        );
      }

      if (allowedRoles.length > 0) {
        const userWithRole = await db.query.members.findFirst({
          where: eq(members.id, req.user.id),
          with: {
            role: true,
          },
        });

        if (!userWithRole?.role) {
          auditLog('CHURCH_ACCESS_FAILED', {
            reason: 'User has no role assigned',
            userId: req.user.id,
            churchId: tenantReq.church.id,
            allowedRoles
          }, req);
          
          throw new AppError('No role assigned to user', 403, AuthErrorCode.INSUFFICIENT_PERMISSIONS);
        }

        if (!allowedRoles.includes(userWithRole.role.name)) {
          auditLog('CHURCH_ACCESS_FAILED', {
            reason: 'Insufficient role',
            userId: req.user.id,
            userRole: userWithRole.role.name,
            allowedRoles,
            churchId: tenantReq.church.id
          }, req);
          
          throw new AppError(
            `Access denied. Required roles: ${allowedRoles.join(', ')}. Current role: ${userWithRole.role.name}`, 
            403, 
            AuthErrorCode.INSUFFICIENT_PERMISSIONS
          );
        }

        auditLog('CHURCH_ACCESS_SUCCESS', {
          userId: req.user.id,
          userRole: userWithRole.role.name,
          churchId: tenantReq.church.id,
          grantedRole: userWithRole.role.name
        }, req);
      } else {
        auditLog('CHURCH_ACCESS_SUCCESS', {
          userId: req.user.id,
          churchId: tenantReq.church.id,
          noRoleRequired: true
        }, req);
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};