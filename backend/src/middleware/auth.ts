import type { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { verifyToken } from '../utils/auth';
import { db } from '../db';
import { members } from '../db/schema/members';
import { eq } from 'drizzle-orm';
import { AppError } from '../utils/errorHandler';
import { PerformanceMonitor } from '../utils/performanceMonitor';
import { AuthMetricsCollector } from '../utils/authMetrics';

export interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    churchId: string;
    roleId?: string;
    permissions: string[];
  };
}

// Enhanced error types for better error responses
export enum AuthErrorCode {
  TOKEN_MISSING = 'TOKEN_MISSING',
  TOKEN_INVALID = 'TOKEN_INVALID',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  TOKEN_MALFORMED = 'TOKEN_MALFORMED',
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_INACTIVE = 'USER_INACTIVE',
  USER_UNVERIFIED = 'USER_UNVERIFIED',
  INSUFFICIENT_PERMISSIONS = 'INSUFFICIENT_PERMISSIONS',
  CHURCH_ACCESS_DENIED = 'CHURCH_ACCESS_DENIED',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED'
}

// Audit logging function
const auditLog = (event: string, details: any, req: Request) => {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    ip: req.ip || req.connection.remoteAddress,
    userAgent: req.get('User-Agent'),
    path: req.path,
    method: req.method,
    ...details
  };

  // In production, this should go to a proper logging service
  console.log('[SECURITY_AUDIT]', JSON.stringify(logEntry));
};

// Rate limiting for authentication endpoints
export const authRateLimit = rateLimit({
  windowMs: 5 * 60 * 1000, // 5 minutes
  max: 10, // Limit each IP to 10 requests per 5 minutes for auth endpoints
  message: {
    status: 'error',
    code: AuthErrorCode.RATE_LIMIT_EXCEEDED,
    message: 'Too many authentication attempts. Please try again in 5 minutes.',
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests against the limit
  handler: (req, res) => {
    auditLog('RATE_LIMIT_EXCEEDED', {
      endpoint: req.path,
      ip: req.ip
    }, req);

    res.status(429).json({
      status: 'error',
      code: AuthErrorCode.RATE_LIMIT_EXCEEDED,
      message: 'Too many authentication attempts. Please try again in 5 minutes.',
    });
  }
});

// General rate limiting for API endpoints
export const apiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // Limit each IP to 100 requests per windowMs
  message: {
    status: 'error',
    code: AuthErrorCode.RATE_LIMIT_EXCEEDED,
    message: 'Too many requests. Please try again later.',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

export const authenticate = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  const timer = PerformanceMonitor.createTimer();
  timer.start();

  try {
    const authHeader = req.headers.authorization;

    if (!authHeader) {
      auditLog('AUTH_FAILED', {
        reason: 'Missing authorization header',
        userId: null
      }, req);

      return next(new AppError('Authorization header is required', 401, AuthErrorCode.TOKEN_MISSING));
    }

    if (!authHeader.startsWith('Bearer ')) {
      auditLog('AUTH_FAILED', {
        reason: 'Invalid authorization header format',
        userId: null
      }, req);

      return next(new AppError('Authorization header must use Bearer token format', 401, AuthErrorCode.TOKEN_MALFORMED));
    }

    const token = authHeader.replace('Bearer ', '');

    if (!token || token.trim() === '') {
      auditLog('AUTH_FAILED', {
        reason: 'Empty token',
        userId: null
      }, req);

      return next(new AppError('Access token is required', 401, AuthErrorCode.TOKEN_MISSING));
    }

    let decoded;
    try {
      decoded = verifyToken(token);
    } catch (error: any) {
      let errorCode = AuthErrorCode.TOKEN_INVALID;
      let message = 'Invalid access token';

      if (error.name === 'TokenExpiredError') {
        errorCode = AuthErrorCode.TOKEN_EXPIRED;
        message = 'Access token has expired. Please refresh your token.';
      } else if (error.name === 'JsonWebTokenError') {
        errorCode = AuthErrorCode.TOKEN_MALFORMED;
        message = 'Malformed access token';
      }

      auditLog('AUTH_FAILED', {
        reason: error.name || 'Token verification failed',
        userId: null,
        tokenError: error.message
      }, req);

      return next(new AppError(message, 401, errorCode));
    }

    if (!decoded.id || !decoded.churchId) {
      auditLog('AUTH_FAILED', {
        reason: 'Invalid token payload',
        userId: decoded.id || null
      }, req);

      return next(new AppError('Invalid token payload', 401, AuthErrorCode.TOKEN_INVALID));
    }

    const member = await db.query.members.findFirst({
      where: eq(members.id, decoded.id),
      with: {
        role: true,
        church: {
          columns: {
            id: true,
            isActive: true
          }
        }
      }
    });

    if (!member) {
      auditLog('AUTH_FAILED', {
        reason: 'User not found',
        userId: decoded.id
      }, req);

      return next(new AppError('User account not found', 401, AuthErrorCode.USER_NOT_FOUND));
    }

    if (member.status !== 'active') {
      auditLog('AUTH_FAILED', {
        reason: 'User inactive',
        userId: member.id,
        userStatus: member.status
      }, req);

      return next(new AppError('User account is inactive. Please contact your administrator.', 401, AuthErrorCode.USER_INACTIVE));
    }

    if (!member.isEmailVerified) {
      auditLog('AUTH_FAILED', {
        reason: 'Email not verified',
        userId: member.id
      }, req);

      return next(new AppError('Email address not verified. Please verify your email before accessing the system.', 401, AuthErrorCode.USER_UNVERIFIED));
    }

    if (!member.church?.isActive) {
      auditLog('AUTH_FAILED', {
        reason: 'Church inactive',
        userId: member.id,
        churchId: member.churchId
      }, req);

      return next(new AppError('Church account is inactive. Please contact support.', 403, AuthErrorCode.CHURCH_ACCESS_DENIED));
    }

    // Verify token church matches user's church
    if (decoded.churchId !== member.churchId) {
      auditLog('AUTH_FAILED', {
        reason: 'Church mismatch',
        userId: member.id,
        tokenChurchId: decoded.churchId,
        userChurchId: member.churchId
      }, req);

      return next(new AppError('Token church mismatch. Please log in again.', 401, AuthErrorCode.TOKEN_INVALID));
    }

    req.user = {
      id: member.id,
      email: member.email,
      firstName: member.firstName,
      lastName: member.lastName,
      churchId: member.churchId,
      roleId: member.roleId || undefined,
      permissions: member.role?.permissions as string[] || []
    };

    // Log successful authentication
    auditLog('AUTH_SUCCESS', {
      userId: member.id,
      churchId: member.churchId,
      roleId: member.roleId
    }, req);

    // Record successful auth check metrics
    const duration = timer.end();
    AuthMetricsCollector.recordAuthCheck(req, true, duration, member.id);

    next();
  } catch (error: any) {
    // Record failed auth check metrics
    const duration = timer.end();
    const errorCode = error.code || 'unknown_error';
    AuthMetricsCollector.recordAuthCheck(req, false, duration, undefined, errorCode);

    auditLog('AUTH_ERROR', {
      reason: 'Unexpected error during authentication',
      error: error.message,
      userId: null
    }, req);

    next(new AppError('Authentication failed', 500));
  }
};

export const authorize = (...permissions: string[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      auditLog('AUTHORIZATION_FAILED', {
        reason: 'No authenticated user',
        requiredPermissions: permissions
      }, req);

      return next(new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING));
    }

    const userPermissions = req.user.permissions || [];
    const hasPermission = permissions.some(permission =>
      userPermissions.includes(permission) || userPermissions.includes('*')
    );

    if (!hasPermission) {
      auditLog('AUTHORIZATION_FAILED', {
        reason: 'Insufficient permissions',
        userId: req.user.id,
        userPermissions,
        requiredPermissions: permissions
      }, req);

      return next(new AppError(
        `Insufficient permissions. Required: ${permissions.join(' or ')}`,
        403,
        AuthErrorCode.INSUFFICIENT_PERMISSIONS
      ));
    }

    auditLog('AUTHORIZATION_SUCCESS', {
      userId: req.user.id,
      grantedPermissions: permissions.filter(p => userPermissions.includes(p) || userPermissions.includes('*'))
    }, req);

    next();
  };
};

export const requireChurchAccess = (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  if (!req.user) {
    auditLog('CHURCH_ACCESS_FAILED', {
      reason: 'No authenticated user'
    }, req);

    return next(new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING));
  }

  // Extract church ID from various possible sources
  const churchId = req.params.churchId || req.params.slug || req.body.churchId;

  if (!churchId) {
    auditLog('CHURCH_ACCESS_FAILED', {
      reason: 'No church identifier provided',
      userId: req.user.id
    }, req);

    return next(new AppError('Church identifier is required', 400));
  }

  // For slug-based access, we need to resolve the church ID
  if (req.params.slug && !req.params.churchId) {
    // This will be handled by the tenant middleware
    return next();
  }

  if (req.user.churchId !== churchId) {
    auditLog('CHURCH_ACCESS_FAILED', {
      reason: 'Church access denied',
      userId: req.user.id,
      userChurchId: req.user.churchId,
      requestedChurchId: churchId
    }, req);

    return next(new AppError(
      'Access denied. You can only access resources from your own church.',
      403,
      AuthErrorCode.CHURCH_ACCESS_DENIED
    ));
  }

  auditLog('CHURCH_ACCESS_SUCCESS', {
    userId: req.user.id,
    churchId: req.user.churchId
  }, req);

  next();
};

// Enhanced role-based access control
export const requireRole = (...roles: string[]) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      auditLog('ROLE_ACCESS_FAILED', {
        reason: 'No authenticated user',
        requiredRoles: roles
      }, req);

      return next(new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING));
    }

    try {
      const member = await db.query.members.findFirst({
        where: eq(members.id, req.user.id),
        with: {
          role: true
        }
      });

      if (!member?.role) {
        auditLog('ROLE_ACCESS_FAILED', {
          reason: 'User has no role assigned',
          userId: req.user.id,
          requiredRoles: roles
        }, req);

        return next(new AppError('No role assigned to user', 403, AuthErrorCode.INSUFFICIENT_PERMISSIONS));
      }

      const hasRole = roles.includes(member.role.name);

      if (!hasRole) {
        auditLog('ROLE_ACCESS_FAILED', {
          reason: 'Insufficient role',
          userId: req.user.id,
          userRole: member.role.name,
          requiredRoles: roles
        }, req);

        return next(new AppError(
          `Insufficient role. Required: ${roles.join(' or ')}. Current: ${member.role.name}`,
          403,
          AuthErrorCode.INSUFFICIENT_PERMISSIONS
        ));
      }

      auditLog('ROLE_ACCESS_SUCCESS', {
        userId: req.user.id,
        userRole: member.role.name,
        grantedRole: member.role.name
      }, req);

      next();
    } catch (error: any) {
      auditLog('ROLE_ACCESS_ERROR', {
        reason: 'Database error during role check',
        userId: req.user.id,
        error: error.message
      }, req);

      next(new AppError('Error checking user role', 500));
    }
  };
};

// Middleware to validate church-scoped resource access
export const validateChurchResource = (resourceChurchIdField: string = 'churchId') => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    if (!req.user) {
      return next(new AppError('Authentication required', 401, AuthErrorCode.TOKEN_MISSING));
    }

    // Extract resource church ID from request body or params
    const resourceChurchId = req.body[resourceChurchIdField] || req.params[resourceChurchIdField];

    if (resourceChurchId && resourceChurchId !== req.user.churchId) {
      auditLog('RESOURCE_ACCESS_FAILED', {
        reason: 'Cross-church resource access attempted',
        userId: req.user.id,
        userChurchId: req.user.churchId,
        resourceChurchId,
        resourceField: resourceChurchIdField
      }, req);

      return next(new AppError(
        'Cannot access resources from other churches',
        403,
        AuthErrorCode.CHURCH_ACCESS_DENIED
      ));
    }

    next();
  };
};