import { Request, Response, NextFunction } from 'express';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  validateTenant, 
  requireTenantAccess, 
  requireSuper<PERSON>d<PERSON>,
  checkChurchAccess,
  TenantRequest 
} from '../tenant';
import { AuthenticatedRequest, AuthErrorCode } from '../auth';
import { db } from '../../db';
import { AppError } from '../../utils/errorHandler';

// Mock dependencies
vi.mock('../../db');

const mockDb = vi.mocked(db);

describe('Tenant Middleware', () => {
  let req: Partial<TenantRequest>;
  let res: Partial<Response>;
  let next: NextFunction;
  let consoleSpy: any;

  beforeEach(() => {
    req = {
      params: {},
      headers: {},
      ip: '127.0.0.1',
      path: '/test',
      method: 'GET',
      get: vi.fn().mockReturnValue('test-user-agent'),
      connection: { remoteAddress: '127.0.0.1' } as any
    };
    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };
    next = vi.fn();
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockRestore();
  });

  describe('validateTenant', () => {
    it('should fail when no slug is provided', async () => {
      req.params = {};

      await validateTenant(req as Request, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Church slug is required',
          statusCode: 400
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_VALIDATION_FAILED')
      );
    });

    it('should fail when slug format is invalid', async () => {
      req.params = { slug: 'Invalid_Slug!' };

      await validateTenant(req as Request, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid church slug format',
          statusCode: 400
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_VALIDATION_FAILED')
      );
    });

    it('should fail when church is not found', async () => {
      req.params = { slug: 'valid-slug' };
      mockDb.query.churches.findFirst.mockResolvedValue(null);

      await validateTenant(req as Request, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Church not found or inactive',
          statusCode: 404
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_VALIDATION_FAILED')
      );
    });

    it('should succeed when church is found and active', async () => {
      req.params = { slug: 'valid-slug' };
      const mockChurch = {
        id: 'church-1',
        name: 'Test Church',
        slug: 'valid-slug',
        settings: {}
      };
      mockDb.query.churches.findFirst.mockResolvedValue(mockChurch);

      await validateTenant(req as Request, res as Response, next);

      expect(req.church).toEqual(mockChurch);
      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_VALIDATION_SUCCESS')
      );
    });

    it('should validate slug format correctly', async () => {
      const validSlugs = ['valid-slug', 'church123', 'my-church-name'];
      const invalidSlugs = ['Invalid_Slug', 'slug with spaces', 'UPPERCASE', 'slug!'];

      for (const slug of validSlugs) {
        req.params = { slug };
        mockDb.query.churches.findFirst.mockResolvedValue({
          id: 'church-1',
          name: 'Test Church',
          slug,
          settings: {}
        });

        await validateTenant(req as Request, res as Response, next);
        expect(next).toHaveBeenCalledWith();
        vi.clearAllMocks();
      }

      for (const slug of invalidSlugs) {
        req.params = { slug };

        await validateTenant(req as Request, res as Response, next);
        expect(next).toHaveBeenCalledWith(
          expect.objectContaining({
            message: 'Invalid church slug format',
            statusCode: 400
          })
        );
        vi.clearAllMocks();
      }
    });
  });

  describe('requireTenantAccess', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read']
      };
      req.church = {
        id: 'church-1',
        name: 'Test Church',
        slug: 'test-church',
        settings: {}
      };
    });

    it('should fail when church context is not found', async () => {
      req.church = undefined;

      await requireTenantAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Church context not found',
          statusCode: 400
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_ACCESS_FAILED')
      );
    });

    it('should fail when user is not authenticated', async () => {
      req.user = undefined;

      await requireTenantAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when user tries to access different church', async () => {
      req.church!.id = 'church-2';

      await requireTenantAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access denied. You can only access resources from your own church.',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_ACCESS_FAILED')
      );
    });

    it('should succeed when user accesses their own church', async () => {
      await requireTenantAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_ACCESS_SUCCESS')
      );
    });
  });

  describe('requireSuperAdmin', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['*']
      };
    });

    it('should fail when user is not authenticated', async () => {
      req.user = undefined;

      await requireSuperAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when user has no role assigned', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: null
      });

      await requireSuperAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No role assigned to user',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should fail when user is not a Super Admin', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Admin' }
      });

      await requireSuperAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Super Admin access required',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should succeed when user is a Super Admin', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Super Admin' }
      });

      await requireSuperAdmin(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('SUPER_ADMIN_ACCESS_SUCCESS')
      );
    });
  });

  describe('checkChurchAccess', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read']
      };
      req.church = {
        id: 'church-1',
        name: 'Test Church',
        slug: 'test-church',
        settings: {}
      };
    });

    it('should fail when church context is missing', async () => {
      req.church = undefined;
      const middleware = checkChurchAccess();

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid request context',
          statusCode: 400
        })
      );
    });

    it('should fail when user is not authenticated', async () => {
      req.user = undefined;
      const middleware = checkChurchAccess();

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid request context',
          statusCode: 400
        })
      );
    });

    it('should fail when user tries to access different church', async () => {
      req.church!.id = 'church-2';
      const middleware = checkChurchAccess();

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access denied to this church',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
    });

    it('should succeed when no roles are required', async () => {
      const middleware = checkChurchAccess();

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('CHURCH_ACCESS_SUCCESS')
      );
    });

    it('should fail when user has no role but roles are required', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: null
      });
      const middleware = checkChurchAccess(['Admin']);

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No role assigned to user',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should fail when user has insufficient role', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Member' }
      });
      const middleware = checkChurchAccess(['Admin', 'Pastor']);

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access denied. Required roles: Admin, Pastor. Current role: Member',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should succeed when user has required role', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Admin' }
      });
      const middleware = checkChurchAccess(['Admin', 'Pastor']);

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('CHURCH_ACCESS_SUCCESS')
      );
    });

    it('should succeed when user has one of multiple allowed roles', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Pastor' }
      });
      const middleware = checkChurchAccess(['Admin', 'Pastor', 'Elder']);

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });
});