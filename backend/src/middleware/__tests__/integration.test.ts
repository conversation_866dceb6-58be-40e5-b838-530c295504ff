import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import { 
  authenticate, 
  authorize, 
  requireChurchAccess,
  authRateLimit,
  apiRateLimit,
  AuthErrorCode 
} from '../auth';
import { validateTenant, requireTenantAccess } from '../tenant';
import { globalErrorHandler } from '../../utils/errorHandler';
import { verifyToken } from '../../utils/auth';
import { db } from '../../db';

// Mock dependencies
vi.mock('../../utils/auth');
vi.mock('../../db');

const mockVerifyToken = vi.mocked(verifyToken);
const mockDb = vi.mocked(db);

describe('Middleware Integration Tests', () => {
  let app: express.Application;
  let consoleSpy: any;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockRestore();
  });

  describe('Authentication Flow', () => {
    it('should handle complete authentication flow for protected route', async () => {
      // Setup route with authentication
      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success', user: req.user });
      });
      app.use(globalErrorHandler);

      // Mock successful authentication
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read'] },
        church: { id: 'church-1', isActive: true }
      });

      const response = await request(app)
        .get('/api/protected')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Success');
      expect(response.body.user).toEqual({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        roleId: 'role-1',
        permissions: ['read']
      });
    });

    it('should handle authentication failure with proper error response', async () => {
      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/protected');

      expect(response.status).toBe(401);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Authorization header is required',
        code: AuthErrorCode.TOKEN_MISSING
      });
    });
  });

  describe('Authorization Flow', () => {
    beforeEach(() => {
      // Mock successful authentication
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read'] },
        church: { id: 'church-1', isActive: true }
      });
    });

    it('should allow access with sufficient permissions', async () => {
      app.get('/api/admin', authenticate, authorize('read'), (req, res) => {
        res.json({ message: 'Admin access granted' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/admin')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Admin access granted');
    });

    it('should deny access with insufficient permissions', async () => {
      app.get('/api/admin', authenticate, authorize('admin'), (req, res) => {
        res.json({ message: 'Admin access granted' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/admin')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(403);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Insufficient permissions. Required: admin',
        code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
      });
    });
  });

  describe('Church Access Control', () => {
    beforeEach(() => {
      // Mock successful authentication
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read'] },
        church: { id: 'church-1', isActive: true }
      });
    });

    it('should allow access to own church resources', async () => {
      app.get('/api/churches/:churchId/members', authenticate, requireChurchAccess, (req, res) => {
        res.json({ message: 'Church members retrieved' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/churches/church-1/members')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Church members retrieved');
    });

    it('should deny access to other church resources', async () => {
      app.get('/api/churches/:churchId/members', authenticate, requireChurchAccess, (req, res) => {
        res.json({ message: 'Church members retrieved' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/churches/church-2/members')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(403);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Access denied. You can only access resources from your own church.',
        code: AuthErrorCode.CHURCH_ACCESS_DENIED
      });
    });
  });

  describe('Tenant Validation', () => {
    it('should validate tenant and allow access', async () => {
      // Mock church lookup - need to mock it for each call
      mockDb.query.churches.findFirst
        .mockResolvedValueOnce({
          id: 'church-1',
          name: 'Test Church',
          slug: 'test-church',
          settings: {}
        });

      // Mock user authentication
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read'] },
        church: { id: 'church-1', isActive: true }
      });

      app.get('/api/churches/:slug/dashboard', 
        validateTenant, 
        authenticate, 
        requireTenantAccess, 
        (req, res) => {
          res.json({ 
            message: 'Dashboard accessed',
            church: req.church,
            user: req.user 
          });
        }
      );
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/churches/test-church/dashboard')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(200);
      expect(response.body.message).toBe('Dashboard accessed');
      expect(response.body.church).toEqual({
        id: 'church-1',
        name: 'Test Church',
        slug: 'test-church',
        settings: {}
      });
    });

    it('should reject invalid church slug', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue(null);

      app.get('/api/churches/:slug/dashboard', validateTenant, (req, res) => {
        res.json({ message: 'Dashboard accessed' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/churches/invalid-church/dashboard');

      expect(response.status).toBe(404);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Church not found or inactive'
      });
    });

    it('should reject malformed church slug', async () => {
      app.get('/api/churches/:slug/dashboard', validateTenant, (req, res) => {
        res.json({ message: 'Dashboard accessed' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/churches/Invalid_Slug!/dashboard');

      expect(response.status).toBe(400);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Invalid church slug format'
      });
    });
  });

  describe('Rate Limiting', () => {
    it('should apply rate limiting to auth endpoints', async () => {
      app.post('/api/auth/login', authRateLimit, (req, res) => {
        res.json({ message: 'Login attempt' });
      });

      // Make multiple requests to trigger rate limit
      const requests = Array(6).fill(null).map(() => 
        request(app).post('/api/auth/login').send({})
      );

      const responses = await Promise.all(requests);
      
      // First 5 should succeed, 6th should be rate limited
      expect(responses.slice(0, 5).every(r => r.status === 200)).toBe(true);
      expect(responses[5].status).toBe(429);
      expect(responses[5].body).toEqual({
        status: 'error',
        code: AuthErrorCode.RATE_LIMIT_EXCEEDED,
        message: 'Too many authentication attempts. Please try again in 15 minutes.'
      });
    });

    it('should apply general rate limiting to API endpoints', async () => {
      app.get('/api/data', apiRateLimit, (req, res) => {
        res.json({ message: 'Data retrieved' });
      });

      // Make multiple requests to trigger rate limit (101 requests)
      const requests = Array(101).fill(null).map(() => 
        request(app).get('/api/data')
      );

      const responses = await Promise.all(requests);
      
      // First 100 should succeed, 101st should be rate limited
      expect(responses.slice(0, 100).every(r => r.status === 200)).toBe(true);
      expect(responses[100].status).toBe(429);
      expect(responses[100].body).toEqual({
        status: 'error',
        code: AuthErrorCode.RATE_LIMIT_EXCEEDED,
        message: 'Too many requests. Please try again later.'
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockRejectedValue(new Error('Database connection failed'));

      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/protected')
        .set('Authorization', 'Bearer valid-token');

      expect(response.status).toBe(500);
      expect(response.body).toEqual({
        status: 'error',
        message: 'Authentication failed'
      });
    });

    it('should include error codes in responses', async () => {
      // Mock token verification to throw malformed token error
      mockVerifyToken.mockImplementation(() => {
        const error = new Error('Invalid token');
        error.name = 'JsonWebTokenError';
        throw error;
      });

      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      const response = await request(app)
        .get('/api/protected')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(401);
      expect(response.body.code).toBe(AuthErrorCode.TOKEN_MALFORMED);
    });
  });

  describe('Audit Logging', () => {
    it('should log authentication events', async () => {
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read'] },
        church: { id: 'church-1', isActive: true }
      });

      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      await request(app)
        .get('/api/protected')
        .set('Authorization', 'Bearer valid-token');

      expect(consoleSpy).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('AUTH_SUCCESS')
      );
    });

    it('should log failed authentication attempts', async () => {
      app.get('/api/protected', authenticate, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      await request(app)
        .get('/api/protected');

      expect(consoleSpy).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('AUTH_FAILED')
      );
    });

    it('should log tenant validation events', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue({
        id: 'church-1',
        name: 'Test Church',
        slug: 'test-church',
        settings: {}
      });

      app.get('/api/churches/:slug/dashboard', validateTenant, (req, res) => {
        res.json({ message: 'Success' });
      });
      app.use(globalErrorHandler);

      await request(app)
        .get('/api/churches/test-church/dashboard');

      expect(consoleSpy).toHaveBeenCalledWith(
        '[TENANT_AUDIT]',
        expect.stringContaining('TENANT_VALIDATION_SUCCESS')
      );
    });
  });
});