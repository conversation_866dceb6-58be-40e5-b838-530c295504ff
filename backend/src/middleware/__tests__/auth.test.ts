import { Request, Response, NextFunction } from 'express';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  authenticate, 
  authorize, 
  requireChurchAccess, 
  requireRole,
  validateChurchResource,
  authRateLimit,
  AuthErrorCode,
  AuthenticatedRequest 
} from '../auth';
import { verifyToken } from '../../utils/auth';
import { db } from '../../db';
import { AppError } from '../../utils/errorHandler';

// Mock dependencies
vi.mock('../../utils/auth');
vi.mock('../../db');

const mockVerifyToken = vi.mocked(verifyToken);
const mockDb = vi.mocked(db);

describe('Authentication Middleware', () => {
  let req: Partial<AuthenticatedRequest>;
  let res: Partial<Response>;
  let next: NextFunction;
  let consoleSpy: any;

  beforeEach(() => {
    req = {
      headers: {},
      ip: '127.0.0.1',
      path: '/test',
      method: 'GET',
      get: vi.fn().mockReturnValue('test-user-agent'),
      connection: { remoteAddress: '127.0.0.1' } as any
    };
    res = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn()
    };
    next = vi.fn();
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
    consoleSpy.mockRestore();
  });

  describe('authenticate', () => {
    it('should fail when no authorization header is provided', async () => {
      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authorization header is required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('AUTH_FAILED')
      );
    });

    it('should fail when authorization header does not start with Bearer', async () => {
      req.headers!.authorization = 'Basic token123';

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authorization header must use Bearer token format',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MALFORMED
        })
      );
    });

    it('should fail when token is empty', async () => {
      req.headers!.authorization = 'Bearer ';

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token is required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when token is expired', async () => {
      req.headers!.authorization = 'Bearer expired-token';
      mockVerifyToken.mockImplementation(() => {
        const error = new Error('Token expired');
        error.name = 'TokenExpiredError';
        throw error;
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access token has expired. Please refresh your token.',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_EXPIRED
        })
      );
    });

    it('should fail when token is malformed', async () => {
      req.headers!.authorization = 'Bearer malformed-token';
      mockVerifyToken.mockImplementation(() => {
        const error = new Error('Invalid token');
        error.name = 'JsonWebTokenError';
        throw error;
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Malformed access token',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MALFORMED
        })
      );
    });

    it('should fail when token payload is invalid', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ email: '<EMAIL>' }); // Missing id and churchId

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Invalid token payload',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_INVALID
        })
      );
    });

    it('should fail when user is not found', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue(null);

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'User account not found',
          statusCode: 401,
          code: AuthErrorCode.USER_NOT_FOUND
        })
      );
    });

    it('should fail when user is inactive', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        status: 'inactive',
        isEmailVerified: true,
        churchId: 'church-1',
        church: { id: 'church-1', isActive: true }
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'User account is inactive. Please contact your administrator.',
          statusCode: 401,
          code: AuthErrorCode.USER_INACTIVE
        })
      );
    });

    it('should fail when user email is not verified', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        status: 'active',
        isEmailVerified: false,
        churchId: 'church-1',
        church: { id: 'church-1', isActive: true }
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Email address not verified. Please verify your email before accessing the system.',
          statusCode: 401,
          code: AuthErrorCode.USER_UNVERIFIED
        })
      );
    });

    it('should fail when church is inactive', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        church: { id: 'church-1', isActive: false }
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Church account is inactive. Please contact support.',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
    });

    it('should fail when token church does not match user church', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-2', // Different church
        church: { id: 'church-2', isActive: true }
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Token church mismatch. Please log in again.',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_INVALID
        })
      );
    });

    it('should succeed with valid token and user', async () => {
      req.headers!.authorization = 'Bearer valid-token';
      mockVerifyToken.mockReturnValue({ id: 'user-1', churchId: 'church-1' });
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'active',
        isEmailVerified: true,
        churchId: 'church-1',
        roleId: 'role-1',
        role: { permissions: ['read', 'write'] },
        church: { id: 'church-1', isActive: true }
      });

      await authenticate(req as AuthenticatedRequest, res as Response, next);

      expect(req.user).toEqual({
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        roleId: 'role-1',
        permissions: ['read', 'write']
      });
      expect(next).toHaveBeenCalledWith();
      expect(consoleSpy).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('AUTH_SUCCESS')
      );
    });
  });

  describe('authorize', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read', 'write']
      };
    });

    it('should fail when user is not authenticated', () => {
      req.user = undefined;
      const middleware = authorize('read');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when user lacks required permission', () => {
      const middleware = authorize('admin');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Insufficient permissions. Required: admin',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should succeed when user has required permission', () => {
      const middleware = authorize('read');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should succeed when user has wildcard permission', () => {
      req.user!.permissions = ['*'];
      const middleware = authorize('admin');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should succeed when user has one of multiple required permissions', () => {
      const middleware = authorize('admin', 'read');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('requireChurchAccess', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read']
      };
    });

    it('should fail when user is not authenticated', () => {
      req.user = undefined;

      requireChurchAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when no church identifier is provided', () => {
      req.params = {};
      req.body = {};

      requireChurchAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Church identifier is required',
          statusCode: 400
        })
      );
    });

    it('should fail when user tries to access different church', () => {
      req.params = { churchId: 'church-2' };

      requireChurchAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Access denied. You can only access resources from your own church.',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
    });

    it('should succeed when user accesses their own church', () => {
      req.params = { churchId: 'church-1' };

      requireChurchAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should pass through for slug-based access', () => {
      req.params = { slug: 'my-church' };

      requireChurchAccess(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('requireRole', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read']
      };
    });

    it('should fail when user is not authenticated', async () => {
      req.user = undefined;
      const middleware = requireRole('Admin');

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when user has no role assigned', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: null
      });
      const middleware = requireRole('Admin');

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No role assigned to user',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should fail when user has insufficient role', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Member' }
      });
      const middleware = requireRole('Admin');

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Insufficient role. Required: Admin. Current: Member',
          statusCode: 403,
          code: AuthErrorCode.INSUFFICIENT_PERMISSIONS
        })
      );
    });

    it('should succeed when user has required role', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Admin' }
      });
      const middleware = requireRole('Admin');

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should succeed when user has one of multiple required roles', async () => {
      mockDb.query.members.findFirst.mockResolvedValue({
        id: 'user-1',
        role: { name: 'Pastor' }
      });
      const middleware = requireRole('Admin', 'Pastor');

      await middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });
  });

  describe('validateChurchResource', () => {
    beforeEach(() => {
      req.user = {
        id: 'user-1',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        churchId: 'church-1',
        permissions: ['read']
      };
    });

    it('should fail when user is not authenticated', () => {
      req.user = undefined;
      const middleware = validateChurchResource();

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Authentication required',
          statusCode: 401,
          code: AuthErrorCode.TOKEN_MISSING
        })
      );
    });

    it('should fail when resource belongs to different church', () => {
      req.body = { churchId: 'church-2' };
      const middleware = validateChurchResource();

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Cannot access resources from other churches',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
    });

    it('should succeed when resource belongs to same church', () => {
      req.body = { churchId: 'church-1' };
      const middleware = validateChurchResource();

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should succeed when no church ID is provided in resource', () => {
      req.body = {};
      req.params = {};
      const middleware = validateChurchResource();

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith();
    });

    it('should use custom field name for church ID', () => {
      req.body = { customChurchId: 'church-2' };
      const middleware = validateChurchResource('customChurchId');

      middleware(req as AuthenticatedRequest, res as Response, next);

      expect(next).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'Cannot access resources from other churches',
          statusCode: 403,
          code: AuthErrorCode.CHURCH_ACCESS_DENIED
        })
      );
    });
  });
});