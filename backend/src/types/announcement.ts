import { z } from 'zod';

// Enums
export const AnnouncementType = z.enum([
  'general',
  'urgent',
  'event_related',
  'service_update',
  'prayer_request',
  'community_news',
  'financial',
  'special_event',
  'ministry_update',
  'volunteer_opportunity',
  'other'
]);

export const Priority = z.enum(['low', 'normal', 'high', 'urgent']);

export const AnnouncementStatus = z.enum([
  'draft',
  'scheduled',
  'published',
  'archived',
  'expired'
]);

export const TargetAudience = z.enum([
  'all_members',
  'specific_branches',
  'specific_roles',
  'custom_group'
]);

export const ReactionType = z.enum(['like', 'love', 'pray', 'amen', 'heart', 'thumbs_up']);

// Base schemas
export const attachmentSchema = z.object({
  name: z.string().min(1).max(255),
  url: z.string().url(),
  type: z.string().max(50), // file type: pdf, image, etc.
  size: z.number().positive().optional(), // file size in bytes
});

export const externalLinkSchema = z.object({
  title: z.string().min(1).max(255),
  url: z.string().url(),
  description: z.string().max(500).optional(),
});

export const notificationChannelsSchema = z.object({
  email: z.boolean().default(true),
  sms: z.boolean().default(false),
  push: z.boolean().default(true),
  inApp: z.boolean().default(true),
});

// Create announcement schema
export const createAnnouncementSchema = z.object({
  title: z.string().min(1).max(255),
  content: z.string().min(1),
  summary: z.string().max(500).optional(),
  type: AnnouncementType.default('general'),
  priority: Priority.default('normal'),
  targetAudience: TargetAudience.default('all_members'),
  targetBranches: z.array(z.string().uuid()).optional(),
  targetRoles: z.array(z.string().min(1)).optional(),
  eventId: z.string().uuid().optional(),
  imageUrl: z.string().url().optional(),
  attachments: z.array(attachmentSchema).max(10).optional(),
  externalLinks: z.array(externalLinkSchema).max(5).optional(),
  tags: z.array(z.string().min(1).max(50)).max(10).optional(),
  scheduledFor: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional(),
  isPinned: z.boolean().default(false),
  allowComments: z.boolean().default(true),
  requiresAcknowledgment: z.boolean().default(false),
  sendNotification: z.boolean().default(true),
  notificationChannels: notificationChannelsSchema.optional(),
  metadata: z.record(z.any()).optional(),
}).refine((data) => {
  // If target audience is specific_branches, targetBranches must be provided
  if (data.targetAudience === 'specific_branches' && (!data.targetBranches || data.targetBranches.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Target branches must be selected when audience is 'specific_branches'",
  path: ['targetBranches']
}).refine((data) => {
  // If target audience is specific_roles, targetRoles must be provided
  if (data.targetAudience === 'specific_roles' && (!data.targetRoles || data.targetRoles.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Target roles must be selected when audience is 'specific_roles'",
  path: ['targetRoles']
}).refine((data) => {
  // If scheduled, scheduledFor must be in the future (with 1 minute tolerance)
  if (data.scheduledFor) {
    const scheduledDate = new Date(data.scheduledFor);
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60000); // 1 minute tolerance
    if (scheduledDate <= oneMinuteAgo) {
      return false;
    }
  }
  return true;
}, {
  message: "Scheduled date must be in the future",
  path: ['scheduledFor']
}).refine((data) => {
  // If expires, expiresAt must be in the future and after scheduledFor
  if (data.expiresAt) {
    const expiryDate = new Date(data.expiresAt);
    const now = new Date();
    const scheduledDate = data.scheduledFor ? new Date(data.scheduledFor) : now;
    
    if (expiryDate <= now) {
      return false;
    }
    if (expiryDate <= scheduledDate) {
      return false;
    }
  }
  return true;
}, {
  message: "Expiry date must be in the future and after the scheduled date",
  path: ['expiresAt']
});

// Update announcement schema
export const updateAnnouncementSchema = createAnnouncementSchema.partial().extend({
  status: AnnouncementStatus.optional(),
});

// Query announcements schema
export const queryAnnouncementsSchema = z.object({
  type: AnnouncementType.optional(),
  priority: Priority.optional(),
  status: AnnouncementStatus.optional(),
  authorId: z.string().uuid().optional(),
  eventId: z.string().uuid().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  search: z.string().max(255).optional(), // Search in title, content, tags
  tags: z.array(z.string()).optional(),
  isPinned: z.boolean().optional(),
  requiresAcknowledgment: z.boolean().optional(),
  unreadOnly: z.boolean().optional(), // Show only unread announcements
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  sortBy: z.enum(['createdAt', 'publishedAt', 'priority', 'title']).default('publishedAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Comment schemas
export const createCommentSchema = z.object({
  content: z.string().min(1).max(2000),
  parentCommentId: z.string().uuid().optional(),
});

export const updateCommentSchema = z.object({
  content: z.string().min(1).max(2000),
});

// Reaction schema
export const createReactionSchema = z.object({
  reactionType: ReactionType,
});

// View/acknowledgment schema
export const markAsViewedSchema = z.object({
  acknowledged: z.boolean().default(false),
  deviceInfo: z.object({
    userAgent: z.string().optional(),
    platform: z.string().optional(),
    browser: z.string().optional(),
  }).optional(),
});

// Bulk operations schemas
export const bulkDeleteAnnouncementsSchema = z.object({
  announcementIds: z.array(z.string().uuid()).min(1).max(50),
});

export const bulkUpdateStatusSchema = z.object({
  announcementIds: z.array(z.string().uuid()).min(1).max(50),
  status: AnnouncementStatus,
});

// Export types
export type CreateAnnouncementInput = z.infer<typeof createAnnouncementSchema>;
export type UpdateAnnouncementInput = z.infer<typeof updateAnnouncementSchema>;
export type QueryAnnouncementsInput = z.infer<typeof queryAnnouncementsSchema>;
export type CreateCommentInput = z.infer<typeof createCommentSchema>;
export type UpdateCommentInput = z.infer<typeof updateCommentSchema>;
export type CreateReactionInput = z.infer<typeof createReactionSchema>;
export type MarkAsViewedInput = z.infer<typeof markAsViewedSchema>;
export type BulkDeleteAnnouncementsInput = z.infer<typeof bulkDeleteAnnouncementsSchema>;
export type BulkUpdateStatusInput = z.infer<typeof bulkUpdateStatusSchema>;

// Response types
export const announcementResponseSchema = z.object({
  id: z.string().uuid(),
  churchId: z.string().uuid(),
  eventId: z.string().uuid().nullable(),
  authorId: z.string().uuid(),
  title: z.string(),
  content: z.string(),
  summary: z.string().nullable(),
  type: AnnouncementType,
  priority: Priority,
  status: AnnouncementStatus,
  targetAudience: TargetAudience,
  targetBranches: z.array(z.string()).nullable(),
  targetRoles: z.array(z.string()).nullable(),
  imageUrl: z.string().nullable(),
  attachments: z.array(attachmentSchema).nullable(),
  externalLinks: z.array(externalLinkSchema).nullable(),
  tags: z.array(z.string()).nullable(),
  publishedAt: z.date().nullable(),
  scheduledFor: z.date().nullable(),
  expiresAt: z.date().nullable(),
  isPinned: z.boolean(),
  allowComments: z.boolean(),
  requiresAcknowledgment: z.boolean(),
  sendNotification: z.boolean(),
  notificationChannels: notificationChannelsSchema.nullable(),
  viewCount: z.number(),
  metadata: z.record(z.any()).nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
  // Relations
  author: z.object({
    id: z.string().uuid(),
    firstName: z.string(),
    lastName: z.string(),
    email: z.string().email(),
    profileImage: z.string().nullable(),
  }).optional(),
  event: z.object({
    id: z.string().uuid(),
    title: z.string(),
    startDate: z.date(),
    type: z.string(),
  }).optional(),
  // Stats
  stats: z.object({
    viewCount: z.number(),
    commentCount: z.number(),
    reactionCount: z.number(),
    acknowledgmentCount: z.number().optional(),
    isViewed: z.boolean().optional(),
    isAcknowledged: z.boolean().optional(),
    userReaction: ReactionType.nullable().optional(),
  }).optional(),
});

export type AnnouncementResponse = z.infer<typeof announcementResponseSchema>;