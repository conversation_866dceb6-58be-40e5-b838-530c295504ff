import { z } from 'zod';

export const eventTypeValues = [
  'sunday_service',
  'bible_study',
  'prayer_meeting',
  'youth_service',
  'choir_practice',
  'community_outreach',
  'fundraiser',
  'conference',
  'retreat',
  'wedding',
  'funeral',
  'baptism',
  'communion',
  'special_service',
  'social_event',
  'other'
] as const;

export const eventStatusValues = ['draft', 'published', 'cancelled', 'completed'] as const;
export const rsvpStatusValues = ['pending', 'attending', 'not_attending', 'maybe'] as const;

export const createEventSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255),
  description: z.string().optional(),
  type: z.enum(eventTypeValues).default('other'),
  status: z.enum(eventStatusValues).default('draft'),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid start date format',
  }),
  endDate: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
    message: 'Invalid end date format',
  }),
  location: z.string().max(500).optional(),
  virtualLink: z.string().url().max(500).optional(),
  maxAttendees: z.number().int().positive().optional(),
  requiresRsvp: z.boolean().default(false),
  allowDonations: z.boolean().default(false),
  donationGoal: z.number().positive().optional(),
  donationDescription: z.string().optional(),
  imageUrl: z.string()
    .refine((url) => {
      if (!url) return true; // Optional field
      // Allow regular URLs or data URLs
      return url.match(/^https?:\/\/.+/) || url.match(/^data:image\/(jpeg|jpg|png|gif|webp);base64,/);
    }, {
      message: 'Must be a valid URL or base64 data URL'
    })
    .optional(),
  recurrencePattern: z.object({
    frequency: z.enum(['daily', 'weekly', 'monthly', 'yearly']),
    interval: z.number().int().positive().default(1),
    daysOfWeek: z.array(z.number().int().min(0).max(6)).optional(),
    endDate: z.string().optional(),
    occurrences: z.number().int().positive().optional(),
  }).optional(),
  tags: z.array(z.string().max(50)).max(10).default([]),
  isPublic: z.boolean().default(true),
});

export const updateEventSchema = createEventSchema.partial();

export const createRsvpSchema = z.object({
  status: z.enum(rsvpStatusValues).default('attending'),
  attendeeCount: z.number().int().positive().default(1),
  notes: z.string().max(500).optional(),
});

export const updateRsvpSchema = createRsvpSchema.partial();

export const eventFilterSchema = z.object({
  type: z.enum(eventTypeValues).optional(),
  status: z.enum(eventStatusValues).optional(),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  tags: z.array(z.string()).optional(),
  isPublic: z.coerce.boolean().optional(),
  requiresRsvp: z.coerce.boolean().optional(),
  allowDonations: z.coerce.boolean().optional(),
  upcoming: z.coerce.boolean().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
});

export const createCommentSchema = z.object({
  content: z.string().min(1, 'Comment content is required').max(1000, 'Comment too long'),
  parentCommentId: z.string().uuid('Invalid parent comment ID').optional(),
});

export const updateCommentSchema = z.object({
  content: z.string().min(1, 'Comment content is required').max(1000, 'Comment too long'),
});

export const commentFilterSchema = z.object({
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(50).default(20),
});

export type CreateEventInput = z.infer<typeof createEventSchema>;
export type UpdateEventInput = z.infer<typeof updateEventSchema>;
export type CreateRsvpInput = z.infer<typeof createRsvpSchema>;
export type UpdateRsvpInput = z.infer<typeof updateRsvpSchema>;
export type EventFilterInput = z.infer<typeof eventFilterSchema>;
export type CreateCommentInput = z.infer<typeof createCommentSchema>;
export type UpdateCommentInput = z.infer<typeof updateCommentSchema>;
export type CommentFilterInput = z.infer<typeof commentFilterSchema>;