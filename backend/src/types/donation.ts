import { z } from 'zod';

export const donationTypeValues = [
  'tithe',
  'offering',
  'event_donation',
  'building_fund',
  'mission_support',
  'special_collection',
  'other'
] as const;

export const donationMethodValues = [
  'cash',
  'check',
  'credit_card',
  'debit_card',
  'bank_transfer',
  'mobile_payment',
  'online',
  'other'
] as const;

export const donationStatusValues = ['pending', 'completed', 'failed', 'refunded', 'cancelled'] as const;

export const createDonationSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3).default('USD'),
  type: z.enum(donationTypeValues).default('offering'),
  method: z.enum(donationMethodValues).default('cash'),
  status: z.enum(donationStatusValues).default('completed'),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  donorName: z.string().max(255).optional(),
  donorEmail: z.string().email().optional(),
  donorPhone: z.string().max(20).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  receiptNumber: z.string().max(100).optional(),
  taxDeductible: z.boolean().default(true),
  isAnonymous: z.boolean().default(false),
  transactionId: z.string().max(255).optional(),
  metadata: z.record(z.any()).optional(),
  donatedAt: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
    message: 'Invalid donation date format',
  }),
});

export const updateDonationSchema = createDonationSchema.partial();

export const donationFilterSchema = z.object({
  type: z.enum(donationTypeValues).optional(),
  method: z.enum(donationMethodValues).optional(),
  status: z.enum(donationStatusValues).optional(),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  donorId: z.string().uuid().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  minAmount: z.coerce.number().positive().optional(),
  maxAmount: z.coerce.number().positive().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  includeAnonymous: z.coerce.boolean().default(true),
});

export const createDonationCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  description: z.string().optional(),
  targetAmount: z.number().positive().optional(),
  isActive: z.boolean().default(true),
});

export const updateDonationCategorySchema = createDonationCategorySchema.partial();

export const donationReportSchema = z.object({
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid start date format',
  }),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid end date format',
  }),
  groupBy: z.enum(['day', 'week', 'month', 'year', 'type', 'method', 'branch']).default('month'),
  includeAnonymous: z.boolean().default(true),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
});

export type CreateDonationInput = z.infer<typeof createDonationSchema>;
export type UpdateDonationInput = z.infer<typeof updateDonationSchema>;
export type DonationFilterInput = z.infer<typeof donationFilterSchema>;
export type CreateDonationCategoryInput = z.infer<typeof createDonationCategorySchema>;
export type UpdateDonationCategoryInput = z.infer<typeof updateDonationCategorySchema>;
export type DonationReportInput = z.infer<typeof donationReportSchema>;