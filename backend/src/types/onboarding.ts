import { z } from 'zod';

export const checkAvailabilitySchema = z.object({
  churchSlug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  adminEmail: z.string().min(1, 'Email is required').email('Invalid email format'),
});

export const initiateOnboardingSchema = z.object({
  church: z.object({
    name: z.string().min(1, 'Church name is required').max(100),
    slug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
    description: z.string().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    website: z.string().url().optional(),
    logo: z.string().url().optional(),
  }),
  admin: z.object({
    firstName: z.string().min(1, 'First name is required').max(50),
    lastName: z.string().min(1, 'Last name is required').max(50),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    phone: z.string().optional(),
    dateOfBirth: z.string().optional(),
    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
    address: z.string().optional(),
  }),
  settings: z.object({
    allowSelfRegistration: z.boolean().default(true),
    requireEmailVerification: z.boolean().default(true),
    timezone: z.string().default('UTC'),
    locale: z.string().default('en'),
    theme: z.object({
      primaryColor: z.string().default('#3B82F6'),
      secondaryColor: z.string().default('#64748B'),
    }).optional(),
    features: z.object({
      events: z.boolean().default(true),
      donations: z.boolean().default(false),
      messaging: z.boolean().default(true),
      calendar: z.boolean().default(true),
      onlineGiving: z.boolean().default(false),
      memberDirectory: z.boolean().default(true),
      eventRsvp: z.boolean().default(true),
      recurringEvents: z.boolean().default(true),
    }).optional(),
  }).optional(),
});

export const completeOnboardingSchema = z.object({
  onboardingToken: z.string().min(1, 'Onboarding token is required'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
  subscribeToUpdates: z.boolean().default(false),
});

export const verifyOnboardingSchema = z.object({
  verificationToken: z.string().min(1, 'Verification token is required'),
});

export const resendVerificationSchema = z.object({
  email: z.string().email('Invalid email format'),
  churchSlug: z.string().min(2).max(50),
});

export const setupInitialDataSchema = z.object({
  createSampleData: z.boolean().default(false),
  setupBranches: z.array(z.object({
    name: z.string().min(1, 'Branch name is required').max(100),
    slug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
    description: z.string().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    capacity: z.number().positive().optional(),
    coordinates: z.object({
      latitude: z.number(),
      longitude: z.number(),
    }).optional(),
    isMainBranch: z.boolean().default(false),
  })).default([]),
  setupRoles: z.array(z.object({
    name: z.string().min(1, 'Role name is required').max(50),
    description: z.string().optional(),
    permissions: z.array(z.string()).default([]),
  })).default([]),
});

export type CheckAvailabilityInput = z.infer<typeof checkAvailabilitySchema>;
export type InitiateOnboardingInput = z.infer<typeof initiateOnboardingSchema>;
export type CompleteOnboardingInput = z.infer<typeof completeOnboardingSchema>;
export type VerifyOnboardingInput = z.infer<typeof verifyOnboardingSchema>;
export type ResendVerificationInput = z.infer<typeof resendVerificationSchema>;
export type SetupInitialDataInput = z.infer<typeof setupInitialDataSchema>;

export interface OnboardingSession {
  id: string;
  churchData: InitiateOnboardingInput['church'];
  adminData: Omit<InitiateOnboardingInput['admin'], 'password'>;
  settingsData?: InitiateOnboardingInput['settings'];
  hashedPassword: string;
  token: string;
  expiresAt: Date;
  isCompleted: boolean;
  isVerified: boolean;
  verificationToken?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface OnboardingProgress {
  step: 'availability' | 'initiation' | 'completion' | 'verification' | 'setup' | 'finished';
  completed: boolean;
  nextStep?: string;
  data?: Record<string, unknown>;
}