import { z } from 'zod';

export const branchSchema = z.object({
  id: z.string(),
  churchId: z.string(),
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug too long')
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().max(20, 'Phone too long').optional(),
  email: z.string().email('Invalid email').max(255, 'Email too long').optional(),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }).optional(),
  capacity: z.string().max(100, 'Capacity description too long').optional(),
  facilities: z.array(z.string()).optional(),
  branchLeader: z.string().optional(),
  settings: z.record(z.any()).optional(),
  isActive: z.boolean().default(true),
  isMainBranch: z.boolean().default(false),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const createBranchSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255, 'Name too long'),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug too long')
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().max(20, 'Phone too long').optional(),
  email: z.string().email('Invalid email').max(255, 'Email too long').optional(),
  coordinates: z.object({
    lat: z.number(),
    lng: z.number(),
  }).optional(),
  capacity: z.string().max(100, 'Capacity description too long').optional(),
  facilities: z.array(z.string()).optional(),
  branchLeader: z.string().uuid('Invalid branch leader ID').optional(),
  settings: z.record(z.any()).optional(),
  isMainBranch: z.boolean().optional(),
});

export const updateBranchSchema = createBranchSchema.partial();

export const branchQuerySchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
  isMainBranch: z.coerce.boolean().optional(),
});

export type Branch = z.infer<typeof branchSchema>;
export type CreateBranchData = z.infer<typeof createBranchSchema>;
export type UpdateBranchData = z.infer<typeof updateBranchSchema>;
export type BranchQuery = z.infer<typeof branchQuerySchema>;