import { z } from 'zod'

export const notificationQuerySchema = z.object({
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(20),
  unreadOnly: z.coerce.boolean().optional(),
})

export const markReadSchema = z.object({
  notificationIds: z.array(z.string().uuid()).optional(),
})

export type NotificationQueryInput = z.infer<typeof notificationQuerySchema>
export type MarkReadInput = z.infer<typeof markReadSchema>
