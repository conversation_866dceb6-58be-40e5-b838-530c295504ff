import { z } from 'zod';

export const createChurchSchema = z.object({
  name: z.string().min(1, 'Church name is required'),
  slug: z.string().min(1, 'Church slug is required').regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email().optional().or(z.literal('')),
  website: z.string().url().optional().or(z.literal('')),
  logo: z.string().optional(),
});

export const registerChurchSchema = z.object({
  church: createChurchSchema,
  admin: z.object({
    firstName: z.string().min(1, 'First name is required').max(100),
    lastName: z.string().min(1, 'Last name is required').max(100),
    email: z.string().email('Invalid email address'),
    password: z.string().min(8, 'Password must be at least 8 characters')
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one lowercase letter, one uppercase letter, and one number'),
    phone: z.string().optional(),
    dateOfBirth: z.string().optional(),
    gender: z.enum(['male', 'female', 'other']).optional(),
    address: z.string().optional(),
  }),
});

export const updateChurchSchema = createChurchSchema.partial();

export const churchSettingsSchema = z.object({
  allowSelfRegistration: z.boolean().default(true),
  requireEmailVerification: z.boolean().default(true),
  defaultRole: z.string().optional(),
  timezone: z.string().default('UTC'),
  locale: z.string().default('en'),
  theme: z.object({
    primaryColor: z.string().default('#3B82F6'),
    secondaryColor: z.string().default('#64748B'),
    logo: z.string().optional(),
  }).optional(),
  features: z.object({
    events: z.boolean().default(true),
    donations: z.boolean().default(false),
    messaging: z.boolean().default(true),
    calendar: z.boolean().default(true),
    onlineGiving: z.boolean().default(false),
    memberDirectory: z.boolean().default(true),
    eventRsvp: z.boolean().default(true),
    recurringEvents: z.boolean().default(true),
  }).optional(),
  eventSettings: z.object({
    defaultEventType: z.string().default('other'),
    requireApprovalForPublicEvents: z.boolean().default(false),
    allowMemberEventCreation: z.boolean().default(false),
    maxEventsPerMonth: z.number().int().positive().optional(),
    eventImageRequired: z.boolean().default(false),
  }).optional(),
  donationSettings: z.object({
    defaultCurrency: z.string().length(3).default('USD'),
    enableAnonymousDonations: z.boolean().default(true),
    requireReceiptNumbers: z.boolean().default(true),
    taxDeductibleByDefault: z.boolean().default(true),
    enableEventDonations: z.boolean().default(true),
    minimumDonationAmount: z.number().positive().default(1),
    enableRecurringDonations: z.boolean().default(false),
  }).optional(),
});

export type CreateChurchInput = z.infer<typeof createChurchSchema>;
export type RegisterChurchInput = z.infer<typeof registerChurchSchema>;
export type UpdateChurchInput = z.infer<typeof updateChurchSchema>;
export type ChurchSettingsInput = z.infer<typeof churchSettingsSchema>;