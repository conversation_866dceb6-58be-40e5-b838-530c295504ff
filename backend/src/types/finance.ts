import { z } from 'zod';

export const financeTypeValues = [
  'tithe',
  'donation',
  'offering',
  'event_donation',
  'building_fund',
  'mission_support',
  'special_collection',
  'other'
] as const;

export const financeMethodValues = [
  'cash',
  'check',
  'credit_card',
  'debit_card',
  'bank_transfer',
  'mobile_payment',
  'online',
  'other'
] as const;

export const financeStatusValues = ['pending', 'completed', 'failed', 'refunded', 'cancelled'] as const;

export const projectStatusValues = ['planning', 'active', 'completed', 'cancelled', 'on_hold'] as const;

export const createFinanceSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().length(3).default('ZMW'),
  type: z.enum(financeTypeValues).default('offering'),
  method: z.enum(financeMethodValues).default('cash'),
  status: z.enum(financeStatusValues).default('completed'),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  projectId: z.string().uuid().optional(),
  donorName: z.string().max(255).optional(),
  donorEmail: z.string().email().optional(),
  donorPhone: z.string().max(20).optional(),
  description: z.string().optional(),
  notes: z.string().optional(),
  receiptNumber: z.string().max(100).optional(),
  taxDeductible: z.boolean().default(true),
  isAnonymous: z.boolean().default(false),
  transactionId: z.string().max(255).optional(),
  metadata: z.record(z.any()).optional(),
  recordedAt: z.string().optional().refine((date) => !date || !isNaN(Date.parse(date)), {
    message: 'Invalid finance record date format',
  }),
});

export const updateFinanceSchema = createFinanceSchema.partial();

export const financeFilterSchema = z.object({
  type: z.enum(financeTypeValues).optional(),
  method: z.enum(financeMethodValues).optional(),
  status: z.enum(financeStatusValues).optional(),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  projectId: z.string().uuid().optional(),
  memberId: z.string().uuid().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  minAmount: z.coerce.number().positive().optional(),
  maxAmount: z.coerce.number().positive().optional(),
  search: z.string().optional(),
  page: z.coerce.number().int().positive().default(1),
  limit: z.coerce.number().int().positive().max(100).default(20),
  includeAnonymous: z.coerce.boolean().default(true),
});

export const createFinanceCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
});

export const updateFinanceCategorySchema = createFinanceCategorySchema.partial();

export const createFinanceProjectSchema = z.object({
  name: z.string().min(1, 'Name is required').max(200),
  description: z.string().optional(),
  targetAmount: z.number().positive('Target amount must be positive'),
  currency: z.string().length(3).default('ZMW'),
  status: z.enum(projectStatusValues).default('planning'),
  startDate: z.string().optional().refine((date) => !date || date.trim() === '' || !isNaN(Date.parse(date)), {
    message: 'Invalid start date format',
  }),
  endDate: z.string().optional().refine((date) => !date || date.trim() === '' || !isNaN(Date.parse(date)), {
    message: 'Invalid end date format',
  }),
  isActive: z.boolean().default(true),
});

export const updateFinanceProjectSchema = createFinanceProjectSchema.partial();

export const financeReportSchema = z.object({
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid start date format',
  }),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid end date format',
  }),
  groupBy: z.enum(['day', 'week', 'month', 'year', 'type', 'method', 'branch']).default('month'),
  includeAnonymous: z.boolean().default(true),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  eventId: z.string().uuid().optional(),
  projectId: z.string().uuid().optional(),
});

export type CreateFinanceInput = z.infer<typeof createFinanceSchema>;
export type UpdateFinanceInput = z.infer<typeof updateFinanceSchema>;
export type FinanceFilterInput = z.infer<typeof financeFilterSchema>;
export type CreateFinanceCategoryInput = z.infer<typeof createFinanceCategorySchema>;
export type UpdateFinanceCategoryInput = z.infer<typeof updateFinanceCategorySchema>;
export type CreateFinanceProjectInput = z.infer<typeof createFinanceProjectSchema>;
export type UpdateFinanceProjectInput = z.infer<typeof updateFinanceProjectSchema>;
export type FinanceReportInput = z.infer<typeof financeReportSchema>;