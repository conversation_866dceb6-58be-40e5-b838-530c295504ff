import { z } from 'zod';

// Analytics query parameters schema
export const analyticsQuerySchema = z.object({
  period: z.enum(['7d', '30d', '90d', '1y', 'all']).default('30d'),
  branchId: z.string().uuid().optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
});

// Dashboard overview types
export const dashboardOverviewSchema = z.object({
  totalMembers: z.number(),
  totalEvents: z.number(),
  totalDonations: z.number(),
  totalRevenue: z.number(),
  memberGrowthRate: z.number(),
  eventAttendanceRate: z.number(),
  donationGrowthRate: z.number(),
  recentActivity: z.array(z.object({
    type: z.enum(['member_joined', 'event_created', 'donation_received', 'event_rsvp']),
    title: z.string(),
    description: z.string(),
    timestamp: z.date(),
    metadata: z.record(z.any()).optional(),
  })),
});

// Member analytics types
export const memberAnalyticsSchema = z.object({
  totalMembers: z.number(),
  membersByStatus: z.array(z.object({
    status: z.string(),
    count: z.number(),
    percentage: z.number(),
  })),
  membersByGender: z.array(z.object({
    gender: z.string(),
    count: z.number(),
    percentage: z.number(),
  })),
  membersByAgeGroup: z.array(z.object({
    ageGroup: z.string(),
    count: z.number(),
    percentage: z.number(),
  })),
  membersByBranch: z.array(z.object({
    branchId: z.string().nullable(),
    branchName: z.string(),
    count: z.number(),
    percentage: z.number(),
  })),
  memberGrowthTrend: z.array(z.object({
    period: z.string(),
    newMembers: z.number(),
    totalMembers: z.number(),
    growthRate: z.number(),
  })),
  topEngagedMembers: z.array(z.object({
    memberId: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    eventAttendance: z.number(),
    totalDonations: z.number(),
    engagementScore: z.number(),
  })),
});

// Event analytics types
export const eventAnalyticsSchema = z.object({
  totalEvents: z.number(),
  eventsByType: z.array(z.object({
    type: z.string(),
    count: z.number(),
    percentage: z.number(),
    averageAttendance: z.number(),
  })),
  eventsByStatus: z.array(z.object({
    status: z.string(),
    count: z.number(),
    percentage: z.number(),
  })),
  attendanceMetrics: z.object({
    totalRsvps: z.number(),
    averageAttendanceRate: z.number(),
    mostPopularEventType: z.string(),
    peakAttendanceDay: z.string(),
  }),
  upcomingEvents: z.array(z.object({
    id: z.string(),
    title: z.string(),
    type: z.string(),
    startDate: z.date(),
    rsvpCount: z.number(),
    attendanceRate: z.number().optional(),
  })),
  eventTrends: z.array(z.object({
    period: z.string(),
    eventCount: z.number(),
    attendanceCount: z.number(),
    averageAttendance: z.number(),
  })),
});

// Financial analytics types
export const financialAnalyticsSchema = z.object({
  totalRevenue: z.number(),
  donationsByType: z.array(z.object({
    type: z.string(),
    count: z.number(),
    totalAmount: z.number(),
    percentage: z.number(),
  })),
  donationsByMethod: z.array(z.object({
    method: z.string(),
    count: z.number(),
    totalAmount: z.number(),
    percentage: z.number(),
  })),
  donationTrends: z.array(z.object({
    period: z.string(),
    donationCount: z.number(),
    totalAmount: z.number(),
    averageAmount: z.number(),
  })),
  topDonors: z.array(z.object({
    donorId: z.string().nullable(),
    donorName: z.string(),
    totalDonations: z.number(),
    donationCount: z.number(),
    isAnonymous: z.boolean(),
  })),
  fundraisingGoals: z.array(z.object({
    eventId: z.string().nullable(),
    eventTitle: z.string().nullable(),
    goal: z.number(),
    raised: z.number(),
    percentage: z.number(),
    daysRemaining: z.number().nullable(),
  })),
});

// Branch analytics types
export const branchAnalyticsSchema = z.object({
  totalBranches: z.number(),
  branchComparison: z.array(z.object({
    branchId: z.string(),
    branchName: z.string(),
    memberCount: z.number(),
    eventCount: z.number(),
    totalDonations: z.number(),
    averageAttendance: z.number(),
  })),
  branchPerformance: z.array(z.object({
    branchId: z.string(),
    branchName: z.string(),
    growthRate: z.number(),
    engagementScore: z.number(),
    revenuePerMember: z.number(),
  })),
});

// Export types
export type AnalyticsQueryInput = z.infer<typeof analyticsQuerySchema>;
export type DashboardOverview = z.infer<typeof dashboardOverviewSchema>;
export type MemberAnalytics = z.infer<typeof memberAnalyticsSchema>;
export type EventAnalytics = z.infer<typeof eventAnalyticsSchema>;
export type FinancialAnalytics = z.infer<typeof financialAnalyticsSchema>;
export type BranchAnalytics = z.infer<typeof branchAnalyticsSchema>;