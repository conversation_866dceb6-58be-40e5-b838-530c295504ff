import { z } from 'zod';

// Enhanced validation schema with comprehensive field validation rules
export const createMemberSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'First name contains invalid characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'Last name contains invalid characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase(),

  phone: z.string()
    .optional()
    .refine((phone) => {
      if (!phone || phone.trim() === '') return true;
      // Remove common formatting characters and validate
      const cleanPhone = phone.replace(/[\s\-()]/g, '');
      return /^[+]?[1-9]\d{6,15}$/.test(cleanPhone);
    }, 'Please enter a valid phone number'),
  dateOfBirth: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return !isNaN(birthDate.getTime()) && age >= 0 && age <= 120;
    }, 'Please enter a valid date of birth'),
  gender: z.enum(['male', 'female', 'other'], {
    message: 'Gender must be male, female, or other'
  }).optional(),
  address: z.string()
    .max(500, 'Address must be less than 500 characters')
    .optional(),
  branchId: z.string()
    .uuid('Invalid branch selection')
    .optional(),
  roleId: z.string()
    .uuid('Invalid role selection')
    .optional(),
  status: z.enum(['active', 'inactive', 'suspended'], {
    message: 'Status must be active, inactive, or suspended'
  }).default('active'),
});

export const updateMemberSchema = createMemberSchema.partial();

export const loginSchema = z.object({
  emailOrUserId: z.string().min(1, 'Email or User ID is required'),
  password: z.string().min(1, 'Password is required'),
});

export const memberFilterSchema = z.object({
  page: z.coerce.number().min(1).default(1),
  limit: z.coerce.number().min(1).max(100).default(10),
  search: z.string().optional(),
  branchId: z.string().uuid('Invalid branch ID').optional(),
  roleId: z.string().uuid('Invalid role ID').optional(),
  status: z.enum(['active', 'inactive', 'suspended']).optional(),
  gender: z.enum(['male', 'female', 'other']).optional(),
});

export type CreateMemberInput = z.infer<typeof createMemberSchema>;
export type UpdateMemberInput = z.infer<typeof updateMemberSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type MemberFilterInput = z.infer<typeof memberFilterSchema>;