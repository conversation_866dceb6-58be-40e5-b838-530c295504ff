import { Router } from 'express';
import {
  createFinance,
  getFinances,
  getFinance,
  updateFinance,
  deleteFinance,
  getFinanceReport,
  createFinanceCategory,
  getFinanceCategories,
  updateFinanceCategory,
  deleteFinanceCategory
} from '../controllers/financeController';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /churches/{slug}/finances:
 *   post:
 *     summary: Record a new finance entry
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [amount]
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 example: 100.00
 *               currency:
 *                 type: string
 *                 length: 3
 *                 default: ZMW
 *                 example: ZMW
 *               type:
 *                 type: string
 *                 enum: [tithe, donation, offering, event_donation, building_fund, mission_support, special_collection, other]
 *                 default: offering
 *               method:
 *                 type: string
 *                 enum: [cash, check, credit_card, debit_card, bank_transfer, mobile_payment, online, other]
 *                 default: cash
 *               status:
 *                 type: string
 *                 enum: [pending, completed, failed, refunded, cancelled]
 *                 default: completed
 *               eventId:
 *                 type: string
 *                 format: uuid
 *                 description: Associated event ID (for event donations)
 *               projectId:
 *                 type: string
 *                 format: uuid
 *                 description: Associated project ID (for project funding)
 *               donorName:
 *                 type: string
 *                 maxLength: 255
 *                 example: "John Doe"
 *               donorEmail:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               donorPhone:
 *                 type: string
 *                 maxLength: 20
 *                 example: "+***********-456"
 *               description:
 *                 type: string
 *                 example: "Sunday offering"
 *               notes:
 *                 type: string
 *                 description: Internal notes (not visible to donor)
 *               receiptNumber:
 *                 type: string
 *                 maxLength: 100
 *                 description: Custom receipt number (auto-generated if not provided)
 *               taxDeductible:
 *                 type: boolean
 *                 default: true
 *               isAnonymous:
 *                 type: boolean
 *                 default: false
 *               transactionId:
 *                 type: string
 *                 maxLength: 255
 *                 description: Payment processor transaction ID
 *               metadata:
 *                 type: object
 *                 description: Additional metadata
 *               recordedAt:
 *                 type: string
 *                 format: date-time
 *                 description: When the finance record was recorded
 *     responses:
 *       201:
 *         description: Finance record created successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Church not found
 */
router.post('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), createFinance);

/**
 * @swagger
 * /churches/{slug}/finances:
 *   get:
 *     summary: Get all finance records
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [tithe, donation, offering, event_donation, building_fund, mission_support, special_collection, other]
 *         description: Filter by finance type
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *           enum: [cash, check, credit_card, debit_card, bank_transfer, mobile_payment, online, other]
 *         description: Filter by payment method
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, failed, refunded, cancelled]
 *         description: Filter by status
 *       - in: query
 *         name: eventId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by event ID
 *       - in: query
 *         name: projectId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by project ID
 *       - in: query
 *         name: memberId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by member ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by start date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by end date
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in description, donor name, or receipt number
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 20
 *           maximum: 100
 *         description: Number of records per page
 *     responses:
 *       200:
 *         description: List of finance records
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Church not found
 */
router.get('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getFinances);

/**
 * @swagger
 * /churches/{slug}/finances/{id}:
 *   get:
 *     summary: Get a specific finance record
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Finance record ID
 *     responses:
 *       200:
 *         description: Finance record details
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Finance record not found
 */
router.get('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getFinance);

/**
 * @swagger
 * /churches/{slug}/finances/{id}:
 *   put:
 *     summary: Update a finance record
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Finance record ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *               currency:
 *                 type: string
 *                 length: 3
 *               type:
 *                 type: string
 *                 enum: [tithe, donation, offering, event_donation, building_fund, mission_support, special_collection, other]
 *               method:
 *                 type: string
 *                 enum: [cash, check, credit_card, debit_card, bank_transfer, mobile_payment, online, other]
 *               status:
 *                 type: string
 *                 enum: [pending, completed, failed, refunded, cancelled]
 *               description:
 *                 type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Finance record updated successfully
 *       400:
 *         description: Invalid input data
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Finance record not found
 */
router.put('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updateFinance);

/**
 * @swagger
 * /churches/{slug}/finances/{id}:
 *   delete:
 *     summary: Delete a finance record
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Finance record ID
 *     responses:
 *       200:
 *         description: Finance record deleted successfully
 *       401:
 *         description: Unauthorized
 *       404:
 *         description: Finance record not found
 */
router.delete('/:id', authenticate, validateTenant, requireTenantAccess, deleteFinance);

/**
 * @swagger
 * /churches/{slug}/finances/report:
 *   get:
 *     summary: Get finance report
 *     tags: [Finances]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date for report
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: End date for report
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [tithe, donation, offering, event_donation, building_fund, mission_support, special_collection, other]
 *         description: Filter by finance type
 *     responses:
 *       200:
 *         description: Finance report data
 *       400:
 *         description: Invalid date range
 *       401:
 *         description: Unauthorized
 */
router.get('/report', authenticate, validateTenant, requireTenantAccess, getFinanceReport);

// Finance Categories Routes
router.post('/categories', authenticate, validateTenant, requireTenantAccess, createFinanceCategory);
router.get('/categories', authenticate, validateTenant, requireTenantAccess, getFinanceCategories);
router.put('/categories/:id', authenticate, validateTenant, requireTenantAccess, updateFinanceCategory);
router.delete('/categories/:id', authenticate, validateTenant, requireTenantAccess, deleteFinanceCategory);



export default router;