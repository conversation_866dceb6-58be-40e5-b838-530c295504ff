import { Router } from 'express';
import { getCurrencies } from '../controllers/currencyController';

const router = Router();

/**
 * @swagger
 * /currencies:
 *   get:
 *     summary: Get available currencies
 *     tags: [Currencies]
 *     responses:
 *       200:
 *         description: Currencies retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     currencies:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           code:
 *                             type: string
 *                             example: USD
 *                           name:
 *                             type: string
 *                             example: US Dollar
 *                           symbol:
 *                             type: string
 *                             example: $
 *                           isActive:
 *                             type: boolean
 *                             example: true
 */
router.get('/', getCurrencies);

export default router;