import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { validateTenant } from '../middleware/tenant';
import { getNotifications, getUnreadCount, markAsRead } from '../controllers/notificationController';

const router = Router({ mergeParams: true });

// All routes require auth and tenant context
router.use(authenticate, validateTenant);

/**
 * @swagger
 * /api/churches/{slug}/notifications:
 *   get:
 *     summary: List notifications for the authenticated member
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: A list of notifications with pagination
 *       401:
 *         description: Unauthorized
 */
router.get('/notifications', getNotifications);

/**
 * @swagger
 * /api/churches/{slug}/notifications/unread-count:
 *   get:
 *     summary: Get unread notifications count for the authenticated member
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Unread count
 *       401:
 *         description: Unauthorized
 */
router.get('/notifications/unread-count', getUnreadCount);

/**
 * @swagger
 * /api/churches/{slug}/notifications/mark-read:
 *   post:
 *     summary: Mark notifications as read
 *     tags: [Notifications]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               notificationIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *             example:
 *               notificationIds: ["d290f1ee-6c54-4b01-90e6-d701748f0851"]
 *     responses:
 *       200:
 *         description: Notifications marked as read
 *       401:
 *         description: Unauthorized
 */
router.post('/notifications/mark-read', markAsRead);

export default router;
