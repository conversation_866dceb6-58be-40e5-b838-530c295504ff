import { Router } from 'express';
import {
  createBranch,
  getBranches,
  getBranch,
  updateBranch,
  deleteBranch
} from '../controllers/branchController';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /churches/{slug}/branches:
 *   post:
 *     summary: Create a new branch for the church
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateBranchRequest'
 *     responses:
 *       201:
 *         description: Branch created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BranchResponse'
 *       400:
 *         description: Bad request - validation error or duplicate slug
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.post('/:slug/branches', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), createBranch);

/**
 * @swagger
 * /churches/{slug}/branches:
 *   get:
 *     summary: Get all branches for the church
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of branches per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search branches by name, description, or address
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *       - in: query
 *         name: isMainBranch
 *         schema:
 *           type: boolean
 *         description: Filter by main branch status
 *     responses:
 *       200:
 *         description: List of branches retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/BranchListResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/:slug/branches', authenticate, validateTenant, requireTenantAccess, getBranches);

/**
 * @swagger
 * /churches/{slug}/branches/{branchId}:
 *   get:
 *     summary: Get a specific branch by ID
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: branchId
 *         required: true
 *         schema:
 *           type: string
 *         description: Branch ID
 *     responses:
 *       200:
 *         description: Branch found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     branch:
 *                       $ref: '#/components/schemas/Branch'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Branch or church not found
 */
router.get('/:slug/branches/:branchId', authenticate, validateTenant, requireTenantAccess, getBranch);

/**
 * @swagger
 * /churches/{slug}/branches/{branchId}:
 *   put:
 *     summary: Update a branch
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: branchId
 *         required: true
 *         schema:
 *           type: string
 *         description: Branch ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateBranchRequest'
 *     responses:
 *       200:
 *         description: Branch updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Branch updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     branch:
 *                       $ref: '#/components/schemas/Branch'
 *       400:
 *         description: Bad request - validation error or duplicate slug
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Branch or church not found
 */
router.put('/:slug/branches/:branchId', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updateBranch);

/**
 * @swagger
 * /churches/{slug}/branches/{branchId}:
 *   delete:
 *     summary: Delete a branch (soft delete)
 *     tags: [Branches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: branchId
 *         required: true
 *         schema:
 *           type: string
 *         description: Branch ID
 *     responses:
 *       200:
 *         description: Branch deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Branch deleted successfully
 *       400:
 *         description: Cannot delete main branch
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Branch or church not found
 */
router.delete('/:slug/branches/:branchId', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), deleteBranch);

export default router;