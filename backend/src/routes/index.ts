import { Router } from 'express';
import authRoutes from './authRoutes';
import churchRoutes from './churchRoutes';
import branchRoutes from './branchRoutes';
import memberRoutes from './memberRoutes';
import roleRoutes from './roleRoutes';
import eventRoutes from './eventRoutes';
import financeRoutes from './financeRoutes';
import donationRoutes from './donationRoutes';
import paymentMethodRoutes from './paymentMethodRoutes';
import currencyRoutes from './currencyRoutes';
import analyticsRoutes from './analyticsRoutes';
import announcementRoutes from './announcementRoutes';
import onboardingRoutes from './onboardingRoutes';
import monitoringRoutes from './monitoringRoutes';
import mailRoutes from './mailRoutes';
import notificationRoutes from './notificationRoutes';

const router = Router();

router.use('/auth', authRoutes);
router.use('/onboarding', onboardingRoutes);
router.use('/monitoring', monitoringRoutes);
router.use('/currencies', currencyRoutes);
router.use('/mail', mailRoutes);
router.use('/churches', churchRoutes);
router.use('/churches', branchRoutes);
router.use('/members', memberRoutes);
router.use('/roles', roleRoutes);

// Church-scoped routes
router.use('/churches/:slug/members', memberRoutes);
router.use('/churches/:slug/events', eventRoutes);
router.use('/churches/:slug/finances', financeRoutes);
router.use('/churches/:slug/donations', donationRoutes);
router.use('/churches/:slug/payment-methods', paymentMethodRoutes);
router.use('/churches/:slug/announcements', announcementRoutes);
router.use('/churches/:slug/branches', branchRoutes);
router.use('/churches/:slug/roles', roleRoutes);
router.use('/churches/:slug/analytics', analyticsRoutes);
router.use('/churches/:slug/mail', mailRoutes);
router.use('/churches/:slug', notificationRoutes);

export default router;