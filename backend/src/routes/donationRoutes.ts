import { Router } from 'express';
import {
  createDonation,
  getDonations,
  getDonation,
  updateDonation,
  deleteDonation,
  getDonationReport,
  createDonationCategory,
  getDonationCategories,
  updateDonationCategory,
  deleteDonationCategory
} from '../controllers/donationController';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /churches/{slug}/donations:
 *   post:
 *     summary: Record a new donation
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [amount]
 *             properties:
 *               amount:
 *                 type: number
 *                 minimum: 0.01
 *                 example: 100.00
 *               currency:
 *                 type: string
 *                 length: 3
 *                 default: USD
 *                 example: USD
 *               type:
 *                 type: string
 *                 enum: [tithe, offering, event_donation, building_fund, mission_support, special_collection, other]
 *                 default: offering
 *               method:
 *                 type: string
 *                 enum: [cash, check, credit_card, debit_card, bank_transfer, mobile_payment, online, other]
 *                 default: cash
 *               status:
 *                 type: string
 *                 enum: [pending, completed, failed, refunded, cancelled]
 *                 default: completed
 *               eventId:
 *                 type: string
 *                 format: uuid
 *                 description: Associated event ID (for event donations)
 *               donorName:
 *                 type: string
 *                 maxLength: 255
 *                 example: "John Doe"
 *               donorEmail:
 *                 type: string
 *                 format: email
 *                 example: "<EMAIL>"
 *               donorPhone:
 *                 type: string
 *                 maxLength: 20
 *                 example: "******-123-4567"
 *               description:
 *                 type: string
 *                 example: "Sunday offering"
 *               notes:
 *                 type: string
 *                 description: Internal notes (not visible to donor)
 *               receiptNumber:
 *                 type: string
 *                 maxLength: 100
 *                 description: Custom receipt number (auto-generated if not provided)
 *               taxDeductible:
 *                 type: boolean
 *                 default: true
 *               isAnonymous:
 *                 type: boolean
 *                 default: false
 *               transactionId:
 *                 type: string
 *                 maxLength: 255
 *                 description: Payment processor transaction ID
 *               metadata:
 *                 type: object
 *                 description: Additional data (payment processor info, etc.)
 *               donatedAt:
 *                 type: string
 *                 format: date-time
 *                 description: When the donation was made (defaults to current time)
 *     responses:
 *       201:
 *         description: Donation recorded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Donation recorded successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     donation:
 *                       $ref: '#/components/schemas/Donation'
 *       400:
 *         description: Validation error or event doesn't allow donations
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church or event not found
 */
router.post('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), createDonation);

/**
 * @swagger
 * /churches/{slug}/donations:
 *   get:
 *     summary: Get church donations
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [tithe, offering, event_donation, building_fund, mission_support, special_collection, other]
 *         description: Filter by donation type
 *       - in: query
 *         name: method
 *         schema:
 *           type: string
 *           enum: [cash, check, credit_card, debit_card, bank_transfer, mobile_payment, online, other]
 *         description: Filter by donation method
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [pending, completed, failed, refunded, cancelled]
 *         description: Filter by donation status
 *       - in: query
 *         name: eventId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by event ID
 *       - in: query
 *         name: donorId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by donor ID
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter donations from this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter donations until this date
 *       - in: query
 *         name: minAmount
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Minimum donation amount
 *       - in: query
 *         name: maxAmount
 *         schema:
 *           type: number
 *           minimum: 0
 *         description: Maximum donation amount
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in donor name, description, or receipt number
 *       - in: query
 *         name: includeAnonymous
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include anonymous donations
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Donations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     donations:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DonationWithDetails'
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalAmount:
 *                           type: number
 *                           example: 15000.00
 *                         totalCount:
 *                           type: integer
 *                           example: 150
 *                         currency:
 *                           type: string
 *                           example: USD
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getDonations);

/**
 * @swagger
 * /churches/{slug}/donations/report:
 *   get:
 *     summary: Get donation report
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Report start date
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Report end date
 *       - in: query
 *         name: groupBy
 *         schema:
 *           type: string
 *           enum: [day, week, month, year, type, method]
 *           default: month
 *         description: Group donations by time period or category
 *       - in: query
 *         name: eventId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific event
 *       - in: query
 *         name: includeAnonymous
 *         schema:
 *           type: boolean
 *           default: true
 *         description: Include anonymous donations
 *     responses:
 *       200:
 *         description: Donation report generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     report:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           period:
 *                             type: string
 *                             example: "2024-01"
 *                           totalAmount:
 *                             type: number
 *                             example: 5000.00
 *                           count:
 *                             type: integer
 *                             example: 45
 *                     summary:
 *                       type: object
 *                       properties:
 *                         totalAmount:
 *                           type: number
 *                           example: 25000.00
 *                         startDate:
 *                           type: string
 *                           format: date
 *                         endDate:
 *                           type: string
 *                           format: date
 *                         groupBy:
 *                           type: string
 *                           example: month
 *       400:
 *         description: Invalid date range or parameters
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/report', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), getDonationReport);

/**
 * @swagger
 * /churches/{slug}/donations/{id}:
 *   get:
 *     summary: Get donation by ID
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Donation ID
 *     responses:
 *       200:
 *         description: Donation retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     donation:
 *                       $ref: '#/components/schemas/DonationWithDetails'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Donation not found
 */
router.get('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getDonation);

/**
 * @swagger
 * /churches/{slug}/donations/{id}:
 *   put:
 *     summary: Update donation
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Donation ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDonationRequest'
 *     responses:
 *       200:
 *         description: Donation updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Donation updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     donation:
 *                       $ref: '#/components/schemas/Donation'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Donation not found
 */
router.put('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updateDonation);

/**
 * @swagger
 * /churches/{slug}/donations/{id}:
 *   delete:
 *     summary: Delete donation
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Donation ID
 *     responses:
 *       200:
 *         description: Donation deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Donation deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Donation not found
 */
router.delete('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), deleteDonation);

/**
 * @swagger
 * /churches/{slug}/donations/categories:
 *   post:
 *     summary: Create donation category
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name]
 *             properties:
 *               name:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Building Fund"
 *               description:
 *                 type: string
 *                 example: "Funds for new church building"
 *               targetAmount:
 *                 type: number
 *                 minimum: 0
 *                 example: 50000.00
 *               isActive:
 *                 type: boolean
 *                 default: true
 *     responses:
 *       201:
 *         description: Donation category created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.post('/categories', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), createDonationCategory);

/**
 * @swagger
 * /churches/{slug}/donations/categories:
 *   get:
 *     summary: Get donation categories
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     responses:
 *       200:
 *         description: Donation categories retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     categories:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/DonationCategory'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/categories', authenticate, validateTenant, requireTenantAccess, getDonationCategories);

/**
 * @swagger
 * /churches/{slug}/donations/categories/{id}:
 *   put:
 *     summary: Update donation category
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateDonationCategoryRequest'
 *     responses:
 *       200:
 *         description: Donation category updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Category not found
 */
router.put('/categories/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updateDonationCategory);

/**
 * @swagger
 * /churches/{slug}/donations/categories/{id}:
 *   delete:
 *     summary: Delete donation category
 *     tags: [Donations]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Category ID
 *     responses:
 *       200:
 *         description: Donation category deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Category not found
 */
router.delete('/categories/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), deleteDonationCategory);

export default router;