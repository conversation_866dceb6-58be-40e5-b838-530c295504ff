import { Router } from 'express';
import {
  createAnnouncement,
  getAnnouncements,
  getAnnouncement,
  updateAnnouncement,
  deleteAnnouncement,
  getComments,
  createComment,
  updateComment,
  deleteComment,
  createReaction,
  deleteReaction,
  markAsViewed,
  bulkDeleteAnnouncements,
  bulkUpdateStatus
} from '../controllers/announcementController';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /churches/{slug}/announcements:
 *   post:
 *     summary: Create a new announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [title, content]
 *             properties:
 *               title:
 *                 type: string
 *                 maxLength: 255
 *                 example: "Sunday Service Update"
 *               content:
 *                 type: string
 *                 example: "This Sunday's service will be held in the main sanctuary at 10 AM."
 *               summary:
 *                 type: string
 *                 maxLength: 500
 *                 example: "Service time change notification"
 *               type:
 *                 type: string
 *                 enum: [general, urgent, event_related, service_update, prayer_request, community_news, financial, special_event, ministry_update, volunteer_opportunity, other]
 *                 default: general
 *               priority:
 *                 type: string
 *                 enum: [low, normal, high, urgent]
 *                 default: normal
 *               targetAudience:
 *                 type: string
 *                 enum: [all_members, specific_branches, specific_roles, custom_group]
 *                 default: all_members
 *               targetBranches:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 description: Required if targetAudience is specific_branches
 *               targetRoles:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Required if targetAudience is specific_roles
 *               eventId:
 *                 type: string
 *                 format: uuid
 *                 description: Link to related event
 *               imageUrl:
 *                 type: string
 *                 format: uri
 *               attachments:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     url:
 *                       type: string
 *                       format: uri
 *                     type:
 *                       type: string
 *                     size:
 *                       type: number
 *                 maxItems: 10
 *               externalLinks:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     title:
 *                       type: string
 *                     url:
 *                       type: string
 *                       format: uri
 *                     description:
 *                       type: string
 *                 maxItems: 5
 *               tags:
 *                 type: array
 *                 items:
 *                   type: string
 *                   maxLength: 50
 *                 maxItems: 10
 *               scheduledFor:
 *                 type: string
 *                 format: date-time
 *                 description: Schedule announcement for later
 *               expiresAt:
 *                 type: string
 *                 format: date-time
 *                 description: Automatically archive after this date
 *               isPinned:
 *                 type: boolean
 *                 default: false
 *               allowComments:
 *                 type: boolean
 *                 default: true
 *               requiresAcknowledgment:
 *                 type: boolean
 *                 default: false
 *               sendNotification:
 *                 type: boolean
 *                 default: true
 *               notificationChannels:
 *                 type: object
 *                 properties:
 *                   email:
 *                     type: boolean
 *                     default: true
 *                   sms:
 *                     type: boolean
 *                     default: false
 *                   push:
 *                     type: boolean
 *                     default: true
 *                   inApp:
 *                     type: boolean
 *                     default: true
 *               metadata:
 *                 type: object
 *                 description: Additional custom fields
 *     responses:
 *       201:
 *         description: Announcement created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Announcement created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     announcement:
 *                       $ref: '#/components/schemas/Announcement'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.post('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), createAnnouncement);

/**
 * @swagger
 * /churches/{slug}/announcements:
 *   get:
 *     summary: Get church announcements
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [general, urgent, event_related, service_update, prayer_request, community_news, financial, special_event, ministry_update, volunteer_opportunity, other]
 *         description: Filter by announcement type
 *       - in: query
 *         name: priority
 *         schema:
 *           type: string
 *           enum: [low, normal, high, urgent]
 *         description: Filter by priority
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, scheduled, published, archived, expired]
 *         description: Filter by status
 *       - in: query
 *         name: authorId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by author
 *       - in: query
 *         name: eventId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by related event
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter announcements published after this date
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Filter announcements published before this date
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search in title, content, and summary
 *       - in: query
 *         name: tags
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: Filter by tags
 *       - in: query
 *         name: isPinned
 *         schema:
 *           type: boolean
 *         description: Filter by pinned status
 *       - in: query
 *         name: requiresAcknowledgment
 *         schema:
 *           type: boolean
 *         description: Filter by acknowledgment requirement
 *       - in: query
 *         name: unreadOnly
 *         schema:
 *           type: boolean
 *         description: Show only unread announcements
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: Items per page
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *           enum: [createdAt, publishedAt, priority, title]
 *           default: publishedAt
 *         description: Sort field
 *       - in: query
 *         name: sortOrder
 *         schema:
 *           type: string
 *           enum: [asc, desc]
 *           default: desc
 *         description: Sort order
 *     responses:
 *       200:
 *         description: Announcements retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     announcements:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AnnouncementWithStats'
 *                     pagination:
 *                       $ref: '#/components/schemas/Pagination'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/', authenticate, validateTenant, requireTenantAccess, getAnnouncements);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}:
 *   get:
 *     summary: Get announcement by ID
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     responses:
 *       200:
 *         description: Announcement retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     announcement:
 *                       $ref: '#/components/schemas/AnnouncementWithDetails'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.get('/:id', authenticate, validateTenant, requireTenantAccess, getAnnouncement);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}:
 *   put:
 *     summary: Update announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateAnnouncementRequest'
 *     responses:
 *       200:
 *         description: Announcement updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Announcement updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     announcement:
 *                       $ref: '#/components/schemas/Announcement'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.put('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updateAnnouncement);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}:
 *   delete:
 *     summary: Delete announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     responses:
 *       200:
 *         description: Announcement deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Announcement deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.delete('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), deleteAnnouncement);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}/view:
 *   post:
 *     summary: Mark announcement as viewed/acknowledged
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     requestBody:
 *       required: false
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               acknowledged:
 *                 type: boolean
 *                 default: false
 *                 description: Whether to mark as acknowledged
 *               deviceInfo:
 *                 type: object
 *                 properties:
 *                   userAgent:
 *                     type: string
 *                   platform:
 *                     type: string
 *                   browser:
 *                     type: string
 *     responses:
 *       200:
 *         description: Announcement marked as viewed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Announcement marked as viewed
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.post('/:id/view', authenticate, validateTenant, requireTenantAccess, markAsViewed);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}/comments:
 *   get:
 *     summary: Get announcement comments
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     responses:
 *       200:
 *         description: Comments retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     comments:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/AnnouncementComment'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied or comments not allowed
 *       404:
 *         description: Announcement not found
 */
router.get('/:id/comments', authenticate, validateTenant, requireTenantAccess, getComments);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}/comments:
 *   post:
 *     summary: Add comment to announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [content]
 *             properties:
 *               content:
 *                 type: string
 *                 maxLength: 2000
 *                 example: "Thank you for this update!"
 *               parentCommentId:
 *                 type: string
 *                 format: uuid
 *                 description: ID of parent comment for replies
 *     responses:
 *       201:
 *         description: Comment created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Comment created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     comment:
 *                       $ref: '#/components/schemas/AnnouncementComment'
 *       400:
 *         description: Validation error or comments not allowed
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.post('/:id/comments', authenticate, validateTenant, requireTenantAccess, createComment);

/**
 * @swagger
 * /churches/{slug}/announcements/comments/{commentId}:
 *   put:
 *     summary: Update announcement comment
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: commentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Comment ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [content]
 *             properties:
 *               content:
 *                 type: string
 *                 maxLength: 2000
 *     responses:
 *       200:
 *         description: Comment updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Comment updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     comment:
 *                       $ref: '#/components/schemas/AnnouncementComment'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied - can only edit own comments
 *       404:
 *         description: Comment not found
 */
router.put('/comments/:commentId', authenticate, validateTenant, requireTenantAccess, updateComment);

/**
 * @swagger
 * /churches/{slug}/announcements/comments/{commentId}:
 *   delete:
 *     summary: Delete announcement comment
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: commentId
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Comment ID
 *     responses:
 *       200:
 *         description: Comment deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Comment deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied - can only delete own comments
 *       404:
 *         description: Comment not found
 */
router.delete('/comments/:commentId', authenticate, validateTenant, requireTenantAccess, deleteComment);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}/reactions:
 *   post:
 *     summary: Add reaction to announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [reactionType]
 *             properties:
 *               reactionType:
 *                 type: string
 *                 enum: [like, love, pray, amen, heart, thumbs_up]
 *                 example: like
 *     responses:
 *       201:
 *         description: Reaction created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Reaction created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     reaction:
 *                       $ref: '#/components/schemas/AnnouncementReaction'
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Announcement not found
 */
router.post('/:id/reactions', authenticate, validateTenant, requireTenantAccess, createReaction);

/**
 * @swagger
 * /churches/{slug}/announcements/{id}/reactions:
 *   delete:
 *     summary: Remove reaction from announcement
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Announcement ID
 *     responses:
 *       200:
 *         description: Reaction deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Reaction deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Reaction not found
 */
router.delete('/:id/reactions', authenticate, validateTenant, requireTenantAccess, deleteReaction);

/**
 * @swagger
 * /churches/{slug}/announcements/bulk/delete:
 *   post:
 *     summary: Bulk delete announcements
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [announcementIds]
 *             properties:
 *               announcementIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 minItems: 1
 *                 maxItems: 50
 *                 example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
 *     responses:
 *       200:
 *         description: Announcements deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: 2 announcements deleted successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.post('/bulk/delete', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), bulkDeleteAnnouncements);

/**
 * @swagger
 * /churches/{slug}/announcements/bulk/status:
 *   post:
 *     summary: Bulk update announcement status
 *     tags: [Announcements]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [announcementIds, status]
 *             properties:
 *               announcementIds:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: uuid
 *                 minItems: 1
 *                 maxItems: 50
 *                 example: ["123e4567-e89b-12d3-a456-************", "123e4567-e89b-12d3-a456-************"]
 *               status:
 *                 type: string
 *                 enum: [draft, scheduled, published, archived, expired]
 *                 example: published
 *     responses:
 *       200:
 *         description: Announcements updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: 2 announcements updated successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.post('/bulk/status', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), bulkUpdateStatus);

export default router;