import { Router } from 'express';
import {
  registerChurch,
  createChurch,
  getChurches,
  getChurch,
  getChurchBySlug,
  updateChurch,
  updateChurchSettings,
  getChurchSettings,
  updateChurchLogo,
  deleteChurch
} from '../controllers/churchController';
import { authenticate, authorize } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router();

/**
 * @swagger
 * /churches/register:
 *   post:
 *     summary: Register a new church with admin user (Public endpoint)
 *     tags: [Churches]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RegisterChurchRequest'
 *     responses:
 *       201:
 *         description: Church and admin account created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RegisterChurchResponse'
 *       400:
 *         description: Bad request - validation error, duplicate slug, or email already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/register', registerChurch);

/**
 * @swagger
 * /churches:
 *   post:
 *     summary: Create a new church (Admin only)
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateChurchRequest'
 *     responses:
 *       201:
 *         description: Church created successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChurchResponse'
 *       400:
 *         description: Bad request - validation error or duplicate slug
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/', authenticate, authorize('manage_system'), createChurch);

/**
 * @swagger
 * /churches:
 *   get:
 *     summary: Get all active churches (Public endpoint)
 *     tags: [Churches]
 *     responses:
 *       200:
 *         description: List of all active churches
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChurchListResponse'
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/', getChurches);

/**
 * @swagger
 * /churches/slug/{slug}:
 *   get:
 *     summary: Get church by slug (Public endpoint)
 *     tags: [Churches]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *           pattern: '^[a-z0-9-]+$'
 *         description: URL-friendly church identifier
 *         example: grace-community-church
 *     responses:
 *       200:
 *         description: Church found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     church:
 *                       $ref: '#/components/schemas/Church'
 *       404:
 *         description: Church not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/slug/:slug', getChurchBySlug);

/**
 * @swagger
 * /churches/{slug}/settings:
 *   get:
 *     summary: Get church settings (Church admins only)
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     responses:
 *       200:
 *         description: Church settings retrieved
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChurchSettingsResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/:slug/settings', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), getChurchSettings);

/**
 * @swagger
 * /churches/{slug}/settings:
 *   put:
 *     summary: Update church settings (Church admins only)
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ChurchSettingsRequest'
 *     responses:
 *       200:
 *         description: Church settings updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChurchSettingsResponse'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.put('/:slug/settings', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), updateChurchSettings);

/**
 * @swagger
 * /churches/{slug}/logo:
 *   put:
 *     summary: Update church logo (Church admins only)
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               logo:
 *                 type: string
 *                 description: Base64 encoded image data URL or URL to image
 *                 example: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQ..."
 *             required:
 *               - logo
 *     responses:
 *       200:
 *         description: Church logo updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Church logo updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     church:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         slug:
 *                           type: string
 *                         logo:
 *                           type: string
 *                         updatedAt:
 *                           type: string
 *                           format: date-time
 *       400:
 *         description: Bad request - missing logo data
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.put('/:slug/logo', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), updateChurchLogo);

/**
 * @swagger
 * /churches/{id}:
 *   get:
 *     summary: Get church by ID
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Church unique identifier
 *     responses:
 *       200:
 *         description: Church found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     church:
 *                       $ref: '#/components/schemas/Church'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Church not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/:id', authenticate, authorize('view_church'), getChurch);

/**
 * @swagger
 * /churches/{slug}:
 *   put:
 *     summary: Update church by slug (Church admins only)
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               address:
 *                 type: string
 *               phone:
 *                 type: string
 *               email:
 *                 type: string
 *                 format: email
 *               website:
 *                 type: string
 *                 format: uri
 *               logo:
 *                 type: string
 *                 format: uri
 *     responses:
 *       200:
 *         description: Church updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Church updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     church:
 *                       $ref: '#/components/schemas/Church'
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.put('/:slug', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), updateChurch);

/**
 * @swagger
 * /churches/{slug}:
 *   delete:
 *     summary: Delete church by slug (soft delete) - Super Admin only
 *     tags: [Churches]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     responses:
 *       200:
 *         description: Church deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Church deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.delete('/:slug', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin']), deleteChurch);

export default router;