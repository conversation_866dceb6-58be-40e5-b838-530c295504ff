import { Router } from 'express';
import {
  create<PERSON><PERSON><PERSON>,
  getM<PERSON>bers,
  getM<PERSON>ber,
  updateM<PERSON>ber,
  deleteMember,
  assignRole,
  removeRole,
  assignBranch,
  removeBranch
} from '../controllers/memberController';
import { authenticate, authorize } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /members:
 *   post:
 *     summary: Create a new member account (Admin only)
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [firstName, lastName, email]
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: Member's first name
 *               lastName:
 *                 type: string
 *                 description: Member's last name
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Member's email address
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: Optional password (if not provided, a temporary one will be generated)
 *               phone:
 *                 type: string
 *                 description: Member's phone number
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Member's date of birth
 *               gender:
 *                 type: string
 *                 enum: [male, female, other]
 *                 description: Member's gender
 *               address:
 *                 type: string
 *                 description: Member's address
 *               branchId:
 *                 type: string
 *                 description: Branch ID (defaults to main branch if not provided)
 *               roleId:
 *                 type: string
 *                 description: Role ID (defaults to Member role if not provided)
 *               status:
 *                 type: string
 *                 enum: [active, inactive, suspended]
 *                 description: Member's status (defaults to active)
 *     responses:
 *       201:
 *         description: Member created successfully
 *       400:
 *         description: Invalid input or email already exists
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.post('/', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), 
  createMember
);

/**
 * @swagger
 * /members:
 *   get:
 *     summary: Get all members (church-specific) with filtering
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Number of members per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search members by name or email
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: string
 *         description: Filter by branch ID
 *       - in: query
 *         name: roleId
 *         schema:
 *           type: string
 *         description: Filter by role ID
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [active, inactive, suspended]
 *         description: Filter by member status
 *       - in: query
 *         name: gender
 *         schema:
 *           type: string
 *           enum: [male, female, other]
 *         description: Filter by gender
 *     responses:
 *       200:
 *         description: List of members with pagination
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.get('/', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member']), 
  getMembers
);

/**
 * @swagger
 * /members/{id}:
 *   get:
 *     summary: Get member by ID
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     responses:
 *       200:
 *         description: Member details
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Member not found
 */
router.get('/:id', authenticate, authorize('view_members'), getMember);

/**
 * @swagger
 * /members/{id}:
 *   put:
 *     summary: Update member
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     responses:
 *       200:
 *         description: Member updated successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Member not found
 */
router.put('/:id', authenticate, authorize('manage_members'), updateMember);

/**
 * @swagger
 * /members/{id}:
 *   delete:
 *     summary: Delete member (soft delete)
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     responses:
 *       200:
 *         description: Member deleted successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Member not found
 */
router.delete('/:id', authenticate, authorize('manage_members'), deleteMember);

/**
 * @swagger
 * /members/{id}/role:
 *   post:
 *     summary: Assign role to member
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [roleId]
 *             properties:
 *               roleId:
 *                 type: string
 *                 description: Role ID to assign
 *     responses:
 *       200:
 *         description: Role assigned successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.post('/:id/role', authenticate, authorize('assign_roles'), assignRole);

/**
 * @swagger
 * /members/{id}/role:
 *   delete:
 *     summary: Remove role from member
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     responses:
 *       200:
 *         description: Role removed successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 */
router.delete('/:id/role', authenticate, authorize('assign_roles'), removeRole);

/**
 * @swagger
 * /members/{id}/branch:
 *   post:
 *     summary: Assign branch to member
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [branchId]
 *             properties:
 *               branchId:
 *                 type: string
 *                 description: Branch ID to assign
 *     responses:
 *       200:
 *         description: Branch assigned successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Member or branch not found
 */
router.post('/:id/branch', authenticate, authorize('manage_members'), assignBranch);

/**
 * @swagger
 * /members/{id}/branch:
 *   delete:
 *     summary: Remove branch assignment from member
 *     tags: [Members]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Member ID
 *     responses:
 *       200:
 *         description: Branch assignment removed successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Insufficient permissions
 *       404:
 *         description: Member not found
 */
router.delete('/:id/branch', authenticate, authorize('manage_members'), removeBranch);

export default router;