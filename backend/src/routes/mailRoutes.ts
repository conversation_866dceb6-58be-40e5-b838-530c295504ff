import { Router } from 'express';
import { 
  sendTestEmail, 
  sendEmail, 
  sendBulkEmail, 
  getMailStatus 
} from '../controllers/mailController.js';
import { authenticate } from '../middleware/auth.js';

const router = Router();

// Public routes (no authentication required)
router.get('/status', getMailStatus);

// Protected routes (authentication required)
router.post('/test', authenticate, sendTestEmail);
router.post('/send', authenticate, sendEmail);

// Church-scoped routes (when used with /churches/:slug/mail)
router.post('/bulk', authenticate, sendBulkEmail);

export default router;