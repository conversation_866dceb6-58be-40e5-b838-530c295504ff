import { Router } from 'express';
import {
  createRole,
  getRoles,
  getRole,
  updateRole,
  deleteRole
} from '../controllers/roleController';
import { authenticate, authorize } from '../middleware/auth';
import { validateTenant, requireTenantAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

// Apply tenant validation and access control to all routes
router.use(validateTenant);
router.use(authenticate);
router.use(requireTenantAccess);

router.post('/', authorize('manage_roles'), createRole);
router.get('/', authorize('manage_roles'), getRoles);
router.get('/:id', authorize('manage_roles'), getRole);
router.put('/:id', authorize('manage_roles'), updateRole);
router.delete('/:id', authorize('manage_roles'), deleteRole);

export default router;