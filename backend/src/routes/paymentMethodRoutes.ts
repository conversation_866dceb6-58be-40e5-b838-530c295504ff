import { Router } from 'express';
import {
  createPaymentMethod,
  getPaymentMethods,
  getPaymentMethod,
  updatePaymentMethod,
  deletePaymentMethod,
  setDefaultPaymentMethod
} from '../controllers/paymentMethodController';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /churches/{slug}/payment-methods:
 *   post:
 *     summary: Create a new payment method
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required: [name, type]
 *             properties:
 *               name:
 *                 type: string
 *                 example: "Church Airtel Money"
 *               type:
 *                 type: string
 *                 enum: [mobile_money, bank_card, bank_transfer, cash]
 *                 example: "mobile_money"
 *               provider:
 *                 type: string
 *                 enum: [airtel, mtn, zamtel, visa, mastercard]
 *                 example: "airtel"
 *               accountNumber:
 *                 type: string
 *                 example: "**********"
 *               accountName:
 *                 type: string
 *                 example: "Grace Community Church"
 *               bankName:
 *                 type: string
 *                 example: "Zanaco Bank"
 *               phoneNumber:
 *                 type: string
 *                 example: "+26**********"
 *               isDefault:
 *                 type: boolean
 *                 default: false
 *     responses:
 *       201:
 *         description: Payment method created successfully
 *       400:
 *         description: Validation error
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 */
router.post('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), createPaymentMethod);

/**
 * @swagger
 * /churches/{slug}/payment-methods:
 *   get:
 *     summary: Get church payment methods
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     responses:
 *       200:
 *         description: Payment methods retrieved successfully
 *       401:
 *         description: Unauthorized
 *       403:
 *         description: Access denied
 */
router.get('/', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getPaymentMethods);

/**
 * @swagger
 * /churches/{slug}/payment-methods/{id}:
 *   get:
 *     summary: Get payment method by ID
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment method ID
 *     responses:
 *       200:
 *         description: Payment method retrieved successfully
 *       404:
 *         description: Payment method not found
 */
router.get('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), getPaymentMethod);

/**
 * @swagger
 * /churches/{slug}/payment-methods/{id}:
 *   put:
 *     summary: Update payment method
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment method ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [mobile_money, bank_card, bank_transfer, cash]
 *               provider:
 *                 type: string
 *                 enum: [airtel, mtn, zamtel, visa, mastercard]
 *               accountNumber:
 *                 type: string
 *               accountName:
 *                 type: string
 *               bankName:
 *                 type: string
 *               phoneNumber:
 *                 type: string
 *               isDefault:
 *                 type: boolean
 *     responses:
 *       200:
 *         description: Payment method updated successfully
 *       400:
 *         description: Validation error
 *       404:
 *         description: Payment method not found
 */
router.put('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), updatePaymentMethod);

/**
 * @swagger
 * /churches/{slug}/payment-methods/{id}:
 *   delete:
 *     summary: Delete payment method
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment method ID
 *     responses:
 *       200:
 *         description: Payment method deleted successfully
 *       404:
 *         description: Payment method not found
 */
router.delete('/:id', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor']), deletePaymentMethod);

/**
 * @swagger
 * /churches/{slug}/payment-methods/{id}/set-default:
 *   post:
 *     summary: Set payment method as default
 *     tags: [Payment Methods]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Payment method ID
 *     responses:
 *       200:
 *         description: Default payment method set successfully
 *       404:
 *         description: Payment method not found
 */
router.post('/:id/set-default', authenticate, validateTenant, requireTenantAccess, checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), setDefaultPaymentMethod);

export default router;