import { Router } from 'express';
import {
  checkAvailability,
  initiateOnboarding,
  completeOnboarding,
  verifyOnboarding,
  resendVerification,
  setupInitialData,
  getOnboardingProgress,
  cleanupExpiredSessions
} from '../controllers/onboardingController';
import { authenticate } from '../middleware/auth';

const router = Router();

/**
 * @swagger
 * /api/onboarding/check-availability:
 *   post:
 *     summary: Check availability of church slug and admin email
 *     tags: [Onboarding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - churchSlug
 *               - adminEmail
 *             properties:
 *               churchSlug:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 50
 *                 pattern: '^[a-z0-9-]+$'
 *                 description: URL-friendly church identifier
 *                 example: 'grace-community-church'
 *               adminEmail:
 *                 type: string
 *                 format: email
 *                 description: Email address for the church admin
 *                 example: '<EMAIL>'
 *     responses:
 *       200:
 *         description: Availability check results
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 data:
 *                   type: object
 *                   properties:
 *                     availability:
 *                       type: object
 *                       properties:
 *                         churchSlug:
 *                           type: object
 *                           properties:
 *                             available:
 *                               type: boolean
 *                             message:
 *                               type: string
 *                         adminEmail:
 *                           type: object
 *                           properties:
 *                             available:
 *                               type: boolean
 *                             message:
 *                               type: string
 *                     allAvailable:
 *                       type: boolean
 *                     canProceed:
 *                       type: boolean
 */
router.post('/check-availability', checkAvailability);

/**
 * @swagger
 * /api/onboarding/initiate:
 *   post:
 *     summary: Initiate church onboarding process
 *     tags: [Onboarding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - church
 *               - admin
 *             properties:
 *               church:
 *                 type: object
 *                 required:
 *                   - name
 *                   - slug
 *                 properties:
 *                   name:
 *                     type: string
 *                     maxLength: 100
 *                     example: 'Grace Community Church'
 *                   slug:
 *                     type: string
 *                     pattern: '^[a-z0-9-]+$'
 *                     example: 'grace-community-church'
 *                   description:
 *                     type: string
 *                     example: 'A vibrant community of believers'
 *                   address:
 *                     type: string
 *                     example: '123 Main St, Springfield, IL 62701'
 *                   phone:
 *                     type: string
 *                     example: '******-0123'
 *                   email:
 *                     type: string
 *                     format: email
 *                     example: '<EMAIL>'
 *                   website:
 *                     type: string
 *                     format: uri
 *                     example: 'https://gracechurch.org'
 *                   logo:
 *                     type: string
 *                     format: uri
 *                     example: 'https://gracechurch.org/logo.png'
 *               admin:
 *                 type: object
 *                 required:
 *                   - firstName
 *                   - lastName
 *                   - email
 *                   - password
 *                 properties:
 *                   firstName:
 *                     type: string
 *                     maxLength: 50
 *                     example: 'John'
 *                   lastName:
 *                     type: string
 *                     maxLength: 50
 *                     example: 'Doe'
 *                   email:
 *                     type: string
 *                     format: email
 *                     example: '<EMAIL>'
 *                   password:
 *                     type: string
 *                     minLength: 8
 *                     example: 'securePassword123'
 *                   phone:
 *                     type: string
 *                     example: '******-0124'
 *                   dateOfBirth:
 *                     type: string
 *                     format: date
 *                     example: '1980-01-15'
 *                   gender:
 *                     type: string
 *                     enum: ['male', 'female', 'other', 'prefer_not_to_say']
 *                     example: 'male'
 *                   address:
 *                     type: string
 *                     example: '456 Oak Ave, Springfield, IL 62702'
 *               settings:
 *                 type: object
 *                 properties:
 *                   allowSelfRegistration:
 *                     type: boolean
 *                     default: true
 *                   requireEmailVerification:
 *                     type: boolean
 *                     default: true
 *                   timezone:
 *                     type: string
 *                     default: 'UTC'
 *                   locale:
 *                     type: string
 *                     default: 'en'
 *                   theme:
 *                     type: object
 *                     properties:
 *                       primaryColor:
 *                         type: string
 *                         default: '#3B82F6'
 *                       secondaryColor:
 *                         type: string
 *                         default: '#64748B'
 *                   features:
 *                     type: object
 *                     properties:
 *                       events:
 *                         type: boolean
 *                         default: true
 *                       donations:
 *                         type: boolean
 *                         default: false
 *                       messaging:
 *                         type: boolean
 *                         default: true
 *                       calendar:
 *                         type: boolean
 *                         default: true
 *                       onlineGiving:
 *                         type: boolean
 *                         default: false
 *                       memberDirectory:
 *                         type: boolean
 *                         default: true
 *                       eventRsvp:
 *                         type: boolean
 *                         default: true
 *                       recurringEvents:
 *                         type: boolean
 *                         default: true
 *     responses:
 *       201:
 *         description: Onboarding initiated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Onboarding initiated successfully. Please complete the setup.'
 *                 data:
 *                   type: object
 *                   properties:
 *                     onboardingToken:
 *                       type: string
 *                       description: Token to complete onboarding
 *                     verificationToken:
 *                       type: string
 *                       description: Token for email verification
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                       description: Token expiration time
 *                     progress:
 *                       type: object
 *                       properties:
 *                         step:
 *                           type: string
 *                           enum: ['availability', 'initiation', 'completion', 'verification', 'setup', 'finished']
 *                         completed:
 *                           type: boolean
 *                         nextStep:
 *                           type: string
 *       400:
 *         description: Church slug or email already exists
 */
router.post('/initiate', initiateOnboarding);

/**
 * @swagger
 * /api/onboarding/complete:
 *   post:
 *     summary: Complete church onboarding setup
 *     tags: [Onboarding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - onboardingToken
 *               - confirmPassword
 *               - agreeToTerms
 *             properties:
 *               onboardingToken:
 *                 type: string
 *                 description: Onboarding token from initiate step
 *               confirmPassword:
 *                 type: string
 *                 minLength: 8
 *                 description: Password confirmation
 *               agreeToTerms:
 *                 type: boolean
 *                 description: Must be true to proceed
 *               subscribeToUpdates:
 *                 type: boolean
 *                 default: false
 *                 description: Subscribe to product updates
 *     responses:
 *       201:
 *         description: Onboarding completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Onboarding completed successfully. Please verify your email to activate your account.'
 *                 data:
 *                   type: object
 *                   properties:
 *                     church:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         slug:
 *                           type: string
 *                         description:
 *                           type: string
 *                         email:
 *                           type: string
 *                         phone:
 *                           type: string
 *                         website:
 *                           type: string
 *                         createdAt:
 *                           type: string
 *                           format: date-time
 *                     admin:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         firstName:
 *                           type: string
 *                         lastName:
 *                           type: string
 *                         email:
 *                           type: string
 *                         churchId:
 *                           type: string
 *                         roleId:
 *                           type: string
 *                         isEmailVerified:
 *                           type: boolean
 *                     mainBranch:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         slug:
 *                           type: string
 *                         isMainBranch:
 *                           type: boolean
 *                     tokens:
 *                       type: object
 *                       properties:
 *                         accessToken:
 *                           type: string
 *                         refreshToken:
 *                           type: string
 *                         expiresIn:
 *                           type: string
 *                     verificationToken:
 *                       type: string
 *                     churchUrl:
 *                       type: string
 *                       format: uri
 *                     adminUrl:
 *                       type: string
 *                       format: uri
 *                     progress:
 *                       type: object
 *                       properties:
 *                         step:
 *                           type: string
 *                         completed:
 *                           type: boolean
 *                         nextStep:
 *                           type: string
 *       400:
 *         description: Invalid or expired onboarding token
 */
router.post('/complete', completeOnboarding);

/**
 * @swagger
 * /api/onboarding/verify:
 *   post:
 *     summary: Verify email during onboarding
 *     tags: [Onboarding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - verificationToken
 *             properties:
 *               verificationToken:
 *                 type: string
 *                 description: Email verification token
 *     responses:
 *       200:
 *         description: Email verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Email verified successfully. Your church onboarding is complete!'
 *                 data:
 *                   type: object
 *                   properties:
 *                     verified:
 *                       type: boolean
 *                     progress:
 *                       type: object
 *                       properties:
 *                         step:
 *                           type: string
 *                         completed:
 *                           type: boolean
 *                         nextStep:
 *                           type: string
 *       400:
 *         description: Invalid or expired verification token
 */
router.post('/verify', verifyOnboarding);

/**
 * @swagger
 * /api/onboarding/resend-verification:
 *   post:
 *     summary: Resend email verification during onboarding
 *     tags: [Onboarding]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - churchSlug
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Admin email address
 *               churchSlug:
 *                 type: string
 *                 description: Church slug identifier
 *     responses:
 *       200:
 *         description: Verification email resent
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Verification email has been resent.'
 */
router.post('/resend-verification', resendVerification);

/**
 * @swagger
 * /api/onboarding/setup-initial-data:
 *   post:
 *     summary: Setup initial church data (branches, roles, sample data)
 *     tags: [Onboarding]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               createSampleData:
 *                 type: boolean
 *                 default: false
 *                 description: Create sample events, members, etc.
 *               setupBranches:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - slug
 *                   properties:
 *                     name:
 *                       type: string
 *                       maxLength: 100
 *                       example: 'Downtown Campus'
 *                     slug:
 *                       type: string
 *                       pattern: '^[a-z0-9-]+$'
 *                       example: 'downtown-campus'
 *                     description:
 *                       type: string
 *                       example: 'Our urban church location'
 *                     address:
 *                       type: string
 *                       example: '789 City Center Blvd'
 *                     phone:
 *                       type: string
 *                       example: '******-0125'
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: '<EMAIL>'
 *                     capacity:
 *                       type: number
 *                       minimum: 1
 *                       example: 250
 *                     coordinates:
 *                       type: object
 *                       properties:
 *                         latitude:
 *                           type: number
 *                           example: 39.7817
 *                         longitude:
 *                           type: number
 *                           example: -89.6501
 *                     isMainBranch:
 *                       type: boolean
 *                       default: false
 *               setupRoles:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                   properties:
 *                     name:
 *                       type: string
 *                       maxLength: 50
 *                       example: 'Youth Leader'
 *                     description:
 *                       type: string
 *                       example: 'Leads youth ministry programs'
 *                     permissions:
 *                       type: array
 *                       items:
 *                         type: string
 *                       example: ['events:read', 'events:create', 'members:read']
 *     responses:
 *       200:
 *         description: Initial data setup completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Initial data setup completed successfully'
 *                 data:
 *                   type: object
 *                   properties:
 *                     branches:
 *                       type: array
 *                       items:
 *                         type: object
 *                     roles:
 *                       type: array
 *                       items:
 *                         type: object
 *                     sampleDataCreated:
 *                       type: boolean
 *                     progress:
 *                       type: object
 *                       properties:
 *                         step:
 *                           type: string
 *                         completed:
 *                           type: boolean
 *                         nextStep:
 *                           type: string
 *       400:
 *         description: Church context required
 *       401:
 *         description: Authentication required
 */
router.post('/setup-initial-data', authenticate, setupInitialData);

/**
 * @swagger
 * /api/onboarding/progress/{token}:
 *   get:
 *     summary: Get onboarding progress by token
 *     tags: [Onboarding]
 *     parameters:
 *       - in: path
 *         name: token
 *         required: true
 *         schema:
 *           type: string
 *         description: Onboarding or verification token
 *     responses:
 *       200:
 *         description: Onboarding progress information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 data:
 *                   type: object
 *                   properties:
 *                     progress:
 *                       type: object
 *                       properties:
 *                         step:
 *                           type: string
 *                           enum: ['availability', 'initiation', 'completion', 'verification', 'setup', 'finished']
 *                         completed:
 *                           type: boolean
 *                         nextStep:
 *                           type: string
 *                     expiresAt:
 *                       type: string
 *                       format: date-time
 *                     churchName:
 *                       type: string
 *                     adminEmail:
 *                       type: string
 *       400:
 *         description: Onboarding session has expired
 *       404:
 *         description: Onboarding session not found
 */
router.get('/progress/:token', getOnboardingProgress);

/**
 * @swagger
 * /api/onboarding/cleanup:
 *   post:
 *     summary: Clean up expired onboarding sessions (admin only)
 *     tags: [Onboarding]
 *     responses:
 *       200:
 *         description: Cleanup completed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: 'success'
 *                 message:
 *                   type: string
 *                   example: 'Cleaned up 5 expired onboarding sessions'
 *                 data:
 *                   type: object
 *                   properties:
 *                     cleanedUp:
 *                       type: number
 */
router.post('/cleanup', cleanupExpiredSessions);

export default router;