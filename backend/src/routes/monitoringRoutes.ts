import { Router } from 'express';
import {
  getAuthHealth,
  getAuthMetrics,
  getSecurityDashboard,
  getSecurityMetrics,
  getSecurityAlerts,
  acknowledgeAlert,
  getUserAuditLogs,
  getIPAuditLogs,
  getPerformanceMetrics,
  exportMetrics,
  exportSecurityData,
  getMonitoringDashboard,
  getRealTimeMetrics,
  getSystemStatus,
} from '../controllers/monitoringController';
import { authenticate, authorize } from '../middleware/auth';

const router = Router();

/**
 * @swagger
 * /api/monitoring/health:
 *   get:
 *     summary: Get authentication system health status
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Authentication health status
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: string
 *                       enum: [healthy, warning, critical]
 *                     metrics:
 *                       type: object
 *                     alerts:
 *                       type: array
 */
router.get('/health', authenticate, authorize('admin', 'super_admin'), getAuthHealth);

/**
 * @swagger
 * /api/monitoring/auth/metrics:
 *   get:
 *     summary: Get authentication metrics
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [5m, 1h, 24h, 7d]
 *         description: Time range for metrics
 *       - in: query
 *         name: eventType
 *         schema:
 *           type: string
 *         description: Filter by specific event type
 *     responses:
 *       200:
 *         description: Authentication metrics data
 */
router.get('/auth/metrics', authenticate, authorize('admin', 'super_admin'), getAuthMetrics);

/**
 * @swagger
 * /api/monitoring/security/dashboard:
 *   get:
 *     summary: Get security dashboard data
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Security dashboard data
 */
router.get('/security/dashboard', authenticate, authorize('admin', 'super_admin'), getSecurityDashboard);

/**
 * @swagger
 * /api/monitoring/security/metrics:
 *   get:
 *     summary: Get security metrics
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: timeRange
 *         schema:
 *           type: string
 *           enum: [1h, 24h, 7d]
 *         description: Time range for metrics
 *     responses:
 *       200:
 *         description: Security metrics data
 */
router.get('/security/metrics', authenticate, authorize('admin', 'super_admin'), getSecurityMetrics);

/**
 * @swagger
 * /api/monitoring/security/alerts:
 *   get:
 *     summary: Get active security alerts
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of active security alerts
 */
router.get('/security/alerts', authenticate, authorize('admin', 'super_admin'), getSecurityAlerts);

/**
 * @swagger
 * /api/monitoring/security/alerts/{alertId}/acknowledge:
 *   post:
 *     summary: Acknowledge a security alert
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: alertId
 *         required: true
 *         schema:
 *           type: string
 *         description: Alert ID to acknowledge
 *     responses:
 *       200:
 *         description: Alert acknowledged successfully
 *       404:
 *         description: Alert not found
 */
router.post('/security/alerts/:alertId/acknowledge', authenticate, authorize('admin', 'super_admin'), acknowledgeAlert);

/**
 * @swagger
 * /api/monitoring/audit/user/{userId}:
 *   get:
 *     summary: Get audit logs for a specific user
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: User ID to get audit logs for
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of logs to return
 *     responses:
 *       200:
 *         description: User audit logs
 */
router.get('/audit/user/:userId', authenticate, authorize('admin', 'super_admin'), getUserAuditLogs);

/**
 * @swagger
 * /api/monitoring/audit/ip/{ipAddress}:
 *   get:
 *     summary: Get audit logs for a specific IP address
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: ipAddress
 *         required: true
 *         schema:
 *           type: string
 *         description: IP address to get audit logs for
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: Maximum number of logs to return
 *     responses:
 *       200:
 *         description: IP audit logs
 */
router.get('/audit/ip/:ipAddress', authenticate, authorize('admin', 'super_admin'), getIPAuditLogs);

/**
 * @swagger
 * /api/monitoring/performance:
 *   get:
 *     summary: Get performance metrics
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Performance metrics data
 */
router.get('/performance', authenticate, authorize('admin', 'super_admin'), getPerformanceMetrics);

/**
 * @swagger
 * /api/monitoring/export/metrics:
 *   get:
 *     summary: Export metrics in various formats
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, prometheus]
 *           default: json
 *         description: Export format
 *     responses:
 *       200:
 *         description: Exported metrics data
 */
router.get('/export/metrics', authenticate, authorize('admin', 'super_admin'), exportMetrics);

/**
 * @swagger
 * /api/monitoring/export/security:
 *   get:
 *     summary: Export security data
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: format
 *         schema:
 *           type: string
 *           enum: [json, csv]
 *           default: json
 *         description: Export format
 *     responses:
 *       200:
 *         description: Exported security data
 */
router.get('/export/security', authenticate, authorize('admin', 'super_admin'), exportSecurityData);

/**
 * @swagger
 * /api/monitoring/dashboard:
 *   get:
 *     summary: Get comprehensive monitoring dashboard
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Complete monitoring dashboard data
 */
router.get('/dashboard', authenticate, authorize('admin', 'super_admin'), getMonitoringDashboard);

/**
 * @swagger
 * /api/monitoring/realtime:
 *   get:
 *     summary: Get real-time metrics for live dashboard updates
 *     tags: [Monitoring]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Real-time metrics data
 */
router.get('/realtime', authenticate, authorize('admin', 'super_admin'), getRealTimeMetrics);

/**
 * @swagger
 * /api/monitoring/status:
 *   get:
 *     summary: Get system status for health checks
 *     tags: [Monitoring]
 *     responses:
 *       200:
 *         description: System status information
 */
router.get('/status', getSystemStatus); // Public endpoint for health checks

export default router;