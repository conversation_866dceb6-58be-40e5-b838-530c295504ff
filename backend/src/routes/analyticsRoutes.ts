import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { validateTenant, requireTenantAccess, checkChurchAccess } from '../middleware/tenant';
import {
  getDashboardOverview,
  getMemberAnalytics,
  getEventAnalytics,
  getFinancialAnalytics,
  getBranchAnalytics
} from '../controllers/analyticsController';

const router = Router({ mergeParams: true });

/**
 * @swagger
 * /api/churches/{slug}/analytics/dashboard:
 *   get:
 *     summary: Get dashboard overview analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific branch
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Custom start date (overrides period)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date-time
 *         description: Custom end date (overrides period)
 *     responses:
 *       200:
 *         description: Dashboard overview analytics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     overview:
 *                       type: object
 *                       properties:
 *                         totalMembers:
 *                           type: number
 *                         totalEvents:
 *                           type: number
 *                         totalDonations:
 *                           type: number
 *                         totalRevenue:
 *                           type: number
 *                         memberGrowthRate:
 *                           type: number
 *                         eventAttendanceRate:
 *                           type: number
 *                         donationGrowthRate:
 *                           type: number
 *                         recentActivity:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               title:
 *                                 type: string
 *                               description:
 *                                 type: string
 *                               timestamp:
 *                                 type: string
 *                                 format: date-time
 *       403:
 *         description: Access denied
 *       404:
 *         description: Church not found
 */
router.get('/dashboard', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), 
  getDashboardOverview
);

// Add alias for frontend compatibility
router.get('/overview', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), 
  getDashboardOverview
);

/**
 * @swagger
 * /api/churches/{slug}/analytics/members:
 *   get:
 *     summary: Get member analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific branch
 *     responses:
 *       200:
 *         description: Member analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     analytics:
 *                       type: object
 *                       properties:
 *                         totalMembers:
 *                           type: number
 *                         membersByStatus:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               status:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               percentage:
 *                                 type: number
 *                         membersByGender:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               gender:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               percentage:
 *                                 type: number
 *                         membersByBranch:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               branchId:
 *                                 type: string
 *                                 nullable: true
 *                               branchName:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               percentage:
 *                                 type: number
 */
router.get('/members', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), 
  getMemberAnalytics
);

/**
 * @swagger
 * /api/churches/{slug}/analytics/events:
 *   get:
 *     summary: Get event analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific branch
 *     responses:
 *       200:
 *         description: Event analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     analytics:
 *                       type: object
 *                       properties:
 *                         totalEvents:
 *                           type: number
 *                         eventsByType:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               percentage:
 *                                 type: number
 *                               averageAttendance:
 *                                 type: number
 *                         upcomingEvents:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: string
 *                               title:
 *                                 type: string
 *                               type:
 *                                 type: string
 *                               startDate:
 *                                 type: string
 *                                 format: date-time
 *                               rsvpCount:
 *                                 type: number
 */
router.get('/events', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), 
  getEventAnalytics
);

/**
 * @swagger
 * /api/churches/{slug}/analytics/financial:
 *   get:
 *     summary: Get financial analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for analytics
 *       - in: query
 *         name: branchId
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific branch
 *     responses:
 *       200:
 *         description: Financial analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     analytics:
 *                       type: object
 *                       properties:
 *                         totalRevenue:
 *                           type: number
 *                         donationsByType:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               type:
 *                                 type: string
 *                               count:
 *                                 type: number
 *                               totalAmount:
 *                                 type: number
 *                               percentage:
 *                                 type: number
 *                         topDonors:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               donorId:
 *                                 type: string
 *                                 nullable: true
 *                               donorName:
 *                                 type: string
 *                               totalDonations:
 *                                 type: number
 *                               donationCount:
 *                                 type: number
 *                               isAnonymous:
 *                                 type: boolean
 */
router.get('/financial', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder']), 
  getFinancialAnalytics
);

/**
 * @swagger
 * /api/churches/{slug}/analytics/branches:
 *   get:
 *     summary: Get branch analytics
 *     tags: [Analytics]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *       - in: query
 *         name: period
 *         schema:
 *           type: string
 *           enum: [7d, 30d, 90d, 1y, all]
 *           default: 30d
 *         description: Time period for analytics
 *     responses:
 *       200:
 *         description: Branch analytics data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     analytics:
 *                       type: object
 *                       properties:
 *                         totalBranches:
 *                           type: number
 *                         branchComparison:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               branchId:
 *                                 type: string
 *                               branchName:
 *                                 type: string
 *                               memberCount:
 *                                 type: number
 *                               eventCount:
 *                                 type: number
 *                               totalDonations:
 *                                 type: number
 *                               averageAttendance:
 *                                 type: number
 */
router.get('/branches', 
  authenticate, 
  validateTenant, 
  requireTenantAccess, 
  checkChurchAccess(['Super Admin', 'Pastor', 'Elder', 'Deacon']), 
  getBranchAnalytics
);

export default router;