import { transporter, mailConfig } from '../config/mail.js';
import type { SendMailOptions } from 'nodemailer';

export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  cc?: string | string[];
  bcc?: string | string[];
  attachments?: Array<{
    filename: string;
    content: Buffer | string;
    contentType?: string;
  }>;
}

export class MailService {
  /**
   * Send a basic email
   */
  static async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      const mailOptions: SendMailOptions = {
        from: `${mailConfig.from.name} <${mailConfig.from.email}>`,
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject: options.subject,
        text: options.text,
        html: options.html,
        cc: options.cc,
        bcc: options.bcc,
        attachments: options.attachments,
      };

      const result = await transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', result.messageId);
      return true;
    } catch (error) {
      console.error('❌ Failed to send email:', error);
      return false;
    }
  }

  /**
   * Send welcome email to new members with email verification
   */
  static async sendWelcomeEmail(
    memberEmail: string,
    memberName: string,
    churchName: string,
    temporaryPassword: string,
    verificationToken: string
  ): Promise<boolean> {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3002'}/verify-email/${verificationToken}`;
    
    const subject = `Welcome to ${churchName} - Please Verify Your Email`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Welcome to ${churchName}!</h2>
        <p>Dear ${memberName},</p>
        <p>We're excited to welcome you to our church family! Your account has been created and you can now access our church management system.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Your Login Credentials:</h3>
          <p><strong>Email:</strong> ${memberEmail}</p>
          <p><strong>Temporary Password:</strong> <code style="background-color: #e5e7eb; padding: 4px 8px; border-radius: 4px;">${temporaryPassword}</code></p>
        </div>
        
        <div style="background-color: #fef3c7; border-left: 4px solid #f59e0b; padding: 16px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #92400e;">⚠️ Email Verification Required</h3>
          <p style="margin-bottom: 0; color: #92400e;">Before you can log in, you must verify your email address by clicking the button below:</p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #16a34a; color: white; padding: 14px 28px; text-decoration: none; border-radius: 8px; display: inline-block; font-weight: bold; font-size: 16px;">✅ Verify Email Address</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280; background-color: #f9fafb; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 14px;">${verificationUrl}</p>
        
        <div style="background-color: #dbeafe; border-left: 4px solid #3b82f6; padding: 16px; margin: 20px 0;">
          <h4 style="margin-top: 0; color: #1e40af;">📋 Next Steps:</h4>
          <ol style="color: #1e40af; margin-bottom: 0;">
            <li>Click the verification button above to verify your email</li>
            <li>Log in using your email and temporary password</li>
            <li>Change your password for security purposes</li>
            <li>Complete your profile information</li>
          </ol>
        </div>
        
        <p><strong>Important:</strong> You won't be able to log in until your email is verified. This helps us ensure the security of your account.</p>
        
        <p>If you have any questions or need assistance, please don't hesitate to contact our church administration.</p>
        
        <p>Blessings,<br>The ${churchName} Team</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 12px; color: #6b7280;">
          If you didn't expect this email, please ignore it. This account was created by a church administrator.
        </p>
      </div>
    `;

    return this.sendEmail({
      to: memberEmail,
      subject,
      html,
    });
  }

  /**
   * Send email verification email
   */
  static async sendEmailVerification(
    email: string,
    name: string,
    verificationToken: string,
    _churchSlug: string
  ): Promise<boolean> {
    const verificationUrl = `${process.env.FRONTEND_URL || 'http://localhost:3002'}/verify-email/${verificationToken}`;
    
    const subject = 'Please Verify Your Email Address';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Email Verification Required</h2>
        <p>Dear ${name},</p>
        <p>Thank you for joining our church community! To complete your account setup, please verify your email address by clicking the button below:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${verificationUrl}" style="background-color: #16a34a; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Verify Email Address</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280;">${verificationUrl}</p>
        
        <p><strong>Important:</strong> You won't be able to log in until your email is verified.</p>
        
        <p>If you didn't create this account, please ignore this email.</p>
        
        <p>Blessings,<br>Church Management Team</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send password reset email
   */
  static async sendPasswordResetEmail(
    email: string,
    name: string,
    resetToken: string,
    _churchSlug: string
  ): Promise<boolean> {
    const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3002'}/${_churchSlug}/reset-password?token=${resetToken}`;
    
    const subject = 'Password Reset Request';
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Password Reset Request</h2>
        <p>Dear ${name},</p>
        <p>We received a request to reset your password. Click the button below to create a new password:</p>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${resetUrl}" style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">Reset Password</a>
        </div>
        
        <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
        <p style="word-break: break-all; color: #6b7280;">${resetUrl}</p>
        
        <p><strong>This link will expire in 1 hour.</strong></p>
        
        <p>If you didn't request this password reset, please ignore this email.</p>
        
        <p>Best regards,<br>Church Management Team</p>
      </div>
    `;

    return this.sendEmail({
      to: email,
      subject,
      html,
    });
  }

  /**
   * Send event notification email
   */
  static async sendEventNotification(
    memberEmail: string,
    memberName: string,
    eventTitle: string,
    eventDate: string,
    eventLocation: string,
    churchName: string
  ): Promise<boolean> {
    const subject = `New Event: ${eventTitle}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">New Event at ${churchName}</h2>
        <p>Dear ${memberName},</p>
        <p>We're excited to announce a new event:</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0; color: #1f2937;">${eventTitle}</h3>
          <p><strong>Date:</strong> ${eventDate}</p>
          <p><strong>Location:</strong> ${eventLocation}</p>
        </div>
        
        <p>We hope to see you there!</p>
        
        <p>Blessings,<br>The ${churchName} Team</p>
      </div>
    `;

    return this.sendEmail({
      to: memberEmail,
      subject,
      html,
    });
  }

  /**
   * Send donation receipt email
   */
  static async sendDonationReceipt(
    donorEmail: string,
    donorName: string,
    amount: number,
    currency: string,
    donationDate: string,
    category: string,
    churchName: string,
    receiptNumber: string
  ): Promise<boolean> {
    const subject = `Donation Receipt - ${receiptNumber}`;
    const html = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2563eb;">Donation Receipt</h2>
        <p>Dear ${donorName},</p>
        <p>Thank you for your generous donation to ${churchName}. This email serves as your official receipt.</p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Donation Details</h3>
          <p><strong>Receipt Number:</strong> ${receiptNumber}</p>
          <p><strong>Amount:</strong> ${currency} ${amount.toFixed(2)}</p>
          <p><strong>Category:</strong> ${category}</p>
          <p><strong>Date:</strong> ${donationDate}</p>
          <p><strong>Donor:</strong> ${donorName}</p>
        </div>
        
        <p>Your generosity helps us continue our mission and ministry. May God bless you abundantly.</p>
        
        <p>With gratitude,<br>The ${churchName} Team</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 12px; color: #6b7280;">
          This is an official donation receipt. Please keep this for your tax records.
        </p>
      </div>
    `;

    return this.sendEmail({
      to: donorEmail,
      subject,
      html,
    });
  }

  /**
   * Send bulk email to multiple recipients
   */
  static async sendBulkEmail(
    recipients: string[],
    subject: string,
    html: string,
    _churchName: string
  ): Promise<{ success: number; failed: number }> {
    let success = 0;
    let failed = 0;

    // Send emails in batches to avoid overwhelming the SMTP server
    const batchSize = 10;
    for (let i = 0; i < recipients.length; i += batchSize) {
      const batch = recipients.slice(i, i + batchSize);
      
      const promises = batch.map(async (email) => {
        try {
          await this.sendEmail({
            to: email,
            subject,
            html,
          });
          success++;
        } catch (error) {
          console.error(`Failed to send email to ${email}:`, error);
          failed++;
        }
      });

      await Promise.all(promises);
      
      // Small delay between batches
      if (i + batchSize < recipients.length) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    return { success, failed };
  }
}