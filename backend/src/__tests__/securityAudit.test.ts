import { describe, it, expect, beforeEach, vi } from 'vitest';
import { SecurityAuditLogger, SecurityEventType } from '../utils/securityAudit';

describe('SecurityAuditLogger', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.log to capture audit logs
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  describe('Event Logging', () => {
    it('should log security events with proper structure', () => {
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        userId: 'user-123',
        churchId: 'church-456',
        success: true,
        severity: 'low',
      });

      expect(console.log).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('"eventType":"LOGIN_SUCCESS"')
      );
    });

    it('should log authentication success', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/api/auth/login',
        method: 'POST',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logAuthSuccess(mockReq, 'user-123', 'church-456');

      expect(console.log).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('"eventType":"LOGIN_SUCCESS"')
      );
    });

    it('should log authentication failure', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/api/auth/login',
        method: 'POST',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logAuthFailure(mockReq, 'Invalid credentials');

      expect(console.log).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('"eventType":"LOGIN_FAILED"')
      );
    });

    it('should log token refresh events', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/api/auth/refresh',
        method: 'POST',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logTokenRefresh(mockReq, 'user-123', true);

      expect(console.log).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('"eventType":"TOKEN_REFRESH_SUCCESS"')
      );
    });

    it('should log password changes', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/api/auth/change-password',
        method: 'POST',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logPasswordChange(mockReq, 'user-123');

      expect(console.log).toHaveBeenCalledWith(
        '[SECURITY_AUDIT]',
        expect.stringContaining('"eventType":"PASSWORD_CHANGED"')
      );
    });
  });

  describe('Critical Event Handling', () => {
    it('should handle critical events with error logging', () => {
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        userId: 'user-123',
        success: false,
        severity: 'critical',
        details: { activity: 'Multiple failed login attempts' },
      });

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('🚨 CRITICAL SECURITY EVENT'),
        expect.objectContaining({
          userId: 'user-123',
        })
      );
    });
  });

  describe('IP Address Extraction', () => {
    it('should extract IP from req.ip', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/test',
        method: 'GET',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logAuthSuccess(mockReq, 'user-123', 'church-456');

      const logCall = (console.log as any).mock.calls[0][1];
      const logData = JSON.parse(logCall);
      expect(logData.ipAddress).toBe('***********');
    });

    it('should fallback to connection.remoteAddress', () => {
      const mockReq = {
        get: vi.fn(() => 'Mozilla/5.0'),
        path: '/test',
        method: 'GET',
        connection: { remoteAddress: '***********' },
      } as any;

      SecurityAuditLogger.logAuthSuccess(mockReq, 'user-123', 'church-456');

      const logCall = (console.log as any).mock.calls[0][1];
      const logData = JSON.parse(logCall);
      expect(logData.ipAddress).toBe('***********');
    });
  });
});