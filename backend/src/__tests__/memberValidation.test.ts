import { describe, it, expect } from 'vitest';
import { createMemberSchema } from '../types/member';
import { ZodError } from 'zod';

describe('Member Validation Schema - Enhanced Validation', () => {
  describe('firstName validation', () => {
    it('should accept valid first names', () => {
      const validNames = [
        '<PERSON>',
        '<PERSON><PERSON><PERSON>',
        "<PERSON>'Connor",
        '<PERSON>',
        '<PERSON>',
        '<PERSON>',
      ];

      validNames.forEach(firstName => {
        const result = createMemberSchema.safeParse({
          firstName,
          lastName: 'Doe',
          email: '<EMAIL>',
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject empty first name', () => {
      const result = createMemberSchema.safeParse({
        firstName: '',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const firstNameError = result.error.issues.find(e => e.path.includes('firstName'));
        expect(firstNameError?.message).toBe('First name is required');
      }
    });

    it('should reject first name with invalid characters', () => {
      const invalidNames = [
        'John123',
        'John@#$',
        'John!',
        'John123!@#',
      ];

      invalidNames.forEach(firstName => {
        const result = createMemberSchema.safeParse({
          firstName,
          lastName: 'Doe',
          email: '<EMAIL>',
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const firstNameError = result.error.issues.find(e => e.path.includes('firstName'));
          expect(firstNameError?.message).toBe('First name contains invalid characters');
        }
      });
    });

    it('should reject first name that is too long', () => {
      const longName = 'a'.repeat(51);
      const result = createMemberSchema.safeParse({
        firstName: longName,
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const firstNameError = result.error.issues.find(e => e.path.includes('firstName'));
        expect(firstNameError?.message).toBe('First name must be less than 50 characters');
      }
    });
  });

  describe('lastName validation', () => {
    it('should accept valid last names', () => {
      const validNames = [
        'Doe',
        'Smith-Jones',
        "O'Connor",
        'Van Der Berg',
        'García',
        'Müller',
      ];

      validNames.forEach(lastName => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName,
          email: '<EMAIL>',
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject empty last name', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: '',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const lastNameError = result.error.issues.find(e => e.path.includes('lastName'));
        expect(lastNameError?.message).toBe('Last name is required');
      }
    });

    it('should reject last name with invalid characters', () => {
      const invalidNames = [
        'Doe123',
        'Doe@#$',
        'Doe!',
        'Doe123!@#',
      ];

      invalidNames.forEach(lastName => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName,
          email: '<EMAIL>',
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const lastNameError = result.error.issues.find(e => e.path.includes('lastName'));
          expect(lastNameError?.message).toBe('Last name contains invalid characters');
        }
      });
    });
  });

  describe('email validation', () => {
    it('should accept valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'test@example',
        'test@.com',
      ];

      invalidEmails.forEach(email => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const emailError = result.error.issues.find(e => e.path.includes('email'));
          expect(emailError?.message).toBe('Please enter a valid email address');
        }
      });
    });

    it('should convert email to lowercase', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.email).toBe('<EMAIL>');
      }
    });

    it('should reject email that is too long', () => {
      const longEmail = 'a'.repeat(250) + '@example.com';
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: longEmail,
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const emailError = result.error.issues.find(e => e.path.includes('email'));
        expect(emailError?.message).toBe('Email must be less than 255 characters');
      }
    });
  });

  describe('password validation', () => {
    it('should accept strong passwords', () => {
      const strongPasswords = [
        'SecurePass123',
        'MyPassword1!',
        'ComplexP@ssw0rd',
        'StrongPassword123',
      ];

      strongPasswords.forEach(password => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          password,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject passwords that are too short', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'short',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const passwordError = result.error.issues.find(e => e.path.includes('password'));
        expect(passwordError?.message).toBe('Password must be at least 8 characters');
      }
    });

    it('should reject passwords without uppercase letters', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'lowercase123',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const passwordError = result.error.issues.find(e => e.path.includes('password'));
        expect(passwordError?.message).toBe('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      }
    });

    it('should reject passwords without lowercase letters', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'UPPERCASE123',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const passwordError = result.error.issues.find(e => e.path.includes('password'));
        expect(passwordError?.message).toBe('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      }
    });

    it('should reject passwords without numbers', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        password: 'NoNumbers',
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const passwordError = result.error.issues.find(e => e.path.includes('password'));
        expect(passwordError?.message).toBe('Password must contain at least one uppercase letter, one lowercase letter, and one number');
      }
    });

    it('should allow password to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.password).toBeUndefined();
      }
    });
  });

  describe('phone validation', () => {
    it('should accept valid phone number formats', () => {
      const validPhones = [
        '+1234567890',
        '****** 567 8900',
        '******-567-8900',
        '+****************',
        '1234567890',
        '+44 20 7946 0958',
        '+33 1 42 86 83 26',
      ];

      validPhones.forEach(phone => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid phone number formats', () => {
      const invalidPhones = [
        'invalid-phone',
        '123',
        'abc123def',
        '++1234567890',
        '0123456789012345678', // Too long
      ];

      invalidPhones.forEach(phone => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const phoneError = result.error.issues.find(e => e.path.includes('phone'));
          expect(phoneError?.message).toBe('Please enter a valid phone number');
        }
      });
    });

    it('should allow phone to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.phone).toBeUndefined();
      }
    });
  });

  describe('dateOfBirth validation', () => {
    it('should accept valid date formats', () => {
      const validDates = [
        '1990-01-01',
        '2000-12-31',
        '1985-06-15',
        '1975-02-28',
      ];

      validDates.forEach(dateOfBirth => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          dateOfBirth,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid date formats', () => {
      const invalidDates = [
        'invalid-date',
        '2000-13-01', // Invalid month
        '2000-01-32', // Invalid day
        '1800-01-01', // Too old (over 120 years)
        '2030-01-01', // Future date
      ];

      invalidDates.forEach(dateOfBirth => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          dateOfBirth,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const dateError = result.error.issues.find(e => e.path.includes('dateOfBirth'));
          expect(dateError?.message).toBe('Please enter a valid date of birth');
        }
      });
    });

    it('should allow dateOfBirth to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.dateOfBirth).toBeUndefined();
      }
    });
  });

  describe('gender validation', () => {
    it('should accept valid gender values', () => {
      const validGenders = ['male', 'female', 'other'];

      validGenders.forEach(gender => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          gender,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid gender values', () => {
      const invalidGenders = ['invalid', 'man', 'woman', 'unknown'];

      invalidGenders.forEach(gender => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          gender,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const genderError = result.error.issues.find(e => e.path.includes('gender'));
          expect(genderError?.message).toContain('expected one of');
        }
      });
    });

    it('should allow gender to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.gender).toBeUndefined();
      }
    });
  });

  describe('address validation', () => {
    it('should accept valid addresses', () => {
      const validAddresses = [
        '123 Main St',
        '456 Oak Avenue, Apt 2B',
        '789 Pine Road, Suite 100, City, State 12345',
        'P.O. Box 123',
      ];

      validAddresses.forEach(address => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          address,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject address that is too long', () => {
      const longAddress = 'a'.repeat(501);
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        address: longAddress,
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        const addressError = result.error.issues.find(e => e.path.includes('address'));
        expect(addressError?.message).toBe('Address must be less than 500 characters');
      }
    });

    it('should allow address to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.address).toBeUndefined();
      }
    });
  });

  describe('branchId validation', () => {
    it('should accept valid UUID format', () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
      ];

      validUUIDs.forEach(branchId => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          branchId,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid UUID format', () => {
      const invalidUUIDs = [
        'invalid-uuid',
        '123',
        'not-a-uuid-at-all',
        '123e4567-e89b-12d3-a456', // Too short
      ];

      invalidUUIDs.forEach(branchId => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          branchId,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const branchError = result.error.issues.find(e => e.path.includes('branchId'));
          expect(branchError?.message).toBe('Invalid branch selection');
        }
      });
    });

    it('should allow branchId to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.branchId).toBeUndefined();
      }
    });
  });

  describe('roleId validation', () => {
    it('should accept valid UUID format', () => {
      const validUUIDs = [
        '123e4567-e89b-12d3-a456-************',
        'f47ac10b-58cc-4372-a567-0e02b2c3d479',
        '6ba7b810-9dad-11d1-80b4-00c04fd430c8',
      ];

      validUUIDs.forEach(roleId => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          roleId,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid UUID format', () => {
      const invalidUUIDs = [
        'invalid-uuid',
        '123',
        'not-a-uuid-at-all',
        '123e4567-e89b-12d3-a456', // Too short
      ];

      invalidUUIDs.forEach(roleId => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          roleId,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const roleError = result.error.issues.find(e => e.path.includes('roleId'));
          expect(roleError?.message).toBe('Invalid role selection');
        }
      });
    });

    it('should allow roleId to be optional', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.roleId).toBeUndefined();
      }
    });
  });

  describe('status validation', () => {
    it('should accept valid status values', () => {
      const validStatuses = ['active', 'inactive', 'suspended'];

      validStatuses.forEach(status => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          status,
        });
        expect(result.success).toBe(true);
      });
    });

    it('should reject invalid status values', () => {
      const invalidStatuses = ['invalid', 'enabled', 'disabled', 'pending'];

      invalidStatuses.forEach(status => {
        const result = createMemberSchema.safeParse({
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          status,
        });

        expect(result.success).toBe(false);
        if (!result.success) {
          const statusError = result.error.issues.find(e => e.path.includes('status'));
          expect(statusError?.message).toContain('expected one of');
        }
      });
    });

    it('should default to active when status is not provided', () => {
      const result = createMemberSchema.safeParse({
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
      });

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data.status).toBe('active');
      }
    });
  });

  describe('multiple validation errors', () => {
    it('should return all validation errors when multiple fields are invalid', () => {
      const result = createMemberSchema.safeParse({
        firstName: '', // Empty
        lastName: '', // Empty
        email: 'invalid-email', // Invalid format
        password: 'weak', // Too weak
        phone: 'invalid-phone', // Invalid format
        gender: 'invalid-gender', // Invalid value
        branchId: 'invalid-uuid', // Invalid UUID
        roleId: 'invalid-uuid', // Invalid UUID
        status: 'invalid-status', // Invalid value
      });

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.issues.length).toBeGreaterThan(0);
        
        const errorFields = result.error.issues.map(e => e.path[0]);
        expect(errorFields).toContain('firstName');
        expect(errorFields).toContain('lastName');
        expect(errorFields).toContain('email');
        expect(errorFields).toContain('password');
        expect(errorFields).toContain('phone');
        expect(errorFields).toContain('gender');
        expect(errorFields).toContain('branchId');
        expect(errorFields).toContain('roleId');
        expect(errorFields).toContain('status');
      }
    });
  });
});