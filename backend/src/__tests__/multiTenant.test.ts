import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../app';
import { db } from '../db';
import { churches, members, roles } from '../db/schema';
import { eq } from 'drizzle-orm';

describe('Multi-Tenant Church Registration', () => {
  beforeEach(async () => {
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  afterEach(async () => {
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  describe('POST /api/churches/register', () => {
    it('should register a new church with admin user', async () => {
      const churchData = {
        church: {
          name: 'Grace Community Church',
          slug: 'grace-community-church',
          description: 'A welcoming community church',
          address: '123 Main St, Anytown, ST 12345',
          phone: '******-123-4567',
          email: '<EMAIL>',
          website: 'https://gracechurch.com',
          logo: 'https://gracechurch.com/logo.png',
        },
        admin: {
          firstName: 'John',
          lastName: 'Pastor',
          email: '<EMAIL>',
          password: 'SecurePass123',
          phone: '******-123-4568',
          gender: 'male',
          address: '456 Church Ave, Anytown, ST 12345',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(churchData)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Church and admin account created successfully');
      expect(response.body.data.church.name).toBe(churchData.church.name);
      expect(response.body.data.church.slug).toBe(churchData.church.slug);
      expect(response.body.data.admin.firstName).toBe(churchData.admin.firstName);
      expect(response.body.data.admin.email).toBe(churchData.admin.email);
      expect(response.body.data.tokens.accessToken).toBeDefined();
      expect(response.body.data.tokens.refreshToken).toBeDefined();
      expect(response.body.data.churchUrl).toContain(churchData.church.slug);

      const createdChurch = await db.query.churches.findFirst({
        where: eq(churches.slug, churchData.church.slug),
      });
      expect(createdChurch).toBeDefined();
      expect(createdChurch?.name).toBe(churchData.church.name);

      const createdAdmin = await db.query.members.findFirst({
        where: eq(members.email, churchData.admin.email),
        with: { role: true },
      });
      expect(createdAdmin).toBeDefined();
      expect(createdAdmin?.churchId).toBe(createdChurch?.id);
      expect(createdAdmin?.role?.name).toBe('Super Admin');
      expect(createdAdmin?.isEmailVerified).toBe(true);
    });

    it('should reject duplicate church slug', async () => {
      const churchData = {
        church: {
          name: 'First Church',
          slug: 'duplicate-slug',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      await request(app)
        .post('/api/churches/register')
        .send(churchData)
        .expect(201);

      const duplicateData = {
        church: {
          name: 'Second Church',
          slug: 'duplicate-slug',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Another',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(duplicateData)
        .expect(400);

      expect(response.body.message).toBe('Church with this slug already exists');
    });

    it('should reject duplicate admin email', async () => {
      const firstChurchData = {
        church: {
          name: 'First Church',
          slug: 'first-church',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      await request(app)
        .post('/api/churches/register')
        .send(firstChurchData)
        .expect(201);

      const secondChurchData = {
        church: {
          name: 'Second Church',
          slug: 'second-church',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Another',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(secondChurchData)
        .expect(400);

      expect(response.body.message).toBe('Email already registered');
    });

    it('should validate required fields', async () => {
      const incompleteData = {
        church: {
          name: 'Test Church',
        },
        admin: {
          firstName: 'Admin',
          email: '<EMAIL>',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(incompleteData)
        .expect(400);

      expect(response.body.message).toContain('validation');
    });
  });

  describe('Tenant Isolation', () => {
    let church1: any;
    let church2: any;
    let admin1Token: string;
    let admin2Token: string;
    let member1: any;
    let member2: any;

    beforeEach(async () => {
      const church1Data = {
        church: {
          name: 'Church One',
          slug: 'church-one',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Admin',
          lastName: 'One',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const church2Data = {
        church: {
          name: 'Church Two',
          slug: 'church-two',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Admin',
          lastName: 'Two',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response1 = await request(app)
        .post('/api/churches/register')
        .send(church1Data)
        .expect(201);

      const response2 = await request(app)
        .post('/api/churches/register')
        .send(church2Data)
        .expect(201);

      church1 = response1.body.data.church;
      church2 = response2.body.data.church;
      admin1Token = response1.body.data.tokens.accessToken;
      admin2Token = response2.body.data.tokens.accessToken;

      const memberData1 = {
        firstName: 'Member',
        lastName: 'One',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'church-one',
      };

      const memberData2 = {
        firstName: 'Member',
        lastName: 'Two',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'church-two',
      };

      const memberResponse1 = await request(app)
        .post('/api/auth/register')
        .send(memberData1)
        .expect(201);

      const memberResponse2 = await request(app)
        .post('/api/auth/register')
        .send(memberData2)
        .expect(201);

      member1 = memberResponse1.body.data.user;
      member2 = memberResponse2.body.data.user;
    });

    it('should isolate church settings access', async () => {
      await request(app)
        .get(`/api/churches/${church1.slug}/settings`)
        .set('Authorization', `Bearer ${admin1Token}`)
        .expect(200);

      await request(app)
        .get(`/api/churches/${church1.slug}/settings`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(403);
    });

    it('should isolate member access', async () => {
      const members1Response = await request(app)
        .get('/api/members')
        .set('Authorization', `Bearer ${admin1Token}`)
        .expect(200);

      const members2Response = await request(app)
        .get('/api/members')
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(200);

      expect(members1Response.body.data.members).toHaveLength(2);
      expect(members2Response.body.data.members).toHaveLength(2);
      
      const church1MemberEmails = members1Response.body.data.members.map((m: any) => m.email);
      const church2MemberEmails = members2Response.body.data.members.map((m: any) => m.email);

      expect(church1MemberEmails).toContain('<EMAIL>');
      expect(church1MemberEmails).toContain('<EMAIL>');
      expect(church1MemberEmails).not.toContain('<EMAIL>');
      expect(church1MemberEmails).not.toContain('<EMAIL>');

      expect(church2MemberEmails).toContain('<EMAIL>');
      expect(church2MemberEmails).toContain('<EMAIL>');
      expect(church2MemberEmails).not.toContain('<EMAIL>');
      expect(church2MemberEmails).not.toContain('<EMAIL>');
    });

    it('should prevent cross-tenant member access', async () => {
      await request(app)
        .get(`/api/members/${member2.id}`)
        .set('Authorization', `Bearer ${admin1Token}`)
        .expect(404);

      await request(app)
        .get(`/api/members/${member1.id}`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(404);
    });

    it('should allow church-scoped login', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'church-one',
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(200);

      expect(response.body.data.user.church.slug).toBe('church-one');
    });

    it('should reject login with wrong church slug', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'church-two',
      };

      await request(app)
        .post('/api/auth/login')
        .send(loginData)
        .expect(401);
    });
  });

  describe('Church Self-Registration Settings', () => {
    let church: any;
    let adminToken: string;

    beforeEach(async () => {
      const churchData = {
        church: {
          name: 'Test Church',
          slug: 'test-church',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Admin',
          lastName: 'User',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(churchData)
        .expect(201);

      church = response.body.data.church;
      adminToken = response.body.data.tokens.accessToken;
    });

    it('should allow self-registration by default', async () => {
      const memberData = {
        firstName: 'New',
        lastName: 'Member',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'test-church',
      };

      await request(app)
        .post('/api/auth/register')
        .send(memberData)
        .expect(201);
    });

    it('should prevent self-registration when disabled', async () => {
      await request(app)
        .put(`/api/churches/${church.slug}/settings`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ allowSelfRegistration: false })
        .expect(200);

      const memberData = {
        firstName: 'New',
        lastName: 'Member',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchSlug: 'test-church',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(memberData)
        .expect(403);

      expect(response.body.message).toBe('Self-registration is not allowed for this church');
    });
  });
});