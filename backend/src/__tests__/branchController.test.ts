import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';

vi.mock('../db', () => ({
  db: {
    query: {
      churches: {
        findFirst: vi.fn(),
      },
      branches: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      members: {
        findFirst: vi.fn(),
      },
    },
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn(() => []),
      })),
    })),
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn(() => []),
        })),
      })),
    })),
    $count: vi.fn(),
  },
}));

const { createBranch, getBranches, getBranch, updateBranch, deleteBranch } = await import('../controllers/branchController');
const { globalErrorHandler } = await import('../utils/errorHandler');

const app = express();
app.use(express.json());

const mockAuthenticateUser = (req: any, res: any, next: any) => {
  req.user = {
    id: 'user-123',
    churchId: 'church-123',
    email: '<EMAIL>',
  };
  next();
};

app.use(mockAuthenticateUser);
app.post('/churches/:slug/branches', createBranch);
app.get('/churches/:slug/branches', getBranches);
app.get('/churches/:slug/branches/:branchId', getBranch);
app.put('/churches/:slug/branches/:branchId', updateBranch);
app.delete('/churches/:slug/branches/:branchId', deleteBranch);
app.use(globalErrorHandler);

describe('Branch Controller', () => {
  const mockChurch = {
    id: 'church-123',
    name: 'Test Church',
    slug: 'test-church',
    isActive: true,
  };

  const mockBranch = {
    id: 'branch-123',
    churchId: 'church-123',
    name: 'Main Campus',
    slug: 'main-campus',
    description: 'Our main campus location',
    address: '123 Main St',
    phone: '555-0123',
    email: '<EMAIL>',
    isActive: true,
    isMainBranch: true,
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /churches/:slug/branches', () => {
    const validBranchData = {
      name: 'Downtown Campus',
      slug: 'downtown-campus',
      description: 'Our downtown location',
      address: '456 Downtown Ave',
      phone: '555-0124',
      email: '<EMAIL>',
      isMainBranch: false,
    };

    it('should create a branch successfully', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);
      
      const mockInsert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{
            ...validBranchData,
            id: 'branch-456',
            churchId: 'church-123',
            createdAt: new Date(),
            updatedAt: new Date(),
          }])
        })
      });
      vi.mocked(db.insert).mockImplementation(mockInsert);

      const response = await request(app)
        .post('/churches/test-church/branches')
        .send(validBranchData);

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Branch created successfully');
      expect(response.body.data.branch).toEqual(expect.objectContaining({
        name: 'Downtown Campus',
        slug: 'downtown-campus',
        churchId: 'church-123',
      }));
    });

    it('should return 404 if church not found', async () => {
      const { db } = await import('../db');
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .post('/churches/nonexistent/branches')
        .send(validBranchData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Church not found');
    });

    it('should return 403 if user does not belong to church', async () => {
      const { db } = await import('../db');
      const otherChurch = { ...mockChurch, id: 'other-church-123' };
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(otherChurch);

      const response = await request(app)
        .post('/churches/test-church/branches')
        .send(validBranchData);

      expect(response.status).toBe(403);
      expect(response.body.message).toBe('Access denied. You can only create branches for your own church.');
    });

    it('should return 400 if branch slug already exists', async () => {
      const { db } = await import('../db');
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(mockBranch);

      const response = await request(app)
        .post('/churches/test-church/branches')
        .send({ ...validBranchData, slug: 'main-campus' });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Branch with this slug already exists in your church');
    });
  });

  describe('GET /churches/:slug/branches', () => {
    it('should return list of branches with pagination', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.$count).mockResolvedValue(1);
      vi.mocked(db.query.branches.findMany).mockResolvedValue([
        {
          ...mockBranch,
          leader: {
            id: 'leader-123',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
          },
          members: [
            { id: 'member-1' },
            { id: 'member-2' },
          ]
        }
      ]);

      const response = await request(app)
        .get('/churches/test-church/branches');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.branches).toHaveLength(1);
      expect(response.body.data.branches[0]).toEqual(expect.objectContaining({
        name: 'Main Campus',
        slug: 'main-campus',
        memberCount: 2,
      }));
      expect(response.body.data.pagination).toEqual(expect.objectContaining({
        page: 1,
        limit: 10,
        totalCount: 1,
        totalPages: 1,
      }));
    });

    it('should return 404 if church not found', async () => {
      const { db } = await import('../db');
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .get('/churches/nonexistent/branches');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Church not found');
    });
  });

  describe('GET /churches/:slug/branches/:branchId', () => {
    it('should return specific branch with leader details', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue({
        ...mockBranch,
        leader: {
          id: 'leader-123',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>',
          phone: '555-0125',
        },
        members: [
          {
            id: 'member-1',
            firstName: 'Jane',
            lastName: 'Smith',
            email: '<EMAIL>',
            status: 'active',
          },
          {
            id: 'member-2',
            firstName: 'Bob',
            lastName: 'Johnson',
            email: '<EMAIL>',
            status: 'active',
          },
        ]
      });

      const response = await request(app)
        .get('/churches/test-church/branches/branch-123');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.branch).toEqual(expect.objectContaining({
        id: 'branch-123',
        name: 'Main Campus',
        memberCount: 2,
        activeMembers: 2,
        leader: expect.objectContaining({
          firstName: 'John',
          lastName: 'Doe',
        }),
      }));
    });

    it('should return 404 if branch not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .get('/churches/test-church/branches/nonexistent');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Branch not found');
    });
  });

  describe('PUT /churches/:slug/branches/:branchId', () => {
    const updateData = {
      name: 'Updated Campus Name',
      description: 'Updated description',
    };

    it('should update branch successfully', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(mockBranch);
      
      const mockUpdate = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{
              ...mockBranch,
              ...updateData,
              updatedAt: new Date(),
            }])
          })
        })
      });
      vi.mocked(db.update).mockImplementation(mockUpdate);

      const response = await request(app)
        .put('/churches/test-church/branches/branch-123')
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Branch updated successfully');
      expect(response.body.data.branch).toEqual(expect.objectContaining({
        name: 'Updated Campus Name',
        description: 'Updated description',
      }));
    });

    it('should return 404 if branch not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .put('/churches/test-church/branches/nonexistent')
        .send(updateData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Branch not found');
    });
  });

  describe('DELETE /churches/:slug/branches/:branchId', () => {
    it('should delete branch successfully (soft delete)', async () => {
      const { db } = await import('../db');
      const nonMainBranch = { ...mockBranch, isMainBranch: false };
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(nonMainBranch);
      
      const mockUpdate = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockResolvedValue(undefined)
        })
      });
      vi.mocked(db.update).mockImplementation(mockUpdate);

      const response = await request(app)
        .delete('/churches/test-church/branches/branch-123');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Branch deleted successfully');
    });

    it('should return 400 if trying to delete main branch', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(mockBranch); // isMainBranch: true

      const response = await request(app)
        .delete('/churches/test-church/branches/branch-123');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Cannot delete the main branch. Please designate another branch as main first.');
    });

    it('should return 404 if branch not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .delete('/churches/test-church/branches/nonexistent');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Branch not found');
    });
  });
});