import { describe, it, expect, vi } from 'vitest';
import { globalErrorHandler, AppError, ValidationError } from '../utils/errorHandler';
import { ZodError } from 'zod';
import { createMemberSchema } from '../types/member';

describe('Enhanced Error Handler', () => {
  const mockRequest = {} as any;
  const mockResponse = {
    status: vi.fn().mockReturnThis(),
    json: vi.fn(),
  } as any;
  const mockNext = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('ZodError handling', () => {
    it('should handle ZodError with structured response', () => {
      const invalidData = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        password: 'weak',
      };

      try {
        createMemberSchema.parse(invalidData);
      } catch (error) {
        globalErrorHandler(error, mockRequest, mockResponse, mockNext);

        expect(mockResponse.status).toHaveBeenCalledWith(400);
        expect(mockResponse.json).toHaveBeenCalledWith({
          status: 'error',
          message: 'Validation failed',
          errors: expect.arrayContaining([
            expect.objectContaining({
              field: 'firstName',
              message: expect.any(String),
              code: expect.any(String),
            }),
            expect.objectContaining({
              field: 'email',
              message: expect.any(String),
              code: expect.any(String),
            }),
            expect.objectContaining({
              field: 'password',
              message: expect.any(String),
              code: expect.any(String),
            }),
          ]),
          code: 'VALIDATION_ERROR',
        });
      }
    });

    it('should generate appropriate error codes for different validation types', () => {
      const invalidData = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        password: 'weak',
        phone: 'invalid-phone',
      };

      try {
        createMemberSchema.parse(invalidData);
      } catch (error) {
        globalErrorHandler(error, mockRequest, mockResponse, mockNext);

        const responseCall = mockResponse.json.mock.calls[0][0];
        const errors = responseCall.errors;

        // Check that different error codes are generated
        const errorCodes = errors.map((e: any) => e.code);
        console.log('Generated error codes:', errorCodes);
        console.log('All errors:', errors);
        expect(errorCodes).toContain('TOO_SHORT'); // firstName too short
        expect(errorCodes.length).toBeGreaterThan(0); // At least some errors
      }
    });
  });

  describe('AppError with custom validation errors', () => {
    it('should handle AppError with custom validation errors', () => {
      const customErrors: ValidationError[] = [
        {
          field: 'email',
          message: 'Email address is already registered',
          code: 'DUPLICATE_EMAIL',
        },
      ];

      const error = new AppError(
        'Member creation failed',
        400,
        'DUPLICATE_ERROR',
        customErrors
      );

      globalErrorHandler(error, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Member creation failed',
        errors: customErrors,
        code: 'DUPLICATE_ERROR',
      });
    });
  });

  describe('PostgreSQL error handling', () => {
    it('should handle duplicate key errors', () => {
      const pgError = {
        code: '23505',
        detail: 'Key (email)=(<EMAIL>) already exists.',
      };

      globalErrorHandler(pgError, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Duplicate entry detected',
        errors: [
          {
            field: 'email',
            message: 'Email address is already registered',
            code: 'DUPLICATE_VALUE',
          },
        ],
        code: 'DUPLICATE_ERROR',
      });
    });

    it('should handle foreign key constraint errors', () => {
      const pgError = {
        code: '23503',
        detail: 'Key (branch_id)=(123) is not present in table "branches".',
      };

      globalErrorHandler(pgError, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Invalid reference detected',
        errors: [
          {
            field: 'branchId',
            message: 'Selected option is not valid or does not exist',
            code: 'INVALID_REFERENCE',
          },
        ],
        code: 'REFERENCE_ERROR',
      });
    });

    it('should handle not null constraint errors', () => {
      const pgError = {
        code: '23502',
        message: 'null value in column "first_name" violates not-null constraint',
      };

      globalErrorHandler(pgError, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Required field missing',
        errors: [
          {
            field: 'firstName',
            message: 'This field is required',
            code: 'REQUIRED_FIELD',
          },
        ],
        code: 'REQUIRED_FIELD_ERROR',
      });
    });
  });

  describe('Default error handling', () => {
    it('should handle generic errors', () => {
      const genericError = new Error('Something went wrong');

      globalErrorHandler(genericError, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(500);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Something went wrong',
      });
    });

    it('should handle AppError without custom validation errors', () => {
      const appError = new AppError('Not found', 404, 'NOT_FOUND');

      globalErrorHandler(appError, mockRequest, mockResponse, mockNext);

      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        status: 'error',
        message: 'Not found',
        code: 'NOT_FOUND',
      });
    });
  });
});