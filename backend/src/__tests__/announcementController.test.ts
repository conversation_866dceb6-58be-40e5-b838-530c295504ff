import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { churches, members, roles, announcements, announcementViews, announcementComments, announcementReactions } from '../db/schema';

describe('Church Announcements', () => {
  let church: any;
  let adminToken: string;
  let memberToken: string;

  beforeEach(async () => {
    await db.delete(announcementReactions);
    await db.delete(announcementComments);
    await db.delete(announcementViews);
    await db.delete(announcements);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);

    const churchData = {
      church: {
        name: 'Grace Community Church',
        slug: 'grace-community',
        email: '<EMAIL>',
      },
      admin: {
        firstName: 'John',
        lastName: 'Pastor',
        email: '<EMAIL>',
        password: 'SecurePass123',
      },
    };

    const response = await request(app)
      .post('/api/churches/register')
      .send(churchData)
      .expect(201);

    church = response.body.data.church;
    adminToken = response.body.data.tokens.accessToken;

    const memberData = {
      firstName: 'Jane',
      lastName: 'Member',
      email: '<EMAIL>',
      password: 'SecurePass123',
      churchSlug: 'grace-community',
    };

    const memberResponse = await request(app)
      .post('/api/auth/register')
      .send(memberData)
      .expect(201);

    memberToken = memberResponse.body.data.tokens.accessToken;
  });

  afterEach(async () => {
    await db.delete(announcementReactions);
    await db.delete(announcementComments);
    await db.delete(announcementViews);
    await db.delete(announcements);
    await db.delete(members);
    await db.delete(roles);    
    await db.delete(churches);
  });

  describe('Announcement Management', () => {
    describe('POST /api/churches/:slug/announcements', () => {
      it('should create a new announcement', async () => {
        const announcementData = {
          title: 'Sunday Service Update',
          content: 'This Sunday\'s service will be held in the main sanctuary at 10 AM.',
          summary: 'Service time change notification',
          type: 'service_update',
          priority: 'normal',
          targetAudience: 'all_members',
          isPinned: false,
          allowComments: true,
          requiresAcknowledgment: false,
          sendNotification: true,
          tags: ['service', 'update'],
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        expect(response.body.status).toBe('success');
        expect(response.body.data.announcement.title).toBe(announcementData.title);
        expect(response.body.data.announcement.content).toBe(announcementData.content);
        expect(response.body.data.announcement.type).toBe(announcementData.type);
        expect(response.body.data.announcement.status).toBe('published');
      });

      it('should create a scheduled announcement', async () => {
        const futureDate = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now
        const announcementData = {
          title: 'Upcoming Event',
          content: 'We have an exciting event planned for next week!',
          type: 'general',
          scheduledFor: futureDate.toISOString(),
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        expect(response.body.status).toBe('success');
        expect(response.body.data.announcement.status).toBe('scheduled');
        expect(response.body.data.announcement.publishedAt).toBeNull();
      });

      it('should require admin/pastor/elder role to create announcements', async () => {
        const announcementData = {
          title: 'Unauthorized Announcement',
          content: 'This should not be created by a regular member.',
        };

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(announcementData)
          .expect(403);
      });

      it('should validate required fields', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({})
          .expect(400);
      });
    });

    describe('GET /api/churches/:slug/announcements', () => {
      it('should get all announcements for church members', async () => {
        // Create test announcements
        const announcement1 = {
          title: 'First Announcement',
          content: 'First announcement content',
          type: 'general',
        };

        const announcement2 = {
          title: 'Second Announcement',
          content: 'Second announcement content',
          type: 'urgent',
          priority: 'high',
          isPinned: true,
        };

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcement1)
          .expect(201);

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcement2)
          .expect(201);

        const response = await request(app)
          .get(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.data.announcements).toHaveLength(2);
        
        // Pinned announcement should be first
        expect(response.body.data.announcements[0].title).toBe('Second Announcement');
        expect(response.body.data.announcements[0].isPinned).toBe(true);
      });

      it('should support filtering by type', async () => {
        const urgentAnnouncement = {
          title: 'Urgent Announcement',
          content: 'This is urgent',
          type: 'urgent',
        };

        const generalAnnouncement = {
          title: 'General Announcement',
          content: 'This is general',
          type: 'general',
        };

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(urgentAnnouncement)
          .expect(201);

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(generalAnnouncement)
          .expect(201);

        const response = await request(app)
          .get(`/api/churches/${church.slug}/announcements?type=urgent`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.data.announcements).toHaveLength(1);
        expect(response.body.data.announcements[0].type).toBe('urgent');
      });

      it('should support search functionality', async () => {
        const searchableAnnouncement = {
          title: 'Sunday Service Special',
          content: 'Join us for a special Sunday service celebration',
          type: 'service_update',
        };

        await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(searchableAnnouncement)
          .expect(201);

        const response = await request(app)
          .get(`/api/churches/${church.slug}/announcements?search=Sunday`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.data.announcements).toHaveLength(1);
        expect(response.body.data.announcements[0].title).toContain('Sunday');
      });

      it('should include pagination', async () => {
        // Create multiple announcements
        for (let i = 1; i <= 25; i++) {
          await request(app)
            .post(`/api/churches/${church.slug}/announcements`)
            .set('Authorization', `Bearer ${adminToken}`)
            .send({
              title: `Announcement ${i}`,
              content: `Content for announcement ${i}`,
              type: 'general',
            })
            .expect(201);
        }

        const response = await request(app)
          .get(`/api/churches/${church.slug}/announcements?page=2&limit=10`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.data.announcements).toHaveLength(10);
        expect(response.body.data.pagination.page).toBe(2);
        expect(response.body.data.pagination.limit).toBe(10);
        expect(response.body.data.pagination.total).toBe(25);
        expect(response.body.data.pagination.totalPages).toBe(3);
      });
    });

    describe('GET /api/churches/:slug/announcements/:id', () => {
      it('should get a specific announcement with details', async () => {
        const announcementData = {
          title: 'Detailed Announcement',
          content: 'This announcement has full details',
          type: 'general',
          allowComments: true,
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        const announcementId = createResponse.body.data.announcement.id;

        const response = await request(app)
          .get(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.data.announcement.id).toBe(announcementId);
        expect(response.body.data.announcement.title).toBe(announcementData.title);
        expect(response.body.data.announcement.author).toBeDefined();
      });

      it('should return 404 for non-existent announcement', async () => {
        const fakeId = '123e4567-e89b-12d3-a456-426614174000';
        
        await request(app)
          .get(`/api/churches/${church.slug}/announcements/${fakeId}`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(404);
      });
    });

    describe('PUT /api/churches/:slug/announcements/:id', () => {
      it('should update an announcement', async () => {
        const announcementData = {
          title: 'Original Title',
          content: 'Original content',
          type: 'general',
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        const announcementId = createResponse.body.data.announcement.id;

        const updateData = {
          title: 'Updated Title',
          content: 'Updated content',
          type: 'urgent',
          priority: 'high',
        };

        const response = await request(app)
          .put(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(updateData)
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.data.announcement.title).toBe('Updated Title');
        expect(response.body.data.announcement.type).toBe('urgent');
      });

      it('should require admin/pastor/elder role to update announcements', async () => {
        const announcementData = {
          title: 'Test Announcement',
          content: 'Test content',
          type: 'general',
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        const announcementId = createResponse.body.data.announcement.id;

        await request(app)
          .put(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ title: 'Unauthorized Update' })
          .expect(403);
      });
    });

    describe('DELETE /api/churches/:slug/announcements/:id', () => {
      it('should delete an announcement', async () => {
        const announcementData = {
          title: 'To Be Deleted',
          content: 'This will be deleted',
          type: 'general',
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        const announcementId = createResponse.body.data.announcement.id;

        await request(app)
          .delete(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        // Verify it's deleted
        await request(app)
          .get(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(404);
      });

      it('should require admin/pastor role to delete announcements', async () => {
        const announcementData = {
          title: 'Protected Announcement',
          content: 'Cannot be deleted by member',
          type: 'general',
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(announcementData)
          .expect(201);

        const announcementId = createResponse.body.data.announcement.id;

        await request(app)
          .delete(`/api/churches/${church.slug}/announcements/${announcementId}`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(403);
      });
    });
  });

  describe('Announcement Interactions', () => {
    let announcementId: string;

    beforeEach(async () => {
      const announcementData = {
        title: 'Interactive Announcement',
        content: 'This announcement supports interactions',
        type: 'general',
        allowComments: true,
      };

      const response = await request(app)
        .post(`/api/churches/${church.slug}/announcements`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(announcementData)
        .expect(201);

      announcementId = response.body.data.announcement.id;
    });

    describe('POST /api/churches/:slug/announcements/:id/view', () => {
      it('should mark announcement as viewed', async () => {
        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/view`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ acknowledged: false })
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.message).toBe('Announcement marked as viewed');
      });

      it('should mark announcement as acknowledged', async () => {
        // First, create an announcement that requires acknowledgment
        const ackAnnouncementData = {
          title: 'Important Notice',
          content: 'Please acknowledge this announcement',
          type: 'urgent',
          requiresAcknowledgment: true,
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(ackAnnouncementData)
          .expect(201);

        const ackAnnouncementId = createResponse.body.data.announcement.id;

        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/${ackAnnouncementId}/view`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ 
            acknowledged: true,
            deviceInfo: {
              userAgent: 'test-agent',
              platform: 'test-platform'
            }
          })
          .expect(200);

        expect(response.body.status).toBe('success');
      });
    });

    describe('POST /api/churches/:slug/announcements/:id/comments', () => {
      it('should create a comment on announcement', async () => {
        const commentData = {
          content: 'Great announcement! Thank you for sharing.',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/comments`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(commentData)
          .expect(201);

        expect(response.body.status).toBe('success');
        expect(response.body.data.comment.content).toBe(commentData.content);
        expect(response.body.data.comment.author).toBeDefined();
      });

      it('should not allow comments on announcements that disable comments', async () => {
        const noCommentsData = {
          title: 'No Comments Allowed',
          content: 'Comments are disabled for this announcement',
          type: 'general',
          allowComments: false,
        };

        const createResponse = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(noCommentsData)
          .expect(201);

        const noCommentsId = createResponse.body.data.announcement.id;

        await request(app)
          .post(`/api/churches/${church.slug}/announcements/${noCommentsId}/comments`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ content: 'This should fail' })
          .expect(403);
      });
    });

    describe('POST /api/churches/:slug/announcements/:id/reactions', () => {
      it('should create a reaction on announcement', async () => {
        const reactionData = {
          reactionType: 'like',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(reactionData)
          .expect(201);

        expect(response.body.status).toBe('success');
        expect(response.body.data.reaction.reactionType).toBe('like');
      });

      it('should update existing reaction', async () => {
        // First reaction
        await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ reactionType: 'like' })
          .expect(201);

        // Update reaction
        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ reactionType: 'love' })
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.data.reaction.reactionType).toBe('love');
      });
    });

    describe('DELETE /api/churches/:slug/announcements/:id/reactions', () => {
      it('should delete user reaction', async () => {
        // First create a reaction
        await request(app)
          .post(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ reactionType: 'like' })
          .expect(201);

        // Then delete it
        const response = await request(app)
          .delete(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.message).toBe('Reaction deleted successfully');
      });

      it('should return 404 when no reaction exists', async () => {
        await request(app)
          .delete(`/api/churches/${church.slug}/announcements/${announcementId}/reactions`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(404);
      });
    });
  });

  describe('Bulk Operations', () => {
    let announcementIds: string[];

    beforeEach(async () => {
      announcementIds = [];
      
      // Create multiple test announcements
      for (let i = 1; i <= 3; i++) {
        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            title: `Bulk Test Announcement ${i}`,
            content: `Content for bulk test ${i}`,
            type: 'general',
            status: 'draft',
          })
          .expect(201);

        announcementIds.push(response.body.data.announcement.id);
      }
    });

    describe('POST /api/churches/:slug/announcements/bulk/status', () => {
      it('should bulk update announcement status', async () => {
        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/bulk/status`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            announcementIds,
            status: 'published',
          })
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.message).toContain('3 announcements updated successfully');
      });

      it('should require admin/pastor/elder role for bulk status update', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/announcements/bulk/status`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({
            announcementIds,
            status: 'published',
          })
          .expect(403);
      });
    });

    describe('POST /api/churches/:slug/announcements/bulk/delete', () => {
      it('should bulk delete announcements', async () => {
        const response = await request(app)
          .post(`/api/churches/${church.slug}/announcements/bulk/delete`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({
            announcementIds,
          })
          .expect(200);

        expect(response.body.status).toBe('success');
        expect(response.body.message).toContain('3 announcements deleted successfully');

        // Verify announcements are deleted
        for (const id of announcementIds) {
          await request(app)
            .get(`/api/churches/${church.slug}/announcements/${id}`)
            .set('Authorization', `Bearer ${memberToken}`)
            .expect(404);
        }
      });

      it('should require admin/pastor role for bulk delete', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/announcements/bulk/delete`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({
            announcementIds,
          })
          .expect(403);
      });
    });
  });

  describe('Multi-tenant Isolation', () => {
    it('should prevent cross-tenant announcement access', async () => {
      // Create second church
      const church2Data = {
        church: {
          name: 'Second Church',
          slug: 'second-church',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Jane',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const church2Response = await request(app)
        .post('/api/churches/register')
        .send(church2Data)
        .expect(201);

      const church2 = church2Response.body.data.church;
      const church2Token = church2Response.body.data.tokens.accessToken;

      // Create announcement in first church
      const announcementData = {
        title: 'Church 1 Announcement',
        content: 'This belongs to church 1',
        type: 'general',
      };

      const response = await request(app)
        .post(`/api/churches/${church.slug}/announcements`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(announcementData)
        .expect(201);

      const announcementId = response.body.data.announcement.id;

      // Try to access from second church
      await request(app)
        .get(`/api/churches/${church2.slug}/announcements/${announcementId}`)
        .set('Authorization', `Bearer ${church2Token}`)
        .expect(404);

      // Try to update from second church
      await request(app)
        .put(`/api/churches/${church2.slug}/announcements/${announcementId}`)
        .set('Authorization', `Bearer ${church2Token}`)
        .send({ title: 'Unauthorized update' })
        .expect(404);
    });
  });
});