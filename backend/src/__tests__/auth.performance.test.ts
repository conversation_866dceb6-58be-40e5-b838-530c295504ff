/**
 * Authentication Performance Tests
 * 
 * Performance tests for authentication operations:
 * - Login/logout response times
 * - Token refresh latency
 * - Concurrent authentication handling
 * - Memory usage during long sessions
 * - Database query optimization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { churches, members, roles, refreshTokens } from '../db/schema';

describe('Authentication Performance Tests', () => {
  let testChurch: any;
  let testUsers: any[] = [];
  let performanceMetrics: { [key: string]: number[] } = {};

  beforeEach(async () => {
    // Clean up database
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);

    // Reset performance metrics
    performanceMetrics = {};

    // Create test church
    const churchData = {
      church: {
        name: 'Performance Test Church',
        slug: 'performance-test-church',
        email: '<EMAIL>',
        churchCode: 'PERF123',
      },
      admin: {
        firstName: 'Performance',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'SecurePass123',
      },
    };

    const response = await request(app)
      .post('/api/churches/register')
      .send(churchData);

    testChurch = response.body.data.church;
    testUsers.push(response.body.data.admin);

    // Create additional test users for concurrent testing
    for (let i = 1; i <= 10; i++) {
      const userData = {
        firstName: `User${i}`,
        lastName: 'Test',
        email: `user${i}@perftest.com`,
        password: 'SecurePass123',
        churchCode: 'PERF123',
      };

      const userResponse = await request(app)
        .post('/api/auth/register')
        .send(userData);

      testUsers.push(userResponse.body.data.user);
    }
  });

  afterEach(async () => {
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  const measurePerformance = async (operation: string, fn: () => Promise<any>) => {
    const startTime = process.hrtime.bigint();
    const result = await fn();
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

    if (!performanceMetrics[operation]) {
      performanceMetrics[operation] = [];
    }
    performanceMetrics[operation].push(duration);

    return { result, duration };
  };

  const getPerformanceStats = (operation: string) => {
    const times = performanceMetrics[operation] || [];
    if (times.length === 0) return null;

    const sorted = times.sort((a, b) => a - b);
    return {
      min: sorted[0],
      max: sorted[sorted.length - 1],
      avg: times.reduce((a, b) => a + b, 0) / times.length,
      median: sorted[Math.floor(sorted.length / 2)],
      p95: sorted[Math.floor(sorted.length * 0.95)],
      p99: sorted[Math.floor(sorted.length * 0.99)],
      count: times.length,
    };
  };

  describe('Login Performance', () => {
    it('should handle login requests within acceptable time limits', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'PERF123',
      };

      // Perform multiple login operations
      for (let i = 0; i < 50; i++) {
        await measurePerformance('login', async () => {
          return request(app)
            .post('/api/auth/login')
            .send(loginData)
            .expect(200);
        });
      }

      const stats = getPerformanceStats('login');
      expect(stats).toBeDefined();
      
      // Login should complete within reasonable time limits
      expect(stats!.avg).toBeLessThan(500); // Average < 500ms
      expect(stats!.p95).toBeLessThan(1000); // 95th percentile < 1s
      expect(stats!.max).toBeLessThan(2000); // Max < 2s

      console.log('Login Performance Stats:', stats);
    });

    it('should maintain consistent performance under load', async () => {
      const loginPromises = testUsers.slice(0, 5).map(async (user, index) => {
        const loginData = {
          email: user.email,
          password: 'SecurePass123',
          churchCode: 'PERF123',
        };

        return measurePerformance(`concurrent_login_${index}`, async () => {
          return request(app)
            .post('/api/auth/login')
            .send(loginData);
        });
      });

      const results = await Promise.all(loginPromises);
      
      // All requests should succeed
      results.forEach(({ result }) => {
        expect(result.status).toBe(200);
      });

      // Performance should be consistent
      const durations = results.map(({ duration }) => duration);
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
      const maxDeviation = Math.max(...durations.map(d => Math.abs(d - avgDuration)));

      // Maximum deviation should not be more than 200% of average
      expect(maxDeviation).toBeLessThan(avgDuration * 2);

      console.log('Concurrent Login Durations:', durations);
    });

    it('should handle failed login attempts efficiently', async () => {
      const invalidLoginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
        churchCode: 'PERF123',
      };

      // Measure failed login performance
      for (let i = 0; i < 20; i++) {
        await measurePerformance('failed_login', async () => {
          return request(app)
            .post('/api/auth/login')
            .send(invalidLoginData)
            .expect(401);
        });
      }

      const stats = getPerformanceStats('failed_login');
      expect(stats).toBeDefined();

      // Failed logins should be fast (no unnecessary processing)
      expect(stats!.avg).toBeLessThan(300); // Average < 300ms
      expect(stats!.p95).toBeLessThan(500); // 95th percentile < 500ms

      console.log('Failed Login Performance Stats:', stats);
    });
  });

  describe('Token Refresh Performance', () => {
    let validTokens: any;

    beforeEach(async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          churchCode: 'PERF123',
        });

      validTokens = loginResponse.body.data.tokens;
    });

    it('should refresh tokens quickly', async () => {
      // Perform multiple token refresh operations
      let currentRefreshToken = validTokens.refreshToken;

      for (let i = 0; i < 30; i++) {
        const { result } = await measurePerformance('token_refresh', async () => {
          return request(app)
            .post('/api/auth/refresh')
            .send({ refreshToken: currentRefreshToken })
            .expect(200);
        });

        currentRefreshToken = result.body.data.tokens.refreshToken;
      }

      const stats = getPerformanceStats('token_refresh');
      expect(stats).toBeDefined();

      // Token refresh should be very fast
      expect(stats!.avg).toBeLessThan(200); // Average < 200ms
      expect(stats!.p95).toBeLessThan(400); // 95th percentile < 400ms

      console.log('Token Refresh Performance Stats:', stats);
    });

    it('should handle concurrent token refresh requests', async () => {
      // Create multiple sessions for concurrent refresh testing
      const sessions = await Promise.all(
        testUsers.slice(0, 5).map(async (user) => {
          const loginResponse = await request(app)
            .post('/api/auth/login')
            .send({
              email: user.email,
              password: 'SecurePass123',
              churchCode: 'PERF123',
            });
          return loginResponse.body.data.tokens;
        })
      );

      // Perform concurrent refresh operations
      const refreshPromises = sessions.map(async (tokens, index) => {
        return measurePerformance(`concurrent_refresh_${index}`, async () => {
          return request(app)
            .post('/api/auth/refresh')
            .send({ refreshToken: tokens.refreshToken });
        });
      });

      const results = await Promise.all(refreshPromises);

      // All refreshes should succeed
      results.forEach(({ result }) => {
        expect(result.status).toBe(200);
      });

      // Performance should be consistent
      const durations = results.map(({ duration }) => duration);
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;

      expect(avgDuration).toBeLessThan(300); // Average < 300ms

      console.log('Concurrent Refresh Durations:', durations);
    });
  });

  describe('Profile Access Performance', () => {
    let validToken: string;

    beforeEach(async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          churchCode: 'PERF123',
        });

      validToken = loginResponse.body.data.tokens.accessToken;
    });

    it('should serve profile requests quickly', async () => {
      // Perform multiple profile requests
      for (let i = 0; i < 100; i++) {
        await measurePerformance('profile_access', async () => {
          return request(app)
            .get('/api/auth/profile')
            .set('Authorization', `Bearer ${validToken}`)
            .expect(200);
        });
      }

      const stats = getPerformanceStats('profile_access');
      expect(stats).toBeDefined();

      // Profile access should be very fast (cached/optimized)
      expect(stats!.avg).toBeLessThan(100); // Average < 100ms
      expect(stats!.p95).toBeLessThan(200); // 95th percentile < 200ms

      console.log('Profile Access Performance Stats:', stats);
    });

    it('should handle high concurrent profile requests', async () => {
      // Create multiple concurrent profile requests
      const concurrentRequests = Array(20).fill(null).map(async (_, index) => {
        return measurePerformance(`concurrent_profile_${index}`, async () => {
          return request(app)
            .get('/api/auth/profile')
            .set('Authorization', `Bearer ${validToken}`);
        });
      });

      const results = await Promise.all(concurrentRequests);

      // All requests should succeed
      results.forEach(({ result }) => {
        expect(result.status).toBe(200);
      });

      // Performance should remain good under load
      const durations = results.map(({ duration }) => duration);
      const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;

      expect(avgDuration).toBeLessThan(150); // Average < 150ms

      console.log('Concurrent Profile Access Durations:', durations);
    });
  });

  describe('Database Query Performance', () => {
    it('should optimize user lookup queries', async () => {
      // Mock database query timing
      const originalQuery = db.query;
      const queryTimes: number[] = [];

      // Intercept database queries
      vi.spyOn(db, 'query' as any).mockImplementation((...args) => {
        const startTime = process.hrtime.bigint();
        const result = originalQuery.apply(db, args);
        
        if (result && typeof result.then === 'function') {
          return result.then((data: any) => {
            const endTime = process.hrtime.bigint();
            const duration = Number(endTime - startTime) / 1000000;
            queryTimes.push(duration);
            return data;
          });
        }
        
        const endTime = process.hrtime.bigint();
        const duration = Number(endTime - startTime) / 1000000;
        queryTimes.push(duration);
        return result;
      });

      // Perform login operations that trigger database queries
      for (let i = 0; i < 10; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123',
            churchCode: 'PERF123',
          });
      }

      // Analyze query performance
      if (queryTimes.length > 0) {
        const avgQueryTime = queryTimes.reduce((a, b) => a + b, 0) / queryTimes.length;
        const maxQueryTime = Math.max(...queryTimes);

        expect(avgQueryTime).toBeLessThan(50); // Average query < 50ms
        expect(maxQueryTime).toBeLessThan(200); // Max query < 200ms

        console.log('Database Query Performance:', {
          avgQueryTime,
          maxQueryTime,
          totalQueries: queryTimes.length,
        });
      }

      vi.restoreAllMocks();
    });

    it('should use efficient indexes for authentication queries', async () => {
      // Test that email lookups are fast even with many users
      const startTime = Date.now();

      // Perform multiple user lookups
      for (let i = 0; i < 50; i++) {
        await request(app)
          .post('/api/auth/login')
          .send({
            email: `user${(i % 10) + 1}@perftest.com`,
            password: 'SecurePass123',
            churchCode: 'PERF123',
          });
      }

      const endTime = Date.now();
      const totalTime = endTime - startTime;
      const avgTimePerLookup = totalTime / 50;

      // Should maintain good performance even with multiple lookups
      expect(avgTimePerLookup).toBeLessThan(100); // Average < 100ms per lookup

      console.log('User Lookup Performance:', {
        totalTime,
        avgTimePerLookup,
        lookupsPerSecond: 1000 / avgTimePerLookup,
      });
    });
  });

  describe('Memory Usage Performance', () => {
    it('should not leak memory during authentication operations', async () => {
      const initialMemory = process.memoryUsage();

      // Perform many authentication operations
      for (let i = 0; i < 100; i++) {
        const loginResponse = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'SecurePass123',
            churchCode: 'PERF123',
          });

        const tokens = loginResponse.body.data.tokens;

        // Access profile
        await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${tokens.accessToken}`);

        // Refresh token
        await request(app)
          .post('/api/auth/refresh')
          .send({ refreshToken: tokens.refreshToken });

        // Logout
        await request(app)
          .post('/api/auth/logout')
          .set('Authorization', `Bearer ${tokens.accessToken}`);

        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      const memoryIncreasePercent = (memoryIncrease / initialMemory.heapUsed) * 100;

      // Memory increase should be minimal (< 50% increase)
      expect(memoryIncreasePercent).toBeLessThan(50);

      console.log('Memory Usage:', {
        initial: Math.round(initialMemory.heapUsed / 1024 / 1024) + 'MB',
        final: Math.round(finalMemory.heapUsed / 1024 / 1024) + 'MB',
        increase: Math.round(memoryIncrease / 1024 / 1024) + 'MB',
        increasePercent: Math.round(memoryIncreasePercent * 100) / 100 + '%',
      });
    });

    it('should handle long-running sessions efficiently', async () => {
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          churchCode: 'PERF123',
        });

      let tokens = loginResponse.body.data.tokens;
      const initialMemory = process.memoryUsage();

      // Simulate long-running session with periodic activity
      for (let i = 0; i < 50; i++) {
        // Access profile multiple times
        for (let j = 0; j < 5; j++) {
          await request(app)
            .get('/api/auth/profile')
            .set('Authorization', `Bearer ${tokens.accessToken}`);
        }

        // Refresh token periodically
        if (i % 10 === 0) {
          const refreshResponse = await request(app)
            .post('/api/auth/refresh')
            .send({ refreshToken: tokens.refreshToken });
          
          tokens = refreshResponse.body.data.tokens;
        }

        // Small delay to simulate real usage
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;

      // Memory should not grow significantly during long sessions
      expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // < 10MB increase

      console.log('Long Session Memory Usage:', {
        initial: Math.round(initialMemory.heapUsed / 1024 / 1024) + 'MB',
        final: Math.round(finalMemory.heapUsed / 1024 / 1024) + 'MB',
        increase: Math.round(memoryIncrease / 1024 / 1024) + 'MB',
      });
    });
  });

  describe('Scalability Performance', () => {
    it('should handle increasing user load gracefully', async () => {
      const loadLevels = [1, 5, 10, 20];
      const results: { [key: number]: number } = {};

      for (const userCount of loadLevels) {
        const startTime = Date.now();

        // Create concurrent login requests
        const loginPromises = Array(userCount).fill(null).map(async (_, index) => {
          const userIndex = (index % testUsers.length);
          return request(app)
            .post('/api/auth/login')
            .send({
              email: testUsers[userIndex].email,
              password: 'SecurePass123',
              churchCode: 'PERF123',
            });
        });

        const responses = await Promise.all(loginPromises);
        const endTime = Date.now();

        // All requests should succeed
        responses.forEach(response => {
          expect(response.status).toBe(200);
        });

        const totalTime = endTime - startTime;
        const avgTimePerRequest = totalTime / userCount;
        results[userCount] = avgTimePerRequest;

        console.log(`Load Level ${userCount}: ${avgTimePerRequest}ms avg per request`);
      }

      // Performance should degrade gracefully (not exponentially)
      const degradationRatio = results[20] / results[1];
      expect(degradationRatio).toBeLessThan(5); // Should not be more than 5x slower

      console.log('Scalability Results:', results);
      console.log('Performance Degradation Ratio (20 users vs 1 user):', degradationRatio);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should maintain baseline performance metrics', async () => {
      // Define baseline performance expectations
      const baselines = {
        login: 500,           // 500ms
        tokenRefresh: 200,    // 200ms
        profileAccess: 100,   // 100ms
        logout: 150,          // 150ms
      };

      // Test each operation
      const operations = [
        {
          name: 'login',
          fn: () => request(app)
            .post('/api/auth/login')
            .send({
              email: '<EMAIL>',
              password: 'SecurePass123',
              churchCode: 'PERF123',
            }),
        },
        {
          name: 'profileAccess',
          fn: async () => {
            const loginResponse = await request(app)
              .post('/api/auth/login')
              .send({
                email: '<EMAIL>',
                password: 'SecurePass123',
                churchCode: 'PERF123',
              });
            
            return request(app)
              .get('/api/auth/profile')
              .set('Authorization', `Bearer ${loginResponse.body.data.tokens.accessToken}`);
          },
        },
      ];

      for (const operation of operations) {
        // Run operation multiple times
        for (let i = 0; i < 10; i++) {
          await measurePerformance(operation.name, operation.fn);
        }

        const stats = getPerformanceStats(operation.name);
        const baseline = baselines[operation.name as keyof typeof baselines];

        if (stats && baseline) {
          expect(stats.avg).toBeLessThan(baseline);
          console.log(`${operation.name} Performance:`, {
            avg: Math.round(stats.avg),
            baseline,
            withinBaseline: stats.avg < baseline,
          });
        }
      }
    });
  });
});