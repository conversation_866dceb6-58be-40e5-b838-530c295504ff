import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest';
import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { churches, members, roles, events, donations, branches } from '../db/schema';
import { eq } from 'drizzle-orm';
import { hashPassword } from '../utils/auth';

describe('Analytics Endpoints', () => {
  let churchId: string;
  let churchSlug: string;
  let accessToken: string;
  let memberId: string;
  let branchId: string;

  beforeAll(async () => {
    // Create test church
    const [church] = await db.insert(churches).values({
      name: 'Test Analytics Church',
      slug: 'test-analytics-church',
      churchCode: 'TEST123',
      description: 'Church for analytics testing',
      email: '<EMAIL>',
      phone: '+1234567890',
      address: '123 Analytics St, Test City, TC 12345',
      settings: {
        allowSelfRegistration: true,
        requireEmailVerification: false,
        timezone: 'UTC',
        locale: 'en',
        theme: { primaryColor: '#3B82F6', secondaryColor: '#64748B' },
        features: { events: true, donations: true, messaging: true, calendar: true }
      }
    }).returning();

    churchId = church.id;
    churchSlug = church.slug;

    // Create test role
    const [role] = await db.insert(roles).values({
      name: 'Super Admin',
      description: 'Full admin access',
      permissions: ['all'],
      churchId: churchId,
    }).returning();

    // Create test branch
    const [branch] = await db.insert(branches).values({
      churchId: churchId,
      name: 'Main Campus',
      slug: 'main-campus',
      description: 'Main church campus',
      address: '123 Main St, Test City, TC 12345',
      isMain: true,
    }).returning();

    branchId = branch.id;

    // Create test admin user
    const hashedPassword = await hashPassword('password123');
    
    const [member] = await db.insert(members).values({
      userId: '1234567890',
      firstName: 'Analytics',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      churchId: churchId,
      roleId: role.id,
      branchId: branchId,
      isEmailVerified: true,
    }).returning();

    memberId = member.id;

    // Login to get access token
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({
        emailOrUserId: '<EMAIL>',
        password: 'password123',
      });

    accessToken = loginResponse.body.data.tokens.accessToken;
  });

  beforeEach(async () => {
    // Ensure cleanup of prior test members (by emails)
    await db.delete(members).where(eq(members.email, '<EMAIL>'));
    await db.delete(members).where(eq(members.email, '<EMAIL>'));
    await db.delete(members).where(eq(members.email, '<EMAIL>'));

    // Create test data for analytics
    await db.insert(members).values([
      {
        userId: '1234567891',
        firstName: 'Test',
        lastName: 'Member1',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: churchId,
        branchId: branchId,
        status: 'active',
        gender: 'male',
      },
      {
        userId: '1234567892',
        firstName: 'Test',
        lastName: 'Member2',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: churchId,
        branchId: branchId,
        status: 'active',
        gender: 'female',
      },
      {
        userId: '1234567893',
        firstName: 'Test',
        lastName: 'Member3',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: churchId,
        status: 'inactive',
        gender: 'male',
      }
    ]);

    // Create test events
    await db.insert(events).values([
      {
        churchId: churchId,
        branchId: branchId,
        createdBy: memberId,
        title: 'Sunday Service',
        description: 'Weekly Sunday service',
        type: 'sunday_service',
        status: 'published',
        startDate: new Date('2024-01-14T10:00:00Z'),
        endDate: new Date('2024-01-14T12:00:00Z'),
        requiresRsvp: true,
      },
      {
        churchId: churchId,
        branchId: branchId,
        createdBy: memberId,
        title: 'Bible Study',
        description: 'Weekly Bible study',
        type: 'bible_study',
        status: 'published',
        startDate: new Date('2024-01-17T19:00:00Z'),
        endDate: new Date('2024-01-17T21:00:00Z'),
        requiresRsvp: false,
      }
    ]);

    // Create test donations
    await db.insert(donations).values([
      {
        churchId: churchId,
        branchId: branchId,
        donorId: memberId,
        amount: '100.00',
        currency: 'USD',
        type: 'tithe',
        method: 'credit_card',
        status: 'completed',
        receiptNumber: 'TEST-001',
        recordedBy: memberId,
      },
      {
        churchId: churchId,
        branchId: branchId,
        amount: '50.00',
        currency: 'USD',
        type: 'offering',
        method: 'cash',
        status: 'completed',
        donorName: 'Anonymous Donor',
        isAnonymous: true,
        receiptNumber: 'TEST-002',
        recordedBy: memberId,
      },
      {
        churchId: churchId,
        amount: '75.00',
        currency: 'USD',
        type: 'building_fund',
        method: 'check',
        status: 'completed',
        donorName: 'Guest Donor',
        receiptNumber: 'TEST-003',
        recordedBy: memberId,
      }
    ]);
  });

  afterAll(async () => {
    // Clean up test data
    await db.delete(donations).where(eq(donations.churchId, churchId));
    await db.delete(events).where(eq(events.churchId, churchId));
    await db.delete(members).where(eq(members.churchId, churchId));
    await db.delete(branches).where(eq(branches.churchId, churchId));
    await db.delete(roles).where(eq(roles.churchId, churchId));
    await db.delete(churches).where(eq(churches.id, churchId));
  });

  describe('GET /api/churches/:slug/analytics/dashboard', () => {
    it('should return dashboard overview analytics', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/dashboard`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.overview).toMatchObject({
        totalMembers: expect.any(Number),
        totalEvents: expect.any(Number),
        totalDonations: expect.any(Number),
        totalRevenue: expect.any(Number),
        memberGrowthRate: expect.any(Number),
        eventAttendanceRate: expect.any(Number),
        donationGrowthRate: expect.any(Number),
        recentActivity: expect.any(Array),
      });

      expect(response.body.data.overview.totalMembers).toBeGreaterThan(0);
      expect(response.body.data.overview.totalEvents).toBeGreaterThan(0);
      expect(response.body.data.overview.totalDonations).toBeGreaterThan(0);
      expect(response.body.data.overview.totalRevenue).toBeGreaterThan(0);
    });

    it('should filter by branch when branchId is provided', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/dashboard`)
        .query({ branchId: branchId })
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.overview).toBeDefined();
    });

    it('should filter by time period', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/dashboard`)
        .query({ period: '7d' })
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.overview).toBeDefined();
    });

    it('should require authentication', async () => {
      await request(app)
        .get(`/api/churches/${churchSlug}/analytics/dashboard`)
        .expect(401);
    });

    it('should require appropriate permissions', async () => {
      // Create a member with limited permissions
      const [limitedRole] = await db.insert(roles).values({
        name: 'Member',
        description: 'Basic member access',
        permissions: ['read_events'],
        churchId: churchId,
      }).returning();

      const [limitedMember] = await db.insert(members).values({
        userId: '1234567894',
        firstName: 'Limited',
        lastName: 'Member',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: churchId,
        roleId: limitedRole.id,
        isEmailVerified: true,
      }).returning();

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          emailOrUserId: '<EMAIL>',
          password: 'password',
        });

      const limitedToken = loginResponse.body.data.tokens.accessToken;

      await request(app)
        .get(`/api/churches/${churchSlug}/analytics/dashboard`)
        .set('Authorization', `Bearer ${limitedToken}`)
        .expect(403);

      // Clean up
      await db.delete(members).where(eq(members.id, limitedMember.id));
      await db.delete(roles).where(eq(roles.id, limitedRole.id));
    });
  });

  describe('GET /api/churches/:slug/analytics/members', () => {
    it('should return member analytics', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/members`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.analytics).toMatchObject({
        totalMembers: expect.any(Number),
        membersByStatus: expect.any(Array),
        membersByGender: expect.any(Array),
        membersByAgeGroup: expect.any(Array),
        membersByBranch: expect.any(Array),
        memberGrowthTrend: expect.any(Array),
        topEngagedMembers: expect.any(Array),
      });

      expect(response.body.data.analytics.totalMembers).toBeGreaterThan(0);
      expect(response.body.data.analytics.membersByStatus.length).toBeGreaterThan(0);
      expect(response.body.data.analytics.membersByGender.length).toBeGreaterThan(0);
    });

    it('should have correct data structure for member breakdowns', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/members`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      const analytics = response.body.data.analytics;
      
      analytics.membersByStatus.forEach((item: any) => {
        expect(item).toMatchObject({
          status: expect.any(String),
          count: expect.any(Number),
          percentage: expect.any(Number),
        });
      });

      analytics.membersByGender.forEach((item: any) => {
        expect(item).toMatchObject({
          gender: expect.any(String),
          count: expect.any(Number),
          percentage: expect.any(Number),
        });
      });

      analytics.membersByBranch.forEach((item: any) => {
        expect(item).toMatchObject({
          branchName: expect.any(String),
          count: expect.any(Number),
          percentage: expect.any(Number),
        });
      });
    });
  });

  describe('GET /api/churches/:slug/analytics/events', () => {
    it('should return event analytics', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/events`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.analytics).toMatchObject({
        totalEvents: expect.any(Number),
        eventsByType: expect.any(Array),
        eventsByStatus: expect.any(Array),
        attendanceMetrics: expect.any(Object),
        upcomingEvents: expect.any(Array),
        eventTrends: expect.any(Array),
      });

      expect(response.body.data.analytics.totalEvents).toBeGreaterThan(0);
    });

    it('should include upcoming events', async () => {
      // Create a future event
      await db.insert(events).values({
        churchId: churchId,
        branchId: branchId,
        createdBy: memberId,
        title: 'Future Event',
        description: 'An upcoming event',
        type: 'other',
        status: 'published',
        startDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        requiresRsvp: true,
      });

      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/events`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.data.analytics.upcomingEvents.length).toBeGreaterThan(0);
      
      response.body.data.analytics.upcomingEvents.forEach((event: any) => {
        expect(event).toMatchObject({
          id: expect.any(String),
          title: expect.any(String),
          type: expect.any(String),
          startDate: expect.any(String),
          rsvpCount: expect.any(Number),
        });
      });
    });
  });

  describe('GET /api/churches/:slug/analytics/financial', () => {
    it('should return financial analytics', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/financial`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.analytics).toMatchObject({
        totalRevenue: expect.any(Number),
        donationsByType: expect.any(Array),
        donationsByMethod: expect.any(Array),
        donationTrends: expect.any(Array),
        topDonors: expect.any(Array),
        fundraisingGoals: expect.any(Array),
      });

      expect(response.body.data.analytics.totalRevenue).toBeGreaterThan(0);
    });

    it('should respect anonymous donations in top donors', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/financial`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      const topDonors = response.body.data.analytics.topDonors;
      const anonymousDonor = topDonors.find((donor: any) => donor.isAnonymous);
      
      if (anonymousDonor) {
        expect(anonymousDonor.donorName).toBe('Anonymous');
      }
    });

    it('should require higher permissions for financial data', async () => {
      // Create a deacon role (should have access)
      const [deaconRole] = await db.insert(roles).values({
        name: 'Deacon',
        description: 'Deacon access',
        permissions: ['read_events', 'manage_donations'],
        churchId: churchId,
      }).returning();

      const [deaconMember] = await db.insert(members).values({
        userId: '1234567895',
        firstName: 'Deacon',
        lastName: 'Member',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: churchId,
        roleId: deaconRole.id,
        isEmailVerified: true,
      }).returning();

      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          emailOrUserId: '<EMAIL>',
          password: 'password',
        });

      const deaconToken = loginResponse.body.data.tokens.accessToken;

      // Deacon should NOT have access to financial analytics (requires Elder+ role)
      await request(app)
        .get(`/api/churches/${churchSlug}/analytics/financial`)
        .set('Authorization', `Bearer ${deaconToken}`)
        .expect(403);

      // Clean up
      await db.delete(members).where(eq(members.id, deaconMember.id));
      await db.delete(roles).where(eq(roles.id, deaconRole.id));
    });
  });

  describe('GET /api/churches/:slug/analytics/branches', () => {
    it('should return branch analytics', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/branches`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.analytics).toMatchObject({
        totalBranches: expect.any(Number),
        branchComparison: expect.any(Array),
        branchPerformance: expect.any(Array),
      });

      expect(response.body.data.analytics.totalBranches).toBeGreaterThan(0);
    });

    it('should include branch comparison data', async () => {
      const response = await request(app)
        .get(`/api/churches/${churchSlug}/analytics/branches`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      const branchComparison = response.body.data.analytics.branchComparison;
      expect(branchComparison.length).toBeGreaterThan(0);

      branchComparison.forEach((branch: any) => {
        expect(branch).toMatchObject({
          branchId: expect.any(String),
          branchName: expect.any(String),
          memberCount: expect.any(Number),
          eventCount: expect.any(Number),
          totalDonations: expect.any(Number),
          averageAttendance: expect.any(Number),
        });
      });
    });
  });

  describe('Cross-tenant isolation', () => {
    it('should not return analytics from other churches', async () => {
      // Create another church
      const [otherChurch] = await db.insert(churches).values({
        name: 'Other Church',
        slug: 'other-church',
        churchCode: 'OTHER123',
        description: 'Another church',
        email: '<EMAIL>',
        settings: { allowSelfRegistration: true }
      }).returning();

      // Create admin for other church
      const [otherRole] = await db.insert(roles).values({
        name: 'Super Admin',
        description: 'Admin for other church',
        permissions: ['all'],
        churchId: otherChurch.id,
      }).returning();

      await db.insert(members).values({
        userId: '1234567896',
        firstName: 'Other',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: otherChurch.id,
        roleId: otherRole.id,
        isEmailVerified: true,
      }).returning();

      // Add some data to other church
      await db.insert(members).values({
        firstName: 'Other',
        lastName: 'Member',
        email: '<EMAIL>',
        password: await hashPassword('password'),
        churchId: otherChurch.id,
      });

      // Test that our admin cannot access other church analytics
      await request(app)
        .get(`/api/churches/other-church/analytics/dashboard`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(403);

      // Clean up
      await db.delete(members).where(eq(members.churchId, otherChurch.id));
      await db.delete(roles).where(eq(roles.churchId, otherChurch.id));
      await db.delete(churches).where(eq(churches.id, otherChurch.id));
    });
  });
});