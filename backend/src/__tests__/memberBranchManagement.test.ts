import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';

vi.mock('../db', () => ({
  db: {
    query: {
      members: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
      branches: {
        findFirst: vi.fn(),
      },
    },
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn(() => []),
        })),
      })),
    })),
    $count: vi.fn(),
  },
}));

const { 
  getMembers, 
  getMember, 
  updateMember, 
  assignBranch, 
  removeBranch 
} = await import('../controllers/memberController');
const { globalErrorHandler } = await import('../utils/errorHandler');

const app = express();
app.use(express.json());

const mockAuthenticateUser = (req: any, res: any, next: any) => {
  req.user = {
    id: 'user-123',
    churchId: 'church-123',
    email: '<EMAIL>',
    permissions: ['view_members', 'manage_members'],
  };
  next();
};

app.use(mockAuthenticateUser);
app.get('/members', getMembers);
app.get('/members/:id', getMember);
app.put('/members/:id', updateMember);
app.post('/members/:id/branch', assignBranch);
app.delete('/members/:id/branch', removeBranch);
app.use(globalErrorHandler);

describe('Member-Branch Management', () => {
  const mockMember = {
    id: 'member-123',
    churchId: 'church-123',
    branchId: 'branch-123',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    status: 'active',
    role: {
      id: 'role-123',
      name: 'Member',
    },
    branch: {
      id: 'branch-123',
      name: 'Main Campus',
      slug: 'main-campus',
      address: '123 Main St',
    },
    createdAt: new Date('2025-01-01'),
    updatedAt: new Date('2025-01-01'),
  };

  const mockBranch = {
    id: 'branch-456',
    churchId: 'church-123',
    name: 'Downtown Campus',
    slug: 'downtown-campus',
    isActive: true,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /members', () => {
    it('should return members with branch information and support branch filtering', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.$count).mockResolvedValue(1);
      vi.mocked(db.query.members.findMany).mockResolvedValue([mockMember]);

      const response = await request(app)
        .get('/members')
        .query({ branchId: 'branch-123', page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.members).toHaveLength(1);
      expect(response.body.data.members[0]).toEqual(expect.objectContaining({
        id: 'member-123',
        firstName: 'John',
        lastName: 'Doe',
        branch: expect.objectContaining({
          id: 'branch-123',
          name: 'Main Campus',
        }),
      }));
      expect(response.body.data.pagination).toEqual(expect.objectContaining({
        page: 1,
        limit: 10,
        totalCount: 1,
      }));
    });

    it('should support search filtering across member fields', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.$count).mockResolvedValue(1);
      vi.mocked(db.query.members.findMany).mockResolvedValue([mockMember]);

      const response = await request(app)
        .get('/members')
        .query({ search: 'John', page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.data.members).toHaveLength(1);
    });
  });

  describe('GET /members/:id', () => {
    it('should return member with detailed branch information', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);

      const response = await request(app)
        .get('/members/member-123');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.member).toEqual(expect.objectContaining({
        id: 'member-123',
        firstName: 'John',
        branch: expect.objectContaining({
          name: 'Main Campus',
          address: '123 Main St',
        }),
      }));
    });

    it('should return 404 if member not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .get('/members/nonexistent');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Member not found');
    });
  });

  describe('PUT /members/:id', () => {
    it('should update member with branch assignment validation', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(mockBranch);
      
      const mockUpdate = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{
              ...mockMember,
              branchId: 'branch-456',
              firstName: 'Jane',
            }])
          })
        })
      });
      vi.mocked(db.update).mockImplementation(mockUpdate);

      const response = await request(app)
        .put('/members/member-123')
        .send({
          firstName: 'Jane',
          branchId: 'branch-456',
        });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Member updated successfully');
      expect(response.body.data.member).toEqual(expect.objectContaining({
        firstName: 'Jane',
        branchId: 'branch-456',
      }));
    });

    it('should return 400 if branch does not belong to church', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .put('/members/member-123')
        .send({
          branchId: 'invalid-branch',
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Branch not found or does not belong to your church');
    });
  });

  describe('POST /members/:id/branch', () => {
    it('should assign branch to member successfully', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(mockBranch);
      
      const mockUpdate = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{
              id: 'member-123',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              branchId: 'branch-456',
            }])
          })
        })
      });
      vi.mocked(db.update).mockImplementation(mockUpdate);

      const response = await request(app)
        .post('/members/member-123/branch')
        .send({ branchId: 'branch-456' });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Branch assigned successfully');
      expect(response.body.data.member).toEqual(expect.objectContaining({
        branchId: 'branch-456',
      }));
    });

    it('should return 400 if branchId is missing', async () => {
      const response = await request(app)
        .post('/members/member-123/branch')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Branch ID is required');
    });

    it('should return 404 if member not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .post('/members/nonexistent/branch')
        .send({ branchId: 'branch-456' });

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Member not found');
    });

    it('should return 404 if branch not found or does not belong to church', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);
      vi.mocked(db.query.branches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .post('/members/member-123/branch')
        .send({ branchId: 'invalid-branch' });

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Branch not found or does not belong to your church');
    });
  });

  describe('DELETE /members/:id/branch', () => {
    it('should remove branch assignment successfully', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockMember);
      
      const mockUpdate = vi.fn().mockReturnValue({
        set: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            returning: vi.fn().mockResolvedValue([{
              id: 'member-123',
              firstName: 'John',
              lastName: 'Doe',
              email: '<EMAIL>',
              branchId: null,
            }])
          })
        })
      });
      vi.mocked(db.update).mockImplementation(mockUpdate);

      const response = await request(app)
        .delete('/members/member-123/branch');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Branch assignment removed successfully');
      expect(response.body.data.member).toEqual(expect.objectContaining({
        branchId: null,
      }));
    });

    it('should return 404 if member not found', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .delete('/members/nonexistent/branch');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Member not found');
    });
  });
});