import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import request from 'supertest';
import express from 'express';

const mockDb = {
  query: {
    churches: {
      findFirst: vi.fn(),
    },
    members: {
      findFirst: vi.fn(),
    },
    onboardingSessions: {
      findFirst: vi.fn(),
      findMany: vi.fn(),
    },
    roles: {
      findFirst: vi.fn(),
    },
    branches: {
      findFirst: vi.fn(),
    },
  },
  insert: vi.fn(() => ({
    values: vi.fn(() => ({
      returning: vi.fn(() => []),
    })),
  })),
  update: vi.fn(() => ({
    set: vi.fn(() => ({
      where: vi.fn(() => ({
        returning: vi.fn(() => []),
      })),
    })),
  })),
  delete: vi.fn(() => ({
    where: vi.fn(),
  })),
};

vi.mock('../db', () => ({
  db: mockDb,
}));

vi.mock('../utils/seedData', () => ({
  seedRoles: vi.fn(),
}));

vi.mock('../utils/auth', () => ({
  hashPassword: vi.fn(),
  generateEmailVerificationToken: vi.fn(),
  generateAccessToken: vi.fn(),
  generateRefreshToken: vi.fn(),
  getTokenExpiration: vi.fn(),
  isTokenExpired: vi.fn(),
}));

const { 
  checkAvailability,
  initiateOnboarding,
  completeOnboarding,
  verifyOnboarding,
  resendVerification,
  setupInitialData,
  getOnboardingProgress,
  cleanupExpiredSessions
} = await import('../controllers/onboardingController');

const { globalErrorHandler } = await import('../utils/errorHandler');

describe('Onboarding Controller', () => {
  let app: express.Application;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    
    // Setup routes
    app.post('/check-availability', checkAvailability);
    app.post('/initiate', initiateOnboarding);
    app.post('/complete', completeOnboarding);
    app.post('/verify', verifyOnboarding);
    app.post('/resend-verification', resendVerification);
    app.post('/setup-initial-data', setupInitialData);
    app.get('/progress/:token', getOnboardingProgress);
    app.post('/cleanup', cleanupExpiredSessions);
    
    app.use(globalErrorHandler);
    
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /check-availability', () => {
    it('should return availability status for church slug and admin email', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue(null);
      mockDb.query.members.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .post('/check-availability')
        .send({
          churchSlug: 'test-church',
          adminEmail: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.allAvailable).toBe(true);
      expect(response.body.data.canProceed).toBe(true);
      expect(response.body.data.availability.churchSlug.available).toBe(true);
      expect(response.body.data.availability.adminEmail.available).toBe(true);
    });

    it('should return unavailable when church slug exists', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue({ id: '1', slug: 'test-church' });
      mockDb.query.members.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .post('/check-availability')
        .send({
          churchSlug: 'test-church',
          adminEmail: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.data.allAvailable).toBe(false);
      expect(response.body.data.availability.churchSlug.available).toBe(false);
      expect(response.body.data.availability.adminEmail.available).toBe(true);
    });

    it('should return unavailable when admin email exists', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue(null);
      mockDb.query.members.findFirst.mockResolvedValue({ id: '1', email: '<EMAIL>' });

      const response = await request(app)
        .post('/check-availability')
        .send({
          churchSlug: 'test-church',
          adminEmail: '<EMAIL>'
        });

      expect(response.status).toBe(200);
      expect(response.body.data.allAvailable).toBe(false);
      expect(response.body.data.availability.churchSlug.available).toBe(true);
      expect(response.body.data.availability.adminEmail.available).toBe(false);
    });

    it('should validate input data', async () => {
      const response = await request(app)
        .post('/check-availability')
        .send({
          churchSlug: 'a', // Too short
          adminEmail: 'invalid-email'
        });

      // The validation error should be handled appropriately
      // In a real app with proper middleware setup, this would be 400
      expect(response.status).toBeGreaterThanOrEqual(400);
    });
  });

  describe('POST /initiate', () => {
    it('should initiate onboarding successfully', async () => {
      const { hashPassword, generateEmailVerificationToken, getTokenExpiration } = await import('../utils/auth');
      
      mockDb.query.churches.findFirst.mockResolvedValue(null);
      mockDb.query.members.findFirst.mockResolvedValue(null);
      hashPassword.mockResolvedValue('hashedPassword');
      generateEmailVerificationToken.mockReturnValue('onboarding-token');
      generateEmailVerificationToken.mockReturnValueOnce('onboarding-token').mockReturnValueOnce('verification-token');
      getTokenExpiration.mockReturnValue(new Date(Date.now() + 24 * 60 * 60 * 1000));
      
      mockDb.insert.mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{
            id: '1',
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
          }])
        })
      });

      const response = await request(app)
        .post('/initiate')
        .send({
          church: {
            name: 'Test Church',
            slug: 'test-church',
            description: 'A test church',
            email: '<EMAIL>'
          },
          admin: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            password: 'password123'
          }
        });

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data.onboardingToken).toBe('onboarding-token');
      expect(response.body.data.verificationToken).toBe('verification-token');
      expect(response.body.data.progress.step).toBe('completion');
    });

    it('should fail when church slug already exists', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue({ id: '1', slug: 'test-church' });
      mockDb.query.members.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .post('/initiate')
        .send({
          church: {
            name: 'Test Church',
            slug: 'test-church'
          },
          admin: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            password: 'password123'
          }
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Church with this slug already exists');
    });

    it('should fail when admin email already exists', async () => {
      mockDb.query.churches.findFirst.mockResolvedValue(null);
      mockDb.query.members.findFirst.mockResolvedValue({ id: '1', email: '<EMAIL>' });

      const response = await request(app)
        .post('/initiate')
        .send({
          church: {
            name: 'Test Church',
            slug: 'test-church'
          },
          admin: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            password: 'password123'
          }
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Email already registered');
    });
  });

  describe('POST /complete', () => {
    it('should complete onboarding successfully', async () => {
      const { isTokenExpired, generateAccessToken, generateRefreshToken, getTokenExpiration } = await import('../utils/auth');
      const { seedRoles } = await import('../utils/seedData');
      
      const mockOnboardingSession = {
        id: '1',
        churchData: {
          name: 'Test Church',
          slug: 'test-church',
          email: '<EMAIL>'
        },
        adminData: {
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>'
        },
        settingsData: {},
        hashedPassword: 'hashedPassword',
        verificationToken: 'verification-token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(mockOnboardingSession);
      isTokenExpired.mockReturnValue(false);
      
      // Mock church creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{
            id: 'church-1',
            name: 'Test Church',
            slug: 'test-church',
            email: '<EMAIL>',
            createdAt: new Date()
          }])
        })
      });

      // Mock role and branch queries
      mockDb.query.roles.findFirst.mockResolvedValue({ id: 'super-admin-role' });
      
      // Mock branch creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{
            id: 'main-branch',
            name: 'Test Church - Main Campus',
            slug: 'test-church-main',
            isMainBranch: true
          }])
        })
      });

      // Mock admin creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{
            id: 'admin-1',
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            churchId: 'church-1',
            roleId: 'super-admin-role',
            isEmailVerified: false
          }])
        })
      });

      // Mock token generation
      generateAccessToken.mockReturnValue('access-token');
      generateRefreshToken.mockReturnValue('refresh-token');
      getTokenExpiration.mockReturnValue(new Date(Date.now() + 7 * 24 * 60 * 60 * 1000));

      // Mock refresh token creation
      mockDb.insert.mockReturnValueOnce({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([{}])
        })
      });

      const response = await request(app)
        .post('/complete')
        .send({
          onboardingToken: 'valid-token',
          confirmPassword: 'password123',
          agreeToTerms: true
        });

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data.church.name).toBe('Test Church');
      expect(response.body.data.admin.firstName).toBe('John');
      expect(response.body.data.mainBranch.isMainBranch).toBe(true);
      expect(response.body.data.tokens.accessToken).toBe('access-token');
      expect(response.body.data.progress.step).toBe('verification');
      expect(seedRoles).toHaveBeenCalledWith('church-1');
    });

    it('should fail with invalid onboarding token', async () => {
      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .post('/complete')
        .send({
          onboardingToken: 'invalid-token',
          confirmPassword: 'password123',
          agreeToTerms: true
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid or expired onboarding token');
    });

    it('should fail with expired onboarding token', async () => {
      const { isTokenExpired } = await import('../utils/auth');
      
      const mockOnboardingSession = {
        id: '1',
        expiresAt: new Date(Date.now() - 1000) // Expired
      };

      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(mockOnboardingSession);
      isTokenExpired.mockReturnValue(true);

      const response = await request(app)
        .post('/complete')
        .send({
          onboardingToken: 'expired-token',
          confirmPassword: 'password123',
          agreeToTerms: true
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid or expired onboarding token');
    });
  });

  describe('POST /verify', () => {
    it('should verify onboarding email successfully', async () => {
      const { isTokenExpired } = await import('../utils/auth');
      
      const mockOnboardingSession = {
        id: '1',
        adminData: { email: '<EMAIL>' },
        isCompleted: true,
        isVerified: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000)
      };

      const mockMember = {
        id: 'member-1',
        email: '<EMAIL>'
      };

      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(mockOnboardingSession);
      mockDb.query.members.findFirst.mockResolvedValue(mockMember);
      isTokenExpired.mockReturnValue(false);

      const response = await request(app)
        .post('/verify')
        .send({
          verificationToken: 'valid-verification-token'
        });

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.verified).toBe(true);
      expect(response.body.data.progress.step).toBe('setup');
    });

    it('should fail with invalid verification token', async () => {
      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .post('/verify')
        .send({
          verificationToken: 'invalid-token'
        });

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Invalid or expired verification token');
    });
  });

  describe('GET /progress/:token', () => {
    it('should return onboarding progress', async () => {
      const { isTokenExpired } = await import('../utils/auth');
      
      const mockOnboardingSession = {
        id: '1',
        isCompleted: true,
        isVerified: false,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
        churchData: { name: 'Test Church' },
        adminData: { email: '<EMAIL>' }
      };

      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(mockOnboardingSession);
      isTokenExpired.mockReturnValue(false);

      const response = await request(app)
        .get('/progress/test-token');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.progress.step).toBe('verification');
      expect(response.body.data.churchName).toBe('Test Church');
      expect(response.body.data.adminEmail).toBe('<EMAIL>');
    });

    it('should fail with non-existent token', async () => {
      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(null);

      const response = await request(app)
        .get('/progress/non-existent-token');

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Onboarding session not found');
    });

    it('should fail with expired session', async () => {
      const { isTokenExpired } = await import('../utils/auth');
      
      const mockOnboardingSession = {
        id: '1',
        expiresAt: new Date(Date.now() - 1000)
      };

      mockDb.query.onboardingSessions.findFirst.mockResolvedValue(mockOnboardingSession);
      isTokenExpired.mockReturnValue(true);

      const response = await request(app)
        .get('/progress/expired-token');

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Onboarding session has expired');
    });
  });

  describe('POST /cleanup', () => {
    it('should clean up expired sessions', async () => {
      const { isTokenExpired } = await import('../utils/auth');
      
      const mockExpiredSessions = [
        { id: '1', expiresAt: new Date(Date.now() - 1000) },
        { id: '2', expiresAt: new Date(Date.now() + 1000) }
      ];

      mockDb.query.onboardingSessions.findMany.mockResolvedValue(mockExpiredSessions);
      isTokenExpired.mockReturnValueOnce(true).mockReturnValueOnce(false);

      const response = await request(app)
        .post('/cleanup');

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.data.cleanedUp).toBe(1);
    });
  });
});