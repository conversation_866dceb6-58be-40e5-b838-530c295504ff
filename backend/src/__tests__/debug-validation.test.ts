import { describe, it, expect } from 'vitest';
import { createMemberSchema } from '../types/member';

describe('Debug Validation', () => {
  it('should debug validation results', () => {
    const testData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
    };

    const result = createMemberSchema.safeParse(testData);
    console.log('Validation result:', result);
    
    if (!result.success) {
      console.log('Validation errors:', result.error.issues);
    }
    
    expect(result.success).toBe(true);
  });

  it('should debug invalid data', () => {
    const testData = {
      firstName: '',
      lastName: 'Doe',
      email: '<EMAIL>',
    };

    const result = createMemberSchema.safeParse(testData);
    console.log('Invalid validation result:', result);
    
    if (!result.success) {
      console.log('Validation errors:', result.error.issues);
    }
  });

  it('should debug phone validation', () => {
    const testData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      phone: 'invalid-phone',
    };

    const result = createMemberSchema.safeParse(testData);
    console.log('Phone validation result:', result);
    
    if (!result.success) {
      console.log('Phone validation errors:', result.error.issues);
    }
  });

  it('should debug gender validation', () => {
    const testData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      gender: 'invalid-gender',
    };

    const result = createMemberSchema.safeParse(testData);
    console.log('Gender validation result:', result);
    
    if (!result.success) {
      console.log('Gender validation errors:', result.error.issues);
    }
  });

  it('should debug specific invalid phone numbers', () => {
    const invalidPhones = [
      'invalid-phone',
      '123',
      'abc123def',
      '++1234567890',
      '0123456789012345678', // Too long
    ];

    invalidPhones.forEach(phone => {
      const testData = {
        firstName: 'John',
        lastName: 'Doe',
        email: '<EMAIL>',
        phone,
      };

      const result = createMemberSchema.safeParse(testData);
      console.log(`Phone "${phone}" validation result:`, result.success);
      
      if (!result.success) {
        console.log(`Phone "${phone}" validation errors:`, result.error.issues);
      }
    });
  });
});