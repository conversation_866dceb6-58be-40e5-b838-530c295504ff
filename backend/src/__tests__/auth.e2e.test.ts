/**
 * End-to-End Authentication Flow Tests
 * 
 * Comprehensive tests covering complete authentication workflows:
 * - Registration to login flow
 * - Token refresh cycles
 * - Multi-tenant authentication
 * - Session management
 * - Cross-tab synchronization simulation
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import app from '../app';
import { db } from '../db';
import { churches, members, roles, refreshTokens } from '../db/schema';
import { eq } from 'drizzle-orm';
import jwt from 'jsonwebtoken';

describe('End-to-End Authentication Flows', () => {
  let testChurch: any;
  let testAdmin: any;
  let adminTokens: any;

  beforeEach(async () => {
    // Clean up database
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);

    // Create test church with admin
    const churchData = {
      church: {
        name: 'E2E Test Church',
        slug: 'e2e-test-church',
        email: '<EMAIL>',
        churchCode: 'E2E123',
      },
      admin: {
        firstName: 'E2E',
        lastName: 'Admin',
        email: '<EMAIL>',
        password: 'SecurePass123',
        phone: '******-123-4567',
        gender: 'male',
      },
    };

    const response = await request(app)
      .post('/api/churches/register')
      .send(churchData);

    testChurch = response.body.data.church;
    testAdmin = response.body.data.admin;
    adminTokens = response.body.data.tokens;
  });

  afterEach(async () => {
    await db.delete(refreshTokens);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  describe('Complete Registration to Login Flow', () => {
    it('should complete full user lifecycle: register -> verify -> login -> access protected resource', async () => {
      // Step 1: Register new member
      const memberData = {
        firstName: 'Test',
        lastName: 'Member',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
        phone: '******-123-4568',
        gender: 'female',
      };

      const registerResponse = await request(app)
        .post('/api/auth/register')
        .send(memberData)
        .expect(201);

      expect(registerResponse.body.data.user.email).toBe(memberData.email);
      expect(registerResponse.body.data.tokens.accessToken).toBeDefined();
      expect(registerResponse.body.data.tokens.refreshToken).toBeDefined();

      const memberTokens = registerResponse.body.data.tokens;

      // Step 2: Access protected resource with new tokens
      const profileResponse = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      expect(profileResponse.body.data.user.email).toBe(memberData.email);
      expect(profileResponse.body.data.user.church.slug).toBe(testChurch.slug);

      // Step 3: Logout and clear tokens
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      // Step 4: Verify tokens are invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(401);

      // Step 5: Login again with credentials
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: memberData.email,
          password: memberData.password,
          churchCode: 'E2E123',
        })
        .expect(200);

      expect(loginResponse.body.data.user.email).toBe(memberData.email);
      expect(loginResponse.body.data.tokens.accessToken).toBeDefined();

      // Step 6: Access protected resource with new login tokens
      const newProfileResponse = await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${loginResponse.body.data.tokens.accessToken}`)
        .expect(200);

      expect(newProfileResponse.body.data.user.email).toBe(memberData.email);
    });

    it('should handle church-specific login flow', async () => {
      // Register member
      const memberData = {
        firstName: 'Church',
        lastName: 'Member',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
      };

      await request(app)
        .post('/api/auth/register')
        .send(memberData)
        .expect(201);

      // Login through church-specific endpoint
      const loginResponse = await request(app)
        .post(`/api/churches/${testChurch.slug}/auth/login`)
        .send({
          email: memberData.email,
          password: memberData.password,
        })
        .expect(200);

      expect(loginResponse.body.data.user.church.slug).toBe(testChurch.slug);

      // Verify access to church-scoped resources
      const churchMembersResponse = await request(app)
        .get(`/api/churches/${testChurch.slug}/members`)
        .set('Authorization', `Bearer ${loginResponse.body.data.tokens.accessToken}`)
        .expect(200);

      expect(churchMembersResponse.body.data.members).toBeDefined();
    });
  });

  describe('Token Refresh Lifecycle', () => {
    let memberTokens: any;

    beforeEach(async () => {
      const memberData = {
        firstName: 'Refresh',
        lastName: 'Test',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(memberData);

      memberTokens = response.body.data.tokens;
    });

    it('should complete full token refresh cycle', async () => {
      // Step 1: Verify initial tokens work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      // Step 2: Simulate token expiration by creating expired token
      const expiredToken = jwt.sign(
        { userId: 'test-user', churchId: testChurch.id },
        process.env.JWT_SECRET || 'test-secret',
        { expiresIn: '-1h' }
      );

      // Step 3: Use expired token (should fail)
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${expiredToken}`)
        .expect(401);

      // Step 4: Refresh tokens
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: memberTokens.refreshToken })
        .expect(200);

      expect(refreshResponse.body.data.tokens.accessToken).toBeDefined();
      expect(refreshResponse.body.data.tokens.refreshToken).toBeDefined();
      expect(refreshResponse.body.data.tokens.accessToken).not.toBe(memberTokens.accessToken);

      const newTokens = refreshResponse.body.data.tokens;

      // Step 5: Verify new tokens work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${newTokens.accessToken}`)
        .expect(200);

      // Step 6: Verify old access token is invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(401);
    });

    it('should handle refresh token expiration', async () => {
      // Create expired refresh token
      const expiredRefreshToken = jwt.sign(
        { userId: 'test-user', type: 'refresh' },
        process.env.JWT_REFRESH_SECRET || 'test-refresh-secret',
        { expiresIn: '-1h' }
      );

      // Attempt refresh with expired token
      const refreshResponse = await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: expiredRefreshToken })
        .expect(401);

      expect(refreshResponse.body.message).toContain('expired');
    });

    it('should handle invalid refresh token', async () => {
      const invalidRefreshToken = 'invalid.token.here';

      await request(app)
        .post('/api/auth/refresh')
        .send({ refreshToken: invalidRefreshToken })
        .expect(401);
    });
  });

  describe('Multi-Tenant Authentication Isolation', () => {
    let church2: any;
    let church2Admin: any;
    let church2Tokens: any;

    beforeEach(async () => {
      // Create second church
      const church2Data = {
        church: {
          name: 'Second E2E Church',
          slug: 'second-e2e-church',
          email: '<EMAIL>',
          churchCode: 'SEC123',
        },
        admin: {
          firstName: 'Second',
          lastName: 'Admin',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response = await request(app)
        .post('/api/churches/register')
        .send(church2Data);

      church2 = response.body.data.church;
      church2Admin = response.body.data.admin;
      church2Tokens = response.body.data.tokens;
    });

    it('should enforce tenant isolation in authentication', async () => {
      // Create member in church 1
      const member1Data = {
        firstName: 'Member',
        lastName: 'One',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
      };

      await request(app)
        .post('/api/auth/register')
        .send(member1Data);

      // Create member in church 2
      const member2Data = {
        firstName: 'Member',
        lastName: 'Two',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'SEC123',
      };

      await request(app)
        .post('/api/auth/register')
        .send(member2Data);

      // Try to login member1 with church2 code (should fail)
      await request(app)
        .post('/api/auth/login')
        .send({
          email: member1Data.email,
          password: member1Data.password,
          churchCode: 'SEC123',
        })
        .expect(401);

      // Try to login member2 with church1 code (should fail)
      await request(app)
        .post('/api/auth/login')
        .send({
          email: member2Data.email,
          password: member2Data.password,
          churchCode: 'E2E123',
        })
        .expect(401);

      // Verify correct logins work
      const login1Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: member1Data.email,
          password: member1Data.password,
          churchCode: 'E2E123',
        })
        .expect(200);

      const login2Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: member2Data.email,
          password: member2Data.password,
          churchCode: 'SEC123',
        })
        .expect(200);

      expect(login1Response.body.data.user.church.slug).toBe(testChurch.slug);
      expect(login2Response.body.data.user.church.slug).toBe(church2.slug);
    });

    it('should prevent cross-tenant resource access', async () => {
      // Admin from church1 tries to access church2 resources
      await request(app)
        .get(`/api/churches/${church2.slug}/members`)
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(403);

      // Admin from church2 tries to access church1 resources
      await request(app)
        .get(`/api/churches/${testChurch.slug}/members`)
        .set('Authorization', `Bearer ${church2Tokens.accessToken}`)
        .expect(403);

      // Verify correct access works
      await request(app)
        .get(`/api/churches/${testChurch.slug}/members`)
        .set('Authorization', `Bearer ${adminTokens.accessToken}`)
        .expect(200);

      await request(app)
        .get(`/api/churches/${church2.slug}/members`)
        .set('Authorization', `Bearer ${church2Tokens.accessToken}`)
        .expect(200);
    });
  });

  describe('Session Management', () => {
    let memberTokens: any;
    let memberId: string;

    beforeEach(async () => {
      const memberData = {
        firstName: 'Session',
        lastName: 'Test',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
      };

      const response = await request(app)
        .post('/api/auth/register')
        .send(memberData);

      memberTokens = response.body.data.tokens;
      memberId = response.body.data.user.id;
    });

    it('should handle concurrent sessions', async () => {
      // Login from another "device" (get second set of tokens)
      const secondLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          churchCode: 'E2E123',
        })
        .expect(200);

      const secondTokens = secondLoginResponse.body.data.tokens;

      // Both sessions should work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${secondTokens.accessToken}`)
        .expect(200);

      // Logout from first session
      await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      // First session should be invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(401);

      // Second session should still work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${secondTokens.accessToken}`)
        .expect(200);
    });

    it('should invalidate all sessions on password change', async () => {
      // Create multiple sessions
      const session2Response = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'SecurePass123',
          churchCode: 'E2E123',
        });

      const session2Tokens = session2Response.body.data.tokens;

      // Verify both sessions work
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(200);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(200);

      // Change password
      await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .send({
          currentPassword: 'SecurePass123',
          newPassword: 'NewSecurePass123',
        })
        .expect(200);

      // All sessions should be invalidated
      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${memberTokens.accessToken}`)
        .expect(401);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${session2Tokens.accessToken}`)
        .expect(401);

      // Login with new password should work
      const newLoginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'NewSecurePass123',
          churchCode: 'E2E123',
        })
        .expect(200);

      await request(app)
        .get('/api/auth/profile')
        .set('Authorization', `Bearer ${newLoginResponse.body.data.tokens.accessToken}`)
        .expect(200);
    });
  });

  describe('Error Recovery Flows', () => {
    it('should handle network interruption during authentication', async () => {
      // Simulate network error during registration
      const originalPost = request(app).post;
      let callCount = 0;

      // Mock network failure on first attempt
      vi.spyOn(request(app), 'post').mockImplementation((url) => {
        callCount++;
        if (callCount === 1 && url === '/api/auth/register') {
          throw new Error('Network error');
        }
        return originalPost.call(request(app), url);
      });

      const memberData = {
        firstName: 'Network',
        lastName: 'Test',
        email: '<EMAIL>',
        password: 'SecurePass123',
        churchCode: 'E2E123',
      };

      // First attempt should fail
      try {
        await request(app)
          .post('/api/auth/register')
          .send(memberData);
        expect.fail('Should have thrown network error');
      } catch (error) {
        expect(error.message).toBe('Network error');
      }

      // Restore original implementation
      vi.restoreAllMocks();

      // Second attempt should succeed
      const response = await request(app)
        .post('/api/auth/register')
        .send(memberData)
        .expect(201);

      expect(response.body.data.user.email).toBe(memberData.email);
    });

    it('should handle malformed token gracefully', async () => {
      const malformedTokens = [
        'malformed.token',
        'Bearer malformed.token',
        '',
        'null',
        'undefined',
      ];

      for (const token of malformedTokens) {
        await request(app)
          .get('/api/auth/profile')
          .set('Authorization', `Bearer ${token}`)
          .expect(401);
      }
    });
  });

  describe('Rate Limiting and Security', () => {
    it('should enforce rate limiting on login attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'WrongPassword',
        churchCode: 'E2E123',
      };

      // Make multiple failed login attempts
      const promises = Array(10).fill(null).map(() =>
        request(app)
          .post('/api/auth/login')
          .send(loginData)
      );

      const responses = await Promise.all(promises);

      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should log security events', async () => {
      // Mock console.log to capture security logs
      const logSpy = vi.spyOn(console, 'log');

      // Failed login attempt
      await request(app)
        .post('/api/auth/login')
        .send({
          email: '<EMAIL>',
          password: 'WrongPassword',
          churchCode: 'E2E123',
        })
        .expect(401);

      // Check if security event was logged
      expect(logSpy).toHaveBeenCalledWith(
        expect.stringContaining('SECURITY_EVENT'),
        expect.objectContaining({
          event: 'FAILED_LOGIN',
          email: '<EMAIL>',
        })
      );

      logSpy.mockRestore();
    });
  });
});