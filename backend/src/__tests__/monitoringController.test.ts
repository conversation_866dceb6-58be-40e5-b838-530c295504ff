import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import monitoringRoutes from '../routes/monitoringRoutes';
import { AuthMetricsCollector } from '../utils/authMetrics';
import { SecurityAuditLogger } from '../utils/securityAudit';

// Mock the middleware
vi.mock('../middleware/auth', () => ({
  authenticate: vi.fn((req, res, next) => {
    req.user = {
      id: 'user123',
      email: '<EMAIL>',
      churchId: 'church123',
      permissions: ['admin'],
    };
    next();
  }),
  authorize: vi.fn(() => (req, res, next) => next()),
}));

// Mock the utilities
vi.mock('../utils/authMetrics');
vi.mock('../utils/securityAudit');
vi.mock('../utils/performanceMonitor');

const app = express();
app.use(express.json());
app.use('/api/monitoring', monitoringRoutes);

describe('Monitoring Controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('GET /api/monitoring/health', () => {
    it('should return authentication health status', async () => {
      const mockHealthStatus = {
        status: 'healthy',
        metrics: {
          successRate: 98.5,
          averageResponseTime: 150,
          errorRate: 1.5,
          activeUsers: 25,
        },
        alerts: [],
      };

      vi.mocked(AuthMetricsCollector.getHealthStatus).mockReturnValue(mockHealthStatus);

      const response = await request(app)
        .get('/api/monitoring/health')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockHealthStatus);
      expect(AuthMetricsCollector.getHealthStatus).toHaveBeenCalled();
    });
  });

  describe('GET /api/monitoring/security/dashboard', () => {
    it('should return security dashboard data', async () => {
      const mockDashboard = {
        summary: {
          totalEvents: 500,
          criticalAlerts: 2,
          blockedIPs: 5,
          suspiciousIPs: 3,
          failureRate: 5.2,
        },
        recentEvents: [
          {
            eventType: 'LOGIN_FAILED',
            timestamp: new Date().toISOString(),
            success: false,
            severity: 'medium',
            ipAddress: '*************',
          },
        ],
        activeAlerts: [
          {
            id: 'alert123',
            eventType: 'SUSPICIOUS_ACTIVITY',
            severity: 'high',
            message: 'Multiple failed attempts detected',
            timestamp: new Date().toISOString(),
            acknowledged: false,
          },
        ],
        topThreats: [
          {
            ip: '*************',
            attempts: 8,
            lastSeen: new Date().toISOString(),
          },
        ],
      };

      vi.mocked(SecurityAuditLogger.getSecurityDashboard).mockReturnValue(mockDashboard);

      const response = await request(app)
        .get('/api/monitoring/security/dashboard')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data).toEqual(mockDashboard);
      expect(SecurityAuditLogger.getSecurityDashboard).toHaveBeenCalled();
    });
  });

  describe('GET /api/monitoring/security/alerts', () => {
    it('should return active security alerts', async () => {
      const mockAlerts = [
        {
          id: 'alert123',
          eventType: 'SUSPICIOUS_ACTIVITY',
          severity: 'high',
          message: 'Multiple failed attempts detected',
          timestamp: new Date().toISOString(),
          acknowledged: false,
        },
        {
          id: 'alert456',
          eventType: 'RATE_LIMIT_EXCEEDED',
          severity: 'medium',
          message: 'Rate limit exceeded from IP',
          timestamp: new Date().toISOString(),
          acknowledged: false,
        },
      ];

      vi.mocked(SecurityAuditLogger.getActiveAlerts).mockReturnValue(mockAlerts);

      const response = await request(app)
        .get('/api/monitoring/security/alerts')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.alerts).toEqual(mockAlerts);
      expect(response.body.data.count).toBe(2);
    });
  });

  describe('POST /api/monitoring/security/alerts/:alertId/acknowledge', () => {
    it('should acknowledge an alert successfully', async () => {
      vi.mocked(SecurityAuditLogger.acknowledgeAlert).mockReturnValue(true);

      const response = await request(app)
        .post('/api/monitoring/security/alerts/alert123/acknowledge')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Alert acknowledged successfully');
      expect(SecurityAuditLogger.acknowledgeAlert).toHaveBeenCalledWith('alert123');
    });

    it('should return 404 for non-existent alert', async () => {
      vi.mocked(SecurityAuditLogger.acknowledgeAlert).mockReturnValue(false);

      const response = await request(app)
        .post('/api/monitoring/security/alerts/nonexistent/acknowledge')
        .expect(404);

      expect(response.body.status).toBe('error');
      expect(response.body.message).toBe('Alert not found');
    });
  });

  describe('GET /api/monitoring/status', () => {
    it('should return system status for health checks', async () => {
      const mockAuthHealth = {
        status: 'healthy',
        metrics: { successRate: 98.5, averageResponseTime: 150, errorRate: 1.5, activeUsers: 25 },
        alerts: [],
      };

      const mockSecuritySummary = {
        totalEvents: 500,
        criticalAlerts: 0,
        blockedIPs: 2,
        suspiciousIPs: 1,
        failureRate: 2.5,
      };

      vi.mocked(AuthMetricsCollector.getHealthStatus).mockReturnValue(mockAuthHealth);
      vi.mocked(SecurityAuditLogger.getSecurityDashboard).mockReturnValue({
        summary: mockSecuritySummary,
        recentEvents: [],
        activeAlerts: [],
        topThreats: [],
      });

      const response = await request(app)
        .get('/api/monitoring/status')
        .expect(200);

      expect(response.body.status).toBe('success');
      expect(response.body.data.overallStatus).toBe('healthy');
      expect(response.body.data.components.authentication.status).toBe('healthy');
      expect(response.body.data.components.security.status).toBe('healthy');
      expect(response.body.data.uptime).toBeGreaterThan(0);
    });

    it('should return critical status when there are critical alerts', async () => {
      const mockAuthHealth = {
        status: 'healthy',
        metrics: { successRate: 98.5, averageResponseTime: 150, errorRate: 1.5, activeUsers: 25 },
        alerts: [],
      };

      const mockSecuritySummary = {
        totalEvents: 500,
        criticalAlerts: 3, // Critical alerts present
        blockedIPs: 5,
        suspiciousIPs: 2,
        failureRate: 8.5,
      };

      vi.mocked(AuthMetricsCollector.getHealthStatus).mockReturnValue(mockAuthHealth);
      vi.mocked(SecurityAuditLogger.getSecurityDashboard).mockReturnValue({
        summary: mockSecuritySummary,
        recentEvents: [],
        activeAlerts: [],
        topThreats: [],
      });

      const response = await request(app)
        .get('/api/monitoring/status')
        .expect(200);

      expect(response.body.data.overallStatus).toBe('critical');
      expect(response.body.data.components.security.status).toBe('critical');
    });
  });
});