import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import { app } from '../app';
import { db } from '../db';
import { members, roles, branches, churches } from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { hashPassword } from '../utils/auth';

// Mock data for testing
const mockChurch = {
  id: '123e4567-e89b-12d3-a456-426614174000',
  name: 'Test Church',
  slug: 'test-church',
  code: '123456',
  address: 'Test Address',
  phone: '+**********',
  email: '<EMAIL>',
  website: 'https://testchurch.com',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockBranch = {
  id: '123e4567-e89b-12d3-a456-426614174001',
  churchId: mockChurch.id,
  name: '<PERSON> Branch',
  slug: 'main-branch',
  address: 'Main Branch Address',
  phone: '+1234567891',
  email: '<EMAIL>',
  isMainBranch: true,
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockRole = {
  id: '123e4567-e89b-12d3-a456-426614174002',
  churchId: mockChurch.id,
  name: 'Member',
  description: 'Regular member',
  permissions: ['read:members'],
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const mockAdmin = {
  id: '123e4567-e89b-12d3-a456-426614174003',
  churchId: mockChurch.id,
  branchId: mockBranch.id,
  roleId: mockRole.id,
  firstName: 'Admin',
  lastName: 'User',
  email: '<EMAIL>',
  password: 'hashedpassword',
  status: 'active' as const,
  isEmailVerified: true,
  joinDate: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Mock JWT token for authenticated requests
const mockToken = 'mock-jwt-token';

// Mock the auth middleware
vi.mock('../middleware/auth', () => ({
  authenticate: (req: any, res: any, next: any) => {
    req.user = {
      id: mockAdmin.id,
      churchId: mockChurch.id,
      email: mockAdmin.email,
      permissions: ['*'], // Admin permissions
    };
    next();
  },
  authRateLimit: (req: any, res: any, next: any) => next(),
  apiRateLimit: (req: any, res: any, next: any) => next(),
  authorize: () => (req: any, res: any, next: any) => next(),
  requireChurchAccess: (req: any, res: any, next: any) => next(),
  requireRole: () => (req: any, res: any, next: any) => next(),
  validateChurchResource: () => (req: any, res: any, next: any) => next(),
  AuthErrorCode: {
    TOKEN_MISSING: 'TOKEN_MISSING',
    TOKEN_INVALID: 'TOKEN_INVALID',
    TOKEN_EXPIRED: 'TOKEN_EXPIRED',
    TOKEN_MALFORMED: 'TOKEN_MALFORMED',
    USER_NOT_FOUND: 'USER_NOT_FOUND',
    USER_INACTIVE: 'USER_INACTIVE',
    USER_UNVERIFIED: 'USER_UNVERIFIED',
    INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
    CHURCH_ACCESS_DENIED: 'CHURCH_ACCESS_DENIED',
    RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED'
  }
}));

describe('Member Controller - Enhanced Validation and Error Handling', () => {
  beforeEach(async () => {
    // Clean up database
    await db.delete(members);
    await db.delete(roles);
    await db.delete(branches);
    await db.delete(churches);

    // Insert test data
    await db.insert(churches).values(mockChurch);
    await db.insert(branches).values(mockBranch);
    await db.insert(roles).values(mockRole);
    await db.insert(members).values(mockAdmin);
  });

  afterEach(async () => {
    // Clean up after each test
    await db.delete(members);
    await db.delete(roles);
    await db.delete(branches);
    await db.delete(churches);
  });

  describe('POST /api/churches/:slug/members - Enhanced Validation', () => {
    const validMemberData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'SecurePass123',
      phone: '+**********',
      dateOfBirth: '1990-01-01',
      gender: 'male',
      address: '123 Main St',
      branchId: mockBranch.id,
      roleId: mockRole.id,
      status: 'active',
    };

    it('should create member with valid data and return structured success response', async () => {
      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(validMemberData)
        .expect(201);

      expect(response.body).toMatchObject({
        status: 'success',
        message: 'Member account created successfully. An email verification link has been sent.',
        data: {
          member: {
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            phone: '+**********',
            status: 'active',
          },
        },
      });

      expect(response.body.data.member.password).toBeUndefined();
      expect(response.body.data.member.emailVerificationToken).toBeUndefined();
    });

    it('should return structured error for duplicate email', async () => {
      // Create first member
      await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(validMemberData);

      // Try to create second member with same email
      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(validMemberData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Member creation failed',
        code: 'DUPLICATE_ERROR',
        errors: [{
          field: 'email',
          message: 'Email address is already registered',
          code: 'DUPLICATE_EMAIL',
        }],
      });
    });

    it('should return structured error for invalid first name', async () => {
      const invalidData = {
        ...validMemberData,
        firstName: '', // Empty first name
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'firstName',
            message: 'First name is required',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid first name with special characters', async () => {
      const invalidData = {
        ...validMemberData,
        firstName: 'John123!@#', // Invalid characters
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'firstName',
            message: 'First name contains invalid characters',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid email format', async () => {
      const invalidData = {
        ...validMemberData,
        email: 'invalid-email', // Invalid email format
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'email',
            message: 'Please enter a valid email address',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for weak password', async () => {
      const invalidData = {
        ...validMemberData,
        password: 'weak', // Weak password
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'password',
            message: expect.stringContaining('Password must contain at least one uppercase letter'),
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid phone number', async () => {
      const invalidData = {
        ...validMemberData,
        phone: 'invalid-phone', // Invalid phone format
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'phone',
            message: 'Please enter a valid phone number',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid date of birth', async () => {
      const invalidData = {
        ...validMemberData,
        dateOfBirth: 'invalid-date', // Invalid date format
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'dateOfBirth',
            message: 'Please enter a valid date of birth',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid gender', async () => {
      const invalidData = {
        ...validMemberData,
        gender: 'invalid-gender', // Invalid gender value
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'gender',
            message: 'Gender must be male, female, or other',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for invalid branch ID', async () => {
      const invalidData = {
        ...validMemberData,
        branchId: 'invalid-uuid', // Invalid UUID format
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'branchId',
            message: 'Invalid branch selection',
            code: expect.any(String),
          }),
        ]),
      });
    });

    it('should return structured error for non-existent branch ID', async () => {
      const invalidData = {
        ...validMemberData,
        branchId: '123e4567-e89b-12d3-a456-************', // Valid UUID but non-existent
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Member creation failed',
        code: 'INVALID_REFERENCE',
        errors: [{
          field: 'branchId',
          message: 'Selected branch is not valid or does not belong to your church',
          code: 'INVALID_BRANCH',
        }],
      });
    });

    it('should return structured error for non-existent role ID', async () => {
      const invalidData = {
        ...validMemberData,
        roleId: '123e4567-e89b-12d3-a456-************', // Valid UUID but non-existent
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Member creation failed',
        code: 'INVALID_REFERENCE',
        errors: [{
          field: 'roleId',
          message: 'Selected role is not valid or does not belong to your church',
          code: 'INVALID_ROLE',
        }],
      });
    });

    it('should handle multiple validation errors and return all of them', async () => {
      const invalidData = {
        firstName: '', // Empty first name
        lastName: '', // Empty last name
        email: 'invalid-email', // Invalid email
        password: 'weak', // Weak password
        phone: 'invalid-phone', // Invalid phone
        gender: 'invalid-gender', // Invalid gender
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      expect(response.body).toMatchObject({
        status: 'error',
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
      });

      expect(response.body.errors).toHaveLength(6);
      expect(response.body.errors).toEqual(
        expect.arrayContaining([
          expect.objectContaining({ field: 'firstName' }),
          expect.objectContaining({ field: 'lastName' }),
          expect.objectContaining({ field: 'email' }),
          expect.objectContaining({ field: 'password' }),
          expect.objectContaining({ field: 'phone' }),
          expect.objectContaining({ field: 'gender' }),
        ])
      );
    });

    it('should accept valid phone number formats', async () => {
      const validPhoneFormats = [
        '+**********',
        '****** 567 8900',
        '******-567-8900',
        '+****************',
        '**********',
      ];

      for (const phone of validPhoneFormats) {
        const memberData = {
          ...validMemberData,
          email: `test${Math.random()}@example.com`, // Unique email for each test
          phone,
        };

        const response = await request(app)
          .post(`/api/churches/${mockChurch.slug}/members`)
          .set('Authorization', `Bearer ${mockToken}`)
          .send(memberData)
          .expect(201);

        expect(response.body.status).toBe('success');
      }
    });

    it('should accept valid date of birth formats', async () => {
      const validDates = [
        '1990-01-01',
        '2000-12-31',
        '1985-06-15',
      ];

      for (const dateOfBirth of validDates) {
        const memberData = {
          ...validMemberData,
          email: `test${Math.random()}@example.com`, // Unique email for each test
          dateOfBirth,
        };

        const response = await request(app)
          .post(`/api/churches/${mockChurch.slug}/members`)
          .set('Authorization', `Bearer ${mockToken}`)
          .send(memberData)
          .expect(201);

        expect(response.body.status).toBe('success');
      }
    });

    it('should generate temporary password when password is not provided', async () => {
      const memberDataWithoutPassword = {
        ...validMemberData,
        email: '<EMAIL>',
      };
      delete memberDataWithoutPassword.password;

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(memberDataWithoutPassword)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.temporaryPassword).toBeDefined();
      expect(typeof response.body.data.temporaryPassword).toBe('string');
      expect(response.body.data.temporaryPassword.length).toBeGreaterThan(0);
    });

    it('should use default role when roleId is not provided', async () => {
      const memberDataWithoutRole = {
        ...validMemberData,
        email: '<EMAIL>',
      };
      delete memberDataWithoutRole.roleId;

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(memberDataWithoutRole)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.member.roleId).toBe(mockRole.id);
    });

    it('should use main branch when branchId is not provided', async () => {
      const memberDataWithoutBranch = {
        ...validMemberData,
        email: '<EMAIL>',
      };
      delete memberDataWithoutBranch.branchId;

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(memberDataWithoutBranch)
        .expect(201);

      expect(response.body.status).toBe('success');
      expect(response.body.data.member.branchId).toBe(mockBranch.id);
    });
  });

  describe('Error Response Format Consistency', () => {
    it('should return consistent error format for all validation errors', async () => {
      const invalidData = {
        firstName: '',
        lastName: '',
        email: 'invalid',
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(invalidData)
        .expect(400);

      // Check that all errors follow the same structure
      expect(response.body.errors).toBeInstanceOf(Array);
      response.body.errors.forEach((error: any) => {
        expect(error).toHaveProperty('field');
        expect(error).toHaveProperty('message');
        expect(error).toHaveProperty('code');
        expect(typeof error.field).toBe('string');
        expect(typeof error.message).toBe('string');
        expect(typeof error.code).toBe('string');
      });
    });

    it('should return consistent success format for member creation', async () => {
      const validData = {
        firstName: 'Test',
        lastName: 'User',
        email: '<EMAIL>',
        password: 'SecurePass123',
      };

      const response = await request(app)
        .post(`/api/churches/${mockChurch.slug}/members`)
        .set('Authorization', `Bearer ${mockToken}`)
        .send(validData)
        .expect(201);

      expect(response.body).toHaveProperty('status', 'success');
      expect(response.body).toHaveProperty('message');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('member');
      expect(typeof response.body.message).toBe('string');
      expect(typeof response.body.data.member).toBe('object');
    });
  });
});