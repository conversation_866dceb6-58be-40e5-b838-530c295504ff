import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import request from 'supertest';
import { app } from '../app';
import { db } from '../db';
import { churches, members, roles, events, eventRsvps, donations, donationCategories } from '../db/schema';
import { eq } from 'drizzle-orm';

describe('Church Events and Donations', () => {
  let church: any;
  let adminToken: string;
  let memberToken: string;
  let adminUser: any;
  let memberUser: any;

  beforeEach(async () => {
    await db.delete(donations);
    await db.delete(donationCategories);
    await db.delete(eventRsvps);
    await db.delete(events);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);

    const churchData = {
      church: {
        name: 'Grace Community Church',
        slug: 'grace-community',
        email: '<EMAIL>',
      },
      admin: {
        firstName: '<PERSON>',
        lastName: 'Pastor',
        email: '<EMAIL>',
        password: 'SecurePass123',
      },
    };

    const response = await request(app)
      .post('/api/churches/register')
      .send(churchData)
      .expect(201);

    church = response.body.data.church;
    adminToken = response.body.data.tokens.accessToken;
    adminUser = response.body.data.admin;

    const memberData = {
      firstName: 'Jane',
      lastName: 'Member',
      email: '<EMAIL>',
      password: 'SecurePass123',
      churchSlug: 'grace-community',
    };

    const memberResponse = await request(app)
      .post('/api/auth/register')
      .send(memberData)
      .expect(201);

    memberToken = memberResponse.body.data.tokens.accessToken;
    memberUser = memberResponse.body.data.user;
  });

  afterEach(async () => {
    await db.delete(donations);
    await db.delete(donationCategories);
    await db.delete(eventRsvps);
    await db.delete(events);
    await db.delete(members);
    await db.delete(roles);
    await db.delete(churches);
  });

  describe('Event Management', () => {
    describe('POST /api/churches/:slug/events', () => {
      it('should create a new event', async () => {
        const eventData = {
          title: 'Sunday Service',
          description: 'Weekly Sunday morning service',
          type: 'sunday_service',
          status: 'published',
          startDate: '2024-01-21T10:00:00Z',
          endDate: '2024-01-21T12:00:00Z',
          location: 'Main Sanctuary',
          requiresRsvp: true,
          allowDonations: true,
          donationGoal: 1000,
          donationDescription: 'Support our weekly service',
          tags: ['worship', 'community'],
          isPublic: true,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(eventData)
          .expect(201);

        expect(response.body.status).toBe('success');
        expect(response.body.data.event.title).toBe(eventData.title);
        expect(response.body.data.event.type).toBe(eventData.type);
        expect(response.body.data.event.requiresRsvp).toBe(true);
        expect(response.body.data.event.allowDonations).toBe(true);
      });

      it('should reject event creation by members without permission', async () => {
        const eventData = {
          title: 'Bible Study',
          startDate: '2024-01-22T19:00:00Z',
        };

        await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(eventData)
          .expect(403);
      });

      it('should validate required fields', async () => {
        const incompleteData = {
          description: 'Missing title',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(incompleteData)
          .expect(400);

        expect(response.body.message).toContain('validation');
      });
    });

    describe('GET /api/churches/:slug/events', () => {
      let event1: any;
      let event2: any;

      beforeEach(async () => {
        const event1Data = {
          title: 'Sunday Service',
          type: 'sunday_service',
          status: 'published',
          startDate: '2024-01-21T10:00:00Z',
          requiresRsvp: true,
        };

        const event2Data = {
          title: 'Bible Study',
          type: 'bible_study',
          status: 'draft',
          startDate: '2024-01-22T19:00:00Z',
          requiresRsvp: false,
        };

        const response1 = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(event1Data)
          .expect(201);

        const response2 = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(event2Data)
          .expect(201);

        event1 = response1.body.data.event;
        event2 = response2.body.data.event;
      });

      it('should get all events for the church', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.events).toHaveLength(2);
        expect(response.body.data.events[0].title).toBe('Bible Study'); // Most recent first
        expect(response.body.data.events[1].title).toBe('Sunday Service');
      });

      it('should filter events by type', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/events?type=sunday_service`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.events).toHaveLength(1);
        expect(response.body.data.events[0].type).toBe('sunday_service');
      });

      it('should filter events by status', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/events?status=published`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.events).toHaveLength(1);
        expect(response.body.data.events[0].status).toBe('published');
      });
    });

    describe('Event RSVP', () => {
      let event: any;

      beforeEach(async () => {
        const eventData = {
          title: 'Church Conference',
          startDate: '2024-02-01T09:00:00Z',
          requiresRsvp: true,
          maxAttendees: 100,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(eventData)
          .expect(201);

        event = response.body.data.event;
      });

      it('should create an RSVP', async () => {
        const rsvpData = {
          status: 'attending',
          attendeeCount: 2,
          notes: 'Bringing my spouse',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/events/${event.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(rsvpData)
          .expect(201);

        expect(response.body.data.rsvp.status).toBe('attending');
        expect(response.body.data.rsvp.attendeeCount).toBe(2);
        expect(response.body.data.rsvp.notes).toBe('Bringing my spouse');
      });

      it('should update an existing RSVP', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/events/${event.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ status: 'attending', attendeeCount: 1 })
          .expect(201);

        const updateData = {
          status: 'maybe',
          attendeeCount: 2,
          notes: 'Schedule conflict',
        };

        const response = await request(app)
          .put(`/api/churches/${church.slug}/events/${event.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send(updateData)
          .expect(200);

        expect(response.body.data.rsvp.status).toBe('maybe');
        expect(response.body.data.rsvp.notes).toBe('Schedule conflict');
      });

      it('should delete an RSVP', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/events/${event.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ status: 'attending' })
          .expect(201);

        await request(app)
          .delete(`/api/churches/${church.slug}/events/${event.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .expect(200);
      });

      it('should reject RSVP for event that does not require it', async () => {
        const noRsvpEventData = {
          title: 'Open House',
          startDate: '2024-02-02T14:00:00Z',
          requiresRsvp: false,
        };

        const eventResponse = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(noRsvpEventData)
          .expect(201);

        const noRsvpEvent = eventResponse.body.data.event;

        await request(app)
          .post(`/api/churches/${church.slug}/events/${noRsvpEvent.id}/rsvp`)
          .set('Authorization', `Bearer ${memberToken}`)
          .send({ status: 'attending' })
          .expect(400);
      });
    });
  });

  describe('Donation Management', () => {
    let event: any;

    beforeEach(async () => {
      const eventData = {
        title: 'Fundraising Dinner',
        type: 'fundraiser',
        startDate: '2024-03-01T18:00:00Z',
        allowDonations: true,
        donationGoal: 5000,
        donationDescription: 'Help us reach our building fund goal',
      };

      const response = await request(app)
        .post(`/api/churches/${church.slug}/events`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(eventData)
        .expect(201);

      event = response.body.data.event;
    });

    describe('POST /api/churches/:slug/donations', () => {
      it('should record a regular donation', async () => {
        const donationData = {
          amount: 100.00,
          type: 'offering',
          method: 'cash',
          donorName: 'Jane Doe',
          donorEmail: '<EMAIL>',
          description: 'Sunday offering',
          taxDeductible: true,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(donationData)
          .expect(201);

        expect(response.body.data.donation.amount).toBe('100.00');
        expect(response.body.data.donation.type).toBe('offering');
        expect(response.body.data.donation.receiptNumber).toBeDefined();
        expect(response.body.data.donation.receiptNumber).toMatch(/^GRACE-COMMUNITY-/);
      });

      it('should record an event donation', async () => {
        const donationData = {
          amount: 250.00,
          type: 'event_donation',
          method: 'credit_card',
          eventId: event.id,
          donorName: 'John Smith',
          description: 'Donation for fundraising dinner',
          transactionId: 'tx_123456789',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(donationData)
          .expect(201);

        expect(response.body.data.donation.amount).toBe('250.00');
        expect(response.body.data.donation.eventId).toBe(event.id);
        expect(response.body.data.donation.transactionId).toBe('tx_123456789');
      });

      it('should record an anonymous donation', async () => {
        const donationData = {
          amount: 50.00,
          type: 'tithe',
          method: 'cash',
          isAnonymous: true,
          description: 'Anonymous tithe',
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(donationData)
          .expect(201);

        expect(response.body.data.donation.isAnonymous).toBe(true);
        expect(response.body.data.donation.donorId).toBeNull();
      });

      it('should reject donation to event that does not allow donations', async () => {
        const nonDonationEventData = {
          title: 'Bible Study',
          startDate: '2024-03-02T19:00:00Z',
          allowDonations: false,
        };

        const eventResponse = await request(app)
          .post(`/api/churches/${church.slug}/events`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(nonDonationEventData)
          .expect(201);

        const nonDonationEvent = eventResponse.body.data.event;

        const donationData = {
          amount: 100.00,
          eventId: nonDonationEvent.id,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(donationData)
          .expect(400);

        expect(response.body.message).toBe('Donations are not allowed for this event');
      });

      it('should validate minimum amount', async () => {
        const donationData = {
          amount: 0,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(donationData)
          .expect(400);

        expect(response.body.message).toContain('positive');
      });
    });

    describe('GET /api/churches/:slug/donations', () => {
      beforeEach(async () => {
        const donations = [
          {
            amount: 100.00,
            type: 'offering',
            method: 'cash',
            donorName: 'John Doe',
            donatedAt: '2024-01-01T10:00:00Z',
          },
          {
            amount: 250.00,
            type: 'event_donation',
            method: 'credit_card',
            eventId: event.id,
            donorName: 'Jane Smith',
            donatedAt: '2024-01-15T14:00:00Z',
          },
          {
            amount: 50.00,
            type: 'tithe',
            method: 'cash',
            isAnonymous: true,
            donatedAt: '2024-01-31T09:00:00Z',
          },
        ];

        for (const donation of donations) {
          await request(app)
            .post(`/api/churches/${church.slug}/donations`)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(donation)
            .expect(201);
        }
      });

      it('should get all donations for the church', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.donations).toHaveLength(3);
        expect(response.body.data.summary.totalAmount).toBe(400.00);
        expect(response.body.data.summary.totalCount).toBe(3);
      });

      it('should filter donations by type', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations?type=event_donation`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.donations).toHaveLength(1);
        expect(response.body.data.donations[0].type).toBe('event_donation');
      });

      it('should filter donations by date range', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations?startDate=2024-01-10&endDate=2024-01-20`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.donations).toHaveLength(1);
        expect(response.body.data.donations[0].donorName).toBe('Jane Smith');
      });

      it('should filter donations by amount range', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations?minAmount=100&maxAmount=200`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.donations).toHaveLength(1);
        expect(response.body.data.donations[0].amount).toBe('100.00');
      });

      it('should exclude anonymous donations when requested', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations?includeAnonymous=false`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.donations).toHaveLength(2);
        expect(response.body.data.donations.every((d: any) => !d.isAnonymous)).toBe(true);
      });
    });

    describe('GET /api/churches/:slug/donations/report', () => {
      beforeEach(async () => {
        const donations = [
          { amount: 100.00, donatedAt: '2024-01-01T10:00:00Z' },
          { amount: 150.00, donatedAt: '2024-01-15T10:00:00Z' },
          { amount: 200.00, donatedAt: '2024-02-01T10:00:00Z' },
          { amount: 75.00, donatedAt: '2024-02-15T10:00:00Z' },
        ];

        for (const donation of donations) {
          await request(app)
            .post(`/api/churches/${church.slug}/donations`)
            .set('Authorization', `Bearer ${adminToken}`)
            .send(donation)
            .expect(201);
        }
      });

      it('should generate monthly donation report', async () => {
        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations/report?startDate=2024-01-01&endDate=2024-02-28&groupBy=month`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.report).toHaveLength(2);
        expect(response.body.data.summary.totalAmount).toBe(525.00);

        const januaryData = response.body.data.report.find((r: any) => r.period.includes('2024-01'));
        const februaryData = response.body.data.report.find((r: any) => r.period.includes('2024-02'));

        expect(januaryData.totalAmount).toBe(250.00);
        expect(januaryData.count).toBe(2);
        expect(februaryData.totalAmount).toBe(275.00);
        expect(februaryData.count).toBe(2);
      });
    });

    describe('Donation Categories', () => {
      it('should create a donation category', async () => {
        const categoryData = {
          name: 'Building Fund',
          description: 'Funds for new church building',
          targetAmount: 50000.00,
        };

        const response = await request(app)
          .post(`/api/churches/${church.slug}/donations/categories`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send(categoryData)
          .expect(201);

        expect(response.body.data.category.name).toBe('Building Fund');
        expect(response.body.data.category.targetAmount).toBe('50000.00');
      });

      it('should get donation categories', async () => {
        await request(app)
          .post(`/api/churches/${church.slug}/donations/categories`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ name: 'Building Fund', targetAmount: 50000 })
          .expect(201);

        await request(app)
          .post(`/api/churches/${church.slug}/donations/categories`)
          .set('Authorization', `Bearer ${adminToken}`)
          .send({ name: 'Mission Support', targetAmount: 10000 })
          .expect(201);

        const response = await request(app)
          .get(`/api/churches/${church.slug}/donations/categories`)
          .set('Authorization', `Bearer ${adminToken}`)
          .expect(200);

        expect(response.body.data.categories).toHaveLength(2);
        expect(response.body.data.categories[0].name).toBe('Building Fund'); // Alphabetical order
        expect(response.body.data.categories[1].name).toBe('Mission Support');
      });
    });
  });

  describe('Cross-tenant Isolation', () => {
    let church2: any;
    let admin2Token: string;

    beforeEach(async () => {
      const church2Data = {
        church: {
          name: 'Second Church',
          slug: 'second-church',
          email: '<EMAIL>',
        },
        admin: {
          firstName: 'Sarah',
          lastName: 'Pastor',
          email: '<EMAIL>',
          password: 'SecurePass123',
        },
      };

      const response2 = await request(app)
        .post('/api/churches/register')
        .send(church2Data)
        .expect(201);

      church2 = response2.body.data.church;
      admin2Token = response2.body.data.tokens.accessToken;
    });

    it('should isolate events between churches', async () => {
      await request(app)
        .post(`/api/churches/${church.slug}/events`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          title: 'Church 1 Event',
          startDate: '2024-01-21T10:00:00Z',
        })
        .expect(201);

      await request(app)
        .post(`/api/churches/${church2.slug}/events`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .send({
          title: 'Church 2 Event',
          startDate: '2024-01-21T14:00:00Z',
        })
        .expect(201);

      const church1Events = await request(app)
        .get(`/api/churches/${church.slug}/events`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const church2Events = await request(app)
        .get(`/api/churches/${church2.slug}/events`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(200);

      expect(church1Events.body.data.events).toHaveLength(1);
      expect(church1Events.body.data.events[0].title).toBe('Church 1 Event');

      expect(church2Events.body.data.events).toHaveLength(1);
      expect(church2Events.body.data.events[0].title).toBe('Church 2 Event');
    });

    it('should isolate donations between churches', async () => {
      await request(app)
        .post(`/api/churches/${church.slug}/donations`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          amount: 100.00,
          donorName: 'Church 1 Donor',
        })
        .expect(201);

      await request(app)
        .post(`/api/churches/${church2.slug}/donations`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .send({
          amount: 200.00,
          donorName: 'Church 2 Donor',
        })
        .expect(201);

      const church1Donations = await request(app)
        .get(`/api/churches/${church.slug}/donations`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      const church2Donations = await request(app)
        .get(`/api/churches/${church2.slug}/donations`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(200);

      expect(church1Donations.body.data.donations).toHaveLength(1);
      expect(church1Donations.body.data.donations[0].donorName).toBe('Church 1 Donor');
      expect(church1Donations.body.data.summary.totalAmount).toBe(100.00);

      expect(church2Donations.body.data.donations).toHaveLength(1);
      expect(church2Donations.body.data.donations[0].donorName).toBe('Church 2 Donor');
      expect(church2Donations.body.data.summary.totalAmount).toBe(200.00);
    });

    it('should prevent cross-tenant access to events', async () => {
      const eventResponse = await request(app)
        .post(`/api/churches/${church.slug}/events`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          title: 'Private Event',
          startDate: '2024-01-21T10:00:00Z',
        })
        .expect(201);

      const eventId = eventResponse.body.data.event.id;

      await request(app)
        .get(`/api/churches/${church2.slug}/events/${eventId}`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(404);
    });

    it('should prevent cross-tenant access to donations', async () => {
      const donationResponse = await request(app)
        .post(`/api/churches/${church.slug}/donations`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          amount: 100.00,
          donorName: 'Private Donor',
        })
        .expect(201);

      const donationId = donationResponse.body.data.donation.id;

      await request(app)
        .get(`/api/churches/${church2.slug}/donations/${donationId}`)
        .set('Authorization', `Bearer ${admin2Token}`)
        .expect(404);
    });
  });
});