import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';

vi.mock('../db', () => ({
  db: {
    query: {
      churches: {
        findFirst: vi.fn(),
      },
    },
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn(() => []),
      })),
    })),
  },
}));

vi.mock('../utils/seedData', () => ({
  seedRoles: vi.fn(),
}));

const { createChurch } = await import('../controllers/churchController');
const { globalErrorHandler } = await import('../utils/errorHandler');

const app = express();
app.use(express.json());
app.post('/churches', createChurch);
app.use(globalErrorHandler);

describe('Church Controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /churches', () => {
    const validChurchData = {
      name: 'Test Church',
      slug: 'test-church',
      description: 'A test church',
      address: '123 Main St',
      phone: '555-0123',
      email: '<EMAIL>',
      website: 'https://testchurch.com',
    };

    it('should create a church and return church URL and members URL', async () => {
      const mockChurch = {
        id: 'church-123',
        ...validChurchData,
        createdAt: new Date('2025-01-01'),
        updatedAt: new Date('2025-01-01'),
      };

      const { db } = await import('../db');
      const { seedRoles } = await import('../utils/seedData');

      const mockInsert = vi.fn().mockReturnValue({
        values: vi.fn().mockReturnValue({
          returning: vi.fn().mockResolvedValue([mockChurch])
        })
      });

      vi.mocked(db.query.churches.findFirst).mockResolvedValue(null);
      vi.mocked(db.insert).mockImplementation(mockInsert);
      vi.mocked(seedRoles).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/churches')
        .send(validChurchData);

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.data.church).toEqual(expect.objectContaining({
        id: 'church-123',
        name: 'Test Church',
        slug: 'test-church',
      }));
      expect(response.body.data.churchUrl).toContain('/api/churches/test-church');
      expect(response.body.data.membersUrl).toContain('/api/churches/test-church/members');
    });

    it('should return 400 if church slug already exists', async () => {
      const existingChurch = { id: 'existing-123', slug: 'test-church' };
      const { db } = await import('../db');
      
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(existingChurch);

      const response = await request(app)
        .post('/churches')
        .send(validChurchData)
        .expect(400);

      expect(response.body.message).toBe('Church with this slug already exists');
    });
  });
});