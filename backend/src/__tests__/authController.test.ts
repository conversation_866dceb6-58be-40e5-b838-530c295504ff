import { describe, it, expect, beforeEach, vi } from 'vitest';
import request from 'supertest';
import express from 'express';
import bcrypt from 'bcryptjs';

vi.mock('../db', () => ({
  db: {
    query: {
      members: {
        findFirst: vi.fn(),
      },
      churches: {
        findFirst: vi.fn(),
      },
    },
    insert: vi.fn(() => ({
      values: vi.fn(() => ({
        returning: vi.fn(() => []),
      })),
    })),
    update: vi.fn(() => ({
      set: vi.fn(() => ({
        where: vi.fn(() => ({
          returning: vi.fn(() => []),
        })),
      })),
    })),
  },
}));

vi.mock('../utils/seedData', () => ({
  seedRoles: vi.fn(),
}));

vi.mock('bcryptjs', () => ({
  default: {
    hash: vi.fn(),
    compare: vi.fn(),
  },
}));

vi.mock('jsonwebtoken', () => ({
  default: {
    sign: vi.fn(),
    verify: vi.fn(),
  },
}));

const { register, login, getProfile, updateProfile, changePassword } = await import('../controllers/authController');
const { globalErrorHandler } = await import('../utils/errorHandler');

const app = express();
app.use(express.json());
app.post('/auth/register', register);
app.post('/auth/login', login);
app.get('/auth/profile', getProfile);
app.put('/auth/profile', updateProfile);
app.post('/auth/change-password', changePassword);
app.use(globalErrorHandler);

describe('Authentication Controller', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('POST /auth/register', () => {
    const validRegistrationData = {
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      password: 'SecurePass123',
      churchId: '123e4567-e89b-12d3-a456-************',
      phone: '+********90',
      gender: 'male',
    };

    it('should register a new user successfully', async () => {
      const mockChurch = { id: validRegistrationData.churchId, name: 'Test Church' };
      const mockUser = {
        id: 'user-123',
        ...validRegistrationData,
        password: 'hashed-password',
        isEmailVerified: false,
        createdAt: new Date(),
      };

      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.insert().values().returning).mockResolvedValue([mockUser]);
      vi.mocked(bcrypt.hash).mockResolvedValue('hashed-password');

      const response = await request(app)
        .post('/auth/register')
        .send(validRegistrationData);

      expect(response.status).toBe(201);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toContain('Registration successful');
      expect(response.body.data.user.email).toBe(validRegistrationData.email);
      expect(response.body.data.tokens).toBeDefined();
      expect(response.body.data.tokens.accessToken).toBeDefined();
      expect(response.body.data.tokens.refreshToken).toBeDefined();
    });

    it('should return 400 if email already exists', async () => {
      const existingUser = { id: 'existing-123', email: validRegistrationData.email };
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(existingUser);

      const response = await request(app)
        .post('/auth/register')
        .send(validRegistrationData);

      expect(response.status).toBe(400);
      expect(response.body.message).toBe('Email already registered');
    });

    it('should return 404 if church does not exist', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .post('/auth/register')
        .send(validRegistrationData);

      expect(response.status).toBe(404);
      expect(response.body.message).toBe('Church not found');
    });

    it('should return 400 for invalid data', async () => {
      const invalidData = {
        firstName: '',
        lastName: 'Doe',
        email: 'invalid-email',
        password: '123', // Too short
        churchId: 'invalid-uuid',
      };

      const response = await request(app)
        .post('/auth/register')
        .send(invalidData);

      expect(response.status).toBe(400);
      expect(response.body.status).toBe('error');
    });
  });

  describe('POST /auth/login', () => {
    const validLoginData = {
      email: '<EMAIL>',
      password: 'SecurePass123',
    };

    it('should login user successfully', async () => {
      const mockUser = {
        id: 'user-123',
        firstName: 'John',
        lastName: 'Doe',
        email: validLoginData.email,
        password: 'hashed-password',
        status: 'active',
        churchId: 'church-123',
        roleId: 'role-123',
        isEmailVerified: true,
        lastLoginAt: null,
        church: { id: 'church-123', name: 'Test Church', slug: 'test-church' },
        role: { id: 'role-123', name: 'Member', permissions: ['view_church'] },
      };

      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(mockUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);
      vi.mocked(db.update().set().where).mockResolvedValue(undefined);

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(200);
      expect(response.body.status).toBe('success');
      expect(response.body.message).toBe('Login successful');
      expect(response.body.data.user.email).toBe(validLoginData.email);
      expect(response.body.data.tokens).toBeDefined();
    });

    it('should return 401 for invalid credentials', async () => {
      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(401);
      expect(response.body.message).toBe('Invalid email or password');
    });

    it('should return 403 for inactive user', async () => {
      const inactiveUser = {
        id: 'user-123',
        email: validLoginData.email,
        password: 'hashed-password',
        status: 'inactive',
      };

      const { db } = await import('../db');
      
      vi.mocked(db.query.members.findFirst).mockResolvedValue(inactiveUser);
      vi.mocked(bcrypt.compare).mockResolvedValue(true);

      const response = await request(app)
        .post('/auth/login')
        .send(validLoginData);

      expect(response.status).toBe(403);
      expect(response.body.message).toContain('Account is inactive');
    });
  });

  describe('Password validation', () => {
    it('should reject weak passwords', async () => {
      const weakPasswords = [
        'password', // No uppercase or numbers
        'PASSWORD', // No lowercase or numbers
        'Password', // No numbers
        'Pass123',  // Too short
        '********', // No letters
      ];

      for (const password of weakPasswords) {
        const response = await request(app)
          .post('/auth/register')
          .send({
            firstName: 'John',
            lastName: 'Doe',
            email: '<EMAIL>',
            password,
            churchId: '123e4567-e89b-12d3-a456-************',
          });

        expect(response.status).toBe(400);
      }
    });

    it('should accept strong passwords', async () => {
      const { db } = await import('../db');
      
      const mockChurch = { id: '123e4567-e89b-12d3-a456-************', name: 'Test Church' };
      const mockUser = { id: 'user-123', email: '<EMAIL>' };

      vi.mocked(db.query.members.findFirst).mockResolvedValue(null);
      vi.mocked(db.query.churches.findFirst).mockResolvedValue(mockChurch);
      vi.mocked(db.insert().values().returning).mockResolvedValue([mockUser]);
      vi.mocked(bcrypt.hash).mockResolvedValue('hashed-password');

      const strongPasswords = [
        'SecurePass123',
        'MyP@ssw0rd!',
        'ComplexPassword1',
      ];

      for (const password of strongPasswords) {
        const response = await request(app)
          .post('/auth/register')
          .send({
            firstName: 'John',
            lastName: 'Doe',
            email: `john${Math.random()}@example.com`,
            password,
            churchId: '123e4567-e89b-12d3-a456-************',
          });

        expect(response.status).toBe(201);
      }
    });
  });
});