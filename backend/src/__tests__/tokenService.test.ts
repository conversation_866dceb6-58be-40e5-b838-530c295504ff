import { describe, it, expect, beforeEach, vi } from 'vitest';
import { TokenService } from '../utils/tokenService';

// Mock the database and dependencies
vi.mock('../db', () => ({
  db: {
    insert: vi.fn(),
    update: vi.fn(),
    query: {
      refreshTokens: {
        findFirst: vi.fn(),
        findMany: vi.fn(),
      },
    },
  },
}));

vi.mock('../db/schema', () => ({
  refreshTokens: {},
}));

vi.mock('../utils/auth', () => ({
  generateRefreshToken: vi.fn(() => 'mock-refresh-token'),
  getTokenExpiration: vi.fn(() => new Date(Date.now() + 7 * 24 * 60 * 60 * 1000)),
  isTokenExpired: vi.fn(() => false),
}));

describe('TokenService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Cookie Management', () => {
    it('should set httpOnly cookie with correct options', () => {
      const mockRes = {
        cookie: vi.fn(),
      } as any;

      TokenService.setRefreshTokenCookie(mockRes, 'test-token');

      expect(mockRes.cookie).toHaveBeenCalledWith('refresh_token', 'test-token', {
        httpOnly: true,
        secure: false, // NODE_ENV is not production in tests
        sameSite: 'lax',
        maxAge: 7 * 24 * 60 * 60 * 1000,
        path: '/api/auth',
      });
    });

    it('should clear httpOnly cookie', () => {
      const mockRes = {
        clearCookie: vi.fn(),
      } as any;

      TokenService.clearRefreshTokenCookie(mockRes);

      expect(mockRes.clearCookie).toHaveBeenCalledWith('refresh_token', {
        httpOnly: true,
        secure: false,
        sameSite: 'lax',
        path: '/api/auth',
      });
    });

    it('should get refresh token from cookie first', () => {
      const mockReq = {
        cookies: {
          refresh_token: 'cookie-token',
        },
        body: {
          refreshToken: 'body-token',
        },
      } as any;

      const token = TokenService.getRefreshToken(mockReq);
      expect(token).toBe('cookie-token');
    });

    it('should fallback to body token if no cookie', () => {
      const mockReq = {
        cookies: {},
        body: {
          refreshToken: 'body-token',
        },
      } as any;

      const token = TokenService.getRefreshToken(mockReq);
      expect(token).toBe('body-token');
    });
  });

  describe('Metadata Extraction', () => {
    it('should extract IP address and user agent', () => {
      const mockReq = {
        ip: '***********',
        get: vi.fn((header) => {
          if (header === 'User-Agent') return 'Mozilla/5.0';
          return undefined;
        }),
        connection: { remoteAddress: '***********' },
      } as any;

      const metadata = TokenService.extractMetadata(mockReq);

      expect(metadata).toEqual({
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0',
      });
    });

    it('should fallback to connection.remoteAddress if no ip', () => {
      const mockReq = {
        get: vi.fn(() => 'Mozilla/5.0'),
        connection: { remoteAddress: '***********' },
      } as any;

      const metadata = TokenService.extractMetadata(mockReq);

      expect(metadata.ipAddress).toBe('***********');
    });
  });

  describe('Security Features', () => {
    it('should handle production environment settings', () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      const mockRes = {
        cookie: vi.fn(),
      } as any;

      TokenService.setRefreshTokenCookie(mockRes, 'test-token');

      expect(mockRes.cookie).toHaveBeenCalledWith('refresh_token', 'test-token', {
        httpOnly: true,
        secure: true, // Should be true in production
        sameSite: 'strict', // Should be strict in production
        maxAge: 7 * 24 * 60 * 60 * 1000,
        path: '/api/auth',
      });

      process.env.NODE_ENV = originalEnv;
    });
  });
});