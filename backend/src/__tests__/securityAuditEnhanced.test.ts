import { SecurityAuditLogger, SecurityEventType } from '../utils/securityAudit';
import { Request } from 'express';

// Mock request object
const mockRequest = {
  ip: '*************',
  get: jest.fn((header: string) => {
    if (header === 'User-Agent') return 'Mozilla/5.0 Test Browser';
    return undefined;
  }),
  path: '/api/auth/login',
  method: 'POST',
  connection: { remoteAddress: '*************' },
} as unknown as Request;

describe('SecurityAuditLogger Enhanced Features', () => {
  beforeEach(() => {
    // Clear events and alerts before each test
    (SecurityAuditLogger as any).events = [];
    (SecurityAuditLogger as any).alerts = [];
    (SecurityAuditLogger as any).blockedIPs = new Set();
    (SecurityAuditLogger as any).suspiciousIPs = new Map();
  });

  describe('Suspicious Activity Detection', () => {
    it('should detect suspicious activity after multiple failed attempts', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Simulate 5 failed login attempts from same IP
      for (let i = 0; i < 5; i++) {
        SecurityAuditLogger.logAuthFailure(mockRequest, 'Invalid credentials');
      }

      // Check if suspicious activity was logged
      const events = (SecurityAuditLogger as any).events;
      const suspiciousEvents = events.filter((e: any) => e.eventType === SecurityEventType.SUSPICIOUS_ACTIVITY);
      
      expect(suspiciousEvents.length).toBeGreaterThan(0);
      expect(suspiciousEvents[0].details.activity).toContain('Multiple failed authentication attempts');

      consoleSpy.mockRestore();
    });

    it('should block IP after excessive failed attempts', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Simulate 10 failed login attempts from same IP (block threshold)
      for (let i = 0; i < 10; i++) {
        SecurityAuditLogger.logAuthFailure(mockRequest, 'Invalid credentials');
      }

      // Check if IP is blocked
      expect(SecurityAuditLogger.isIPBlocked('*************')).toBe(true);

      // Check if alert was generated
      const alerts = SecurityAuditLogger.getActiveAlerts();
      expect(alerts.length).toBeGreaterThan(0);
      expect(alerts.some(alert => alert.message.includes('IP blocked'))).toBe(true);

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });

    it('should clean up old suspicious IP records', (done) => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Record suspicious activity
      SecurityAuditLogger.logAuthFailure(mockRequest, 'Invalid credentials');

      // Manually set the lastSeen time to be old
      const suspiciousIPs = (SecurityAuditLogger as any).suspiciousIPs;
      const oldTime = new Date(Date.now() - 20 * 60 * 1000); // 20 minutes ago
      suspiciousIPs.set('*************', { count: 1, lastSeen: oldTime });

      // Trigger another event to clean up old records
      setTimeout(() => {
        SecurityAuditLogger.logAuthFailure(mockRequest, 'Invalid credentials');
        
        // The old record should be cleaned up
        expect(suspiciousIPs.has('*************')).toBe(true); // New record should exist
        
        consoleSpy.mockRestore();
        done();
      }, 100);
    });
  });

  describe('Alert Management', () => {
    it('should generate alerts for critical events', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: 'critical',
        success: false,
        ipAddress: '*************',
        details: { activity: 'Test critical event' },
      });

      const alerts = SecurityAuditLogger.getActiveAlerts();
      expect(alerts.length).toBe(1);
      expect(alerts[0].severity).toBe('critical');
      expect(alerts[0].acknowledged).toBe(false);

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });

    it('should acknowledge alerts', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        severity: 'high',
        success: false,
      });

      const alerts = SecurityAuditLogger.getActiveAlerts();
      const alertId = alerts[0].id;

      const acknowledged = SecurityAuditLogger.acknowledgeAlert(alertId);
      expect(acknowledged).toBe(true);

      const activeAlerts = SecurityAuditLogger.getActiveAlerts();
      expect(activeAlerts.length).toBe(0); // Should be filtered out

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });

    it('should generate appropriate alert messages', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      const testCases = [
        {
          eventType: SecurityEventType.LOGIN_FAILED,
          expectedMessage: 'Failed login attempt from *************',
        },
        {
          eventType: SecurityEventType.UNAUTHORIZED_ACCESS,
          expectedMessage: 'Unauthorized access attempt to /api/auth/login from *************',
        },
        {
          eventType: SecurityEventType.RATE_LIMIT_EXCEEDED,
          expectedMessage: 'Rate limit exceeded from *************',
        },
      ];

      testCases.forEach(({ eventType, expectedMessage }) => {
        SecurityAuditLogger.logEvent({
          eventType,
          severity: 'high',
          success: false,
          ipAddress: '*************',
          endpoint: '/api/auth/login',
        });
      });

      const alerts = SecurityAuditLogger.getActiveAlerts();
      expect(alerts.length).toBe(testCases.length);
      
      testCases.forEach(({ expectedMessage }, index) => {
        expect(alerts[index].message).toBe(expectedMessage);
      });

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });
  });

  describe('Security Metrics', () => {
    it('should calculate security metrics correctly', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      const now = new Date();
      const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

      // Log various security events
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        success: true,
        severity: 'low',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        success: false,
        severity: 'medium',
        details: { reason: 'Invalid credentials' },
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        success: false,
        severity: 'high',
        details: { reason: 'Multiple failed attempts' },
      });

      const metrics = SecurityAuditLogger.getSecurityMetrics({ start: oneHourAgo, end: now });

      expect(metrics.totalEvents).toBe(3);
      expect(metrics.eventsByType[SecurityEventType.LOGIN_SUCCESS]).toBe(1);
      expect(metrics.eventsByType[SecurityEventType.LOGIN_FAILED]).toBe(1);
      expect(metrics.eventsByType[SecurityEventType.SUSPICIOUS_ACTIVITY]).toBe(1);
      expect(metrics.eventsBySeverity['low']).toBe(1);
      expect(metrics.eventsBySeverity['medium']).toBe(1);
      expect(metrics.eventsBySeverity['high']).toBe(1);
      expect(metrics.failedLoginAttempts).toBe(1);
      expect(metrics.suspiciousActivities).toBe(1);
      expect(metrics.topFailureReasons).toEqual([
        { reason: 'Invalid credentials', count: 1 },
        { reason: 'Multiple failed attempts', count: 1 },
      ]);

      consoleSpy.mockRestore();
    });

    it('should provide security dashboard data', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();

      // Generate some test data
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        success: true,
        severity: 'low',
        userId: 'user1',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        success: false,
        severity: 'medium',
        ipAddress: '*************',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        success: false,
        severity: 'critical',
        ipAddress: '*************',
      });

      const dashboard = SecurityAuditLogger.getSecurityDashboard();

      expect(dashboard.summary.totalEvents).toBeGreaterThan(0);
      expect(dashboard.summary.criticalAlerts).toBeGreaterThan(0);
      expect(dashboard.recentEvents.length).toBeGreaterThan(0);
      expect(dashboard.activeAlerts.length).toBeGreaterThan(0);

      consoleSpy.mockRestore();
      consoleWarnSpy.mockRestore();
    });
  });

  describe('Audit Log Queries', () => {
    it('should retrieve user audit logs', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        userId: 'user123',
        success: true,
        severity: 'low',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.PASSWORD_CHANGED,
        userId: 'user123',
        success: true,
        severity: 'medium',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        userId: 'user456',
        success: true,
        severity: 'low',
      });

      const userLogs = SecurityAuditLogger.getUserAuditLogs('user123');
      expect(userLogs.length).toBe(2);
      expect(userLogs.every(log => log.userId === 'user123')).toBe(true);

      consoleSpy.mockRestore();
    });

    it('should retrieve IP audit logs', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        ipAddress: '*************',
        success: false,
        severity: 'medium',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        ipAddress: '*************',
        success: false,
        severity: 'high',
      });

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_FAILED,
        ipAddress: '*************',
        success: false,
        severity: 'medium',
      });

      const ipLogs = SecurityAuditLogger.getIPAuditLogs('*************');
      expect(ipLogs.length).toBe(2);
      expect(ipLogs.every(log => log.ipAddress === '*************')).toBe(true);

      consoleSpy.mockRestore();
    });

    it('should limit audit log results', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Generate more logs than the limit
      for (let i = 0; i < 10; i++) {
        SecurityAuditLogger.logEvent({
          eventType: SecurityEventType.LOGIN_SUCCESS,
          userId: 'user123',
          success: true,
          severity: 'low',
        });
      }

      const limitedLogs = SecurityAuditLogger.getUserAuditLogs('user123', 5);
      expect(limitedLogs.length).toBe(5);

      consoleSpy.mockRestore();
    });
  });

  describe('Data Export', () => {
    it('should export security data in JSON format', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        success: true,
        severity: 'low',
      });

      const exported = SecurityAuditLogger.exportSecurityData('json');
      const data = JSON.parse(exported);

      expect(data).toHaveProperty('events');
      expect(data).toHaveProperty('alerts');
      expect(data).toHaveProperty('blockedIPs');
      expect(data).toHaveProperty('suspiciousIPs');
      expect(data).toHaveProperty('exportedAt');
      expect(data.events.length).toBe(1);

      consoleSpy.mockRestore();
    });

    it('should export security data in CSV format', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGIN_SUCCESS,
        userId: 'user123',
        ipAddress: '*************',
        success: true,
        severity: 'low',
      });

      const exported = SecurityAuditLogger.exportSecurityData('csv');
      const lines = exported.split('\n');

      expect(lines[0]).toContain('timestamp,eventType,userId,ipAddress,success,severity,details');
      expect(lines[1]).toContain('LOGIN_SUCCESS');
      expect(lines[1]).toContain('user123');
      expect(lines[1]).toContain('*************');
      expect(lines[1]).toContain('true');
      expect(lines[1]).toContain('low');

      consoleSpy.mockRestore();
    });
  });

  describe('Critical Event Handling', () => {
    it('should auto-block IPs for critical suspicious activity', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
        ipAddress: '*************',
        success: false,
        severity: 'critical',
      });

      expect(SecurityAuditLogger.isIPBlocked('*************')).toBe(true);

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });

    it('should auto-block IPs for critical unauthorized access', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();
      const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation();

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.UNAUTHORIZED_ACCESS,
        ipAddress: '*************',
        success: false,
        severity: 'critical',
      });

      expect(SecurityAuditLogger.isIPBlocked('*************')).toBe(true);

      consoleSpy.mockRestore();
      consoleErrorSpy.mockRestore();
    });
  });
});