import type { Response } from 'express';
import { and, desc, eq, inArray, sql } from 'drizzle-orm';
import { db } from '../db';
import { notifications } from '../db/schema';
import { catchAsync, AppError } from '../utils/errorHandler';
import type { TenantRequest } from '../middleware/tenant';
import { notificationQuerySchema, type NotificationQueryInput, markReadSchema, type MarkReadInput } from '../types/notification';

export const getNotifications = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church || !req.user) throw new AppError('Invalid request context', 400);

  const filters: NotificationQueryInput = notificationQuerySchema.parse(req.query);

  const where = [
    eq(notifications.churchId, req.church.id),
    eq(notifications.memberId, req.user.id),
  ];
  if (filters.unreadOnly) where.push(eq(notifications.isRead, false));

  const offset = (filters.page - 1) * filters.limit;

  const [items, total] = await Promise.all([
    db.select().from(notifications).where(and(...where)).orderBy(desc(notifications.createdAt)).limit(filters.limit).offset(offset),
    db.select({ count: sql<number>`count(*)` }).from(notifications).where(and(...where)).then(r => r[0]?.count || 0),
  ]);

  res.json({
    status: 'success',
    data: {
      notifications: items,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total,
        totalPages: Math.ceil(total / filters.limit),
      }
    }
  });
});

export const getUnreadCount = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church || !req.user) throw new AppError('Invalid request context', 400);

  const count = await db.select({ count: sql<number>`count(*)` })
    .from(notifications)
    .where(and(
      eq(notifications.churchId, req.church.id),
      eq(notifications.memberId, req.user.id),
      eq(notifications.isRead, false)
    ))
    .then(r => r[0]?.count || 0);

  res.json({ status: 'success', data: { count } });
});

export const markAsRead = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church || !req.user) throw new AppError('Invalid request context', 400);

  const payload: MarkReadInput = markReadSchema.parse(req.body);

  const whereBase = and(
    eq(notifications.churchId, req.church.id),
    eq(notifications.memberId, req.user.id),
    eq(notifications.isRead, false)
  );

  const where = payload.notificationIds && payload.notificationIds.length > 0
    ? and(whereBase, inArray(notifications.id, payload.notificationIds))
    : whereBase;

  const [result] = await db.update(notifications)
    .set({ isRead: true, readAt: new Date() })
    .where(where)
    .returning({ updated: notifications.id });

  res.json({ status: 'success', message: 'Notifications marked as read' });
});
