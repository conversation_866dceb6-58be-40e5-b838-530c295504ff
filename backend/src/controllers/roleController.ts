import type { Response } from 'express';
import { db } from '../db';
import { roles } from '../db/schema';
import { createRoleSchema, updateRoleSchema } from '../types/role';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and } from 'drizzle-orm';
import type { AuthenticatedRequest } from '../middleware/auth';

export const createRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData = createRoleSchema.parse(req.body);

  const [role] = await db.insert(roles).values({
    ...validatedData,
    churchId: req.user!.churchId,
  }).returning();

  res.status(201).json({
    status: 'success',
    data: { role }
  });
});

export const getRoles = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const churchRoles = await db.query.roles.findMany({
    where: eq(roles.churchId, req.user!.churchId)
  });

  res.json({
    status: 'success',
    data: { roles: churchRoles }
  });
});

export const getRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Role ID is required', 400);
  }

  const role = await db.query.roles.findFirst({
    where: and(
      eq(roles.id, id),
      eq(roles.churchId, req.user!.churchId)
    )
  });

  if (!role) {
    throw new AppError('Role not found', 404);
  }

  res.json({
    status: 'success',
    data: { role }
  });
});

export const updateRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Role ID is required', 400);
  }

  const validatedData = updateRoleSchema.parse(req.body);

  const [updatedRole] = await db
    .update(roles)
    .set({ ...validatedData, updatedAt: new Date() })
    .where(and(
      eq(roles.id, id),
      eq(roles.churchId, req.user!.churchId)
    ))
    .returning();

  if (!updatedRole) {
    throw new AppError('Role not found', 404);
  }

  res.json({
    status: 'success',
    data: { role: updatedRole }
  });
});

export const deleteRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Role ID is required', 400);
  }

  const role = await db.query.roles.findFirst({
    where: and(
      eq(roles.id, id),
      eq(roles.churchId, req.user!.churchId)
    )
  });

  if (!role) {
    throw new AppError('Role not found', 404);
  }

  if (role.isSystem) {
    throw new AppError('Cannot delete system role', 400);
  }

  await db.delete(roles).where(eq(roles.id, id));

  res.json({
    status: 'success',
    message: 'Role deleted successfully'
  });
});