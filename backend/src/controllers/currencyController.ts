import type { Request, Response } from 'express';
import { catchAsync } from '../utils/errorHandler';

const currencies = [
  { code: 'USD', name: 'US Dollar', symbol: '$', isActive: true },
  { code: 'ZM<PERSON>', name: 'Zambian <PERSON>', symbol: 'ZK', isActive: true },
  { code: 'EUR', name: 'Euro', symbol: '€', isActive: true },
  { code: 'GBP', name: 'British Pound', symbol: '£', isActive: true },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', isActive: true },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', isActive: true },
  { code: 'UGX', name: 'Ugandan <PERSON>', symbol: 'USh', isActive: true },
  { code: 'TZS', name: 'Tanzanian <PERSON>', symbol: 'TSh', isActive: true },
];

export const getCurrencies = catchAsync(async (req: Request, res: Response) => {
  res.json({
    status: 'success',
    data: { currencies }
  });
});