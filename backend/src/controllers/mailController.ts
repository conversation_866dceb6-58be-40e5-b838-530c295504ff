import { Request, Response } from 'express';
import { z } from 'zod';
import { MailService } from '../services/mailService.js';
import { verifyMailConnection } from '../config/mail.js';

// Validation schemas
const sendEmailSchema = z.object({
  to: z.union([z.string().email(), z.array(z.string().email())]),
  subject: z.string().min(1),
  html: z.string().min(1),
  text: z.string().optional(),
});

const bulkEmailSchema = z.object({
  recipients: z.array(z.string().email()).min(1),
  subject: z.string().min(1),
  html: z.string().min(1),
});

const testEmailSchema = z.object({
  email: z.string().email(),
});

/**
 * @swagger
 * /api/mail/test:
 *   post:
 *     summary: Test email configuration
 *     tags: [Mail]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address to send test email to
 *             required:
 *               - email
 *     responses:
 *       200:
 *         description: Test email sent successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Failed to send test email
 */
export const sendTestEmail = async (req: Request, res: Response) => {
  try {
    const { email } = testEmailSchema.parse(req.body);

    const success = await MailService.sendEmail({
      to: email,
      subject: 'Test Email - Church Management System',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Email Configuration Test</h2>
          <p>Congratulations! Your email configuration is working correctly.</p>
          <p>This is a test email sent from your Church Management System.</p>
          <p>Timestamp: ${new Date().toISOString()}</p>
        </div>
      `,
    });

    if (success) {
      res.json({
        success: true,
        message: 'Test email sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to send test email',
      });
    }
  } catch (error) {
    console.error('Test email error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid request data',
      error: error instanceof z.ZodError ? error.errors : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/mail/send:
 *   post:
 *     summary: Send a single email
 *     tags: [Mail]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               to:
 *                 oneOf:
 *                   - type: string
 *                     format: email
 *                   - type: array
 *                     items:
 *                       type: string
 *                       format: email
 *               subject:
 *                 type: string
 *               html:
 *                 type: string
 *               text:
 *                 type: string
 *             required:
 *               - to
 *               - subject
 *               - html
 *     responses:
 *       200:
 *         description: Email sent successfully
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Failed to send email
 */
export const sendEmail = async (req: Request, res: Response) => {
  try {
    const emailData = sendEmailSchema.parse(req.body);

    const success = await MailService.sendEmail(emailData);

    if (success) {
      res.json({
        success: true,
        message: 'Email sent successfully',
      });
    } else {
      res.status(500).json({
        success: false,
        message: 'Failed to send email',
      });
    }
  } catch (error) {
    console.error('Send email error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid request data',
      error: error instanceof z.ZodError ? error.errors : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/churches/{slug}/mail/bulk:
 *   post:
 *     summary: Send bulk email to multiple recipients
 *     tags: [Mail]
 *     parameters:
 *       - in: path
 *         name: slug
 *         required: true
 *         schema:
 *           type: string
 *         description: Church slug
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               recipients:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: email
 *               subject:
 *                 type: string
 *               html:
 *                 type: string
 *             required:
 *               - recipients
 *               - subject
 *               - html
 *     responses:
 *       200:
 *         description: Bulk email sent with results
 *       400:
 *         description: Invalid request data
 *       500:
 *         description: Failed to send bulk email
 */
export const sendBulkEmail = async (req: Request, res: Response) => {
  try {
    const { recipients, subject, html } = bulkEmailSchema.parse(req.body);
    // slug is available via req.params.slug if needed in future

    // Get church name from request context (set by tenant middleware)
    const churchName = (req as any).church?.name || 'Church';

    const result = await MailService.sendBulkEmail(recipients, subject, html, churchName);

    res.json({
      success: true,
      message: 'Bulk email completed',
      results: {
        total: recipients.length,
        successful: result.success,
        failed: result.failed,
      },
    });
  } catch (error) {
    console.error('Bulk email error:', error);
    res.status(400).json({
      success: false,
      message: 'Invalid request data',
      error: error instanceof z.ZodError ? error.errors : 'Unknown error',
    });
  }
};

/**
 * @swagger
 * /api/mail/status:
 *   get:
 *     summary: Check email service status
 *     tags: [Mail]
 *     responses:
 *       200:
 *         description: Email service status
 */
export const getMailStatus = async (req: Request, res: Response) => {
  try {
    const isConnected = await verifyMailConnection();

    res.json({
      success: true,
      status: isConnected ? 'connected' : 'disconnected',
      message: isConnected 
        ? 'Email service is working correctly' 
        : 'Email service connection failed',
    });
  } catch (error) {
    console.error('Mail status error:', error);
    res.status(500).json({
      success: false,
      status: 'error',
      message: 'Failed to check email service status',
    });
  }
};