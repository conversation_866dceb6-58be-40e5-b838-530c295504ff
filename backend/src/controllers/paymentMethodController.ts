import type { Response } from 'express';
import { db } from '../db';
import { paymentMethods } from '../db/schema/paymentMethods';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and } from 'drizzle-orm';
import type { TenantRequest } from '../middleware/tenant';
import { z } from 'zod';

const createPaymentMethodSchema = z.object({
  name: z.string().min(1, 'Name is required').max(255),
  type: z.enum(['mobile_money', 'bank_card', 'bank_transfer', 'cash']),
  provider: z.enum(['airtel', 'mtn', 'zamtel', 'visa', 'mastercard']).optional(),
  accountNumber: z.string().max(100).optional(),
  accountName: z.string().max(255).optional(),
  bankName: z.string().max(255).optional(),
  phoneNumber: z.string().max(20).optional(),
  isDefault: z.boolean().default(false),
});

const updatePaymentMethodSchema = createPaymentMethodSchema.partial();

export const createPaymentMethod = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData = createPaymentMethodSchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  // If this is being set as default, unset other defaults
  if (validatedData.isDefault) {
    await db
      .update(paymentMethods)
      .set({ isDefault: false, updatedAt: new Date() })
      .where(eq(paymentMethods.churchId, req.church.id));
  }

  const [paymentMethod] = await db.insert(paymentMethods).values({
    ...validatedData,
    churchId: req.church.id,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Payment method created successfully',
    data: { paymentMethod }
  });
});

export const getPaymentMethods = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const methods = await db.query.paymentMethods.findMany({
    where: and(
      eq(paymentMethods.churchId, req.church.id),
      eq(paymentMethods.isActive, true)
    ),
    orderBy: [paymentMethods.isDefault, paymentMethods.name]
  });

  res.json({
    status: 'success',
    data: { paymentMethods: methods }
  });
});

export const getPaymentMethod = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Payment method ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const paymentMethod = await db.query.paymentMethods.findFirst({
    where: and(
      eq(paymentMethods.id, id),
      eq(paymentMethods.churchId, req.church.id)
    )
  });

  if (!paymentMethod) {
    throw new AppError('Payment method not found', 404);
  }

  res.json({
    status: 'success',
    data: { paymentMethod }
  });
});

export const updatePaymentMethod = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;
  const validatedData = updatePaymentMethodSchema.parse(req.body);

  if (!id) {
    throw new AppError('Payment method ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingMethod = await db.query.paymentMethods.findFirst({
    where: and(
      eq(paymentMethods.id, id),
      eq(paymentMethods.churchId, req.church.id)
    )
  });

  if (!existingMethod) {
    throw new AppError('Payment method not found', 404);
  }

  // If this is being set as default, unset other defaults
  if (validatedData.isDefault) {
    await db
      .update(paymentMethods)
      .set({ isDefault: false, updatedAt: new Date() })
      .where(and(
        eq(paymentMethods.churchId, req.church.id),
        eq(paymentMethods.isActive, true)
      ));
  }

  const [updatedMethod] = await db
    .update(paymentMethods)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(paymentMethods.id, id))
    .returning();

  res.json({
    status: 'success',
    message: 'Payment method updated successfully',
    data: { paymentMethod: updatedMethod }
  });
});

export const deletePaymentMethod = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Payment method ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingMethod = await db.query.paymentMethods.findFirst({
    where: and(
      eq(paymentMethods.id, id),
      eq(paymentMethods.churchId, req.church.id)
    )
  });

  if (!existingMethod) {
    throw new AppError('Payment method not found', 404);
  }

  // Soft delete by setting isActive to false
  await db
    .update(paymentMethods)
    .set({ 
      isActive: false, 
      isDefault: false,
      updatedAt: new Date() 
    })
    .where(eq(paymentMethods.id, id));

  res.json({
    status: 'success',
    message: 'Payment method deleted successfully'
  });
});

export const setDefaultPaymentMethod = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Payment method ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingMethod = await db.query.paymentMethods.findFirst({
    where: and(
      eq(paymentMethods.id, id),
      eq(paymentMethods.churchId, req.church.id),
      eq(paymentMethods.isActive, true)
    )
  });

  if (!existingMethod) {
    throw new AppError('Payment method not found', 404);
  }

  // Unset all other defaults
  await db
    .update(paymentMethods)
    .set({ isDefault: false, updatedAt: new Date() })
    .where(and(
      eq(paymentMethods.churchId, req.church.id),
      eq(paymentMethods.isActive, true)
    ));

  // Set this one as default
  const [updatedMethod] = await db
    .update(paymentMethods)
    .set({ 
      isDefault: true,
      updatedAt: new Date() 
    })
    .where(eq(paymentMethods.id, id))
    .returning();

  res.json({
    status: 'success',
    message: 'Default payment method set successfully',
    data: { paymentMethod: updatedMethod }
  });
});