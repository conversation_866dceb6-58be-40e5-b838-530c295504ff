import type { Response } from 'express';
import { db } from '../db';
import { events, eventRsvps, eventLikes, eventComments } from '../db/schema';
import {
  createEventSchema,
  updateEventSchema,
  createRsvpSchema,
  updateRsvpSchema,
  eventFilterSchema,
  createCommentSchema,
  updateCommentSchema,
  commentFilterSchema,
  type CreateEventInput,
  type UpdateEventInput,
  type CreateRsvpInput,
  type UpdateRsvpInput,
  type EventFilterInput,
  type CreateCommentInput,
  type UpdateCommentInput,
  type CommentFilterInput
} from '../types/event';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, gte, lte, like, or, desc, sql } from 'drizzle-orm';
import type { TenantRequest } from '../middleware/tenant';

export const createEvent = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: CreateEventInput = createEventSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const [event] = await db.insert(events).values({
    ...validatedData,
    churchId: req.church.id,
    createdBy: req.user.id,
    startDate: new Date(validatedData.startDate),
    endDate: validatedData.endDate ? new Date(validatedData.endDate) : undefined,
    recurrencePattern: validatedData.recurrencePattern ? JSON.stringify(validatedData.recurrencePattern) : undefined,
    tags: JSON.stringify(validatedData.tags),
    donationGoal: validatedData.donationGoal ? validatedData.donationGoal.toString() : undefined,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Event created successfully',
    data: { event }
  });
});

export const getEvents = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: EventFilterInput = eventFilterSchema.parse(req.query);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [eq(events.churchId, req.church.id)];

  if (filters.type) {
    whereConditions.push(eq(events.type, filters.type));
  }

  if (filters.status) {
    whereConditions.push(eq(events.status, filters.status));
  }

  if (filters.startDate) {
    whereConditions.push(gte(events.startDate, new Date(filters.startDate)));
  }

  if (filters.endDate) {
    whereConditions.push(lte(events.startDate, new Date(filters.endDate)));
  }

  if (filters.isPublic !== undefined) {
    whereConditions.push(eq(events.isPublic, filters.isPublic));
  }

  if (filters.requiresRsvp !== undefined) {
    whereConditions.push(eq(events.requiresRsvp, filters.requiresRsvp));
  }

  if (filters.allowDonations !== undefined) {
    whereConditions.push(eq(events.allowDonations, filters.allowDonations));
  }

  if (filters.upcoming) {
    whereConditions.push(gte(events.startDate, new Date()));
  }

  if (filters.search) {
    const searchCondition = or(
      like(events.title, `%${filters.search}%`),
      like(events.description, `%${filters.search}%`),
      like(events.location, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  const offset = (filters.page - 1) * filters.limit;

  const [eventList, totalCount] = await Promise.all([
    db.query.events.findMany({
      where: and(...whereConditions),
      with: {
        createdBy: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        rsvps: {
          with: {
            member: {
              columns: {
                id: true,
                firstName: true,
                lastName: true,
              }
            }
          }
        },
        likes: {
          columns: {
            id: true,
            memberId: true,
          }
        },
        comments: {
          where: eq(eventComments.parentCommentId, null),
          columns: {
            id: true,
          }
        }
      },
      orderBy: [desc(events.startDate)],
      limit: filters.limit,
      offset,
    }),
    db.select({ count: sql<number>`count(*)` })
      .from(events)
      .where(and(...whereConditions))
      .then(result => result[0]?.count || 0)
  ]);

  const eventsWithStats = eventList.map(event => ({
    ...event,
    tags: event.tags ? JSON.parse(event.tags as string) : [],
    recurrencePattern: event.recurrencePattern ? JSON.parse(event.recurrencePattern as string) : null,
    rsvpStats: {
      total: event.rsvps.length,
      attending: event.rsvps.filter(r => r.status === 'attending').length,
      notAttending: event.rsvps.filter(r => r.status === 'not_attending').length,
      maybe: event.rsvps.filter(r => r.status === 'maybe').length,
      pending: event.rsvps.filter(r => r.status === 'pending').length,
    },
    socialStats: {
      likesCount: event.likes?.length || 0,
      commentsCount: event.comments?.length || 0,
      userLiked: req.user ? event.likes?.some(like => like.memberId === req.user!.id) || false : false,
      userRsvp: req.user ? event.rsvps.find(rsvp => rsvp.memberId === req.user!.id) : undefined,
    }
  }));

  res.json({
    status: 'success',
    data: {
      events: eventsWithStats,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: totalCount,
        pages: Math.ceil(totalCount / filters.limit)
      }
    }
  });
});

export const getEvent = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, id),
      eq(events.churchId, req.church.id)
    ),
    with: {
      createdBy: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      rsvps: {
        with: {
          member: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              email: true,
            }
          }
        },
        orderBy: [desc(eventRsvps.createdAt)]
      },
      likes: {
        with: {
          member: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              profileImage: true,
            }
          }
        }
      },
      comments: {
        where: eq(eventComments.parentCommentId, null),
        with: {
          member: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              profileImage: true,
            }
          },
          replies: {
            with: {
              member: {
                columns: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  profileImage: true,
                }
              }
            },
            orderBy: [eventComments.createdAt]
          }
        },
        orderBy: [desc(eventComments.createdAt)],
        limit: 5 // Show only first 5 comments, user can load more
      }
    }
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  const eventWithParsedData = {
    ...event,
    tags: event.tags ? JSON.parse(event.tags as string) : [],
    recurrencePattern: event.recurrencePattern ? JSON.parse(event.recurrencePattern as string) : null,
    rsvpStats: {
      total: event.rsvps.length,
      attending: event.rsvps.filter(r => r.status === 'attending').length,
      notAttending: event.rsvps.filter(r => r.status === 'not_attending').length,
      maybe: event.rsvps.filter(r => r.status === 'maybe').length,
      pending: event.rsvps.filter(r => r.status === 'pending').length,
    },
    socialStats: {
      likesCount: event.likes?.length || 0,
      commentsCount: event.comments?.length || 0,
      userLiked: req.user ? event.likes?.some(like => like.memberId === req.user!.id) || false : false,
      userRsvp: req.user ? event.rsvps.find(rsvp => rsvp.memberId === req.user!.id) : undefined,
    }
  };

  res.json({
    status: 'success',
    data: { event: eventWithParsedData }
  });
});

export const updateEvent = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;
  const validatedData: UpdateEventInput = updateEventSchema.parse(req.body);

  if (!id) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const existingEvent = await db.query.events.findFirst({
    where: and(
      eq(events.id, id),
      eq(events.churchId, req.church.id)
    )
  });

  if (!existingEvent) {
    throw new AppError('Event not found', 404);
  }

  const updateData: any = {
    ...validatedData,
    updatedAt: new Date(),
  };

  if (validatedData.startDate) {
    updateData.startDate = new Date(validatedData.startDate);
  }

  if (validatedData.endDate) {
    updateData.endDate = new Date(validatedData.endDate);
  }

  if (validatedData.recurrencePattern) {
    updateData.recurrencePattern = JSON.stringify(validatedData.recurrencePattern);
  }

  if (validatedData.tags) {
    updateData.tags = JSON.stringify(validatedData.tags);
  }

  const [updatedEvent] = await db
    .update(events)
    .set(updateData)
    .where(eq(events.id, id))
    .returning();

  res.json({
    status: 'success',
    message: 'Event updated successfully',
    data: { event: updatedEvent }
  });
});

export const deleteEvent = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingEvent = await db.query.events.findFirst({
    where: and(
      eq(events.id, id),
      eq(events.churchId, req.church.id)
    )
  });

  if (!existingEvent) {
    throw new AppError('Event not found', 404);
  }

  await db.delete(events).where(eq(events.id, id));

  res.json({
    status: 'success',
    message: 'Event deleted successfully'
  });
});

export const createRsvp = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;
  const validatedData: CreateRsvpInput = createRsvpSchema.parse(req.body);

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, eventId),
      eq(events.churchId, req.church.id)
    )
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  if (!event.requiresRsvp) {
    throw new AppError('This event does not require RSVP', 400);
  }

  const existingRsvp = await db.query.eventRsvps.findFirst({
    where: and(
      eq(eventRsvps.eventId, eventId),
      eq(eventRsvps.memberId, req.user.id)
    )
  });

  if (existingRsvp) {
    throw new AppError('You have already RSVP\'d to this event', 400);
  }

  const [rsvp] = await db.insert(eventRsvps).values({
    eventId,
    memberId: req.user.id,
    status: validatedData.status,
    attendeeCount: validatedData.attendeeCount,
    notes: validatedData.notes,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'RSVP created successfully',
    data: { rsvp }
  });
});

export const updateRsvp = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;
  const validatedData: UpdateRsvpInput = updateRsvpSchema.parse(req.body);

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const existingRsvp = await db.query.eventRsvps.findFirst({
    where: and(
      eq(eventRsvps.eventId, eventId),
      eq(eventRsvps.memberId, req.user.id)
    ),
    with: {
      event: true
    }
  });

  if (!existingRsvp) {
    throw new AppError('RSVP not found', 404);
  }

  if ((existingRsvp.event as any).churchId !== req.church.id) {
    throw new AppError('Access denied', 403);
  }

  const [updatedRsvp] = await db
    .update(eventRsvps)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(eq(eventRsvps.id, existingRsvp.id))
    .returning();

  res.json({
    status: 'success',
    message: 'RSVP updated successfully',
    data: { rsvp: updatedRsvp }
  });
});

export const deleteRsvp = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const existingRsvp = await db.query.eventRsvps.findFirst({
    where: and(
      eq(eventRsvps.eventId, eventId),
      eq(eventRsvps.memberId, req.user.id)
    ),
    with: {
      event: true
    }
  });

  if (!existingRsvp) {
    throw new AppError('RSVP not found', 404);
  }

  if ((existingRsvp.event as any).churchId !== req.church.id) {
    throw new AppError('Access denied', 403);
  }

  await db.delete(eventRsvps).where(eq(eventRsvps.id, existingRsvp.id));

  res.json({
    status: 'success',
    message: 'RSVP deleted successfully'
  });
});

// Event Likes
export const toggleEventLike = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, eventId),
      eq(events.churchId, req.church.id)
    )
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  const existingLike = await db.query.eventLikes.findFirst({
    where: and(
      eq(eventLikes.eventId, eventId),
      eq(eventLikes.memberId, req.user.id)
    )
  });

  if (existingLike) {
    // Unlike the event
    await db.delete(eventLikes).where(eq(eventLikes.id, existingLike.id));
    
    res.json({
      status: 'success',
      message: 'Event unliked successfully',
      data: { liked: false }
    });
  } else {
    // Like the event
    const [like] = await db.insert(eventLikes).values({
      eventId,
      memberId: req.user.id,
    }).returning();

    res.json({
      status: 'success',
      message: 'Event liked successfully',
      data: { liked: true, like }
    });
  }
});

export const getEventLikes = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, eventId),
      eq(events.churchId, req.church.id)
    )
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  const likes = await db.query.eventLikes.findMany({
    where: eq(eventLikes.eventId, eventId),
    with: {
      member: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        }
      }
    },
    orderBy: [desc(eventLikes.createdAt)]
  });

  res.json({
    status: 'success',
    data: { 
      likes,
      count: likes.length,
      userLiked: req.user ? likes.some(like => like.memberId === req.user!.id) : false
    }
  });
});

// Event Comments
export const createEventComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;
  const validatedData: CreateCommentInput = createCommentSchema.parse(req.body);

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, eventId),
      eq(events.churchId, req.church.id)
    )
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  // If replying to a comment, verify parent comment exists
  if (validatedData.parentCommentId) {
    const parentComment = await db.query.eventComments.findFirst({
      where: and(
        eq(eventComments.id, validatedData.parentCommentId),
        eq(eventComments.eventId, eventId)
      )
    });

    if (!parentComment) {
      throw new AppError('Parent comment not found', 404);
    }
  }

  const [comment] = await db.insert(eventComments).values({
    eventId,
    memberId: req.user.id,
    content: validatedData.content,
    parentCommentId: validatedData.parentCommentId,
  }).returning();

  // Fetch the comment with member details
  const commentWithMember = await db.query.eventComments.findFirst({
    where: eq(eventComments.id, comment.id),
    with: {
      member: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        }
      }
    }
  });

  res.status(201).json({
    status: 'success',
    message: 'Comment created successfully',
    data: { comment: commentWithMember }
  });
});

export const getEventComments = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId } = req.params;
  const filters: CommentFilterInput = commentFilterSchema.parse(req.query);

  if (!eventId) {
    throw new AppError('Event ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const event = await db.query.events.findFirst({
    where: and(
      eq(events.id, eventId),
      eq(events.churchId, req.church.id)
    )
  });

  if (!event) {
    throw new AppError('Event not found', 404);
  }

  const offset = (filters.page - 1) * filters.limit;

  // Get top-level comments (no parent)
  const [comments, totalCount] = await Promise.all([
    db.query.eventComments.findMany({
      where: and(
        eq(eventComments.eventId, eventId),
        eq(eventComments.parentCommentId, null)
      ),
      with: {
        member: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            profileImage: true,
          }
        },
        replies: {
          with: {
            member: {
              columns: {
                id: true,
                firstName: true,
                lastName: true,
                profileImage: true,
              }
            }
          },
          orderBy: [eventComments.createdAt]
        }
      },
      orderBy: [desc(eventComments.createdAt)],
      limit: filters.limit,
      offset,
    }),
    db.select({ count: sql<number>`count(*)` })
      .from(eventComments)
      .where(and(
        eq(eventComments.eventId, eventId),
        eq(eventComments.parentCommentId, null)
      ))
      .then(result => result[0]?.count || 0)
  ]);

  res.json({
    status: 'success',
    data: {
      comments,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: totalCount,
        pages: Math.ceil(totalCount / filters.limit)
      }
    }
  });
});

export const updateEventComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId, commentId } = req.params;
  const validatedData: UpdateCommentInput = updateCommentSchema.parse(req.body);

  if (!eventId || !commentId) {
    throw new AppError('Event ID and Comment ID are required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const comment = await db.query.eventComments.findFirst({
    where: and(
      eq(eventComments.id, commentId),
      eq(eventComments.eventId, eventId),
      eq(eventComments.memberId, req.user.id)
    ),
    with: {
      event: true
    }
  });

  if (!comment) {
    throw new AppError('Comment not found or access denied', 404);
  }

  if ((comment.event as any).churchId !== req.church.id) {
    throw new AppError('Access denied', 403);
  }

  const [updatedComment] = await db
    .update(eventComments)
    .set({
      content: validatedData.content,
      updatedAt: new Date(),
    })
    .where(eq(eventComments.id, commentId))
    .returning();

  res.json({
    status: 'success',
    message: 'Comment updated successfully',
    data: { comment: updatedComment }
  });
});

export const deleteEventComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id: eventId, commentId } = req.params;

  if (!eventId || !commentId) {
    throw new AppError('Event ID and Comment ID are required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const comment = await db.query.eventComments.findFirst({
    where: and(
      eq(eventComments.id, commentId),
      eq(eventComments.eventId, eventId),
      eq(eventComments.memberId, req.user.id)
    ),
    with: {
      event: true
    }
  });

  if (!comment) {
    throw new AppError('Comment not found or access denied', 404);
  }

  if ((comment.event as any).churchId !== req.church.id) {
    throw new AppError('Access denied', 403);
  }

  await db.delete(eventComments).where(eq(eventComments.id, commentId));

  res.json({
    status: 'success',
    message: 'Comment deleted successfully'
  });
});