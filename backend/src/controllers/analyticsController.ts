import type { Response } from 'express';
import { db } from '../db';
import { churches, members, events, donations, branches, finances } from '../db/schema';
import {
  analyticsQuerySchema,
  type AnalyticsQueryInput,
  type DashboardOverview,
  type MemberAnalytics,
  type EventAnalytics,
  type FinancialAnalytics,
  type BranchAnalytics
} from '../types/analytics';
import { catchAsync, AppError } from '../utils/errorHandler';
import type { AuthenticatedRequest } from '../middleware/auth';
import { eq, and, gte, count, sum, desc, sql } from 'drizzle-orm';

const getPeriodFilter = (period: string, startDate?: string, endDate?: string) => {
  if (startDate && endDate) {
    return {
      gte: new Date(startDate),
      lte: new Date(endDate)
    };
  }

  const now = new Date();
  let start: Date;

  switch (period) {
    case '7d':
      start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      break;
    case '30d':
      start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      break;
    case '90d':
      start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      break;
    case '1y':
      start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      break;
    default:
      return null;
  }

  return {
    gte: start,
    lte: now
  };
};

export const getMemberAnalytics = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const query: AnalyticsQueryInput = analyticsQuerySchema.parse(req.query);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const periodFilter = getPeriodFilter(query.period, query.startDate, query.endDate);
  const churchFilter = eq(members.churchId, church.id);
  const branchFilter = query.branchId ? eq(members.branchId, query.branchId) : undefined;


  const [
    totalMembersResult,
    membersByStatus,
    membersByGender,
    membersByBranch
  ] = await Promise.all([
    db.select({ count: count() }).from(members).where(and(churchFilter, branchFilter)),

    db.select({
      status: members.status,
      count: count()
    }).from(members)
      .where(and(churchFilter, branchFilter))
      .groupBy(members.status),

    db.select({
      gender: members.gender,
      count: count()
    }).from(members)
      .where(and(churchFilter, branchFilter))
      .groupBy(members.gender),

    db.select({
      branchId: members.branchId,
      branchName: sql<string>`COALESCE(${branches.name}, 'No Branch')`,
      count: count()
    }).from(members)
      .leftJoin(branches, eq(members.branchId, branches.id))
      .where(churchFilter)
      .groupBy(members.branchId, branches.name)
  ]);

  const totalMembers = totalMembersResult[0]?.count || 0;

  // Member growth trend: new members per bucket and cumulative totals
  const bucketExprMembers = query.period === '1y'
    ? sql`date_trunc('month', ${members.joinDate})`
    : sql`date_trunc('day', ${members.joinDate})`;

  const memberTrendQuery = periodFilter
    ? sql`SELECT 
        ${bucketExprMembers} AS bucket,
        COUNT(*) AS new_count
      FROM ${members}
      WHERE ${members.churchId} = ${church.id}
      ${branchFilter ? sql`AND ${members.branchId} = ${query.branchId}` : sql``}
      AND ${members.joinDate} >= ${periodFilter.gte}
      GROUP BY bucket
      ORDER BY bucket ASC`
    : sql`SELECT 
        ${bucketExprMembers} AS bucket,
        COUNT(*) AS new_count
      FROM ${members}
      WHERE ${members.churchId} = ${church.id}
      ${branchFilter ? sql`AND ${members.branchId} = ${query.branchId}` : sql``}
      GROUP BY bucket
      ORDER BY bucket ASC`;

  const memberTrendRows = (await db.execute(memberTrendQuery)).rows as Array<{ bucket: string; new_count: string | number }>

  let runningTotal = 0;
  const memberGrowthTrend = memberTrendRows.map(row => {
    const newCount = Number(row.new_count || 0)
    const periodStr = new Date(row.bucket as unknown as string).toISOString().slice(0, 10)
    const growthRate = runningTotal > 0 ? Number(((newCount / runningTotal) * 100).toFixed(2)) : 0
    runningTotal += newCount
    return {
      period: periodStr,
      newMembers: newCount,
      totalMembers: runningTotal,
      growthRate
    }
  })

  const analytics: MemberAnalytics = {
    totalMembers,
    membersByStatus: membersByStatus.map(item => ({
      status: item.status || 'unknown',
      count: item.count,
      percentage: Math.round((item.count / totalMembers) * 100)
    })),
    membersByGender: membersByGender.map(item => ({
      gender: item.gender || 'not_specified',
      count: item.count,
      percentage: Math.round((item.count / totalMembers) * 100)
    })),
    membersByAgeGroup: [], // TODO: Calculate age groups from dateOfBirth
    membersByBranch: membersByBranch.map(item => ({
      branchId: item.branchId,
      branchName: item.branchName,
      count: item.count,
      percentage: Math.round((item.count / totalMembers) * 100)
    })),
    memberGrowthTrend,
    topEngagedMembers: [] // TODO: Calculate engagement scores
  };

  res.json({
    status: 'success',
    data: { analytics }
  });
});

export const getDashboardOverview = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const query: AnalyticsQueryInput = analyticsQuerySchema.parse(req.query);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const churchId = church.id;
  const branchId = query.branchId;
  const periodFilter = getPeriodFilter(query.period, query.startDate, query.endDate);

  // totals
  const [memberCount, eventCount, donationCount, revenueResult] = await Promise.all([
    db.select({ count: count() }).from(members).where(and(eq(members.churchId, churchId), branchId ? eq(members.branchId, branchId) : undefined)),
    db.select({ count: count() }).from(events).where(and(eq(events.churchId, churchId), branchId ? eq(events.branchId, branchId) : undefined)),
    db.select({ count: count() }).from(donations).where(and(eq(donations.churchId, churchId), branchId ? eq(donations.branchId, branchId) : undefined)),
    db.select({ total: sum(donations.amount) }).from(donations).where(and(eq(donations.churchId, churchId), branchId ? eq(donations.branchId, branchId) : undefined, periodFilter ? gte(donations.donatedAt, periodFilter.gte) : undefined))
  ]);

  const totalMembers = memberCount[0]?.count || 0;
  const totalEvents = eventCount[0]?.count || 0;
  const totalDonations = donationCount[0]?.count || 0;
  const totalRevenue = Number(revenueResult[0]?.total || 0);

  // Simple growth rates (placeholder calculations)
  const memberGrowthRate = 0;
  const eventAttendanceRate = 0;
  const donationGrowthRate = 0;

  const overview = {
    totalMembers,
    totalEvents,
    totalDonations,
    totalRevenue,
    memberGrowthRate,
    eventAttendanceRate,
    donationGrowthRate,
    recentActivity: []
  } satisfies DashboardOverview;

  res.json({ status: 'success', data: { overview } });
});

export const getEventAnalytics = catchAsync(async (req: AuthenticatedRequest, res: Response) => {  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const query: AnalyticsQueryInput = analyticsQuerySchema.parse(req.query);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const periodFilter = getPeriodFilter(query.period, query.startDate, query.endDate);
  const churchFilter = eq(events.churchId, church.id);
  const branchFilter = query.branchId ? eq(events.branchId, query.branchId) : undefined;

  const [
    totalEventsResult,
    eventsByType,
    eventsByStatus,
    upcomingEvents
  ] = await Promise.all([
    db.select({ count: count() }).from(events).where(
      and(
        churchFilter,
        branchFilter,
        periodFilter ? gte(events.createdAt, periodFilter.gte) : undefined
      )
    ),

    db.select({
      type: events.type,
      count: count(),
      avgAttendance: sql<number>`COALESCE(AVG(rsvp_counts.total), 0)`
    }).from(events)
      .leftJoin(
        sql`(
          SELECT event_id, COUNT(*) as total 
          FROM event_rsvps 
          WHERE status = 'attending' 
          GROUP BY event_id
        ) as rsvp_counts`,
        sql`rsvp_counts.event_id = ${events.id}`
      )
      .where(and(churchFilter, branchFilter))
      .groupBy(events.type),

    db.select({
      status: events.status,
      count: count()
    }).from(events)
      .where(and(churchFilter, branchFilter))
      .groupBy(events.status),

    db.select({
      id: events.id,
      title: events.title,
      type: events.type,
      startDate: events.startDate,
      rsvpCount: sql<number>`COALESCE(rsvp_counts.total, 0)`
    }).from(events)
      .leftJoin(
        sql`(
          SELECT event_id, COUNT(*) as total 
          FROM event_rsvps 
          GROUP BY event_id
        ) as rsvp_counts`,
        sql`rsvp_counts.event_id = ${events.id}`
      )
      .where(and(
        churchFilter,
        branchFilter,
        gte(events.startDate, new Date())
      ))
      .orderBy(events.startDate)
      .limit(10)
  ]);

  const totalEvents = totalEventsResult[0]?.count || 0;

  // Event trends: bucket events and RSVPs
  const bucketExprEvents = query.period === '1y'
    ? sql`date_trunc('month', ${events.startDate})`
    : sql`date_trunc('day', ${events.startDate})`;

  const eventTrendsRows = (await db.execute(sql`SELECT 
      buckets.bucket,
      COALESCE(event_counts.event_count, 0) as event_count,
      COALESCE(rsvp_counts.attendance_count, 0) as attendance_count
    FROM (
      SELECT ${bucketExprEvents} AS bucket
      FROM ${events}
      WHERE ${events.churchId} = ${church.id}
      ${branchFilter ? sql`AND ${events.branchId} = ${query.branchId}` : sql``}
      ${periodFilter ? sql`AND ${events.startDate} >= ${periodFilter.gte}` : sql``}
      GROUP BY bucket
    ) buckets
    LEFT JOIN (
      SELECT ${bucketExprEvents} AS bucket, COUNT(*) AS event_count
      FROM ${events}
      WHERE ${events.churchId} = ${church.id}
      ${branchFilter ? sql`AND ${events.branchId} = ${query.branchId}` : sql``}
      ${periodFilter ? sql`AND ${events.startDate} >= ${periodFilter.gte}` : sql``}
      GROUP BY bucket
    ) event_counts ON event_counts.bucket = buckets.bucket
    LEFT JOIN (
      SELECT date_trunc(${query.period === '1y' ? sql`'month'` : sql`'day'`}, er.created_at) AS bucket, COUNT(*) AS attendance_count
      FROM event_rsvps er
      JOIN events e ON e.id = er.event_id
      WHERE e.church_id = ${church.id}
      ${branchFilter ? sql`AND e.branch_id = ${query.branchId}` : sql``}
      ${periodFilter ? sql`AND e.start_date >= ${periodFilter.gte}` : sql``}
      AND er.status = 'attending'
      GROUP BY bucket
    ) rsvp_counts ON rsvp_counts.bucket = buckets.bucket
    ORDER BY buckets.bucket ASC`)).rows as Array<{ bucket: string; event_count: string | number; attendance_count: string | number }>

  const eventTrends = eventTrendsRows.map(row => ({
    period: new Date(row.bucket as unknown as string).toISOString().slice(0, 10),
    eventCount: Number(row.event_count || 0),
    attendanceCount: Number(row.attendance_count || 0),
    averageAttendance: Number(row.event_count || 0) > 0 ? Number((Number(row.attendance_count || 0) / Number(row.event_count)).toFixed(2)) : 0
  }))

  const totalRsvps = eventTrends.reduce((sum, r) => sum + r.attendanceCount, 0)
  const averageAttendanceRate = totalEvents > 0 ? Number(((totalRsvps / totalEvents) * 100).toFixed(2)) : 0

  const analytics: EventAnalytics = {
    totalEvents,
    eventsByType: eventsByType.map(item => ({
      type: item.type,
      count: item.count,
      percentage: Math.round((item.count / totalEvents) * 100),
      averageAttendance: Number(item.avgAttendance || 0)
    })),
    eventsByStatus: eventsByStatus.map(item => ({
      status: item.status,
      count: item.count,
      percentage: Math.round((item.count / totalEvents) * 100)
    })),
    attendanceMetrics: {
      totalRsvps,
      averageAttendanceRate,
      mostPopularEventType: eventsByType.length > 0 ? eventsByType.sort((a,b)=>Number(b.count)-Number(a.count))[0]?.type || 'none' : 'none',
      peakAttendanceDay: eventTrends.length ? new Date(eventTrends.sort((a,b)=>b.attendanceCount-a.attendanceCount)[0].period).toLocaleDateString('en-US', { weekday: 'long' }) : 'Sunday'
    },
    upcomingEvents: upcomingEvents.map(event => ({
      id: event.id,
      title: event.title,
      type: event.type,
      startDate: new Date(event.startDate),
      rsvpCount: Number(event.rsvpCount),
      attendanceRate: undefined
    })),
    eventTrends
  };

  res.json({
    status: 'success',
    data: { analytics }
  });
});

export const getFinancialAnalytics = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const query: AnalyticsQueryInput = analyticsQuerySchema.parse(req.query);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const periodFilter = getPeriodFilter(query.period, query.startDate, query.endDate);
  const churchFilter = eq(donations.churchId, church.id);
  const branchFilter = query.branchId ? eq(donations.branchId, query.branchId) : undefined;

  const [
    totalRevenueResult,
    donationsByType,
    donationsByMethod,
    topDonors,
    trends
  ] = await Promise.all([
    db.select({ total: sum(finances.amount) }).from(finances).where(
      and(
        eq(finances.churchId, church.id),
        branchFilter,
        periodFilter ? gte(finances.recordedAt, periodFilter.gte) : undefined
      )
    ),

    db.select({
      type: finances.type,
      count: count(),
      totalAmount: sum(finances.amount)
    }).from(finances)
      .where(and(eq(finances.churchId, church.id), branchFilter))
      .groupBy(finances.type),

    db.select({
      method: finances.method,
      count: count(),
      totalAmount: sum(finances.amount)
    }).from(finances)
      .where(and(eq(finances.churchId, church.id), branchFilter))
      .groupBy(finances.method),

    db.select({
      donorId: finances.memberId,
      donorName: sql<string>`COALESCE(CONCAT(${members.firstName}, ' ', ${members.lastName}), ${finances.donorName})`,
      totalDonations: sum(finances.amount),
      donationCount: count(),
      isAnonymous: sql<boolean>`COALESCE(${finances.isAnonymous}, false)`
    }).from(finances)
      .leftJoin(members, eq(finances.memberId, members.id))
      .where(and(eq(finances.churchId, church.id), branchFilter))
      .groupBy(finances.memberId, members.firstName, members.lastName, finances.donorName, finances.isAnonymous)
      .orderBy(desc(sum(finances.amount)))
      .limit(10),

    // Trends: bucket by day for 7d/30d/90d, by month for 1y
    db.execute(sql`SELECT 
        ${query.period === '1y' ? sql`date_trunc('month', ${finances.recordedAt})` : sql`date_trunc('day', ${finances.recordedAt})`} as bucket,
        SUM(${finances.amount}) as total_amount,
        COUNT(*) as total_count
      FROM ${finances}
      WHERE ${finances.churchId} = ${church.id}
      ${branchFilter ? sql`AND ${finances.branchId} = ${query.branchId}` : sql``}
      ${periodFilter ? sql`AND ${finances.recordedAt} >= ${periodFilter.gte}` : sql``}
      GROUP BY bucket
      ORDER BY bucket ASC`)
  ]);

  const totalRevenue = Number(totalRevenueResult[0]?.total || 0);

  const analytics: FinancialAnalytics = {
    totalRevenue,
    donationsByType: donationsByType.map(item => ({
      type: item.type,
      count: item.count,
      totalAmount: Number(item.totalAmount || 0),
      percentage: Math.round((Number(item.totalAmount || 0) / totalRevenue) * 100)
    })),
    donationsByMethod: donationsByMethod.map(item => ({
      method: item.method,
      count: item.count,
      totalAmount: Number(item.totalAmount || 0),
      percentage: Math.round((Number(item.totalAmount || 0) / totalRevenue) * 100)
    })),
    donationTrends: Array.isArray(trends as any)
      ? (trends as any).map((row: any) => ({
          period: new Date(row.bucket).toISOString().slice(0, 10),
          amount: Number(row.total_amount || 0),
          count: Number(row.total_count || 0),
          date: new Date(row.bucket)
        }))
      : [],
    topDonors: topDonors.map(donor => ({
      donorId: donor.donorId,
      donorName: donor.isAnonymous ? 'Anonymous' : donor.donorName,
      totalDonations: Number(donor.totalDonations),
      donationCount: donor.donationCount,
      isAnonymous: donor.isAnonymous
    })),
    fundraisingGoals: [] // TODO: Calculate fundraising goals from events
  };

  res.json({
    status: 'success',
    data: { analytics }
  });
});

export const getBranchAnalytics = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const [
    totalBranchesResult,
    branchComparison
  ] = await Promise.all([
    db.select({ count: count() }).from(branches).where(eq(branches.churchId, church.id)),

    db.select({
      branchId: branches.id,
      branchName: branches.name,
      memberCount: sql<number>`COALESCE(member_counts.total, 0)`,
      eventCount: sql<number>`COALESCE(event_counts.total, 0)`,
      totalDonations: sql<number>`COALESCE(donation_totals.total, 0)`
    }).from(branches)
      .leftJoin(
        sql`(
          SELECT branch_id, COUNT(*) as total 
          FROM members 
          WHERE church_id = ${church.id}
          GROUP BY branch_id
        ) as member_counts`,
        sql`member_counts.branch_id = ${branches.id}`
      )
      .leftJoin(
        sql`(
          SELECT branch_id, COUNT(*) as total 
          FROM events 
          WHERE church_id = ${church.id}
          GROUP BY branch_id
        ) as event_counts`,
        sql`event_counts.branch_id = ${branches.id}`
      )
      .leftJoin(
        sql`(
          SELECT branch_id, SUM(amount) as total 
          FROM donations 
          WHERE church_id = ${church.id}
          GROUP BY branch_id
        ) as donation_totals`,
        sql`donation_totals.branch_id = ${branches.id}`
      )
      .where(eq(branches.churchId, church.id))
  ]);

  const analytics: BranchAnalytics = {
    totalBranches: totalBranchesResult[0]?.count || 0,
    branchComparison: branchComparison.map(branch => ({
      branchId: branch.branchId,
      branchName: branch.branchName,
      memberCount: Number(branch.memberCount),
      eventCount: Number(branch.eventCount),
      totalDonations: Number(branch.totalDonations),
      averageAttendance: 0 // TODO: Calculate average attendance
    })),
    branchPerformance: branchComparison.map(branch => ({
      branchId: branch.branchId,
      branchName: branch.branchName,
      growthRate: 0, // TODO: Calculate growth rate
      engagementScore: 0, // TODO: Calculate engagement score
      revenuePerMember: Number(branch.memberCount) > 0 ? Number(branch.totalDonations) / Number(branch.memberCount) : 0
    }))
  };

  res.json({
    status: 'success',
    data: { analytics }
  });
});