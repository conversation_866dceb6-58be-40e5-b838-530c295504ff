import { Request, Response } from 'express';
import { db } from '../db';
import { churches, members, roles, branches, onboardingSessions } from '../db/schema';
import { 
  checkAvailabilitySchema,
  initiateOnboardingSchema,
  completeOnboardingSchema,
  verifyOnboardingSchema,
  resendVerificationSchema,
  setupInitialDataSchema,
  CheckAvailabilityInput,
  InitiateOnboardingInput,
  CompleteOnboardingInput,
  VerifyOnboardingInput,
  ResendVerificationInput,
  SetupInitialDataInput,
  OnboardingProgress
} from '../types/onboarding';
import { catchAsync, AppError } from '../utils/errorHandler';
import { seedRoles } from '../utils/seedData';
import { eq, and, or } from 'drizzle-orm';
import { 
  hashPassword, 
  generateAccessToken, 
  generateRefreshToken, 
  getTokenExpiration,
  generateEmailVerificationToken,
  isTokenExpired
} from '../utils/auth';
import { generateUniqueChurchCode } from '../utils/churchCode';
import { refreshTokens } from '../db/schema';
import { AuthenticatedRequest } from '../middleware/auth';

export const checkAvailability = catchAsync(async (req: Request, res: Response) => {
  const validatedData: CheckAvailabilityInput = checkAvailabilitySchema.parse(req.body);
  
  const [existingChurch, existingUser] = await Promise.all([
    db.query.churches.findFirst({
      where: eq(churches.slug, validatedData.churchSlug)
    }),
    db.query.members.findFirst({
      where: eq(members.email, validatedData.adminEmail)
    })
  ]);

  const availability = {
    churchSlug: {
      available: !existingChurch,
      message: existingChurch ? 'Church slug is already taken' : 'Church slug is available'
    },
    adminEmail: {
      available: !existingUser,
      message: existingUser ? 'Email is already registered' : 'Email is available'
    }
  };

  const allAvailable = availability.churchSlug.available && availability.adminEmail.available;

  res.json({
    status: 'success',
    data: { 
      availability,
      allAvailable,
      canProceed: allAvailable
    }
  });
});

export const initiateOnboarding = catchAsync(async (req: Request, res: Response) => {
  const validatedData: InitiateOnboardingInput = initiateOnboardingSchema.parse(req.body);
  
  const [existingChurch, existingUser] = await Promise.all([
    db.query.churches.findFirst({
      where: eq(churches.slug, validatedData.church.slug)
    }),
    db.query.members.findFirst({
      where: eq(members.email, validatedData.admin.email)
    })
  ]);

  if (existingChurch) {
    throw new AppError('Church with this slug already exists', 400);
  }

  if (existingUser) {
    throw new AppError('Email already registered', 400);
  }

  const hashedPassword = await hashPassword(validatedData.admin.password);
  const onboardingToken = generateEmailVerificationToken();
  const verificationToken = generateEmailVerificationToken();
  const expiresAt = getTokenExpiration(24); // 24 hours

  const { password: _password, ...adminDataWithoutPassword } = validatedData.admin;

  const [onboardingSession] = await db.insert(onboardingSessions).values({
    churchData: validatedData.church,
    adminData: adminDataWithoutPassword,
    settingsData: validatedData.settings || {},
    hashedPassword,
    token: onboardingToken,
    verificationToken,
    expiresAt,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Onboarding initiated successfully. Please complete the setup.',
    data: {
      onboardingToken,
      verificationToken,
      expiresAt: onboardingSession.expiresAt,
      progress: {
        step: 'completion',
        completed: false,
        nextStep: 'Complete onboarding setup',
      } as OnboardingProgress
    }
  });
});

export const completeOnboarding = catchAsync(async (req: Request, res: Response) => {
  const validatedData: CompleteOnboardingInput = completeOnboardingSchema.parse(req.body);
  
  const onboardingSession = await db.query.onboardingSessions.findFirst({
    where: and(
      eq(onboardingSessions.token, validatedData.onboardingToken),
      eq(onboardingSessions.isCompleted, false)
    )
  });

  if (!onboardingSession || isTokenExpired(onboardingSession.expiresAt)) {
    throw new AppError('Invalid or expired onboarding token', 400);
  }

  const churchData = onboardingSession.churchData as InitiateOnboardingInput['church'];
  const adminData = onboardingSession.adminData as Omit<InitiateOnboardingInput['admin'], 'password'>;
  const settingsData = onboardingSession.settingsData as InitiateOnboardingInput['settings'];

  const churchCode = await generateUniqueChurchCode();

  const [church] = await db.insert(churches).values({
    ...churchData,
    churchCode,
    settings: {
      allowSelfRegistration: settingsData?.allowSelfRegistration ?? true,
      requireEmailVerification: settingsData?.requireEmailVerification ?? true,
      timezone: settingsData?.timezone || 'UTC',
      locale: settingsData?.locale || 'en',
      theme: settingsData?.theme || {
        primaryColor: '#3B82F6',
        secondaryColor: '#64748B',
      },
      features: settingsData?.features || {
        events: true,
        donations: false,
        messaging: true,
        calendar: true,
        onlineGiving: false,
        memberDirectory: true,
        eventRsvp: true,
        recurringEvents: true,
      }
    }
  }).returning();

  if (!church) {
    throw new AppError('Failed to create church', 500);
  }

  await seedRoles(church.id);

  const superAdminRole = await db.query.roles.findFirst({
    where: and(
      eq(roles.churchId, church.id),
      eq(roles.name, 'Super Admin')
    )
  });

  const [mainBranch] = await db.insert(branches).values({
    name: `${church.name} - Main Campus`,
    slug: `${church.slug}-main`,
    description: 'Main church campus',
    address: church.address,
    phone: church.phone,
    email: church.email,
    churchId: church.id,
    isMainBranch: true,
    isActive: true,
  }).returning();

  const [admin] = await db.insert(members).values({
    ...adminData,
    password: onboardingSession.hashedPassword,
    churchId: church.id,
    branchId: mainBranch.id,
    roleId: superAdminRole?.id,
    dateOfBirth: adminData.dateOfBirth ? new Date(adminData.dateOfBirth) : undefined,
    isEmailVerified: false,
    status: 'active',
  }).returning();

  await db.update(onboardingSessions)
    .set({ 
      isCompleted: true,
      updatedAt: new Date()
    })
    .where(eq(onboardingSessions.id, onboardingSession.id));

  const accessToken = generateAccessToken({
    id: admin.id,
    email: admin.email,
    churchId: admin.churchId,
    roleId: admin.roleId || undefined,
  });

  const refreshTokenValue = generateRefreshToken();
  const refreshTokenExpiry = getTokenExpiration(7 * 24);

  await db.insert(refreshTokens).values({
    memberId: admin.id,
    token: refreshTokenValue,
    expiresAt: refreshTokenExpiry,
  });

  const churchUrl = `${req.protocol}://${req.get('host')}/api/churches/${church.slug}`;

  res.status(201).json({
    status: 'success',
    message: 'Onboarding completed successfully. Please verify your email to activate your account.',
    data: { 
      church: {
        id: church.id,
        name: church.name,
        slug: church.slug,
        description: church.description,
        email: church.email,
        phone: church.phone,
        website: church.website,
        createdAt: church.createdAt,
      },
      admin: {
        id: admin.id,
        firstName: admin.firstName,
        lastName: admin.lastName,
        email: admin.email,
        churchId: admin.churchId,
        roleId: admin.roleId,
        isEmailVerified: admin.isEmailVerified,
      },
      mainBranch: {
        id: mainBranch.id,
        name: mainBranch.name,
        slug: mainBranch.slug,
        isMainBranch: mainBranch.isMainBranch,
      },
      tokens: {
        accessToken,
        refreshToken: refreshTokenValue,
        expiresIn: '15m',
      },
      verificationToken: onboardingSession.verificationToken,
      churchUrl,
      adminUrl: `${churchUrl}/admin`,
      progress: {
        step: 'verification',
        completed: false,
        nextStep: 'Verify email address',
      } as OnboardingProgress
    }
  });
});

export const verifyOnboarding = catchAsync(async (req: Request, res: Response) => {
  const validatedData: VerifyOnboardingInput = verifyOnboardingSchema.parse(req.body);
  
  const onboardingSession = await db.query.onboardingSessions.findFirst({
    where: and(
      eq(onboardingSessions.verificationToken, validatedData.verificationToken),
      eq(onboardingSessions.isCompleted, true),
      eq(onboardingSessions.isVerified, false)
    )
  });

  if (!onboardingSession || isTokenExpired(onboardingSession.expiresAt)) {
    throw new AppError('Invalid or expired verification token', 400);
  }

  const adminData = onboardingSession.adminData as Omit<InitiateOnboardingInput['admin'], 'password'>;
  
  const member = await db.query.members.findFirst({
    where: eq(members.email, adminData.email)
  });

  if (!member) {
    throw new AppError('Associated user not found', 404);
  }

  await Promise.all([
    db.update(members)
      .set({
        isEmailVerified: true,
        updatedAt: new Date(),
      })
      .where(eq(members.id, member.id)),
    
    db.update(onboardingSessions)
      .set({ 
        isVerified: true,
        updatedAt: new Date()
      })
      .where(eq(onboardingSessions.id, onboardingSession.id))
  ]);

  res.json({
    status: 'success',
    message: 'Email verified successfully. Your church onboarding is complete!',
    data: {
      verified: true,
      progress: {
        step: 'setup',
        completed: true,
        nextStep: 'Setup initial data (optional)',
      } as OnboardingProgress
    }
  });
});

export const resendVerification = catchAsync(async (req: Request, res: Response) => {
  const validatedData: ResendVerificationInput = resendVerificationSchema.parse(req.body);
  
  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, validatedData.churchSlug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const member = await db.query.members.findFirst({
    where: and(
      eq(members.email, validatedData.email),
      eq(members.churchId, church.id)
    )
  });

  if (!member) {
    return res.json({
      status: 'success',
      message: 'If the email exists, a verification link has been sent.',
    });
  }

  if (member.isEmailVerified) {
    throw new AppError('Email is already verified', 400);
  }

  const verificationToken = generateEmailVerificationToken();

  await db.update(members)
    .set({
      emailVerificationToken: verificationToken,
      updatedAt: new Date(),
    })
    .where(eq(members.id, member.id));

  res.json({
    status: 'success',
    message: 'Verification email has been resent.',
    ...(process.env.NODE_ENV === 'development' && { verificationToken }),
  });
});

export const setupInitialData = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData: SetupInitialDataInput = setupInitialDataSchema.parse(req.body);
  
  if (!req.user?.churchId) {
    throw new AppError('Church context required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.id, req.user.churchId)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  const results: {
    branches: typeof branches[];
    roles: typeof roles[];
    sampleDataCreated: boolean;
  } = {
    branches: [],
    roles: [],
    sampleDataCreated: false,
  };

  if (validatedData.setupBranches.length > 0) {
    for (const branchData of validatedData.setupBranches) {
      const [branch] = await db.insert(branches).values({
        ...branchData,
        churchId: req.user.churchId,
        isActive: true,
      }).returning();
      
      results.branches.push(branch);
    }
  }

  if (validatedData.setupRoles.length > 0) {
    for (const roleData of validatedData.setupRoles) {
      const [role] = await db.insert(roles).values({
        ...roleData,
        churchId: req.user.churchId,
        isActive: true,
      }).returning();
      
      results.roles.push(role);
    }
  }

  if (validatedData.createSampleData) {
    results.sampleDataCreated = true;
  }

  res.json({
    status: 'success',
    message: 'Initial data setup completed successfully',
    data: {
      ...results,
      progress: {
        step: 'finished',
        completed: true,
        nextStep: 'Start using your church management system',
      } as OnboardingProgress
    }
  });
});

export const getOnboardingProgress = catchAsync(async (req: Request, res: Response) => {
  const { token } = req.params;
  
  const onboardingSession = await db.query.onboardingSessions.findFirst({
    where: or(
      eq(onboardingSessions.token, token),
      eq(onboardingSessions.verificationToken, token)
    )
  });

  if (!onboardingSession) {
    throw new AppError('Onboarding session not found', 404);
  }

  if (isTokenExpired(onboardingSession.expiresAt)) {
    throw new AppError('Onboarding session has expired', 400);
  }

  let progress: OnboardingProgress;

  if (!onboardingSession.isCompleted) {
    progress = {
      step: 'completion',
      completed: false,
      nextStep: 'Complete onboarding setup',
    };
  } else if (!onboardingSession.isVerified) {
    progress = {
      step: 'verification',
      completed: false,
      nextStep: 'Verify email address',
    };
  } else {
    progress = {
      step: 'setup',
      completed: true,
      nextStep: 'Setup initial data (optional)',
    };
  }

  res.json({
    status: 'success',
    data: {
      progress,
      expiresAt: onboardingSession.expiresAt,
      churchName: (onboardingSession.churchData as InitiateOnboardingInput['church'])?.name,
      adminEmail: (onboardingSession.adminData as Omit<InitiateOnboardingInput['admin'], 'password'>)?.email,
    }
  });
});

export const cleanupExpiredSessions = catchAsync(async (req: Request, res: Response) => {
  const expiredSessions = await db.query.onboardingSessions.findMany({
    where: and(
      eq(onboardingSessions.isCompleted, false),
    )
  });

  const expiredCount = expiredSessions.filter(session => 
    isTokenExpired(session.expiresAt)
  ).length;

  if (expiredCount > 0) {
    await db.delete(onboardingSessions).where(
      and(
        eq(onboardingSessions.isCompleted, false),
      )
    );
  }

  res.json({
    status: 'success',
    message: `Cleaned up ${expiredCount} expired onboarding sessions`,
    data: { cleanedUp: expiredCount }
  });
});