import type { Response } from 'express';
import { db } from '../db';
import { finances, financeCategories, events } from '../db/schema';
import {
  createFinanceSchema,
  updateFinanceSchema,
  financeFilterSchema,
  createFinanceCategorySchema,
  updateFinanceCategorySchema,

  financeReportSchema,
  type CreateFinanceInput,
  type UpdateFinanceInput,
  type FinanceFilterInput,
  type CreateFinanceCategoryInput,
  type UpdateFinanceCategoryInput,

  type FinanceReportInput
} from '../types/finance';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, gte, lte, like, or, desc, sql, sum } from 'drizzle-orm';
import type { TenantRequest } from '../middleware/tenant';

export const createFinance = catchAsync(async (req: TenantRequest, res: Response) => {
  let validatedData: CreateFinanceInput;
  
  try {
    validatedData = createFinanceSchema.parse(req.body);
  } catch (validationError: any) {
    console.error('Finance validation error:', validationError);
    console.error('Request body:', req.body);
    throw new AppError(`Validation failed: ${validationError.message}`, 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  // Verify event belongs to church if eventId provided
  if (validatedData.eventId) {
    const event = await db.query.events.findFirst({
      where: and(
        eq(events.id, validatedData.eventId),
        eq(events.churchId, req.church.id)
      )
    });

    if (!event) {
      throw new AppError('Event not found', 404);
    }

    if (!event.allowDonations) {
      throw new AppError('Donations are not allowed for this event', 400);
    }
  }



  // Generate receipt number if not provided
  let receiptNumber = validatedData.receiptNumber;
  if (!receiptNumber) {
    const year = new Date().getFullYear();
    const count = await db.select({ count: sql<number>`count(*)` })
      .from(finances)
      .where(eq(finances.churchId, req.church.id))
      .then(result => result[0]?.count || 0);
    receiptNumber = `${req.church.slug.toUpperCase()}-${year}-${String(count + 1).padStart(4, '0')}`;
  }

  const [finance] = await db.insert(finances).values({
    ...validatedData,
    amount: validatedData.amount.toString(),
    churchId: req.church.id,
    memberId: validatedData.isAnonymous ? null : req.user.id,
    recordedBy: req.user.id,
    receiptNumber,
    recordedAt: validatedData.recordedAt ? new Date(validatedData.recordedAt) : new Date(),
    metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Finance record created successfully',
    data: { finance }
  });
});

export const getFinances = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: FinanceFilterInput = financeFilterSchema.parse(req.query);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [eq(finances.churchId, req.church.id)];

  if (filters.type) {
    whereConditions.push(eq(finances.type, filters.type));
  }

  if (filters.method) {
    whereConditions.push(eq(finances.method, filters.method));
  }

  if (filters.status) {
    whereConditions.push(eq(finances.status, filters.status));
  }

  if (filters.eventId) {
    whereConditions.push(eq(finances.eventId, filters.eventId));
  }

  if (filters.projectId) {
    whereConditions.push(eq(finances.projectId, filters.projectId));
  }

  if (filters.memberId) {
    whereConditions.push(eq(finances.memberId, filters.memberId));
  }

  if (filters.startDate) {
    whereConditions.push(gte(finances.recordedAt, new Date(filters.startDate)));
  }

  if (filters.endDate) {
    whereConditions.push(lte(finances.recordedAt, new Date(filters.endDate)));
  }

  if (filters.search) {
    whereConditions.push(
      or(
        like(finances.description, `%${filters.search}%`),
        like(finances.donorName, `%${filters.search}%`),
        like(finances.receiptNumber, `%${filters.search}%`)
      )
    );
  }

  const page = parseInt(filters.page || '1');
  const limit = parseInt(filters.limit || '20');
  const offset = (page - 1) * limit;

  const [financesData, totalCount] = await Promise.all([
    db.query.finances.findMany({
      where: and(...whereConditions),
      with: {
        member: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        event: {
          columns: {
            id: true,
            title: true,
          }
        },
        project: {
          columns: {
            id: true,
            name: true,
          }
        },
        recordedBy: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: [desc(finances.recordedAt)],
      limit,
      offset,
    }),
    db.select({ count: sql<number>`count(*)` })
      .from(finances)
      .where(and(...whereConditions))
      .then(result => result[0]?.count || 0)
  ]);

  res.json({
    status: 'success',
    data: {
      finances: financesData,
      pagination: {
        page,
        limit,
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    }
  });
});

export const getFinance = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const finance = await db.query.finances.findFirst({
    where: and(
      eq(finances.id, id),
      eq(finances.churchId, req.church.id)
    ),
    with: {
      member: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      event: {
        columns: {
          id: true,
          title: true,
        }
      },
      project: {
        columns: {
          id: true,
          name: true,
        }
      },
      recordedBy: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
        }
      }
    }
  });

  if (!finance) {
    throw new AppError('Finance record not found', 404);
  }

  res.json({
    status: 'success',
    data: { finance }
  });
});

export const updateFinance = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;
  const validatedData: UpdateFinanceInput = updateFinanceSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const [finance] = await db.update(finances)
    .set({
      ...validatedData,
      amount: validatedData.amount ? validatedData.amount.toString() : undefined,
      updatedAt: new Date(),
      metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined,
    })
    .where(and(
      eq(finances.id, id),
      eq(finances.churchId, req.church.id)
    ))
    .returning();

  if (!finance) {
    throw new AppError('Finance record not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Finance record updated successfully',
    data: { finance }
  });
});

export const deleteFinance = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const [finance] = await db.delete(finances)
    .where(and(
      eq(finances.id, id),
      eq(finances.churchId, req.church.id)
    ))
    .returning();

  if (!finance) {
    throw new AppError('Finance record not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Finance record deleted successfully'
  });
});

export const getFinanceReport = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: FinanceReportInput = financeReportSchema.parse(req.query);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [eq(finances.churchId, req.church.id)];

  if (filters.startDate) {
    whereConditions.push(gte(finances.recordedAt, new Date(filters.startDate)));
  }

  if (filters.endDate) {
    whereConditions.push(lte(finances.recordedAt, new Date(filters.endDate)));
  }

  if (filters.type) {
    whereConditions.push(eq(finances.type, filters.type));
  }

  const [totalAmount, typeBreakdown, methodBreakdown] = await Promise.all([
    db.select({ total: sum(finances.amount) })
      .from(finances)
      .where(and(...whereConditions))
      .then(result => result[0]?.total || '0'),
    
    db.select({
      type: finances.type,
      total: sum(finances.amount),
      count: sql<number>`count(*)`
    })
      .from(finances)
      .where(and(...whereConditions))
      .groupBy(finances.type),
    
    db.select({
      method: finances.method,
      total: sum(finances.amount),
      count: sql<number>`count(*)`
    })
      .from(finances)
      .where(and(...whereConditions))
      .groupBy(finances.method)
  ]);

  res.json({
    status: 'success',
    data: {
      totalAmount,
      typeBreakdown,
      methodBreakdown,
      filters
    }
  });
});

// Finance Categories
export const createFinanceCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: CreateFinanceCategoryInput = createFinanceCategorySchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const [category] = await db.insert(financeCategories).values({
    ...validatedData,
    churchId: req.church.id,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Finance category created successfully',
    data: { category }
  });
});

export const getFinanceCategories = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const categories = await db.query.financeCategories.findMany({
    where: eq(financeCategories.churchId, req.church.id),
    orderBy: [financeCategories.name],
  });

  res.json({
    status: 'success',
    data: { categories }
  });
});

export const updateFinanceCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;
  const validatedData: UpdateFinanceCategoryInput = updateFinanceCategorySchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const [category] = await db.update(financeCategories)
    .set({
      ...validatedData,
      updatedAt: new Date(),
    })
    .where(and(
      eq(financeCategories.id, id),
      eq(financeCategories.churchId, req.church.id)
    ))
    .returning();

  if (!category) {
    throw new AppError('Finance category not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Finance category updated successfully',
    data: { category }
  });
});

export const deleteFinanceCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const [category] = await db.delete(financeCategories)
    .where(and(
      eq(financeCategories.id, id),
      eq(financeCategories.churchId, req.church.id)
    ))
    .returning();

  if (!category) {
    throw new AppError('Finance category not found', 404);
  }

  res.json({
    status: 'success',
    message: 'Finance category deleted successfully'
  });
});

