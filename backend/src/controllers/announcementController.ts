import type { Response } from 'express';
import { db } from '../db';
import { announcements, announcementViews, announcementComments, announcementReactions } from '../db/schema';
import {
  createAnnouncementSchema,
  updateAnnouncementSchema,
  queryAnnouncementsSchema,
  createCommentSchema,
  updateCommentSchema,
  createReactionSchema,
  markAsViewedSchema,
  bulkDeleteAnnouncementsSchema,
  bulkUpdateStatusSchema,
  type CreateAnnouncementInput,
  type UpdateAnnouncementInput,
  type QueryAnnouncementsInput,
  type CreateCommentInput,
  type UpdateCommentInput,
  type CreateReactionInput,
  type MarkAsViewedInput,
  type BulkDeleteAnnouncementsInput,
  type BulkUpdateStatusInput
} from '../types/announcement';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, gte, lte, like, or, desc, asc, sql, inArray } from 'drizzle-orm';
import type { TenantRequest } from '../middleware/tenant';

export const createAnnouncement = catchAsync(async (req: TenantRequest, res: Response) => {
  let validatedData: CreateAnnouncementInput;
  try {
    validatedData = createAnnouncementSchema.parse(req.body);
  } catch (error: any) {
    console.error('Validation error:', error);
    if (error.errors) {
      const errorMessages = error.errors.map((err: any) => `${err.path.join('.')}: ${err.message}`).join(', ');
      throw new AppError(`Validation failed: ${errorMessages}`, 400);
    }
    throw new AppError('Validation error', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  // Determine initial status
  let status: 'draft' | 'scheduled' | 'published' = 'draft';
  let publishedAt: Date | null = null;

  if (validatedData.scheduledFor) {
    status = 'scheduled';
  } else if (!validatedData.scheduledFor) {
    status = 'published';
    publishedAt = new Date();
  }

  const [announcement] = await db.insert(announcements).values({
    ...validatedData,
    churchId: req.church.id,
    authorId: req.user.id,
    status,
    publishedAt,
    scheduledFor: validatedData.scheduledFor ? new Date(validatedData.scheduledFor) : undefined,
    expiresAt: validatedData.expiresAt ? new Date(validatedData.expiresAt) : undefined,
    targetBranches: validatedData.targetBranches ? JSON.stringify(validatedData.targetBranches) : undefined,
    targetRoles: validatedData.targetRoles ? JSON.stringify(validatedData.targetRoles) : undefined,
    attachments: validatedData.attachments ? JSON.stringify(validatedData.attachments) : undefined,
    externalLinks: validatedData.externalLinks ? JSON.stringify(validatedData.externalLinks) : undefined,
    tags: validatedData.tags ? JSON.stringify(validatedData.tags) : undefined,
    notificationChannels: validatedData.notificationChannels ? JSON.stringify(validatedData.notificationChannels) : undefined,
    metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined,
  }).returning();

  // Create in-app notifications for target members if enabled
  if (announcement.sendNotification) {
    const { notifyForAnnouncement } = await import('../utils/notificationService');
    await notifyForAnnouncement({
      churchId: req.church.id,
      title: announcement.title,
      summary: announcement.summary ?? undefined,
      announcementId: announcement.id,
      target: {
        audience: announcement.targetAudience as any,
        branchIds: announcement.targetBranches ? JSON.parse(announcement.targetBranches) : undefined,
        roleNames: announcement.targetRoles ? JSON.parse(announcement.targetRoles) : undefined,
      }
    }).catch((e) => console.warn('Failed to create notifications:', e));
  }

  res.status(201).json({
    status: 'success',
    message: 'Announcement created successfully',
    data: { announcement }
  });
});

export const getAnnouncements = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: QueryAnnouncementsInput = queryAnnouncementsSchema.parse(req.query);

  if (!req.church || !req.user) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [eq(announcements.churchId, req.church.id)];

  // Filter by type
  if (filters.type) {
    whereConditions.push(eq(announcements.type, filters.type));
  }

  // Filter by priority
  if (filters.priority) {
    whereConditions.push(eq(announcements.priority, filters.priority));
  }

  // Filter by status
  if (filters.status) {
    whereConditions.push(eq(announcements.status, filters.status));
  }

  // Filter by author
  if (filters.authorId) {
    whereConditions.push(eq(announcements.authorId, filters.authorId));
  }

  // Filter by event
  if (filters.eventId) {
    whereConditions.push(eq(announcements.eventId, filters.eventId));
  }

  // Filter by date range
  if (filters.startDate) {
    whereConditions.push(gte(announcements.publishedAt, new Date(filters.startDate)));
  }

  if (filters.endDate) {
    whereConditions.push(lte(announcements.publishedAt, new Date(filters.endDate)));
  }

  // Filter by pinned status
  if (filters.isPinned !== undefined) {
    whereConditions.push(eq(announcements.isPinned, filters.isPinned));
  }

  // Filter by acknowledgment requirement
  if (filters.requiresAcknowledgment !== undefined) {
    whereConditions.push(eq(announcements.requiresAcknowledgment, filters.requiresAcknowledgment));
  }

  // Search functionality
  if (filters.search) {
    const searchCondition = or(
      like(announcements.title, `%${filters.search}%`),
      like(announcements.content, `%${filters.search}%`),
      like(announcements.summary, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  // Tag filtering
  if (filters.tags && filters.tags.length > 0) {
    // This is a simplified approach - in production you might want more sophisticated JSON querying
    const tagConditions = filters.tags.map(tag =>
      like(announcements.tags, `%"${tag}"%`)
    );
    const tagCondition = or(...tagConditions);
    if (tagCondition) {
      whereConditions.push(tagCondition);
    }
  }

  const offset = (filters.page - 1) * filters.limit;

  // Define sorting
  let orderBy;
  const sortField = filters.sortBy;
  const sortDirection = filters.sortOrder === 'asc' ? asc : desc;

  switch (sortField) {
    case 'createdAt':
      orderBy = [sortDirection(announcements.createdAt)];
      break;
    case 'publishedAt':
      orderBy = [sortDirection(announcements.publishedAt)];
      break;
    case 'priority':
      orderBy = [sortDirection(announcements.priority)];
      break;
    case 'title':
      orderBy = [sortDirection(announcements.title)];
      break;
    default:
      orderBy = [desc(announcements.publishedAt)];
  }

  // Add pinned announcements to the top
  orderBy.unshift(desc(announcements.isPinned));

  const [announcementList, totalCount] = await Promise.all([
    db.query.announcements.findMany({
      where: and(...whereConditions),
      with: {
        author: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
            profileImage: true,
          }
        },
        event: {
          columns: {
            id: true,
            title: true,
            startDate: true,
            type: true,
          }
        }
      },
      orderBy,
      limit: filters.limit,
      offset
    }),
    db.select({ count: sql<number>`count(*)` })
      .from(announcements)
      .where(and(...whereConditions))
      .then(result => result[0]?.count || 0)
  ]);

  // If unreadOnly is requested, filter out announcements the user has already viewed
  let filteredAnnouncements = announcementList;
  if (filters.unreadOnly) {
    const viewedAnnouncementIds = await db.select({ announcementId: announcementViews.announcementId })
      .from(announcementViews)
      .where(eq(announcementViews.memberId, req.user.id))
      .then(views => views.map(v => v.announcementId));

    filteredAnnouncements = announcementList.filter(
      announcement => !viewedAnnouncementIds.includes(announcement.id)
    );
  }

  // Calculate stats for each announcement
  const announcementsWithStats = await Promise.all(
    filteredAnnouncements.map(async (announcement) => {
      const [viewCount, commentCount, reactionCount, userView, userReaction] = await Promise.all([
        db.select({ count: sql<number>`count(*)` })
          .from(announcementViews)
          .where(eq(announcementViews.announcementId, announcement.id))
          .then(result => result[0]?.count || 0),

        db.select({ count: sql<number>`count(*)` })
          .from(announcementComments)
          .where(eq(announcementComments.announcementId, announcement.id))
          .then(result => result[0]?.count || 0),

        db.select({ count: sql<number>`count(*)` })
          .from(announcementReactions)
          .where(eq(announcementReactions.announcementId, announcement.id))
          .then(result => result[0]?.count || 0),

        db.query.announcementViews.findFirst({
          where: and(
            eq(announcementViews.announcementId, announcement.id),
            eq(announcementViews.memberId, req.user!.id)
          )
        }),

        db.query.announcementReactions.findFirst({
          where: and(
            eq(announcementReactions.announcementId, announcement.id),
            eq(announcementReactions.memberId, req.user!.id)
          )
        })
      ]);

      let acknowledgmentCount: number | undefined;
      if (announcement.requiresAcknowledgment) {
        acknowledgmentCount = await db.select({ count: sql<number>`count(*)` })
          .from(announcementViews)
          .where(and(
            eq(announcementViews.announcementId, announcement.id),
            sql`${announcementViews.acknowledgedAt} IS NOT NULL`
          ))
          .then(result => result[0]?.count || 0);
      }

      return {
        ...announcement,
        stats: {
          viewCount,
          commentCount,
          reactionCount,
          acknowledgmentCount,
          isViewed: !!userView,
          isAcknowledged: !!userView?.acknowledgedAt,
          userReaction: userReaction?.reactionType || null,
        }
      };
    })
  );

  const totalPages = Math.ceil(totalCount / filters.limit);

  res.json({
    status: 'success',
    data: {
      announcements: announcementsWithStats,
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: totalCount,
        totalPages,
        hasNext: filters.page < totalPages,
        hasPrev: filters.page > 1
      }
    }
  });
});

export const getAnnouncement = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    ),
    with: {
      author: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          profileImage: true,
        }
      },
      event: {
        columns: {
          id: true,
          title: true,
          startDate: true,
          type: true,
        }
      },
      comments: {
        with: {
          author: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
              profileImage: true,
            }
          }
        },
        orderBy: [desc(announcementComments.createdAt)]
      },
      reactions: {
        with: {
          member: {
            columns: {
              id: true,
              firstName: true,
              lastName: true,
            }
          }
        }
      }
    }
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  // Record the view
  await db.insert(announcementViews).values({
    announcementId: announcement.id,
    memberId: req.user.id,
  }).onConflictDoNothing();

  // Update view count
  await db.update(announcements)
    .set({ viewCount: sql`${announcements.viewCount} + 1` })
    .where(eq(announcements.id, announcement.id));

  res.json({
    status: 'success',
    data: { announcement }
  });
});

export const updateAnnouncement = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  const validatedData: UpdateAnnouncementInput = updateAnnouncementSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const existingAnnouncement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!existingAnnouncement) {
    throw new AppError('Announcement not found', 404);
  }

  // Prepare update data
  const updateData: Record<string, unknown> = {
    ...validatedData,
    updatedAt: new Date(),
  };

  // Handle JSON fields
  if (validatedData.targetBranches !== undefined) {
    updateData.targetBranches = validatedData.targetBranches ? JSON.stringify(validatedData.targetBranches) : null;
  }
  if (validatedData.targetRoles !== undefined) {
    updateData.targetRoles = validatedData.targetRoles ? JSON.stringify(validatedData.targetRoles) : null;
  }
  if (validatedData.attachments !== undefined) {
    updateData.attachments = validatedData.attachments ? JSON.stringify(validatedData.attachments) : null;
  }
  if (validatedData.externalLinks !== undefined) {
    updateData.externalLinks = validatedData.externalLinks ? JSON.stringify(validatedData.externalLinks) : null;
  }
  if (validatedData.tags !== undefined) {
    updateData.tags = validatedData.tags ? JSON.stringify(validatedData.tags) : null;
  }
  if (validatedData.notificationChannels !== undefined) {
    updateData.notificationChannels = validatedData.notificationChannels ? JSON.stringify(validatedData.notificationChannels) : null;
  }
  if (validatedData.metadata !== undefined) {
    updateData.metadata = validatedData.metadata ? JSON.stringify(validatedData.metadata) : null;
  }

  // Handle date fields
  if (validatedData.scheduledFor !== undefined) {
    updateData.scheduledFor = validatedData.scheduledFor ? new Date(validatedData.scheduledFor) : null;
  }
  if (validatedData.expiresAt !== undefined) {
    updateData.expiresAt = validatedData.expiresAt ? new Date(validatedData.expiresAt) : null;
  }

  // Handle status changes
  if (validatedData.status === 'published' && existingAnnouncement.status !== 'published') {
    updateData.publishedAt = new Date();
  }

  const [updatedAnnouncement] = await db.update(announcements)
    .set(updateData)
    .where(eq(announcements.id, id))
    .returning();

  res.json({
    status: 'success',
    message: 'Announcement updated successfully',
    data: { announcement: updatedAnnouncement }
  });
});

export const deleteAnnouncement = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  await db.delete(announcements).where(eq(announcements.id, id));

  res.json({
    status: 'success',
    message: 'Announcement deleted successfully'
  });
});

export const getComments = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  if (!announcement.allowComments) {
    throw new AppError('Comments are not allowed on this announcement', 403);
  }

  const comments = await db.query.announcementComments.findMany({
    where: eq(announcementComments.announcementId, id),
    with: {
      author: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        }
      }
    },
    orderBy: [desc(announcementComments.createdAt)]
  });

  res.json({
    status: 'success',
    data: { comments }
  });
});

// Comments
export const createComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  const validatedData: CreateCommentInput = createCommentSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  if (!announcement.allowComments) {
    throw new AppError('Comments are not allowed on this announcement', 403);
  }

  const [comment] = await db.insert(announcementComments).values({
    announcementId: id,
    authorId: req.user.id,
    content: validatedData.content,
    parentCommentId: validatedData.parentCommentId,
  }).returning();

  if (!comment) {
    throw new AppError('Failed to create comment', 500);
  }

  const commentWithAuthor = await db.query.announcementComments.findFirst({
    where: eq(announcementComments.id, comment.id),
    with: {
      author: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          profileImage: true,
        }
      }
    }
  });

  res.status(201).json({
    status: 'success',
    message: 'Comment created successfully',
    data: { comment: commentWithAuthor }
  });
});

export const updateComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { commentId } = req.params;

  if (!commentId) {
    throw new AppError('Comment ID is required', 400);
  }

  const validatedData: UpdateCommentInput = updateCommentSchema.parse(req.body);

  if (!req.user) {
    throw new AppError('Authentication required', 401);
  }

  const comment = await db.query.announcementComments.findFirst({
    where: eq(announcementComments.id, commentId),
    with: {
      announcement: true
    }
  });

  if (!comment) {
    throw new AppError('Comment not found', 404);
  }

  if (comment.authorId !== req.user.id) {
    throw new AppError('You can only edit your own comments', 403);
  }

  const [updatedComment] = await db.update(announcementComments)
    .set({
      content: validatedData.content,
      isEdited: true,
      editedAt: new Date(),
      updatedAt: new Date(),
    })
    .where(eq(announcementComments.id, commentId))
    .returning();

  res.json({
    status: 'success',
    message: 'Comment updated successfully',
    data: { comment: updatedComment }
  });
});

export const deleteComment = catchAsync(async (req: TenantRequest, res: Response) => {
  const { commentId } = req.params;

  if (!commentId) {
    throw new AppError('Comment ID is required', 400);
  }

  if (!req.user) {
    throw new AppError('Authentication required', 401);
  }

  const comment = await db.query.announcementComments.findFirst({
    where: eq(announcementComments.id, commentId)
  });

  if (!comment) {
    throw new AppError('Comment not found', 404);
  }

  if (comment.authorId !== req.user.id) {
    throw new AppError('You can only delete your own comments', 403);
  }

  await db.delete(announcementComments).where(eq(announcementComments.id, commentId));

  res.json({
    status: 'success',
    message: 'Comment deleted successfully'
  });
});

// Reactions
export const createReaction = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  const validatedData: CreateReactionInput = createReactionSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  // Check if user already reacted
  const existingReaction = await db.query.announcementReactions.findFirst({
    where: and(
      eq(announcementReactions.announcementId, id),
      eq(announcementReactions.memberId, req.user.id)
    )
  });

  if (existingReaction) {
    // Update existing reaction
    const [updatedReaction] = await db.update(announcementReactions)
      .set({ reactionType: validatedData.reactionType })
      .where(eq(announcementReactions.id, existingReaction.id))
      .returning();

    return res.json({
      status: 'success',
      message: 'Reaction updated successfully',
      data: { reaction: updatedReaction }
    });
  }

  // Create new reaction
  const [reaction] = await db.insert(announcementReactions).values({
    announcementId: id,
    memberId: req.user.id,
    reactionType: validatedData.reactionType,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Reaction created successfully',
    data: { reaction }
  });
});

export const deleteReaction = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  if (!req.user) {
    throw new AppError('Authentication required', 401);
  }

  const reaction = await db.query.announcementReactions.findFirst({
    where: and(
      eq(announcementReactions.announcementId, id),
      eq(announcementReactions.memberId, req.user.id)
    )
  });

  if (!reaction) {
    throw new AppError('Reaction not found', 404);
  }

  await db.delete(announcementReactions).where(eq(announcementReactions.id, reaction.id));

  res.json({
    status: 'success',
    message: 'Reaction deleted successfully'
  });
});

// Mark as viewed/acknowledged
export const markAsViewed = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Announcement ID is required', 400);
  }

  const validatedData: MarkAsViewedInput = markAsViewedSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  const announcement = await db.query.announcements.findFirst({
    where: and(
      eq(announcements.id, id),
      eq(announcements.churchId, req.church.id)
    )
  });

  if (!announcement) {
    throw new AppError('Announcement not found', 404);
  }

  const updateData: Record<string, unknown> = {};

  // Only add fields that have actual values
  if (validatedData.deviceInfo) {
    updateData.deviceInfo = JSON.stringify(validatedData.deviceInfo);
  }

  if (validatedData.acknowledged && announcement.requiresAcknowledgment) {
    updateData.acknowledgedAt = new Date();
  }

  // Always update viewedAt timestamp
  updateData.viewedAt = new Date();

  await db.insert(announcementViews).values({
    announcementId: id,
    memberId: req.user.id,
    viewedAt: new Date(),
    ...(validatedData.deviceInfo && { deviceInfo: JSON.stringify(validatedData.deviceInfo) }),
    ...(validatedData.acknowledged && announcement.requiresAcknowledgment && { acknowledgedAt: new Date() }),
  }).onConflictDoUpdate({
    target: [announcementViews.announcementId, announcementViews.memberId],
    set: updateData,
  });

  res.json({
    status: 'success',
    message: 'Announcement marked as viewed'
  });
});

// Bulk operations
export const bulkDeleteAnnouncements = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: BulkDeleteAnnouncementsInput = bulkDeleteAnnouncementsSchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  await db.delete(announcements)
    .where(and(
      inArray(announcements.id, validatedData.announcementIds),
      eq(announcements.churchId, req.church.id)
    ));

  res.json({
    status: 'success',
    message: `${validatedData.announcementIds.length} announcements deleted successfully`
  });
});

export const bulkUpdateStatus = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: BulkUpdateStatusInput = bulkUpdateStatusSchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const updateData: Record<string, unknown> = {
    status: validatedData.status,
    updatedAt: new Date(),
  };

  if (validatedData.status === 'published') {
    updateData.publishedAt = new Date();
  }

  await db.update(announcements)
    .set(updateData)
    .where(and(
      inArray(announcements.id, validatedData.announcementIds),
      eq(announcements.churchId, req.church.id)
    ));

  res.json({
    status: 'success',
    message: `${validatedData.announcementIds.length} announcements updated successfully`
  });
});