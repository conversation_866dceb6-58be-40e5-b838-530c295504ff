import type { Request, Response } from 'express';
import { AuthMetricsCollector } from '../utils/authMetrics';
import { SecurityAuditLogger } from '../utils/securityAudit';
import { PerformanceMonitor } from '../utils/performanceMonitor';
import { catchAsync } from '../utils/errorHandler';
// import { AuthenticatedRequest } from '../middleware/auth';

/**
 * Get authentication health status
 */
export const getAuthHealth = catchAsync(async (_req: Request, res: Response) => {
  const healthStatus = AuthMetricsCollector.getHealthStatus();

  res.json({
    status: 'success',
    data: healthStatus,
  });
});

/**
 * Get authentication metrics summary
 */
export const getAuthMetrics = catchAsync(async (req: Request, res: Response) => {
  const { timeRange = '1h', eventType } = req.query;

  // Parse time range
  let startTime: Date;
  const endTime = new Date();

  switch (timeRange) {
    case '5m':
      startTime = new Date(Date.now() - 5 * 60 * 1000);
      break;
    case '1h':
      startTime = new Date(Date.now() - 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(Date.now() - 60 * 60 * 1000);
  }

  const summary = AuthMetricsCollector.getMetricsSummary(
    startTime,
    endTime,
    eventType as string
  );

  const performance = AuthMetricsCollector.getPerformanceMetrics(startTime, endTime);
  const errorRates = AuthMetricsCollector.getErrorRates(startTime, endTime);

  res.json({
    status: 'success',
    data: {
      timeRange: {
        start: startTime,
        end: endTime,
        duration: timeRange,
      },
      summary,
      performance,
      errorRates,
    },
  });
});

/**
 * Get security dashboard data
 */
export const getSecurityDashboard = catchAsync(async (_req: Request, res: Response) => {
  const dashboard = SecurityAuditLogger.getSecurityDashboard();

  res.json({
    status: 'success',
    data: dashboard,
  });
});

/**
 * Get security metrics
 */
export const getSecurityMetrics = catchAsync(async (req: Request, res: Response) => {
  const { timeRange = '1h' } = req.query;

  // Parse time range
  let startTime: Date;
  const endTime = new Date();

  switch (timeRange) {
    case '1h':
      startTime = new Date(Date.now() - 60 * 60 * 1000);
      break;
    case '24h':
      startTime = new Date(Date.now() - 24 * 60 * 60 * 1000);
      break;
    case '7d':
      startTime = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
      break;
    default:
      startTime = new Date(Date.now() - 60 * 60 * 1000);
  }

  const metrics = SecurityAuditLogger.getSecurityMetrics({ start: startTime, end: endTime });

  res.json({
    status: 'success',
    data: {
      timeRange: {
        start: startTime,
        end: endTime,
        duration: timeRange,
      },
      metrics,
    },
  });
});

/**
 * Get active security alerts
 */
export const getSecurityAlerts = catchAsync(async (_req: Request, res: Response) => {
  const alerts = SecurityAuditLogger.getActiveAlerts();

  res.json({
    status: 'success',
    data: {
      alerts,
      count: alerts.length,
    },
  });
});

/**
 * Acknowledge a security alert
 */
export const acknowledgeAlert = catchAsync(async (req: Request, res: Response) => {
  const { alertId } = req.params;

  if (!alertId) {
    return res.status(400).json({
      status: 'error',
      message: 'Alert ID is required',
    });
  }

  const success = SecurityAuditLogger.acknowledgeAlert(alertId);

  if (!success) {
    return res.status(404).json({
      status: 'error',
      message: 'Alert not found',
    });
  }

  res.json({
    status: 'success',
    message: 'Alert acknowledged successfully',
  });
});

/**
 * Get audit logs for a specific user
 */
export const getUserAuditLogs = catchAsync(async (req: Request, res: Response) => {
  const { userId } = req.params;
  const { limit = 100 } = req.query;

  if (!userId) {
    return res.status(400).json({
      status: 'error',
      message: 'User ID is required',
    });
  }

  const logs = SecurityAuditLogger.getUserAuditLogs(userId, Number(limit));

  res.json({
    status: 'success',
    data: {
      logs,
      count: logs.length,
      userId,
    },
  });
});

/**
 * Get audit logs for a specific IP address
 */
export const getIPAuditLogs = catchAsync(async (req: Request, res: Response) => {
  const { ipAddress } = req.params;
  const { limit = 100 } = req.query;

  if (!ipAddress) {
    return res.status(400).json({
      status: 'error',
      message: 'IP address is required',
    });
  }

  const logs = SecurityAuditLogger.getIPAuditLogs(ipAddress, Number(limit));

  res.json({
    status: 'success',
    data: {
      logs,
      count: logs.length,
      ipAddress,
    },
  });
});

/**
 * Get performance metrics
 */
export const getPerformanceMetrics = catchAsync(async (_req: Request, res: Response) => {
  const summary = PerformanceMonitor.getPerformanceSummary();

  res.json({
    status: 'success',
    data: summary,
  });
});

/**
 * Export metrics in various formats
 */
export const exportMetrics = catchAsync(async (req: Request, res: Response) => {
  const { format = 'json' } = req.query;

  if (format === 'prometheus') {
    const prometheusMetrics = AuthMetricsCollector.exportMetrics('prometheus');
    res.set('Content-Type', 'text/plain');
    res.send(prometheusMetrics);
  } else {
    const jsonMetrics = AuthMetricsCollector.exportMetrics('json');
    res.set('Content-Type', 'application/json');
    res.send(jsonMetrics);
  }
});

/**
 * Export security data
 */
export const exportSecurityData = catchAsync(async (req: Request, res: Response) => {
  const { format = 'json' } = req.query;

  const securityData = SecurityAuditLogger.exportSecurityData(format as 'json' | 'csv');

  if (format === 'csv') {
    res.set('Content-Type', 'text/csv');
    res.set('Content-Disposition', 'attachment; filename=security-audit.csv');
  } else {
    res.set('Content-Type', 'application/json');
  }

  res.send(securityData);
});

/**
 * Get comprehensive monitoring dashboard
 */
export const getMonitoringDashboard = catchAsync(async (_req: Request, res: Response) => {
  const authHealth = AuthMetricsCollector.getHealthStatus();
  const securityDashboard = SecurityAuditLogger.getSecurityDashboard();
  const performanceSummary = PerformanceMonitor.getPerformanceSummary();

  // Get recent metrics
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  const now = new Date();
  const authMetrics = AuthMetricsCollector.getMetricsSummary(oneHourAgo, now);
  const securityMetrics = SecurityAuditLogger.getSecurityMetrics({ start: oneHourAgo, end: now });

  res.json({
    status: 'success',
    data: {
      timestamp: new Date(),
      authentication: {
        health: authHealth,
        metrics: authMetrics,
      },
      security: {
        dashboard: securityDashboard,
        metrics: securityMetrics,
      },
      performance: performanceSummary,
      summary: {
        overallStatus: authHealth.status,
        totalRequests: authMetrics.totalRequests,
        successRate: authMetrics.totalRequests > 0
          ? (authMetrics.successfulRequests / authMetrics.totalRequests) * 100
          : 100,
        activeAlerts: securityDashboard.summary.criticalAlerts,
        blockedIPs: securityDashboard.summary.blockedIPs,
      },
    },
  });
});

/**
 * Get real-time metrics (for live dashboard updates)
 */
export const getRealTimeMetrics = catchAsync(async (_req: Request, res: Response) => {
  const realTimeMetrics = AuthMetricsCollector.getRealTimeMetrics();
  const healthStatus = AuthMetricsCollector.getHealthStatus();
  const activeAlerts = SecurityAuditLogger.getActiveAlerts();

  res.json({
    status: 'success',
    data: {
      timestamp: new Date(),
      metrics: realTimeMetrics,
      health: healthStatus,
      alerts: activeAlerts.slice(0, 5), // Only recent alerts
      summary: {
        requestsPerMinute: Math.round(realTimeMetrics.totalRequests / 5), // 5-minute window
        errorRate: realTimeMetrics.totalRequests > 0
          ? ((realTimeMetrics.totalRequests - realTimeMetrics.successfulRequests) / realTimeMetrics.totalRequests) * 100
          : 0,
        activeUsers: realTimeMetrics.uniqueUsers,
        responseTime: realTimeMetrics.averageResponseTime,
      },
    },
  });
});

/**
 * Get system status for health checks
 */
export const getSystemStatus = catchAsync(async (_req: Request, res: Response) => {
  const authHealth = AuthMetricsCollector.getHealthStatus();
  const securitySummary = SecurityAuditLogger.getSecurityDashboard().summary;

  const overallStatus = authHealth.status === 'critical' || securitySummary.criticalAlerts > 0
    ? 'critical'
    : authHealth.status === 'warning'
      ? 'warning'
      : 'healthy';

  res.json({
    status: 'success',
    data: {
      overallStatus,
      timestamp: new Date(),
      components: {
        authentication: {
          status: authHealth.status,
          successRate: authHealth.metrics.successRate,
          responseTime: authHealth.metrics.averageResponseTime,
        },
        security: {
          status: securitySummary.criticalAlerts > 0 ? 'critical' : 'healthy',
          blockedIPs: securitySummary.blockedIPs,
          suspiciousIPs: securitySummary.suspiciousIPs,
          failureRate: securitySummary.failureRate,
        },
      },
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
    },
  });
});