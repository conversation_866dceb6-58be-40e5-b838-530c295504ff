import type { Response } from 'express';
import { db } from '../db';
import { branches, churches, members } from '../db/schema';
import {
  createBranchSchema,
  updateBranchSchema,
  branchQuerySchema,
  type CreateBranchData,
  type UpdateBranchData,
  type BranchQuery
} from '../types/branch';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, or, ilike, desc, asc } from 'drizzle-orm';
import type { AuthenticatedRequest } from '../middleware/auth';

export const createBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const validatedData: CreateBranchData = createBranchSchema.parse(req.body);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only create branches for your own church.', 403);
  }

  const existingBranch = await db.query.branches.findFirst({
    where: and(
      eq(branches.churchId, church.id),
      eq(branches.slug, validatedData.slug)
    )
  });

  if (existingBranch) {
    throw new AppError('Branch with this slug already exists in your church', 400);
  }

  if (validatedData.isMainBranch) {
    const existingMainBranch = await db.query.branches.findFirst({
      where: and(
        eq(branches.churchId, church.id),
        eq(branches.isMainBranch, true)
      )
    });

    if (existingMainBranch) {
      await db
        .update(branches)
        .set({ isMainBranch: false, updatedAt: new Date() })
        .where(eq(branches.id, existingMainBranch.id));
    }
  }

  if (validatedData.branchLeader) {
    const leader = await db.query.members.findFirst({
      where: and(
        eq(members.id, validatedData.branchLeader),
        eq(members.churchId, church.id)
      )
    });

    if (!leader) {
      throw new AppError('Branch leader must be a member of the church', 400);
    }
  }

  const [branch] = await db.insert(branches).values({
    ...validatedData,
    churchId: church.id,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Branch created successfully',
    data: { branch }
  });
});

export const getBranches = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const query: BranchQuery = branchQuerySchema.parse(req.query);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only view branches for your own church.', 403);
  }

  const whereConditions = [eq(branches.churchId, church.id)];

  if (query.search) {
    whereConditions.push(
      or(
        ilike(branches.name, `%${query.search}%`),
        ilike(branches.description, `%${query.search}%`),
        ilike(branches.address, `%${query.search}%`)
      )!
    );
  }

  if (query.isActive !== undefined) {
    whereConditions.push(eq(branches.isActive, query.isActive));
  }

  if (query.isMainBranch !== undefined) {
    whereConditions.push(eq(branches.isMainBranch, query.isMainBranch));
  }

  const totalCount = await db.$count(branches, and(...whereConditions));

  const branchList = await db.query.branches.findMany({
    where: and(...whereConditions),
    with: {
      leader: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
        }
      },
      members: {
        columns: {
          id: true,
        }
      },
    },
    limit: query.limit,
    offset: (query.page - 1) * query.limit,
    orderBy: [desc(branches.isMainBranch), asc(branches.name)],
  });

  // Add member count to each branch
  const branchesWithStats = branchList.map(branch => ({
    ...branch,
    memberCount: branch.members.length,
    members: undefined, // Remove the members array from response
  }));

  const totalPages = Math.ceil(totalCount / query.limit);

  res.json({
    status: 'success',
    data: {
      branches: branchesWithStats,
      pagination: {
        page: query.page,
        limit: query.limit,
        totalCount,
        totalPages,
        hasNextPage: query.page < totalPages,
        hasPrevPage: query.page > 1,
      }
    }
  });
});

export const getBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug, branchId } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  if (!branchId) {
    throw new AppError('Branch ID is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only view branches for your own church.', 403);
  }

  const branch = await db.query.branches.findFirst({
    where: and(
      eq(branches.id, branchId),
      eq(branches.churchId, church.id)
    ),
    with: {
      leader: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
        }
      },
      members: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          status: true,
        },
        where: eq(members.status, 'active'),
      }
    }
  });

  if (!branch) {
    throw new AppError('Branch not found', 404);
  }

  // Add member count and statistics
  const branchWithStats = {
    ...branch,
    memberCount: branch.members.length,
    activeMembers: branch.members.filter((m: { status: string; }) => m.status === 'active').length,
  };

  res.json({
    status: 'success',
    data: { branch: branchWithStats }
  });
});

export const updateBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug, branchId } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  if (!branchId) {
    throw new AppError('Branch ID is required', 400);
  }

  const validatedData: UpdateBranchData = updateBranchSchema.parse(req.body);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only update branches for your own church.', 403);
  }

  const branch = await db.query.branches.findFirst({
    where: and(
      eq(branches.id, branchId),
      eq(branches.churchId, church.id)
    )
  });

  if (!branch) {
    throw new AppError('Branch not found', 404);
  }

  if (validatedData.slug && validatedData.slug !== branch.slug) {
    const existingBranch = await db.query.branches.findFirst({
      where: and(
        eq(branches.churchId, church.id),
        eq(branches.slug, validatedData.slug)
      )
    });

    if (existingBranch) {
      throw new AppError('Branch with this slug already exists in your church', 400);
    }
  }

  if (validatedData.isMainBranch && !branch.isMainBranch) {
    const existingMainBranch = await db.query.branches.findFirst({
      where: and(
        eq(branches.churchId, church.id),
        eq(branches.isMainBranch, true)
      )
    });

    if (existingMainBranch) {
      await db
        .update(branches)
        .set({ isMainBranch: false, updatedAt: new Date() })
        .where(eq(branches.id, existingMainBranch.id));
    }
  }

  if (validatedData.branchLeader) {
    const leader = await db.query.members.findFirst({
      where: and(
        eq(members.id, validatedData.branchLeader),
        eq(members.churchId, church.id)
      )
    });

    if (!leader) {
      throw new AppError('Branch leader must be a member of the church', 400);
    }
  }

  const [updatedBranch] = await db
    .update(branches)
    .set({ ...validatedData, updatedAt: new Date() })
    .where(eq(branches.id, branchId))
    .returning();

  res.json({
    status: 'success',
    message: 'Branch updated successfully',
    data: { branch: updatedBranch }
  });
});

export const deleteBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug, branchId } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  if (!branchId) {
    throw new AppError('Branch ID is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only delete branches for your own church.', 403);
  }

  const branch = await db.query.branches.findFirst({
    where: and(
      eq(branches.id, branchId),
      eq(branches.churchId, church.id)
    )
  });

  if (!branch) {
    throw new AppError('Branch not found', 404);
  }

  if (branch.isMainBranch) {
    throw new AppError('Cannot delete the main branch. Please designate another branch as main first.', 400);
  }

  await db
    .update(branches)
    .set({ isActive: false, updatedAt: new Date() })
    .where(eq(branches.id, branchId));

  res.json({
    status: 'success',
    message: 'Branch deleted successfully'
  });
});