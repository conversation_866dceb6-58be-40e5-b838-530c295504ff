import type { Response } from 'express';
import { db } from '../db';
import { members, roles, branches, churches } from '../db/schema';
import { 
  updateMemberSchema, 
  memberFilterSchema, 
  createMemberSchema,
  type MemberFilterInput,
  type CreateMemberInput 
} from '../types/member';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, or, ilike, desc } from 'drizzle-orm';
import type { AuthenticatedRequest } from '../middleware/auth';
import { 
  hashPassword, 
  generateEmailVerificationToken 
} from '../utils/auth';
import { generateUniqueUserId } from '../utils/userIdGenerator';
import { MailService } from '../services/mailService';

export const createMember = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData: CreateMemberInput = createMemberSchema.parse(req.body);

  // Check if user already exists with enhanced error response
  const existingUser = await db.query.members.findFirst({
    where: eq(members.email, validatedData.email)
  });

  if (existingUser) {
    throw new AppError('Member creation failed', 400, 'DUPLICATE_ERROR', [{
      field: 'email',
      message: 'Email address is already registered',
      code: 'DUPLICATE_EMAIL'
    }]);
  }

  // Generate unique user ID
  const userId = await generateUniqueUserId();

  // Always generate a secure temporary password
  const tempPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);
  const hashedPassword = await hashPassword(tempPassword);
  const emailVerificationToken = generateEmailVerificationToken();



  // Get default role if not specified
  let roleId = validatedData.roleId;
  if (!roleId) {
    const defaultRole = await db.query.roles.findFirst({
      where: and(
        eq(roles.churchId, req.user!.churchId),
        eq(roles.name, 'Member')
      )
    });
    roleId = defaultRole?.id;
  } else {
    // Verify role belongs to same church
    const role = await db.query.roles.findFirst({
      where: and(
        eq(roles.id, roleId),
        eq(roles.churchId, req.user!.churchId)
      )
    });
    if (!role) {
      throw new AppError('Member creation failed', 400, 'INVALID_REFERENCE', [{
        field: 'roleId',
        message: 'Selected role is not valid or does not belong to your church',
        code: 'INVALID_ROLE'
      }]);
    }
  }

  // Get main branch if not specified
  let branchId = validatedData.branchId;
  if (!branchId) {
    const mainBranch = await db.query.branches.findFirst({
      where: and(
        eq(branches.churchId, req.user!.churchId),
        eq(branches.isMainBranch, true)
      )
    });
    branchId = mainBranch?.id;
  } else {
    // Verify branch belongs to same church
    const branch = await db.query.branches.findFirst({
      where: and(
        eq(branches.id, branchId),
        eq(branches.churchId, req.user!.churchId)
      )
    });
    if (!branch) {
      throw new AppError('Member creation failed', 400, 'INVALID_REFERENCE', [{
        field: 'branchId',
        message: 'Selected branch is not valid or does not belong to your church',
        code: 'INVALID_BRANCH'
      }]);
    }
  }

  // Get church information for email
  const church = await db.query.churches.findFirst({
    where: eq(churches.id, req.user!.churchId),
    columns: {
      name: true,
    },
  });

  // Create member
  const [newMember] = await db.insert(members).values({
    ...validatedData,
    userId,
    churchId: req.user!.churchId,
    branchId,
    roleId,
    password: hashedPassword,
    emailVerificationToken,
    dateOfBirth: validatedData.dateOfBirth || undefined,
    status: validatedData.status || 'active',
    isEmailVerified: false, // Require email verification
  }).returning();



  // Send welcome email with auto-generated password and verification token
  try {
    const emailSent = await MailService.sendWelcomeEmail(
      newMember.email,
      `${newMember.firstName} ${newMember.lastName}`,
      church?.name || 'Church',
      tempPassword,
      emailVerificationToken
    );
    
    if (emailSent) {
      console.log(`✅ Welcome email sent successfully to ${newMember.email}`);
    } else {
      console.error(`❌ Failed to send welcome email to ${newMember.email}`);
    }
  } catch (error) {
    console.error('Failed to send welcome email:', error);
  }

  // Return member data without sensitive information
  const memberResponse = {
    id: newMember.id,
    userId: newMember.userId,
    firstName: newMember.firstName,
    lastName: newMember.lastName,
    email: newMember.email,
    phone: newMember.phone,
    dateOfBirth: newMember.dateOfBirth,
    gender: newMember.gender,
    address: newMember.address,
    churchId: newMember.churchId,
    branchId: newMember.branchId,
    roleId: newMember.roleId,
    status: newMember.status,
    isEmailVerified: newMember.isEmailVerified,
    joinDate: newMember.joinDate,
    createdAt: newMember.createdAt,
  };

  res.status(201).json({
    status: 'success',
    message: 'Member account created successfully. Login credentials have been sent via email.',
    data: {
      member: memberResponse,
      emailVerificationToken: process.env.NODE_ENV === 'development' ? emailVerificationToken : undefined,
      temporaryPassword: process.env.NODE_ENV === 'development' ? tempPassword : undefined,
    },
  });
});

export const getMembers = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const query: MemberFilterInput = memberFilterSchema.parse(req.query);
  
  const whereConditions = [eq(members.churchId, req.user!.churchId)];

  if (query.search) {
    whereConditions.push(
      or(
        ilike(members.firstName, `%${query.search}%`),
        ilike(members.lastName, `%${query.search}%`),
        ilike(members.email, `%${query.search}%`)
      )!
    );
  }

  if (query.branchId) {
    whereConditions.push(eq(members.branchId, query.branchId));
  }

  if (query.roleId) {
    whereConditions.push(eq(members.roleId, query.roleId));
  }

  if (query.status) {
    whereConditions.push(eq(members.status, query.status));
  }

  if (query.gender) {
    whereConditions.push(eq(members.gender, query.gender));
  }

  const totalCount = await db.$count(members, and(...whereConditions));

  const churchMembers = await db.query.members.findMany({
    where: and(...whereConditions),
    with: {
      role: true,
      branch: {
        columns: {
          id: true,
          name: true,
          slug: true,
        }
      },
    },
    columns: {
      password: false,
      passwordResetToken: false,
      emailVerificationToken: false,
    },
    limit: query.limit,
    offset: (query.page - 1) * query.limit,
    orderBy: [desc(members.createdAt)],
  });

  const totalPages = Math.ceil(totalCount / query.limit);

  res.json({
    status: 'success',
    data: {
      members: churchMembers,
      pagination: {
        page: query.page,
        limit: query.limit,
        totalCount,
        totalPages,
        hasNextPage: query.page < totalPages,
        hasPrevPage: query.page > 1,
      }
    },
  });
});

export const getMember = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
    with: {
      role: true,
      branch: {
        columns: {
          id: true,
          name: true,
          slug: true,
          address: true,
        }
      },
      church: true,
    },
    columns: {
      password: false,
      passwordResetToken: false,
      emailVerificationToken: false,
    },
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  res.json({
    status: 'success',
    data: { member },
  });
});

export const updateMember = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const validatedData = updateMemberSchema.parse(req.body);

  // Verify member belongs to same church
  const existingMember = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!existingMember) {
    throw new AppError('Member not found', 404);
  }

  // If branchId is provided, verify it belongs to the same church
  if (validatedData.branchId) {
    const branch = await db.query.branches.findFirst({
      where: and(
        eq(branches.id, validatedData.branchId),
        eq(branches.churchId, req.user!.churchId)
      )
    });

    if (!branch) {
      throw new AppError('Branch not found or does not belong to your church', 400);
    }
  }

  // Prevent users from updating their own status (except admin)
  if (id === req.user!.id && validatedData.status && !req.user!.permissions.includes('*')) {
    throw new AppError('Cannot change your own status', 400);
  }

  const [updatedMember] = await db.update(members)
    .set({
      ...validatedData,
      dateOfBirth: validatedData.dateOfBirth || undefined,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      phone: members.phone,
      dateOfBirth: members.dateOfBirth,
      gender: members.gender,
      address: members.address,
      profileImage: members.profileImage,
      branchId: members.branchId,
      status: members.status,
      updatedAt: members.updatedAt,
    });

  res.json({
    status: 'success',
    message: 'Member updated successfully',
    data: { member: updatedMember },
  });
});

export const deleteMember = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Verify member belongs to same church
  const existingMember = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!existingMember) {
    throw new AppError('Member not found', 404);
  }

  // Prevent users from deleting themselves
  if (id === req.user!.id) {
    throw new AppError('Cannot delete your own account', 400);
  }

  // Soft delete by setting status to inactive
  await db.update(members)
    .set({ 
      status: 'inactive',
      updatedAt: new Date(),
    })
    .where(eq(members.id, id));

  res.json({
    status: 'success',
    message: 'Member deleted successfully',
  });
});

export const assignRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { roleId } = req.body;

  if (!roleId) {
    throw new AppError('Role ID is required', 400);
  }

  // Verify member belongs to same church
  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  // Verify role belongs to same church
  const role = await db.query.roles.findFirst({
    where: and(
      eq(roles.id, roleId),
      eq(roles.churchId, req.user!.churchId)
    ),
  });

  if (!role) {
    throw new AppError('Role not found', 404);
  }

  // Update member's role
  const [updatedMember] = await db.update(members)
    .set({ 
      roleId,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      roleId: members.roleId,
    });

  res.json({
    status: 'success',
    message: 'Role assigned successfully',
    data: { member: updatedMember },
  });
});

export const assignBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { branchId } = req.body;

  if (!branchId) {
    throw new AppError('Branch ID is required', 400);
  }

  // Verify member belongs to same church
  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  // Verify branch belongs to same church
  const branch = await db.query.branches.findFirst({
    where: and(
      eq(branches.id, branchId),
      eq(branches.churchId, req.user!.churchId)
    ),
  });

  if (!branch) {
    throw new AppError('Branch not found or does not belong to your church', 404);
  }

  // Update member's branch
  const [updatedMember] = await db.update(members)
    .set({ 
      branchId,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      branchId: members.branchId,
    });

  res.json({
    status: 'success',
    message: 'Branch assigned successfully',
    data: { member: updatedMember },
  });
});

export const removeBranch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Verify member belongs to same church
  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  // Remove branch assignment
  const [updatedMember] = await db.update(members)
    .set({ 
      branchId: null,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      branchId: members.branchId,
    });

  res.json({
    status: 'success',
    message: 'Branch assignment removed successfully',
    data: { member: updatedMember },
  });
});

export const removeRole = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Verify member belongs to same church
  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  // Remove role
  const [updatedMember] = await db.update(members)
    .set({ 
      roleId: null,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      roleId: members.roleId,
    });

  res.json({
    status: 'success',
    message: 'Role removed successfully',
    data: { member: updatedMember },
  });
});

export const verifyMemberEmail = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  // Verify member belongs to same church
  const member = await db.query.members.findFirst({
    where: and(
      eq(members.id, id),
      eq(members.churchId, req.user!.churchId)
    ),
  });

  if (!member) {
    throw new AppError('Member not found', 404);
  }

  if (member.isEmailVerified) {
    throw new AppError('Member email is already verified', 400);
  }

  // Verify the member's email
  const [updatedMember] = await db.update(members)
    .set({ 
      isEmailVerified: true,
      emailVerificationToken: null,
      updatedAt: new Date(),
    })
    .where(eq(members.id, id))
    .returning({
      id: members.id,
      firstName: members.firstName,
      lastName: members.lastName,
      email: members.email,
      isEmailVerified: members.isEmailVerified,
    });

  res.json({
    status: 'success',
    message: 'Member email verified successfully',
    data: { member: updatedMember },
  });
});