import type { Request, Response } from 'express';
import { db } from '../db';
import { churches, members, roles, branches } from '../db/schema';
import {
  createChurchSchema,
  updateChurchSchema,
  registerChurchSchema,
  churchSettingsSchema,
  type CreateChurchInput,
  type UpdateChurchInput,
  type RegisterChurchInput,
  type ChurchSettingsInput
} from '../types/church';
import { catchAsync, AppError } from '../utils/errorHandler';
import { seedRoles } from '../utils/seedData';
import { eq, and } from 'drizzle-orm';
import type { AuthenticatedRequest } from '../middleware/auth';
import { hashPassword, generateAccessToken, generateRefreshToken, getTokenExpiration } from '../utils/auth';
import { refreshTokens } from '../db/schema';
import { generateUniqueUserId } from '../utils/userIdGenerator';

export const registerChurch = catchAsync(async (req: Request, res: Response) => {
  const validatedData: RegisterChurchInput = registerChurchSchema.parse(req.body);

  const existingChurch = await db.query.churches.findFirst({
    where: eq(churches.slug, validatedData.church.slug)
  });

  if (existingChurch) {
    throw new AppError('Church with this slug already exists', 400);
  }

  const existingUser = await db.query.members.findFirst({
    where: eq(members.email, validatedData.admin.email)
  });

  if (existingUser) {
    throw new AppError('Email already registered', 400);
  }

  // Generate a unique 6-digit church code
  const churchCode = Math.floor(100000 + Math.random() * 900000).toString();

  const [church] = await db.insert(churches).values({
    ...validatedData.church,
    churchCode,
    settings: JSON.stringify({
      allowSelfRegistration: true,
      requireEmailVerification: true,
      timezone: 'UTC',
      locale: 'en',
      theme: {
        primaryColor: '#3B82F6',
        secondaryColor: '#64748B',
      },
      features: {
        events: true,
        donations: false,
        messaging: true,
        calendar: true,
      }
    })
  }).returning();

  if (!church) {
    throw new AppError('Failed to create church', 500);
  }

  await seedRoles(church.id);

  const superAdminRole = await db.query.roles.findFirst({
    where: and(
      eq(roles.churchId, church.id),
      eq(roles.name, 'Super Admin')
    )
  });

  // Create main branch for the church
  const [mainBranch] = await db.insert(branches).values({
    name: `${church.name} - Main Campus`,
    slug: `${church.slug}-main`,
    description: 'Main church campus',
    address: church.address,
    phone: church.phone,
    email: church.email,
    churchId: church.id,
    isMainBranch: true,
    isActive: true,
  }).returning();

  // Generate unique user ID for admin
  const adminUserId = await generateUniqueUserId();
  const hashedPassword = await hashPassword(validatedData.admin.password);

  const [admin] = await db.insert(members).values({
    ...validatedData.admin,
    userId: adminUserId,
    password: hashedPassword,
    churchId: church.id,
    branchId: mainBranch.id,
    roleId: superAdminRole?.id,
    dateOfBirth: validatedData.admin.dateOfBirth ? new Date(validatedData.admin.dateOfBirth) : undefined,
    isEmailVerified: true,
  }).returning();

  const accessToken = generateAccessToken({
    id: admin.id,
    email: admin.email,
    firstName: admin.firstName,
    lastName: admin.lastName,
    churchId: admin.churchId,
    roleId: admin.roleId || undefined,
    permissions: superAdminRole?.permissions as string[] || [],
  });

  const refreshTokenValue = generateRefreshToken();
  const refreshTokenExpiry = getTokenExpiration(7 * 24);

  await db.insert(refreshTokens).values({
    memberId: admin.id,
    token: refreshTokenValue,
    expiresAt: refreshTokenExpiry,
  });

  // Generate frontend URLs instead of API URLs
  const frontendBaseUrl = process.env.FRONTEND_URL || 'http://localhost:3002';
  const churchUrl = `${frontendBaseUrl}/${church.slug}`;
  const adminUrl = `${frontendBaseUrl}/${church.slug}/dashboard`;

  res.status(201).json({
    status: 'success',
    message: 'Church and admin account created successfully',
    data: {
      church: {
        id: church.id,
        name: church.name,
        slug: church.slug,
        description: church.description,
        email: church.email,
        phone: church.phone,
        website: church.website,
        createdAt: church.createdAt,
      },
      admin: {
        id: admin.id,
        userId: admin.userId,
        firstName: admin.firstName,
        lastName: admin.lastName,
        email: admin.email,
        churchId: admin.churchId,
        roleId: admin.roleId,
      },
      mainBranch: {
        id: mainBranch.id,
        name: mainBranch.name,
        slug: mainBranch.slug,
        isMainBranch: mainBranch.isMainBranch,
      },
      tokens: {
        accessToken,
        refreshToken: refreshTokenValue,
        expiresIn: '15m',
      },
      churchUrl,
      adminUrl
    }
  });
});

export const createChurch = catchAsync(async (req: Request, res: Response) => {
  const validatedData: CreateChurchInput = createChurchSchema.parse(req.body);

  const existingChurch = await db.query.churches.findFirst({
    where: eq(churches.slug, validatedData.slug)
  });

  if (existingChurch) {
    throw new AppError('Church with this slug already exists', 400);
  }

  // Generate a unique 6-digit church code
  const churchCode = Math.floor(100000 + Math.random() * 900000).toString();

  const [church] = await db.insert(churches).values({
    ...validatedData,
    churchCode
  }).returning();

  if (!church) {
    throw new AppError('Failed to create church', 500);
  }

  await seedRoles(church.id);

  const churchUrl = `${req.protocol}://${req.get('host')}/api/churches/${church.slug}`;

  res.status(201).json({
    status: 'success',
    data: {
      church,
      churchUrl,
      membersUrl: `${churchUrl}/members`
    }
  });
});

export const getChurches = catchAsync(async (req: Request, res: Response) => {
  const churchList = await db.query.churches.findMany({
    where: eq(churches.isActive, true),
    columns: {
      id: true,
      name: true,
      slug: true,
      description: true,
      email: true,
      phone: true,
      website: true,
      logo: true,
      createdAt: true,
    }
  });

  res.json({
    status: 'success',
    data: { churches: churchList }
  });
});

export const getChurch = catchAsync(async (req: Request, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Church ID is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: and(eq(churches.id, id), eq(churches.isActive, true)),
    columns: {
      id: true,
      name: true,
      slug: true,
      description: true,
      address: true,
      phone: true,
      email: true,
      website: true,
      logo: true,
      createdAt: true,
      updatedAt: true,
    }
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  res.json({
    status: 'success',
    data: { church }
  });
});

export const getChurchBySlug = catchAsync(async (req: Request, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: and(eq(churches.slug, slug), eq(churches.isActive, true)),
    columns: {
      id: true,
      name: true,
      slug: true,
      churchCode: true,
      description: true,
      address: true,
      phone: true,
      email: true,
      website: true,
      logo: true,
      createdAt: true,
      updatedAt: true,
    }
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  res.json({
    status: 'success',
    data: { church }
  });
});

export const updateChurch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const validatedData: UpdateChurchInput = updateChurchSchema.parse(req.body);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only update your own church.', 403);
  }

  const result = await db
    .update(churches)
    .set({ ...validatedData, updatedAt: new Date() })
    .where(eq(churches.id, church.id))
    .returning();

  const updatedChurch = result[0];

  if (!updatedChurch) {
    throw new AppError('Church update failed', 500);
  }

  res.json({
    status: 'success',
    message: 'Church updated successfully',
    data: { church: updatedChurch }
  });
});

export const updateChurchSettings = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const validatedData: ChurchSettingsInput = churchSettingsSchema.parse(req.body);

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only update your own church settings.', 403);
  }

  const currentSettings = typeof church.settings === 'string'
    ? JSON.parse(church.settings)
    : church.settings || {};

  const result = await db
    .update(churches)
    .set({
      settings: JSON.stringify({ ...currentSettings, ...validatedData }),
      updatedAt: new Date()
    })
    .where(eq(churches.id, church.id))
    .returning();

  const updatedChurch = result[0];

  if (!updatedChurch) {
    throw new AppError('Church settings update failed', 500);
  }

  res.json({
    status: 'success',
    message: 'Church settings updated successfully',
    data: {
      church: {
        id: updatedChurch.id,
        name: updatedChurch.name,
        slug: updatedChurch.slug,
        settings: updatedChurch.settings,
        updatedAt: updatedChurch.updatedAt,
      }
    }
  });
});

export const getChurchSettings = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug),
    columns: {
      id: true,
      name: true,
      slug: true,
      settings: true,
    }
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only view your own church settings.', 403);
  }

  res.json({
    status: 'success',
    data: {
      church: {
        id: church.id,
        name: church.name,
        slug: church.slug,
        settings: church.settings,
      }
    }
  });
});

export const updateChurchLogo = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const { logo } = req.body;

  if (!logo) {
    throw new AppError('Logo data is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only update your own church logo.', 403);
  }

  const result = await db
    .update(churches)
    .set({ logo, updatedAt: new Date() })
    .where(eq(churches.id, church.id))
    .returning();

  const updatedChurch = result[0];

  if (!updatedChurch) {
    throw new AppError('Church logo update failed', 500);
  }

  res.json({
    status: 'success',
    message: 'Church logo updated successfully',
    data: { 
      church: {
        id: updatedChurch.id,
        name: updatedChurch.name,
        slug: updatedChurch.slug,
        logo: updatedChurch.logo,
        updatedAt: updatedChurch.updatedAt,
      }
    }
  });
});

export const deleteChurch = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { slug } = req.params;

  if (!slug) {
    throw new AppError('Church slug is required', 400);
  }

  const church = await db.query.churches.findFirst({
    where: eq(churches.slug, slug)
  });

  if (!church) {
    throw new AppError('Church not found', 404);
  }

  if (req.user!.churchId !== church.id) {
    throw new AppError('Access denied. You can only delete your own church.', 403);
  }

  await db
    .update(churches)
    .set({ isActive: false, updatedAt: new Date() })
    .where(eq(churches.id, church.id))
    .returning();

  res.json({
    status: 'success',
    message: 'Church deleted successfully'
  });
});