import type { Response } from 'express';
import { db } from '../db';
import { donations, donationCategories, events } from '../db/schema';
import {
  createDonationSchema,
  updateDonationSchema,
  donationFilterSchema,
  createDonationCategorySchema,
  updateDonationCategorySchema,
  donationReportSchema,
  type CreateDonationInput,
  type UpdateDonationInput,
  type DonationFilterInput,
  type CreateDonationCategoryInput,
  type UpdateDonationCategoryInput,
  type DonationReportInput
} from '../types/donation';
import { catchAsync, AppError } from '../utils/errorHandler';
import { eq, and, gte, lte, like, or, desc, sql, sum } from 'drizzle-orm';
import type { TenantRequest } from '../middleware/tenant';

export const createDonation = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: CreateDonationInput = createDonationSchema.parse(req.body);

  if (!req.church || !req.user) {
    throw new AppError('Invalid request context', 400);
  }

  // Verify event belongs to church if eventId provided
  if (validatedData.eventId) {
    const event = await db.query.events.findFirst({
      where: and(
        eq(events.id, validatedData.eventId),
        eq(events.churchId, req.church.id)
      )
    });

    if (!event) {
      throw new AppError('Event not found', 404);
    }

    if (!event.allowDonations) {
      throw new AppError('Donations are not allowed for this event', 400);
    }
  }

  // Generate receipt number if not provided
  let receiptNumber = validatedData.receiptNumber;
  if (!receiptNumber) {
    const year = new Date().getFullYear();
    const count = await db.select({ count: sql<number>`count(*)` })
      .from(donations)
      .where(eq(donations.churchId, req.church.id))
      .then(result => result[0]?.count || 0);
    receiptNumber = `${req.church.slug.toUpperCase()}-${year}-${String(count + 1).padStart(4, '0')}`;
  }

  const [donation] = await db.insert(donations).values({
    ...validatedData,
    amount: validatedData.amount.toString(),
    churchId: req.church.id,
    donorId: validatedData.isAnonymous ? null : req.user.id,
    recordedBy: req.user.id,
    receiptNumber,
    donatedAt: validatedData.donatedAt ? new Date(validatedData.donatedAt) : new Date(),
    metadata: validatedData.metadata ? JSON.stringify(validatedData.metadata) : undefined,
  }).returning();

  res.status(201).json({
    status: 'success',
    message: 'Donation recorded successfully',
    data: { donation }
  });
});

export const getDonations = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: DonationFilterInput = donationFilterSchema.parse(req.query);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [eq(donations.churchId, req.church.id)];

  if (filters.type) {
    whereConditions.push(eq(donations.type, filters.type));
  }

  if (filters.method) {
    whereConditions.push(eq(donations.method, filters.method));
  }

  if (filters.status) {
    whereConditions.push(eq(donations.status, filters.status));
  }

  if (filters.eventId) {
    whereConditions.push(eq(donations.eventId, filters.eventId));
  }

  if (filters.donorId) {
    whereConditions.push(eq(donations.donorId, filters.donorId));
  }

  if (filters.startDate) {
    whereConditions.push(gte(donations.donatedAt, new Date(filters.startDate)));
  }

  if (filters.endDate) {
    whereConditions.push(lte(donations.donatedAt, new Date(filters.endDate)));
  }

  if (filters.minAmount) {
    whereConditions.push(gte(donations.amount, filters.minAmount.toString()));
  }

  if (filters.maxAmount) {
    whereConditions.push(lte(donations.amount, filters.maxAmount.toString()));
  }

  if (!filters.includeAnonymous) {
    whereConditions.push(eq(donations.isAnonymous, false));
  }

  if (filters.search) {
    const searchCondition = or(
      like(donations.donorName, `%${filters.search}%`),
      like(donations.description, `%${filters.search}%`),
      like(donations.receiptNumber, `%${filters.search}%`)
    );
    if (searchCondition) {
      whereConditions.push(searchCondition);
    }
  }

  const offset = (filters.page - 1) * filters.limit;

  const [donationList, totalCount, totalAmount] = await Promise.all([
    db.query.donations.findMany({
      where: and(...whereConditions),
      with: {
        donor: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          }
        },
        event: {
          columns: {
            id: true,
            title: true,
            type: true,
          }
        },
        recordedBy: {
          columns: {
            id: true,
            firstName: true,
            lastName: true,
          }
        }
      },
      orderBy: [desc(donations.donatedAt)],
      limit: filters.limit,
      offset,
    }),
    db.select({ count: sql<number>`count(*)` })
      .from(donations)
      .where(and(...whereConditions))
      .then(result => result[0]?.count || 0),
    db.select({ total: sum(donations.amount) })
      .from(donations)
      .where(and(...whereConditions))
      .then(result => result[0]?.total || 0)
  ]);

  const donationsWithParsedData = donationList.map(donation => ({
    ...donation,
    metadata: donation.metadata ? JSON.parse(donation.metadata as string) : null,
  }));

  res.json({
    status: 'success',
    data: {
      donations: donationsWithParsedData,
      summary: {
        totalAmount: parseFloat(totalAmount.toString()),
        totalCount,
        currency: donationList[0]?.currency || 'USD',
      },
      pagination: {
        page: filters.page,
        limit: filters.limit,
        total: totalCount,
        pages: Math.ceil(totalCount / filters.limit)
      }
    }
  });
});

export const getDonation = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Donation ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const donation = await db.query.donations.findFirst({
    where: and(
      eq(donations.id, id),
      eq(donations.churchId, req.church.id)
    ),
    with: {
      donor: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
          email: true,
          phone: true,
        }
      },
      event: {
        columns: {
          id: true,
          title: true,
          type: true,
          startDate: true,
        }
      },
      recordedBy: {
        columns: {
          id: true,
          firstName: true,
          lastName: true,
        }
      }
    }
  });

  if (!donation) {
    throw new AppError('Donation not found', 404);
  }

  const donationWithParsedData = {
    ...donation,
    metadata: donation.metadata ? JSON.parse(donation.metadata as string) : null,
  };

  res.json({
    status: 'success',
    data: { donation: donationWithParsedData }
  });
});

export const updateDonation = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Donation ID is required', 400);
  }

  const validatedData: UpdateDonationInput = updateDonationSchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingDonation = await db.query.donations.findFirst({
    where: and(
      eq(donations.id, id),
      eq(donations.churchId, req.church.id)
    )
  });

  if (!existingDonation) {
    throw new AppError('Donation not found', 404);
  }

  const updateData: any = {
    ...validatedData,
    updatedAt: new Date(),
  };

  if (validatedData.donatedAt) {
    updateData.donatedAt = new Date(validatedData.donatedAt);
  }

  if (validatedData.metadata) {
    updateData.metadata = JSON.stringify(validatedData.metadata);
  }

  // Convert amount to string if provided
  if (updateData.amount !== undefined) {
    updateData.amount = updateData.amount.toString();
  }

  const [updatedDonation] = await db
    .update(donations)
    .set(updateData)
    .where(eq(donations.id, id))
    .returning();

  res.json({
    status: 'success',
    message: 'Donation updated successfully',
    data: { donation: updatedDonation }
  });
});

export const deleteDonation = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Donation ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingDonation = await db.query.donations.findFirst({
    where: and(
      eq(donations.id, id),
      eq(donations.churchId, req.church.id)
    )
  });

  if (!existingDonation) {
    throw new AppError('Donation not found', 404);
  }

  await db.delete(donations).where(eq(donations.id, id));

  res.json({
    status: 'success',
    message: 'Donation deleted successfully'
  });
});

export const getDonationReport = catchAsync(async (req: TenantRequest, res: Response) => {
  const filters: DonationReportInput = donationReportSchema.parse(req.query);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  let whereConditions = [
    eq(donations.churchId, req.church.id),
    gte(donations.donatedAt, new Date(filters.startDate)),
    lte(donations.donatedAt, new Date(filters.endDate))
  ];

  if (filters.eventId) {
    whereConditions.push(eq(donations.eventId, filters.eventId));
  }

  if (!filters.includeAnonymous) {
    whereConditions.push(eq(donations.isAnonymous, false));
  }

  let groupByClause;
  let selectClause;

  switch (filters.groupBy) {
    case 'day':
      groupByClause = sql`DATE(donated_at)`;
      selectClause = sql`DATE(donated_at) as period`;
      break;
    case 'week':
      groupByClause = sql`DATE_TRUNC('week', donated_at)`;
      selectClause = sql`DATE_TRUNC('week', donated_at) as period`;
      break;
    case 'month':
      groupByClause = sql`DATE_TRUNC('month', donated_at)`;
      selectClause = sql`DATE_TRUNC('month', donated_at) as period`;
      break;
    case 'year':
      groupByClause = sql`DATE_TRUNC('year', donated_at)`;
      selectClause = sql`DATE_TRUNC('year', donated_at) as period`;
      break;
    case 'type':
      groupByClause = donations.type;
      selectClause = sql`${donations.type} as period`;
      break;
    case 'method':
      groupByClause = donations.method;
      selectClause = sql`${donations.method} as period`;
      break;
    default:
      groupByClause = sql`DATE_TRUNC('month', donated_at)`;
      selectClause = sql`DATE_TRUNC('month', donated_at) as period`;
  }

  const reportData = await db
    .select({
      period: selectClause,
      totalAmount: sum(donations.amount),
      count: sql<number>`count(*)`,
    })
    .from(donations)
    .where(and(...whereConditions))
    .groupBy(groupByClause)
    .orderBy(selectClause);

  const totalAmount = await db
    .select({ total: sum(donations.amount) })
    .from(donations)
    .where(and(...whereConditions))
    .then(result => result[0]?.total || 0);

  res.json({
    status: 'success',
    data: {
      report: reportData.map(item => ({
        period: item.period,
        totalAmount: parseFloat(item.totalAmount?.toString() || '0'),
        count: item.count,
      })),
      summary: {
        totalAmount: parseFloat(totalAmount.toString()),
        startDate: filters.startDate,
        endDate: filters.endDate,
        groupBy: filters.groupBy,
      }
    }
  });
});

export const createDonationCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const validatedData: CreateDonationCategoryInput = createDonationCategorySchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  // Prepare insert data with proper type conversions
  const insertData: any = {
    ...validatedData,
    churchId: req.church.id,
  };

  // Convert targetAmount to string if provided
  if (insertData.targetAmount !== undefined) {
    insertData.targetAmount = insertData.targetAmount.toString();
  }

  const [category] = await db.insert(donationCategories).values(insertData).returning();

  res.status(201).json({
    status: 'success',
    message: 'Donation category created successfully',
    data: { category }
  });
});

export const getDonationCategories = catchAsync(async (req: TenantRequest, res: Response) => {
  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const categories = await db.query.donationCategories.findMany({
    where: eq(donationCategories.churchId, req.church.id),
    orderBy: [donationCategories.name]
  });

  res.json({
    status: 'success',
    data: { categories }
  });
});

export const updateDonationCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Category ID is required', 400);
  }

  const validatedData: UpdateDonationCategoryInput = updateDonationCategorySchema.parse(req.body);

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingCategory = await db.query.donationCategories.findFirst({
    where: and(
      eq(donationCategories.id, id),
      eq(donationCategories.churchId, req.church.id)
    )
  });

  if (!existingCategory) {
    throw new AppError('Donation category not found', 404);
  }

  // Prepare update data with proper type conversions
  const updateData: any = {
    ...validatedData,
    updatedAt: new Date(),
  };

  // Convert targetAmount to string if provided
  if (updateData.targetAmount !== undefined) {
    updateData.targetAmount = updateData.targetAmount.toString();
  }

  const result = await db
    .update(donationCategories)
    .set(updateData)
    .where(eq(donationCategories.id, id))
    .returning();

  const updatedCategory = result[0];

  if (!updatedCategory) {
    throw new AppError('Category update failed', 500);
  }

  res.json({
    status: 'success',
    message: 'Donation category updated successfully',
    data: { category: updatedCategory }
  });
});

export const deleteDonationCategory = catchAsync(async (req: TenantRequest, res: Response) => {
  const { id } = req.params;

  if (!id) {
    throw new AppError('Category ID is required', 400);
  }

  if (!req.church) {
    throw new AppError('Church context required', 400);
  }

  const existingCategory = await db.query.donationCategories.findFirst({
    where: and(
      eq(donationCategories.id, id),
      eq(donationCategories.churchId, req.church.id)
    )
  });

  if (!existingCategory) {
    throw new AppError('Donation category not found', 404);
  }

  await db.delete(donationCategories).where(eq(donationCategories.id, id));

  res.json({
    status: 'success',
    message: 'Donation category deleted successfully'
  });
});