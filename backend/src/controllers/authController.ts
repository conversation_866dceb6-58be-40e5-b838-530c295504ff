import type { Request, Response } from 'express';
import { db } from '../db';
import { members, churches, roles } from '../db/schema';
import { eq } from 'drizzle-orm';
import {
  changePasswordSchema,
  forgotPasswordSchema,
  resetPasswordSchema,
  updateProfileSchema,
  type ChangePasswordInput,
  type ForgotPasswordInput,
  type ResetPasswordInput,
  type UpdateProfileInput
} from '../types/auth';
import {
  loginSchema,
  type LoginInput
} from '../types/member';
import {
  hashPassword,
  comparePassword,
  generateAccessToken,
  generatePasswordResetToken,
  getTokenExpiration,
  isTokenExpired
} from '../utils/auth';
import { catchAsync, AppError } from '../utils/errorHandler';
import type { AuthenticatedRequest } from '../middleware/auth';
import { TokenService } from '../utils/tokenService';
import { SecurityAuditLogger, SecurityEventType } from '../utils/securityAudit';
import { isValidEmailFormat } from '../utils/userIdGenerator';
import { MailService } from '../services/mailService';

export const register = catchAsync(async (_req: Request, _res: Response) => {
  // Public registration is disabled - only church registration through onboarding is allowed
  throw new AppError('Public registration is not allowed. Please contact your church administrator to create an account.', 403);
});

export const login = catchAsync(async (req: Request, res: Response) => {
  const validatedData: LoginInput = loginSchema.parse(req.body);

  try {
    // Determine if input is email or userId
    const isEmail = isValidEmailFormat(validatedData.emailOrUserId);
    
    // Find user by email or userId (simplified query)
    const user = await db.query.members.findFirst({
      where: isEmail 
        ? eq(members.email, validatedData.emailOrUserId)
        : eq(members.userId, validatedData.emailOrUserId),
    });

    if (!user) {
      SecurityAuditLogger.logAuthFailure(req, 'User not found', {
        emailOrUserId: validatedData.emailOrUserId,
        isEmail
      });
      throw new AppError('Invalid email/user ID or password. If you don\'t have an account, please contact your church administrator.', 401);
    }

    // Get user's role and church separately
    const [userRole, userChurch] = await Promise.all([
      user.roleId ? db.query.roles.findFirst({
        where: eq(roles.id, user.roleId)
      }) : null,
      db.query.churches.findFirst({
        where: eq(churches.id, user.churchId)
      })
    ]);

    if (!userChurch) {
      SecurityAuditLogger.logAuthFailure(req, 'Church not found', {
        churchId: user.churchId
      });
      throw new AppError('Church not found. Please contact support.', 404);
    }

    // Create combined user object
    const userWithRelations = {
      ...user,
      role: userRole,
      church: userChurch
    };

    if (!(await comparePassword(validatedData.password, userWithRelations.password))) {
      SecurityAuditLogger.logAuthFailure(req, 'Invalid credentials', {
        emailOrUserId: validatedData.emailOrUserId,
        isEmail
      });
      throw new AppError('Invalid email/user ID or password. If you don\'t have an account, please contact your church administrator.', 401);
    }

    // Check if the user's church is active
    if (!userWithRelations.church.isActive) {
      SecurityAuditLogger.logAuthFailure(req, 'Church inactive', {
        churchId: userWithRelations.church.id,
        emailOrUserId: validatedData.emailOrUserId
      });
      throw new AppError('Church is inactive. Please contact support.', 403);
    }

    if (userWithRelations.status !== 'active') {
      SecurityAuditLogger.logAuthFailure(req, 'Account inactive', {
        userId: userWithRelations.id,
        status: userWithRelations.status
      });
      throw new AppError('Account is inactive. Please contact your church administrator.', 403);
    }

    if (!userWithRelations.isEmailVerified) {
      SecurityAuditLogger.logAuthFailure(req, 'Email not verified', {
        userId: userWithRelations.id
      });
      throw new AppError('Please verify your email address before logging in. Check your email for a verification link.', 403);
    }

    // Update last login
    await db.update(members)
      .set({ lastLoginAt: new Date() })
      .where(eq(members.id, userWithRelations.id));

    // Generate access token
    const accessToken = generateAccessToken({
      id: userWithRelations.id,
      email: userWithRelations.email,
      firstName: userWithRelations.firstName,
      lastName: userWithRelations.lastName,
      churchId: userWithRelations.churchId,
      roleId: userWithRelations.roleId || undefined,
      permissions: userWithRelations.role?.permissions as string[] || [],
    });

    // Revoke old refresh tokens with audit logging
    const revokedCount = await TokenService.revokeAllUserTokens(userWithRelations.id, {
      reason: 'new_login',
      revokedBy: userWithRelations.id,
    });

    if (revokedCount > 0) {
      SecurityAuditLogger.logSessionEvent(
        SecurityEventType.ALL_SESSIONS_REVOKED,
        userWithRelations.id,
        undefined,
        { reason: 'new_login', revokedCount }
      );
    }

    // Create new refresh token with metadata
    const metadata = TokenService.extractMetadata(req);
    const refreshTokenValue = await TokenService.createRefreshToken(userWithRelations.id, metadata);

    // Set httpOnly cookie for refresh token
    TokenService.setRefreshTokenCookie(res, refreshTokenValue);

    // Log successful authentication
    SecurityAuditLogger.logAuthSuccess(req, userWithRelations.id, userWithRelations.churchId);
    SecurityAuditLogger.logSessionEvent(
      SecurityEventType.SESSION_CREATED,
      userWithRelations.id,
      undefined,
      { loginMethod: 'password' }
    );

    res.json({
      status: 'success',
      message: 'Login successful',
      data: {
        user: {
          id: userWithRelations.id,
          firstName: userWithRelations.firstName,
          lastName: userWithRelations.lastName,
          email: userWithRelations.email,
          churchId: userWithRelations.churchId,
          church: {
            id: userWithRelations.church.id,
            name: userWithRelations.church.name,
            slug: userWithRelations.church.slug,
          },
          role: userWithRelations.role ? {
            id: userWithRelations.role.id,
            name: userWithRelations.role.name,
            permissions: userWithRelations.role.permissions,
          } : null,
          isEmailVerified: userWithRelations.isEmailVerified,
          lastLoginAt: userWithRelations.lastLoginAt,
        },
        tokens: {
          accessToken,
          refreshToken: refreshTokenValue, // Still include in response for backward compatibility
          expiresIn: '15m',
        },
      },
    });
  } catch (error) {
    // Re-throw AppErrors as they already have proper logging
    if (error instanceof AppError) {
      throw error;
    }

    // Log unexpected errors
    SecurityAuditLogger.logAuthFailure(req, 'Unexpected error during login', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
});

export const refreshToken = catchAsync(async (req: Request, res: Response) => {
  try {
    // Get refresh token from cookie or request body
    const refreshTokenValue = TokenService.getRefreshToken(req);

    if (!refreshTokenValue) {
      SecurityAuditLogger.logTokenRefresh(req, 'unknown', false, 'No refresh token provided');
      throw new AppError('Refresh token is required', 401);
    }

    // Validate and use the refresh token
    const metadata = TokenService.extractMetadata(req);
    const result = await TokenService.validateAndUseRefreshToken(refreshTokenValue, metadata);

    if (!result) {
      SecurityAuditLogger.logTokenRefresh(req, 'unknown', false, 'Invalid or expired refresh token');
      throw new AppError('Invalid or expired refresh token', 401);
    }

    const { member, tokenRecord } = result;

    // Generate new access token
    const accessToken = generateAccessToken({
      id: member.id,
      email: member.email,
      firstName: member.firstName,
      lastName: member.lastName,
      churchId: member.churchId,
      roleId: member.roleId || undefined,
      permissions: member.role?.permissions as string[] || [],
    });

    // Revoke old token with audit logging
    await TokenService.revokeToken(refreshTokenValue, {
      reason: 'token_refresh',
      revokedBy: member.id,
    });

    SecurityAuditLogger.logTokenRevocation(
      member.id,
      tokenRecord.id,
      'token_refresh',
      member.id
    );

    // Create new refresh token
    const newRefreshToken = await TokenService.createRefreshToken(member.id, metadata);

    // Set new httpOnly cookie
    TokenService.setRefreshTokenCookie(res, newRefreshToken);

    // Log successful token refresh
    SecurityAuditLogger.logTokenRefresh(req, member.id, true);

    res.json({
      status: 'success',
      data: {
        tokens: {
          accessToken,
          refreshToken: newRefreshToken, // Still include in response for backward compatibility
          expiresIn: '15m',
        },
      },
    });
  } catch (error) {
    // Re-throw AppErrors as they already have proper logging
    if (error instanceof AppError) {
      throw error;
    }

    // Log unexpected errors
    SecurityAuditLogger.logTokenRefresh(req, 'unknown', false,
      error instanceof Error ? error.message : 'Unknown error'
    );
    throw error;
  }
});

export const logout = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  try {
    // Get refresh token from cookie or request body
    const refreshTokenValue = TokenService.getRefreshToken(req);

    if (refreshTokenValue) {
      await TokenService.revokeToken(refreshTokenValue, {
        reason: 'user_logout',
        revokedBy: req.user?.id,
      });

      SecurityAuditLogger.logTokenRevocation(
        req.user?.id || 'unknown',
        undefined,
        'user_logout',
        req.user?.id
      );
    }

    // Revoke all user's refresh tokens for complete logout
    if (req.user) {
      const revokedCount = await TokenService.revokeAllUserTokens(req.user.id, {
        reason: 'user_logout',
        revokedBy: req.user.id,
      });

      SecurityAuditLogger.logSessionEvent(
        SecurityEventType.ALL_SESSIONS_REVOKED,
        req.user.id,
        undefined,
        { reason: 'user_logout', revokedCount }
      );

      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.LOGOUT,
        userId: req.user.id,
        churchId: req.user.churchId,
        ipAddress: TokenService.extractMetadata(req).ipAddress,
        userAgent: TokenService.extractMetadata(req).userAgent,
        endpoint: req.path,
        method: req.method,
        success: true,
        severity: 'low',
      });
    }

    // Clear httpOnly cookie
    TokenService.clearRefreshTokenCookie(res);

    res.json({
      status: 'success',
      message: 'Logged out successfully',
    });
  } catch (error) {
    // Log error but still return success to prevent information leakage
    SecurityAuditLogger.logEvent({
      eventType: SecurityEventType.LOGOUT,
      userId: req.user?.id,
      success: false,
      severity: 'medium',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error'
      },
    });

    res.json({
      status: 'success',
      message: 'Logged out successfully',
    });
  }
});

export const getProfile = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const user = await db.query.members.findFirst({
    where: eq(members.id, req.user!.id),
    with: {
      role: true,
      branch: {
        columns: {
          id: true,
          name: true,
          slug: true,
          address: true,
        }
      },
      church: true,
    },
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  res.json({
    status: 'success',
    data: {
      user: {
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        address: user.address,
        profileImage: user.profileImage,
        joinDate: user.joinDate,
        status: user.status,
        isEmailVerified: user.isEmailVerified,
        lastLoginAt: user.lastLoginAt,
        church: {
          id: user.church.id,
          name: user.church.name,
          slug: user.church.slug,
        },
        branch: user.branch ? {
          id: user.branch.id,
          name: user.branch.name,
          slug: user.branch.slug,
          address: user.branch.address,
        } : null,
        role: user.role ? {
          id: user.role.id,
          name: user.role.name,
          permissions: user.role.permissions,
        } : null,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      },
    },
  });
});

export const updateProfile = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData: UpdateProfileInput = updateProfileSchema.parse(req.body);

  const result = await db.update(members)
    .set({
      ...validatedData,
      dateOfBirth: validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : undefined,
      updatedAt: new Date(),
    })
    .where(eq(members.id, req.user!.id))
    .returning();

  const updatedUser = result[0];

  if (!updatedUser) {
    throw new AppError('User not found or update failed', 404);
  }

  res.json({
    status: 'success',
    message: 'Profile updated successfully',
    data: {
      user: {
        id: updatedUser.id,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        email: updatedUser.email,
        phone: updatedUser.phone,
        dateOfBirth: updatedUser.dateOfBirth,
        gender: updatedUser.gender,
        address: updatedUser.address,
        profileImage: updatedUser.profileImage,
        updatedAt: updatedUser.updatedAt,
      },
    },
  });
});

export const changePassword = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const validatedData: ChangePasswordInput = changePasswordSchema.parse(req.body);

  const user = await db.query.members.findFirst({
    where: eq(members.id, req.user!.id),
  });

  if (!user || !(await comparePassword(validatedData.currentPassword, user.password))) {
    SecurityAuditLogger.logEvent({
      eventType: SecurityEventType.PASSWORD_CHANGED,
      userId: req.user!.id,
      success: false,
      severity: 'medium',
      details: { reason: 'Invalid current password' },
    });
    throw new AppError('Invalid current password', 400);
  }

  const hashedNewPassword = await hashPassword(validatedData.newPassword);

  await db.update(members)
    .set({
      password: hashedNewPassword,
      updatedAt: new Date(),
    })
    .where(eq(members.id, req.user!.id));

  // Revoke all refresh tokens to force re-login with enhanced audit logging
  const revokedCount = await TokenService.revokeAllUserTokens(req.user!.id, {
    reason: 'password_changed',
    revokedBy: req.user!.id,
  });

  // Log password change and session revocation
  SecurityAuditLogger.logPasswordChange(req, req.user!.id);
  SecurityAuditLogger.logSessionEvent(
    SecurityEventType.ALL_SESSIONS_REVOKED,
    req.user!.id,
    undefined,
    { reason: 'password_changed', revokedCount }
  );

  // Clear httpOnly cookie
  TokenService.clearRefreshTokenCookie(res);

  res.json({
    status: 'success',
    message: 'Password changed successfully. Please log in again.',
  });
});

export const forgotPassword = catchAsync(async (req: Request, res: Response) => {
  const validatedData: ForgotPasswordInput = forgotPasswordSchema.parse(req.body);

  const user = await db.query.members.findFirst({
    where: eq(members.email, validatedData.email),
    with: {
      church: {
        columns: {
          name: true,
          slug: true,
        }
      }
    }
  });

  if (!user) {
    // Don't reveal if user exists
    return res.json({
      status: 'success',
      message: 'If the email exists, a password reset link has been sent.',
    });
  }

  const resetToken = generatePasswordResetToken();
  const resetExpiry = getTokenExpiration(1); // 1 hour

  await db.update(members)
    .set({
      passwordResetToken: resetToken,
      passwordResetExpires: resetExpiry,
      updatedAt: new Date(),
    })
    .where(eq(members.id, user.id));

  // Send password reset email (don't block response if email fails)
  MailService.sendPasswordResetEmail(
    user.email,
    `${user.firstName} ${user.lastName}`,
    resetToken,
    user.church.slug
  ).catch(error => {
    console.error('Failed to send password reset email:', error);
  });

  res.json({
    status: 'success',
    message: 'If the email exists, a password reset link has been sent.',
    ...(process.env.NODE_ENV === 'development' && { resetToken }),
  });
});

export const resetPassword = catchAsync(async (req: Request, res: Response) => {
  const validatedData: ResetPasswordInput = resetPasswordSchema.parse(req.body);

  const user = await db.query.members.findFirst({
    where: eq(members.passwordResetToken, validatedData.token),
  });

  if (!user || !user.passwordResetExpires || isTokenExpired(user.passwordResetExpires)) {
    SecurityAuditLogger.logEvent({
      eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
      success: false,
      severity: 'medium',
      details: { reason: 'Invalid or expired reset token' },
    });
    throw new AppError('Invalid or expired reset token', 400);
  }

  const hashedPassword = await hashPassword(validatedData.newPassword);

  await db.update(members)
    .set({
      password: hashedPassword,
      passwordResetToken: null,
      passwordResetExpires: null,
      updatedAt: new Date(),
    })
    .where(eq(members.id, user.id));

  // Revoke all refresh tokens with enhanced audit logging
  const revokedCount = await TokenService.revokeAllUserTokens(user.id, {
    reason: 'password_reset',
    revokedBy: user.id,
  });

  // Log password reset completion and session revocation
  SecurityAuditLogger.logEvent({
    eventType: SecurityEventType.PASSWORD_RESET_COMPLETED,
    userId: user.id,
    success: true,
    severity: 'medium',
  });

  SecurityAuditLogger.logSessionEvent(
    SecurityEventType.ALL_SESSIONS_REVOKED,
    user.id,
    undefined,
    { reason: 'password_reset', revokedCount }
  );

  res.json({
    status: 'success',
    message: 'Password reset successful. Please log in with your new password.',
  });
});

export const verifyEmail = catchAsync(async (req: Request, res: Response) => {
  const { token } = req.params;

  const user = await db.query.members.findFirst({
    where: eq(members.emailVerificationToken, token),
  });

  if (!user) {
    throw new AppError('Invalid verification token', 400);
  }

  await db.update(members)
    .set({
      isEmailVerified: true,
      emailVerificationToken: null,
      updatedAt: new Date(),
    })
    .where(eq(members.id, user.id));

  SecurityAuditLogger.logEvent({
    eventType: SecurityEventType.EMAIL_VERIFICATION,
    userId: user.id,
    success: true,
    severity: 'low',
  });

  res.json({
    status: 'success',
    message: 'Email verified successfully',
  });
});

export const getActiveSessions = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const sessions = await TokenService.getUserActiveSessions(req.user!.id);

  res.json({
    status: 'success',
    data: {
      sessions: sessions.map(session => ({
        id: session.id,
        createdAt: session.createdAt,
        lastUsedAt: session.lastUsedAt,
        expiresAt: session.expiresAt,
        ipAddress: session.ipAddress,
        userAgent: session.userAgent,
        isCurrent: false, // Could be enhanced to detect current session
      })),
    },
  });
});

export const revokeSession = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const { sessionId } = req.params;

  if (!sessionId) {
    throw new AppError('Session ID is required', 400);
  }

  const success = await TokenService.revokeSession(sessionId, req.user!.id, {
    reason: 'user_revoked',
    revokedBy: req.user!.id,
  });

  if (!success) {
    throw new AppError('Session not found or already revoked', 404);
  }

  SecurityAuditLogger.logSessionEvent(
    SecurityEventType.SESSION_REVOKED,
    req.user!.id,
    sessionId,
    { reason: 'user_revoked' }
  );

  res.json({
    status: 'success',
    message: 'Session revoked successfully',
  });
});

export const revokeAllSessions = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const revokedCount = await TokenService.revokeAllUserTokens(req.user!.id, {
    reason: 'user_revoked_all',
    revokedBy: req.user!.id,
  });

  SecurityAuditLogger.logSessionEvent(
    SecurityEventType.ALL_SESSIONS_REVOKED,
    req.user!.id,
    undefined,
    { reason: 'user_revoked_all', revokedCount }
  );

  // Clear httpOnly cookie
  TokenService.clearRefreshTokenCookie(res);

  res.json({
    status: 'success',
    message: `${revokedCount} sessions revoked successfully`,
    data: {
      revokedCount,
    },
  });
});

export const resendVerificationEmail = catchAsync(async (req: AuthenticatedRequest, res: Response) => {
  const user = await db.query.members.findFirst({
    where: eq(members.id, req.user!.id),
    with: {
      church: {
        columns: {
          name: true,
          slug: true,
        }
      }
    }
  });

  if (!user) {
    throw new AppError('User not found', 404);
  }

  if (user.isEmailVerified) {
    throw new AppError('Email is already verified', 400);
  }

  // Generate new verification token if needed
  let verificationToken = user.emailVerificationToken;
  if (!verificationToken) {
    verificationToken = generatePasswordResetToken(); // Reuse the token generation function
    
    await db.update(members)
      .set({
        emailVerificationToken: verificationToken,
        updatedAt: new Date(),
      })
      .where(eq(members.id, user.id));
  }

  // Send verification email (don't block response if email fails)
  MailService.sendEmailVerification(
    user.email,
    `${user.firstName} ${user.lastName}`,
    verificationToken,
    user.church.slug
  ).catch(error => {
    console.error('Failed to send verification email:', error);
  });

  res.json({
    status: 'success',
    message: 'Verification email sent successfully',
    ...(process.env.NODE_ENV === 'development' && { verificationToken }),
  });
});