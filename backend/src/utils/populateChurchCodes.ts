import { db } from '../db';
import { churches } from '../db/schema';
import { generateUniqueChurchCode } from '../utils/churchCode';
import { eq, isNull } from 'drizzle-orm';

/**
 * Populate existing churches with unique church codes
 */
export async function populateChurchCodes() {
  console.log('🔄 Starting to populate church codes...');

  try {
    // Find all churches without codes
    const churchesWithoutCodes = await db.query.churches.findMany({
      where: isNull(churches.churchCode),
      columns: { id: true, name: true, slug: true }
    });

    console.log(`📋 Found ${churchesWithoutCodes.length} churches without codes`);

    for (const church of churchesWithoutCodes) {
      const churchCode = await generateUniqueChurchCode();
      
      await db
        .update(churches)
        .set({ churchCode })
        .where(eq(churches.id, church.id));

      console.log(`✅ Generated code ${churchCode} for church: ${church.name} (${church.slug})`);
    }

    console.log('🎉 Successfully populated all church codes!');
  } catch (error) {
    console.error('❌ Error populating church codes:', error);
    throw error;
  }
}

// Run if this module is executed directly (ESM-safe check)
const isDirectExecution = typeof process !== 'undefined' && process.argv[1] &&
  (new URL('', import.meta.url).pathname === process.argv[1] || process.argv[1].endsWith('/populateChurchCodes.ts'));

if (isDirectExecution) {
  populateChurchCodes()
    .then(() => {
      console.log('✨ Church code population completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Church code population failed:', error);
      process.exit(1);
    });
}