import { db } from '../db';
import { churches } from '../db/schema';
import { eq } from 'drizzle-orm';

/**
 * Generates a unique 6-digit church code
 * Format: 6 digits (100000 - 999999)
 * Excludes confusing sequences like 111111, 123456, etc.
 */
export async function generateUniqueChurchCode(): Promise<string> {
  const maxAttempts = 100;
  let attempts = 0;

  while (attempts < maxAttempts) {
    const code = generateRandomCode();
    
    // Check if code already exists
    const existingChurch = await db.query.churches.findFirst({
      where: eq(churches.churchCode, code),
      columns: { id: true }
    });

    if (!existingChurch) {
      return code;
    }

    attempts++;
  }

  throw new Error('Unable to generate unique church code after maximum attempts');
}

/**
 * Generates a random 6-digit code
 * Avoids patterns that are hard to remember or confusing
 */
function generateRandomCode(): string {
  let code: string;
  
  do {
    // Generate 6-digit number between 100000 and 999999
    const randomNum = Math.floor(Math.random() * 900000) + 100000;
    code = randomNum.toString();
  } while (isConfusingCode(code));

  return code;
}

/**
 * Checks if a code is confusing or hard to remember
 * Avoids:
 * - All same digits (111111, 222222, etc.)
 * - Sequential digits (123456, 654321, etc.)
 * - Repeating patterns (121212, 101010, etc.)
 */
function isConfusingCode(code: string): boolean {
  // Check for all same digits
  if (new Set(code).size === 1) {
    return true;
  }

  // Check for sequential ascending (123456)
  let isAscending = true;
  for (let i = 1; i < code.length; i++) {
    if (parseInt(code[i]) !== parseInt(code[i - 1]) + 1) {
      isAscending = false;
      break;
    }
  }
  if (isAscending) return true;

  // Check for sequential descending (654321)
  let isDescending = true;
  for (let i = 1; i < code.length; i++) {
    if (parseInt(code[i]) !== parseInt(code[i - 1]) - 1) {
      isDescending = false;
      break;
    }
  }
  if (isDescending) return true;

  // Check for alternating patterns (121212, 101010)
  const first = code[0];
  const second = code[1];
  if (code === (first + second).repeat(3)) {
    return true;
  }

  // Check for common weak patterns
  const weakPatterns = ['000000', '111111', '222222', '333333', '444444', '555555', '666666', '777777', '888888', '999999'];
  if (weakPatterns.includes(code)) {
    return true;
  }

  return false;
}

/**
 * Validates a church code format
 */
export function isValidChurchCode(code: string): boolean {
  return /^\d{6}$/.test(code);
}