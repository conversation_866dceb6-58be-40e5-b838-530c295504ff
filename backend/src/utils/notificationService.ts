import { db } from '../db';
import { notifications, members, roles } from '../db/schema';
import { and, eq, inArray } from 'drizzle-orm';

export type CreateNotificationParams = {
  churchId: string;
  memberIds: string[];
  type: string;
  title: string;
  message?: string;
  link?: string;
  metadata?: Record<string, unknown>;
};

export async function createNotifications(params: CreateNotificationParams) {
  const rows = params.memberIds.map((memberId) => ({
    churchId: params.churchId,
    memberId,
    type: params.type,
    title: params.title,
    message: params.message,
    link: params.link,
    metadata: params.metadata ? JSON.stringify(params.metadata) : undefined,
  }));
  if (rows.length === 0) return [];
  return db.insert(notifications).values(rows).returning();
}

export async function notifyForAnnouncement(opts: {
  churchId: string;
  title: string;
  summary?: string;
  announcementId: string;
  target: {
    audience: 'all_members' | 'specific_branches' | 'specific_roles' | 'custom_group';
    branchIds?: string[];
    roleNames?: string[];
    memberIds?: string[];
  }
}) {
  // Determine memberIds by target
  let memberWhere = eq(members.churchId, opts.churchId);
  if (opts.target.audience === 'specific_branches' && opts.target.branchIds?.length) {
    memberWhere = and(memberWhere, inArray(members.branchId, opts.target.branchIds));
  }
  if (opts.target.audience === 'specific_roles' && opts.target.roleNames?.length) {
    // Map role names to role IDs, then filter members by those role IDs
    const roleRows = await db.select({ id: roles.id })
      .from(roles)
      .where(and(eq(roles.churchId, opts.churchId), inArray(roles.name, opts.target.roleNames)));
    const roleIds = roleRows.map(r => r.id);
    if (roleIds.length > 0) {
      memberWhere = and(memberWhere, inArray(members.roleId, roleIds));
    } else {
      // No matching roles found; result should be empty
      memberWhere = and(memberWhere, eq(members.id, '00000000-0000-0000-0000-000000000000'));
    }
  }

  let memberIds: string[] = [];
  if (opts.target.audience === 'custom_group' && opts.target.memberIds?.length) {
    memberIds = opts.target.memberIds;
  } else {
    const rows = await db.select({ id: members.id }).from(members).where(memberWhere);
    memberIds = rows.map(r => r.id);
  }

  const title = opts.title;
  const message = opts.summary ?? 'New announcement';
  const link = `/announcements/${opts.announcementId}`;

  return createNotifications({
    churchId: opts.churchId,
    memberIds,
    type: 'announcement',
    title,
    message,
    link,
    metadata: { announcementId: opts.announcementId },
  });
}
