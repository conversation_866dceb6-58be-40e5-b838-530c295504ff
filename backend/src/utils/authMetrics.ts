import { Request } from 'express';

export interface AuthMetric {
  timestamp: Date;
  eventType: string;
  userId?: string;
  churchId?: string;
  duration?: number;
  success: boolean;
  errorType?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
}

export interface AuthMetricsSummary {
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  errorsByType: Record<string, number>;
  requestsByEndpoint: Record<string, number>;
  uniqueUsers: number;
  uniqueIPs: number;
}

export interface PerformanceMetrics {
  loginDuration: number[];
  tokenRefreshDuration: number[];
  authMiddlewareDuration: number[];
  averageLoginTime: number;
  averageTokenRefreshTime: number;
  averageAuthCheckTime: number;
  p95LoginTime: number;
  p95TokenRefreshTime: number;
  p95AuthCheckTime: number;
}

export class AuthMetricsCollector {
  private static metrics: AuthMetric[] = [];
  private static readonly MAX_METRICS_IN_MEMORY = 10000;
  private static readonly CLEANUP_INTERVAL = 60 * 60 * 1000; // 1 hour

  static {
    // Periodic cleanup of old metrics
    setInterval(() => {
      this.cleanupOldMetrics();
    }, this.CLEANUP_INTERVAL);
  }

  /**
   * Record an authentication metric
   */
  static recordMetric(metric: Omit<AuthMetric, 'timestamp'>): void {
    const fullMetric: AuthMetric = {
      timestamp: new Date(),
      ...metric,
    };

    this.metrics.push(fullMetric);

    // Keep only recent metrics in memory
    if (this.metrics.length > this.MAX_METRICS_IN_MEMORY) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS_IN_MEMORY);
    }

    // In production, send to monitoring service
    this.sendToMonitoringService(fullMetric);
  }

  /**
   * Record login attempt
   */
  static recordLogin(
    req: Request,
    success: boolean,
    duration: number,
    userId?: string,
    churchId?: string,
    errorType?: string
  ): void {
    this.recordMetric({
      eventType: 'login',
      userId,
      churchId,
      duration,
      success,
      errorType,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
    });
  }

  /**
   * Record token refresh attempt
   */
  static recordTokenRefresh(
    req: Request,
    success: boolean,
    duration: number,
    userId?: string,
    errorType?: string
  ): void {
    this.recordMetric({
      eventType: 'token_refresh',
      userId,
      duration,
      success,
      errorType,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
    });
  }

  /**
   * Record authentication middleware performance
   */
  static recordAuthCheck(
    req: Request,
    success: boolean,
    duration: number,
    userId?: string,
    errorType?: string
  ): void {
    this.recordMetric({
      eventType: 'auth_check',
      userId,
      duration,
      success,
      errorType,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
    });
  }

  /**
   * Record logout event
   */
  static recordLogout(
    req: Request,
    success: boolean,
    userId?: string,
    errorType?: string
  ): void {
    this.recordMetric({
      eventType: 'logout',
      userId,
      success,
      errorType,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
    });
  }

  /**
   * Get metrics summary for a time range
   */
  static getMetricsSummary(
    startTime: Date,
    endTime: Date,
    eventType?: string
  ): AuthMetricsSummary {
    const filteredMetrics = this.metrics.filter(metric => {
      const inTimeRange = metric.timestamp >= startTime && metric.timestamp <= endTime;
      const matchesType = !eventType || metric.eventType === eventType;
      return inTimeRange && matchesType;
    });

    const totalRequests = filteredMetrics.length;
    const successfulRequests = filteredMetrics.filter(m => m.success).length;
    const failedRequests = totalRequests - successfulRequests;

    const durations = filteredMetrics
      .filter(m => m.duration !== undefined)
      .map(m => m.duration!);
    const averageResponseTime = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;

    const errorsByType: Record<string, number> = {};
    filteredMetrics
      .filter(m => !m.success && m.errorType)
      .forEach(m => {
        errorsByType[m.errorType!] = (errorsByType[m.errorType!] || 0) + 1;
      });

    const requestsByEndpoint: Record<string, number> = {};
    filteredMetrics
      .filter(m => m.endpoint)
      .forEach(m => {
        requestsByEndpoint[m.endpoint!] = (requestsByEndpoint[m.endpoint!] || 0) + 1;
      });

    const uniqueUsers = new Set(
      filteredMetrics.filter(m => m.userId).map(m => m.userId!)
    ).size;

    const uniqueIPs = new Set(
      filteredMetrics.filter(m => m.ipAddress).map(m => m.ipAddress!)
    ).size;

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      errorsByType,
      requestsByEndpoint,
      uniqueUsers,
      uniqueIPs,
    };
  }

  /**
   * Get performance metrics
   */
  static getPerformanceMetrics(startTime: Date, endTime: Date): PerformanceMetrics {
    const filteredMetrics = this.metrics.filter(metric => 
      metric.timestamp >= startTime && 
      metric.timestamp <= endTime &&
      metric.duration !== undefined
    );

    const loginDurations = filteredMetrics
      .filter(m => m.eventType === 'login' && m.success)
      .map(m => m.duration!);

    const tokenRefreshDurations = filteredMetrics
      .filter(m => m.eventType === 'token_refresh' && m.success)
      .map(m => m.duration!);

    const authCheckDurations = filteredMetrics
      .filter(m => m.eventType === 'auth_check' && m.success)
      .map(m => m.duration!);

    return {
      loginDuration: loginDurations,
      tokenRefreshDuration: tokenRefreshDurations,
      authMiddlewareDuration: authCheckDurations,
      averageLoginTime: this.calculateAverage(loginDurations),
      averageTokenRefreshTime: this.calculateAverage(tokenRefreshDurations),
      averageAuthCheckTime: this.calculateAverage(authCheckDurations),
      p95LoginTime: this.calculatePercentile(loginDurations, 95),
      p95TokenRefreshTime: this.calculatePercentile(tokenRefreshDurations, 95),
      p95AuthCheckTime: this.calculatePercentile(authCheckDurations, 95),
    };
  }

  /**
   * Get error rate by type
   */
  static getErrorRates(startTime: Date, endTime: Date): Record<string, { count: number; rate: number }> {
    const filteredMetrics = this.metrics.filter(metric => 
      metric.timestamp >= startTime && metric.timestamp <= endTime
    );

    const totalRequests = filteredMetrics.length;
    const errorsByType: Record<string, number> = {};

    filteredMetrics
      .filter(m => !m.success && m.errorType)
      .forEach(m => {
        errorsByType[m.errorType!] = (errorsByType[m.errorType!] || 0) + 1;
      });

    const errorRates: Record<string, { count: number; rate: number }> = {};
    Object.entries(errorsByType).forEach(([errorType, count]) => {
      errorRates[errorType] = {
        count,
        rate: totalRequests > 0 ? (count / totalRequests) * 100 : 0,
      };
    });

    return errorRates;
  }

  /**
   * Get real-time metrics (last 5 minutes)
   */
  static getRealTimeMetrics(): AuthMetricsSummary {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const now = new Date();
    return this.getMetricsSummary(fiveMinutesAgo, now);
  }

  /**
   * Get authentication health status
   */
  static getHealthStatus(): {
    status: 'healthy' | 'warning' | 'critical';
    metrics: {
      successRate: number;
      averageResponseTime: number;
      errorRate: number;
      activeUsers: number;
    };
    alerts: string[];
  } {
    const realTimeMetrics = this.getRealTimeMetrics();
    const successRate = realTimeMetrics.totalRequests > 0 
      ? (realTimeMetrics.successfulRequests / realTimeMetrics.totalRequests) * 100 
      : 100;

    const errorRate = 100 - successRate;
    const alerts: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // Define thresholds
    if (successRate < 95) {
      alerts.push(`Low success rate: ${successRate.toFixed(1)}%`);
      status = 'warning';
    }

    if (successRate < 90) {
      status = 'critical';
    }

    if (realTimeMetrics.averageResponseTime > 2000) {
      alerts.push(`High response time: ${realTimeMetrics.averageResponseTime.toFixed(0)}ms`);
      status = status === 'critical' ? 'critical' : 'warning';
    }

    if (realTimeMetrics.averageResponseTime > 5000) {
      status = 'critical';
    }

    return {
      status,
      metrics: {
        successRate,
        averageResponseTime: realTimeMetrics.averageResponseTime,
        errorRate,
        activeUsers: realTimeMetrics.uniqueUsers,
      },
      alerts,
    };
  }

  /**
   * Export metrics for external monitoring systems
   */
  static exportMetrics(format: 'json' | 'prometheus' = 'json'): string {
    if (format === 'prometheus') {
      return this.exportPrometheusMetrics();
    }

    const summary = this.getRealTimeMetrics();
    const performance = this.getPerformanceMetrics(
      new Date(Date.now() - 60 * 60 * 1000), // Last hour
      new Date()
    );

    return JSON.stringify({
      summary,
      performance,
      timestamp: new Date().toISOString(),
    }, null, 2);
  }

  /**
   * Clean up old metrics from memory
   */
  private static cleanupOldMetrics(): void {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    this.metrics = this.metrics.filter(metric => metric.timestamp > oneHourAgo);
  }

  /**
   * Send metric to external monitoring service
   */
  private static sendToMonitoringService(metric: AuthMetric): void {
    // In production, send to services like:
    // - CloudWatch
    // - Datadog
    // - New Relic
    // - Prometheus
    // - Custom monitoring endpoint

    if (process.env.NODE_ENV === 'development') {
      // Only log in development to avoid spam
      if (metric.eventType === 'login' || !metric.success) {
        console.log('[AUTH_METRICS]', {
          type: metric.eventType,
          success: metric.success,
          duration: metric.duration,
          error: metric.errorType,
        });
      }
    }
  }

  /**
   * Export metrics in Prometheus format
   */
  private static exportPrometheusMetrics(): string {
    const summary = this.getRealTimeMetrics();
    const performance = this.getPerformanceMetrics(
      new Date(Date.now() - 60 * 60 * 1000),
      new Date()
    );

    return `
# HELP auth_requests_total Total number of authentication requests
# TYPE auth_requests_total counter
auth_requests_total{status="success"} ${summary.successfulRequests}
auth_requests_total{status="failure"} ${summary.failedRequests}

# HELP auth_response_time_seconds Authentication response time in seconds
# TYPE auth_response_time_seconds histogram
auth_response_time_seconds_sum ${summary.averageResponseTime / 1000}
auth_response_time_seconds_count ${summary.totalRequests}

# HELP auth_active_users Number of unique active users
# TYPE auth_active_users gauge
auth_active_users ${summary.uniqueUsers}

# HELP auth_login_duration_seconds Average login duration in seconds
# TYPE auth_login_duration_seconds gauge
auth_login_duration_seconds ${performance.averageLoginTime / 1000}

# HELP auth_token_refresh_duration_seconds Average token refresh duration in seconds
# TYPE auth_token_refresh_duration_seconds gauge
auth_token_refresh_duration_seconds ${performance.averageTokenRefreshTime / 1000}
`.trim();
  }

  /**
   * Calculate average of an array of numbers
   */
  private static calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, n) => sum + n, 0) / numbers.length;
  }

  /**
   * Calculate percentile of an array of numbers
   */
  private static calculatePercentile(numbers: number[], percentile: number): number {
    if (numbers.length === 0) return 0;
    
    const sorted = [...numbers].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;
    return sorted[Math.max(0, index)];
  }

  /**
   * Get client IP address
   */
  private static getClientIP(req: Request): string {
    return req.ip || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress || 
           'unknown';
  }
}