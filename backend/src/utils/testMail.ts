#!/usr/bin/env bun
import { MailService } from '../services/mailService';
import { verifyMailConnection } from '../config/mail';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testMailSystem() {
  console.log('🧪 Testing Mail System...\n');

  try {
    // Test 1: Verify connection
    console.log('1. Testing SMTP connection...');
    const isConnected = await verifyMailConnection();
    
    if (!isConnected) {
      console.error('❌ SMTP connection failed. Please check your email configuration.');
      process.exit(1);
    }

    // Test 2: Send a test email
    console.log('2. Sending test email...');
    const testEmail = process.env.SMTP_USER || '<EMAIL>';
    
    const emailSent = await MailService.sendEmail({
      to: testEmail,
      subject: 'Church Management System - Mail Test',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">🎉 Mail System Test Successful!</h2>
          <p>Congratulations! Your Church Management System email configuration is working perfectly.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Test Details:</h3>
            <p><strong>Timestamp:</strong> ${new Date().toISOString()}</p>
            <p><strong>SMTP Host:</strong> ${process.env.SMTP_HOST}</p>
            <p><strong>From Email:</strong> ${process.env.FROM_EMAIL}</p>
          </div>
          
          <p>Your system is now ready to send:</p>
          <ul>
            <li>Welcome emails to new members</li>
            <li>Password reset emails</li>
            <li>Event notifications</li>
            <li>Donation receipts</li>
            <li>Bulk announcements</li>
          </ul>
          
          <p>Happy church management! 🙏</p>
        </div>
      `,
    });

    if (emailSent) {
      console.log('✅ Test email sent successfully!');
      console.log(`📧 Check your inbox at: ${testEmail}`);
    } else {
      console.error('❌ Failed to send test email');
      process.exit(1);
    }

    // Test 3: Test welcome email template
    console.log('3. Testing welcome email template...');
    const welcomeEmailSent = await MailService.sendWelcomeEmail(
      testEmail,
      'Test User',
      'Test Church',
      'temp123',
      'test-verification-token-123'
    );

    if (welcomeEmailSent) {
      console.log('✅ Welcome email template test successful!');
    } else {
      console.error('❌ Welcome email template test failed');
    }

    console.log('\n🎉 All mail system tests passed!');
    console.log('\n📋 Next steps:');
    console.log('1. Update your .env file with your actual Gmail credentials');
    console.log('2. Make sure to use an App Password for Gmail (not your regular password)');
    console.log('3. Test the system by creating a new member or requesting a password reset');

  } catch (error) {
    console.error('❌ Mail system test failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (import.meta.main) {
  testMailSystem();
}

export { testMailSystem };