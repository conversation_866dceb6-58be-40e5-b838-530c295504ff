import { db } from '../db';

async function displayChurchCodes() {
  console.log('🏛️  Current Church Codes:');
  console.log('========================');
  
  try {
    const allChurches = await db.query.churches.findMany({
      columns: { 
        name: true, 
        slug: true, 
        churchCode: true,
        isActive: true
      },
      orderBy: (churches, { asc }) => [asc(churches.name)]
    });

    if (allChurches.length === 0) {
      console.log('No churches found');
      return;
    }

    allChurches.forEach((church) => {
      const status = church.isActive ? '✅' : '❌';
      console.log(`${status} ${church.name}`);
      console.log(`   Code: ${church.churchCode}`);
      console.log(`   Slug: ${church.slug}`);
      console.log('');
    });

    console.log(`📊 Total churches: ${allChurches.length}`);
    
  } catch (error) {
    console.error('❌ Error fetching church codes:', error);
    throw error;
  }
}

// Run if this module is executed directly (ESM-safe check)
const isDirectExecution = typeof process !== 'undefined' && process.argv[1] &&
  (new URL('', import.meta.url).pathname === process.argv[1] || process.argv[1].endsWith('/displayChurchCodes.ts'));

if (isDirectExecution) {
  displayChurchCodes()
    .then(() => {
      console.log('✨ Church codes displayed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Failed to display church codes:', error);
      process.exit(1);
    });
}