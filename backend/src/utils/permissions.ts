export const PERMISSIONS = {
  // Church management
  MANAGE_CHURCH: 'manage_church',
  VIEW_CHURCH: 'view_church',
  
  // Member management
  MANAGE_MEMBERS: 'manage_members',
  VIEW_MEMBERS: 'view_members',
  INVITE_MEMBERS: 'invite_members',
  REMOVE_MEMBERS: 'remove_members',
  
  // Role management
  MANAGE_ROLES: 'manage_roles',
  VIEW_ROLES: 'view_roles',
  ASSIGN_ROLES: 'assign_roles',
  
  // Events management
  MANAGE_EVENTS: 'manage_events',
  VIEW_EVENTS: 'view_events',
  CREATE_EVENTS: 'create_events',
  
  // Financial management
  MANAGE_FINANCES: 'manage_finances',
  VIEW_FINANCES: 'view_finances',
  
  // Communication
  SEND_ANNOUNCEMENTS: 'send_announcements',
  MANAGE_COMMUNICATIONS: 'manage_communications',
  
  // Reports and analytics
  VIEW_REPORTS: 'view_reports',
  GENERATE_REPORTS: 'generate_reports',
  
  // Settings
  MANAGE_SETTINGS: 'manage_settings',
  
  // Super admin (all permissions)
  ALL: '*',
} as const;

export const DEFAULT_ROLES = {
  SUPER_ADMIN: {
    name: 'Super Admin',
    description: 'Full access to all church operations',
    permissions: [PERMISSIONS.ALL],
    isSystem: true,
  },
  PASTOR: {
    name: 'Pastor',
    description: 'Church leadership with comprehensive access',
    permissions: [
      PERMISSIONS.MANAGE_CHURCH,
      PERMISSIONS.MANAGE_MEMBERS,
      PERMISSIONS.MANAGE_ROLES,
      PERMISSIONS.ASSIGN_ROLES,
      PERMISSIONS.MANAGE_EVENTS,
      PERMISSIONS.MANAGE_FINANCES,
      PERMISSIONS.SEND_ANNOUNCEMENTS,
      PERMISSIONS.MANAGE_COMMUNICATIONS,
      PERMISSIONS.VIEW_REPORTS,
      PERMISSIONS.GENERATE_REPORTS,
      PERMISSIONS.MANAGE_SETTINGS,
    ],
    isSystem: true,
  },
  ELDER: {
    name: 'Elder',
    description: 'Church elder with administrative privileges',
    permissions: [
      PERMISSIONS.VIEW_CHURCH,
      PERMISSIONS.MANAGE_MEMBERS,
      PERMISSIONS.VIEW_ROLES,
      PERMISSIONS.ASSIGN_ROLES,
      PERMISSIONS.MANAGE_EVENTS,
      PERMISSIONS.VIEW_FINANCES,
      PERMISSIONS.SEND_ANNOUNCEMENTS,
      PERMISSIONS.VIEW_REPORTS,
    ],
    isSystem: true,
  },
  DEACON: {
    name: 'Deacon',
    description: 'Church deacon with service-oriented permissions',
    permissions: [
      PERMISSIONS.VIEW_CHURCH,
      PERMISSIONS.VIEW_MEMBERS,
      PERMISSIONS.INVITE_MEMBERS,
      PERMISSIONS.CREATE_EVENTS,
      PERMISSIONS.VIEW_EVENTS,
      PERMISSIONS.SEND_ANNOUNCEMENTS,
    ],
    isSystem: true,
  },
  MEMBER: {
    name: 'Member',
    description: 'Regular church member',
    permissions: [
      PERMISSIONS.VIEW_CHURCH,
      PERMISSIONS.VIEW_MEMBERS,
      PERMISSIONS.VIEW_EVENTS,
    ],
    isSystem: true,
  },
  VISITOR: {
    name: 'Visitor',
    description: 'Church visitor with limited access',
    permissions: [
      PERMISSIONS.VIEW_CHURCH,
      PERMISSIONS.VIEW_EVENTS,
    ],
    isSystem: true,
  },
} as const;

export type Permission = typeof PERMISSIONS[keyof typeof PERMISSIONS];
export type DefaultRoleName = keyof typeof DEFAULT_ROLES;