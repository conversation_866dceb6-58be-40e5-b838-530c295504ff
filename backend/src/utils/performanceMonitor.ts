import { Request, Response, NextFunction } from 'express';
import { AuthMetricsCollector } from './authMetrics';

export interface PerformanceTimer {
  start(): void;
  end(): number;
}

export class PerformanceMonitor {
  private startTime: number;

  constructor() {
    this.startTime = Date.now();
  }

  start(): void {
    this.startTime = Date.now();
  }

  end(): number {
    return Date.now() - this.startTime;
  }

  static createTimer(): PerformanceTimer {
    return new PerformanceMonitor();
  }

  /**
   * Middleware to track authentication endpoint performance
   */
  static trackAuthPerformance() {
    return (req: Request, res: Response, next: NextFunction) => {
      const timer = PerformanceMonitor.createTimer();
      timer.start();

      // Store timer in request for later use
      (req as any).performanceTimer = timer;

      // Override res.json to capture response time
      const originalJson = res.json;
      res.json = function(body: any) {
        const duration = timer.end();
        const success = res.statusCode < 400;
        
        // Determine event type based on endpoint
        if (req.path.includes('/login')) {
          AuthMetricsCollector.recordLogin(
            req,
            success,
            duration,
            body?.data?.user?.id,
            body?.data?.user?.churchId,
            success ? undefined : body?.code || 'unknown_error'
          );
        } else if (req.path.includes('/refresh')) {
          AuthMetricsCollector.recordTokenRefresh(
            req,
            success,
            duration,
            body?.data?.user?.id,
            success ? undefined : body?.code || 'unknown_error'
          );
        } else if (req.path.includes('/logout')) {
          AuthMetricsCollector.recordLogout(
            req,
            success,
            body?.data?.user?.id,
            success ? undefined : body?.code || 'unknown_error'
          );
        }

        return originalJson.call(this, body);
      };

      next();
    };
  }

  /**
   * Middleware to track authentication middleware performance
   */
  static trackAuthMiddlewarePerformance() {
    return (req: Request, res: Response, next: NextFunction) => {
      const timer = PerformanceMonitor.createTimer();
      timer.start();

      // Store original next function
      const originalNext = next;
      
      // Override next to capture timing
      const timedNext = (error?: any) => {
        const duration = timer.end();
        const success = !error;
        
        AuthMetricsCollector.recordAuthCheck(
          req,
          success,
          duration,
          (req as any).user?.id,
          error?.code || (success ? undefined : 'auth_middleware_error')
        );

        originalNext(error);
      };

      // Call next with timing wrapper
      next = timedNext;
      next();
    };
  }

  /**
   * Decorator for timing async functions
   */
  static timeAsync<T extends (...args: any[]) => Promise<any>>(
    fn: T,
    metricName: string
  ): T {
    return (async (...args: any[]) => {
      const timer = PerformanceMonitor.createTimer();
      timer.start();
      
      try {
        const result = await fn(...args);
        const duration = timer.end();
        
        // Log performance metric
        console.log(`[PERFORMANCE] ${metricName}: ${duration}ms`);
        
        return result;
      } catch (error) {
        const duration = timer.end();
        console.log(`[PERFORMANCE] ${metricName} (ERROR): ${duration}ms`);
        throw error;
      }
    }) as T;
  }

  /**
   * Decorator for timing synchronous functions
   */
  static timeSync<T extends (...args: any[]) => any>(
    fn: T,
    metricName: string
  ): T {
    return ((...args: any[]) => {
      const timer = PerformanceMonitor.createTimer();
      timer.start();
      
      try {
        const result = fn(...args);
        const duration = timer.end();
        
        // Log performance metric
        console.log(`[PERFORMANCE] ${metricName}: ${duration}ms`);
        
        return result;
      } catch (error) {
        const duration = timer.end();
        console.log(`[PERFORMANCE] ${metricName} (ERROR): ${duration}ms`);
        throw error;
      }
    }) as T;
  }

  /**
   * Track database query performance
   */
  static async trackDatabaseQuery<T>(
    queryName: string,
    queryFn: () => Promise<T>
  ): Promise<T> {
    const timer = PerformanceMonitor.createTimer();
    timer.start();
    
    try {
      const result = await queryFn();
      const duration = timer.end();
      
      // Log slow queries (> 100ms)
      if (duration > 100) {
        console.warn(`[SLOW_QUERY] ${queryName}: ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = timer.end();
      console.error(`[QUERY_ERROR] ${queryName}: ${duration}ms`, error);
      throw error;
    }
  }

  /**
   * Get performance summary
   */
  static getPerformanceSummary(): {
    averageResponseTimes: Record<string, number>;
    slowQueries: Array<{ query: string; duration: number; timestamp: Date }>;
    errorRates: Record<string, number>;
  } {
    // In a real implementation, this would aggregate data from a metrics store
    return {
      averageResponseTimes: {
        login: 150,
        tokenRefresh: 50,
        authCheck: 25,
      },
      slowQueries: [],
      errorRates: {
        login: 2.5,
        tokenRefresh: 1.0,
        authCheck: 0.5,
      },
    };
  }
}

/**
 * Performance monitoring decorators
 */
export function TimedAsync(metricName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
      const timer = PerformanceMonitor.createTimer();
      timer.start();
      
      try {
        const result = await originalMethod.apply(this, args);
        const duration = timer.end();
        
        console.log(`[PERFORMANCE] ${metricName}: ${duration}ms`);
        
        return result;
      } catch (error) {
        const duration = timer.end();
        console.log(`[PERFORMANCE] ${metricName} (ERROR): ${duration}ms`);
        throw error;
      }
    };
    
    return descriptor;
  };
}

export function TimedSync(metricName: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;
    
    descriptor.value = function (...args: any[]) {
      const timer = PerformanceMonitor.createTimer();
      timer.start();
      
      try {
        const result = originalMethod.apply(this, args);
        const duration = timer.end();
        
        console.log(`[PERFORMANCE] ${metricName}: ${duration}ms`);
        
        return result;
      } catch (error) {
        const duration = timer.end();
        console.log(`[PERFORMANCE] ${metricName} (ERROR): ${duration}ms`);
        throw error;
      }
    };
    
    return descriptor;
  };
}