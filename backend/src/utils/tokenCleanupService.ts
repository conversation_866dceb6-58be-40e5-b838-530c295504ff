import { TokenService } from './tokenService';
import { SecurityAuditLogger, SecurityEventType } from './securityAudit';

export class TokenCleanupService {
  private static instance: TokenCleanupService;
  private cleanupInterval: ReturnType<typeof setInterval> | null = null;
  private readonly CLEANUP_INTERVAL_MS = 60 * 60 * 1000; // 1 hour

  private constructor() {}

  static getInstance(): TokenCleanupService {
    if (!TokenCleanupService.instance) {
      TokenCleanupService.instance = new TokenCleanupService();
    }
    return TokenCleanupService.instance;
  }

  /**
   * Start the automatic token cleanup service
   */
  start(): void {
    if (this.cleanupInterval) {
      console.log('Token cleanup service is already running');
      return;
    }

    console.log('Starting token cleanup service...');
    
    // Run cleanup immediately
    this.performCleanup();

    // Schedule periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.CLEANUP_INTERVAL_MS);

    console.log(`Token cleanup service started. Running every ${this.CLEANUP_INTERVAL_MS / 1000 / 60} minutes.`);
  }

  /**
   * Stop the automatic token cleanup service
   */
  stop(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
      console.log('Token cleanup service stopped');
    }
  }

  /**
   * Perform token cleanup
   */
  private async performCleanup(): Promise<void> {
    try {
      console.log('Running token cleanup...');
      
      const cleanedCount = await TokenService.cleanupExpiredTokens();
      
      if (cleanedCount > 0) {
        console.log(`Cleaned up ${cleanedCount} expired tokens`);
        
        SecurityAuditLogger.logEvent({
          eventType: SecurityEventType.TOKEN_EXPIRED,
          success: true,
          severity: 'low',
          details: { 
            cleanedCount,
            cleanupType: 'automatic'
          },
        });
      }
    } catch (error) {
      console.error('Error during token cleanup:', error);
      
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.TOKEN_EXPIRED,
        success: false,
        severity: 'medium',
        details: { 
          error: error instanceof Error ? error.message : 'Unknown error',
          cleanupType: 'automatic'
        },
      });
    }
  }

  /**
   * Manually trigger token cleanup
   */
  async manualCleanup(): Promise<number> {
    try {
      const cleanedCount = await TokenService.cleanupExpiredTokens();
      
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.TOKEN_EXPIRED,
        success: true,
        severity: 'low',
        details: { 
          cleanedCount,
          cleanupType: 'manual'
        },
      });

      return cleanedCount;
    } catch (error) {
      SecurityAuditLogger.logEvent({
        eventType: SecurityEventType.TOKEN_EXPIRED,
        success: false,
        severity: 'medium',
        details: { 
          error: error instanceof Error ? error.message : 'Unknown error',
          cleanupType: 'manual'
        },
      });
      
      throw error;
    }
  }

  /**
   * Get cleanup service status
   */
  getStatus(): { isRunning: boolean; intervalMs: number } {
    return {
      isRunning: this.cleanupInterval !== null,
      intervalMs: this.CLEANUP_INTERVAL_MS,
    };
  }
}