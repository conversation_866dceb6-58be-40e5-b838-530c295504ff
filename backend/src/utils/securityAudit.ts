import { Request } from 'express';

export enum SecurityEventType {
  // Authentication events
  LOGIN_SUCCESS = 'LOGIN_SUCCESS',
  LOGIN_FAILED = 'LOGIN_FAILED',
  LOGOUT = 'LOGOUT',
  
  // Token events
  TOKEN_REFRESH_SUCCESS = 'TOKEN_REFRESH_SUCCESS',
  TOKEN_REFRESH_FAILED = 'TOKEN_REFRESH_FAILED',
  TOKEN_REVOKED = 'TOKEN_REVOKED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  
  // Password events
  PASSWORD_CHANGED = 'PASSWORD_CHANGED',
  PASSWORD_RESET_REQUESTED = 'PASSWORD_RESET_REQUESTED',
  PASSWORD_RESET_COMPLETED = 'PASSWORD_RESET_COMPLETED',
  
  // Security violations
  INVALID_TOKEN = 'INVALID_TOKEN',
  MALFORMED_TOKEN = 'MALFORMED_TOKEN',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
  
  // Session management
  SESSION_CREATED = 'SESSION_CREATED',
  SESSION_REVOKED = 'SESSION_REVOKED',
  ALL_SESSIONS_REVOKED = 'ALL_SESSIONS_REVOKED',
  
  // Account events
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  ACCOUNT_UNLOCKED = 'ACCOUNT_UNLOCKED',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
}

export interface SecurityAuditEvent {
  eventType: SecurityEventType;
  userId?: string;
  churchId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
  endpoint?: string;
  method?: string;
  success: boolean;
  details?: Record<string, any>;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface SecurityAlert {
  id: string;
  eventType: SecurityEventType;
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  userId?: string;
  churchId?: string;
  ipAddress?: string;
  timestamp: Date;
  acknowledged: boolean;
  details?: Record<string, any>;
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsByType: Record<SecurityEventType, number>;
  eventsBySeverity: Record<string, number>;
  failedLoginAttempts: number;
  suspiciousActivities: number;
  blockedIPs: Set<string>;
  alertsGenerated: number;
  topFailureReasons: Array<{ reason: string; count: number }>;
}

export class SecurityAuditLogger {
  private static readonly LOG_PREFIX = '[SECURITY_AUDIT]';
  private static events: SecurityAuditEvent[] = [];
  private static alerts: SecurityAlert[] = [];
  private static blockedIPs: Set<string> = new Set();
  private static suspiciousIPs: Map<string, { count: number; lastSeen: Date }> = new Map();
  private static readonly MAX_EVENTS_IN_MEMORY = 5000;
  private static readonly SUSPICIOUS_THRESHOLD = 5; // Failed attempts before marking as suspicious
  private static readonly BLOCK_THRESHOLD = 10; // Failed attempts before blocking

  /**
   * Log a security event with enhanced monitoring
   */
  static logEvent(event: Partial<SecurityAuditEvent> & { eventType: SecurityEventType }): void {
    const auditEvent: SecurityAuditEvent = {
      timestamp: new Date(),
      success: true,
      severity: 'medium',
      ...event,
    };

    // Store event in memory for analysis
    this.events.push(auditEvent);
    if (this.events.length > this.MAX_EVENTS_IN_MEMORY) {
      this.events = this.events.slice(-this.MAX_EVENTS_IN_MEMORY);
    }

    // Analyze for suspicious patterns
    this.analyzeSecurityEvent(auditEvent);

    // Send to logging service
    console.log(this.LOG_PREFIX, JSON.stringify(auditEvent, null, 2));

    // Handle alerts based on severity
    if (auditEvent.severity === 'critical' || auditEvent.severity === 'high') {
      this.generateAlert(auditEvent);
    }

    // For critical events, trigger immediate response
    if (auditEvent.severity === 'critical') {
      this.handleCriticalEvent(auditEvent);
    }
  }

  /**
   * Log authentication success
   */
  static logAuthSuccess(req: Request, userId: string, churchId: string): void {
    this.logEvent({
      eventType: SecurityEventType.LOGIN_SUCCESS,
      userId,
      churchId,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success: true,
      severity: 'low',
    });
  }

  /**
   * Log authentication failure
   */
  static logAuthFailure(req: Request, reason: string, details?: Record<string, any>): void {
    this.logEvent({
      eventType: SecurityEventType.LOGIN_FAILED,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success: false,
      severity: 'medium',
      details: { reason, ...details },
    });
  }

  /**
   * Log token refresh events
   */
  static logTokenRefresh(req: Request, userId: string, success: boolean, reason?: string): void {
    this.logEvent({
      eventType: success ? SecurityEventType.TOKEN_REFRESH_SUCCESS : SecurityEventType.TOKEN_REFRESH_FAILED,
      userId,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success,
      severity: success ? 'low' : 'medium',
      details: reason ? { reason } : undefined,
    });
  }

  /**
   * Log token revocation
   */
  static logTokenRevocation(
    userId: string, 
    sessionId?: string, 
    reason?: string, 
    revokedBy?: string
  ): void {
    this.logEvent({
      eventType: SecurityEventType.TOKEN_REVOKED,
      userId,
      sessionId,
      success: true,
      severity: 'low',
      details: { reason, revokedBy },
    });
  }

  /**
   * Log password change
   */
  static logPasswordChange(req: Request, userId: string): void {
    this.logEvent({
      eventType: SecurityEventType.PASSWORD_CHANGED,
      userId,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success: true,
      severity: 'medium',
    });
  }

  /**
   * Log security violations
   */
  static logSecurityViolation(
    req: Request, 
    eventType: SecurityEventType, 
    details?: Record<string, any>
  ): void {
    this.logEvent({
      eventType,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success: false,
      severity: 'high',
      details,
    });
  }

  /**
   * Log suspicious activity
   */
  static logSuspiciousActivity(
    req: Request, 
    userId: string | undefined, 
    activity: string, 
    details?: Record<string, any>
  ): void {
    this.logEvent({
      eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
      userId,
      ipAddress: this.getClientIP(req),
      userAgent: req.get('User-Agent'),
      endpoint: req.path,
      method: req.method,
      success: false,
      severity: 'critical',
      details: { activity, ...details },
    });
  }

  /**
   * Log session management events
   */
  static logSessionEvent(
    eventType: SecurityEventType.SESSION_CREATED | SecurityEventType.SESSION_REVOKED | SecurityEventType.ALL_SESSIONS_REVOKED,
    userId: string,
    sessionId?: string,
    details?: Record<string, any>
  ): void {
    this.logEvent({
      eventType,
      userId,
      sessionId,
      success: true,
      severity: 'low',
      details,
    });
  }

  /**
   * Extract client IP address
   */
  private static getClientIP(req: Request): string {
    return req.ip || 
           req.connection.remoteAddress || 
           req.socket.remoteAddress || 
           'unknown';
  }

  /**
   * Analyze security event for suspicious patterns
   */
  private static analyzeSecurityEvent(event: SecurityAuditEvent): void {
    if (!event.success && event.ipAddress) {
      const ip = event.ipAddress;
      const current = this.suspiciousIPs.get(ip) || { count: 0, lastSeen: new Date() };
      
      // Increment failure count for this IP
      current.count++;
      current.lastSeen = new Date();
      this.suspiciousIPs.set(ip, current);

      // Mark as suspicious if threshold exceeded
      if (current.count >= this.SUSPICIOUS_THRESHOLD && current.count < this.BLOCK_THRESHOLD) {
        this.logEvent({
          eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
          ipAddress: ip,
          success: false,
          severity: 'high',
          details: { 
            activity: 'Multiple failed authentication attempts',
            failureCount: current.count,
            timeWindow: '15 minutes'
          },
        });
      }

      // Block IP if block threshold exceeded
      if (current.count >= this.BLOCK_THRESHOLD) {
        this.blockedIPs.add(ip);
        this.generateAlert({
          eventType: SecurityEventType.SUSPICIOUS_ACTIVITY,
          severity: 'critical',
          ipAddress: ip,
          details: { 
            activity: 'IP blocked due to excessive failed attempts',
            failureCount: current.count
          },
        } as SecurityAuditEvent);
      }
    }

    // Clean up old suspicious IP records (older than 15 minutes)
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    for (const [ip, data] of this.suspiciousIPs.entries()) {
      if (data.lastSeen < fifteenMinutesAgo) {
        this.suspiciousIPs.delete(ip);
      }
    }
  }

  /**
   * Generate security alert
   */
  private static generateAlert(event: SecurityAuditEvent): void {
    const alert: SecurityAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      eventType: event.eventType,
      severity: event.severity,
      message: this.generateAlertMessage(event),
      userId: event.userId,
      churchId: event.churchId,
      ipAddress: event.ipAddress,
      timestamp: event.timestamp,
      acknowledged: false,
      details: event.details,
    };

    this.alerts.push(alert);

    // In production, send to alerting system
    this.sendAlert(alert);
  }

  /**
   * Generate human-readable alert message
   */
  private static generateAlertMessage(event: SecurityAuditEvent): string {
    switch (event.eventType) {
      case SecurityEventType.LOGIN_FAILED:
        return `Failed login attempt from ${event.ipAddress}`;
      case SecurityEventType.SUSPICIOUS_ACTIVITY:
        return `Suspicious activity detected from ${event.ipAddress}: ${event.details?.activity}`;
      case SecurityEventType.UNAUTHORIZED_ACCESS:
        return `Unauthorized access attempt to ${event.endpoint} from ${event.ipAddress}`;
      case SecurityEventType.RATE_LIMIT_EXCEEDED:
        return `Rate limit exceeded from ${event.ipAddress}`;
      case SecurityEventType.MALFORMED_TOKEN:
        return `Malformed token received from ${event.ipAddress}`;
      default:
        return `Security event: ${event.eventType} from ${event.ipAddress}`;
    }
  }

  /**
   * Send alert to external systems
   */
  private static sendAlert(alert: SecurityAlert): void {
    // In production, integrate with:
    // - Slack/Teams notifications
    // - Email alerts
    // - PagerDuty
    // - SMS alerts for critical events
    // - SIEM systems

    console.warn(`🚨 SECURITY ALERT [${alert.severity.toUpperCase()}]: ${alert.message}`, {
      id: alert.id,
      eventType: alert.eventType,
      timestamp: alert.timestamp,
      details: alert.details,
    });

    // For critical alerts, you might want immediate notification
    if (alert.severity === 'critical') {
      // Send immediate notification
      this.sendImmediateNotification(alert);
    }
  }

  /**
   * Send immediate notification for critical alerts
   */
  private static sendImmediateNotification(alert: SecurityAlert): void {
    // In production, this would trigger immediate notifications
    console.error(`🚨 IMMEDIATE ATTENTION REQUIRED: ${alert.message}`);
  }

  /**
   * Check if IP is blocked
   */
  static isIPBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }

  /**
   * Get security metrics with enhanced analysis
   */
  static getSecurityMetrics(timeRange: { start: Date; end: Date }): SecurityMetrics {
    const filteredEvents = this.events.filter(event => 
      event.timestamp >= timeRange.start && event.timestamp <= timeRange.end
    );

    const eventsByType: Record<SecurityEventType, number> = {} as Record<SecurityEventType, number>;
    const eventsBySeverity: Record<string, number> = {};
    const failureReasons: Record<string, number> = {};

    filteredEvents.forEach(event => {
      // Count by type
      eventsByType[event.eventType] = (eventsByType[event.eventType] || 0) + 1;
      
      // Count by severity
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;
      
      // Count failure reasons
      if (!event.success && event.details?.reason) {
        failureReasons[event.details.reason] = (failureReasons[event.details.reason] || 0) + 1;
      }
    });

    const topFailureReasons = Object.entries(failureReasons)
      .map(([reason, count]) => ({ reason, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalEvents: filteredEvents.length,
      eventsByType,
      eventsBySeverity,
      failedLoginAttempts: eventsByType[SecurityEventType.LOGIN_FAILED] || 0,
      suspiciousActivities: eventsByType[SecurityEventType.SUSPICIOUS_ACTIVITY] || 0,
      blockedIPs: new Set(this.blockedIPs),
      alertsGenerated: this.alerts.filter(alert => 
        alert.timestamp >= timeRange.start && alert.timestamp <= timeRange.end
      ).length,
      topFailureReasons,
    };
  }

  /**
   * Get active alerts
   */
  static getActiveAlerts(): SecurityAlert[] {
    return this.alerts.filter(alert => !alert.acknowledged);
  }

  /**
   * Acknowledge alert
   */
  static acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * Get audit logs for a user (enhanced)
   */
  static getUserAuditLogs(userId: string, limit: number = 100): SecurityAuditEvent[] {
    return this.events
      .filter(event => event.userId === userId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get audit logs by IP address
   */
  static getIPAuditLogs(ipAddress: string, limit: number = 100): SecurityAuditEvent[] {
    return this.events
      .filter(event => event.ipAddress === ipAddress)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get security dashboard data
   */
  static getSecurityDashboard(): {
    summary: {
      totalEvents: number;
      criticalAlerts: number;
      blockedIPs: number;
      suspiciousIPs: number;
      failureRate: number;
    };
    recentEvents: SecurityAuditEvent[];
    activeAlerts: SecurityAlert[];
    topThreats: Array<{ ip: string; attempts: number; lastSeen: Date }>;
  } {
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentEvents = this.events.filter(event => event.timestamp > oneHourAgo);
    
    const totalEvents = recentEvents.length;
    const failedEvents = recentEvents.filter(event => !event.success).length;
    const failureRate = totalEvents > 0 ? (failedEvents / totalEvents) * 100 : 0;

    const topThreats = Array.from(this.suspiciousIPs.entries())
      .map(([ip, data]) => ({ ip, attempts: data.count, lastSeen: data.lastSeen }))
      .sort((a, b) => b.attempts - a.attempts)
      .slice(0, 10);

    return {
      summary: {
        totalEvents,
        criticalAlerts: this.alerts.filter(a => a.severity === 'critical' && !a.acknowledged).length,
        blockedIPs: this.blockedIPs.size,
        suspiciousIPs: this.suspiciousIPs.size,
        failureRate,
      },
      recentEvents: recentEvents.slice(-20),
      activeAlerts: this.getActiveAlerts().slice(-10),
      topThreats,
    };
  }

  /**
   * Export security data for external analysis
   */
  static exportSecurityData(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'eventType', 'userId', 'ipAddress', 'success', 'severity', 'details'];
      const rows = this.events.map(event => [
        event.timestamp.toISOString(),
        event.eventType,
        event.userId || '',
        event.ipAddress || '',
        event.success.toString(),
        event.severity,
        JSON.stringify(event.details || {}),
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    }

    return JSON.stringify({
      events: this.events,
      alerts: this.alerts,
      blockedIPs: Array.from(this.blockedIPs),
      suspiciousIPs: Object.fromEntries(this.suspiciousIPs),
      exportedAt: new Date().toISOString(),
    }, null, 2);
  }

  /**
   * Handle critical security events
   */
  private static handleCriticalEvent(event: SecurityAuditEvent): void {
    // In production, this could:
    // - Send alerts to security team
    // - Trigger automated responses
    // - Update threat intelligence systems
    // - Block suspicious IPs
    
    console.error(`🚨 CRITICAL SECURITY EVENT: ${event.eventType}`, {
      userId: event.userId,
      ipAddress: event.ipAddress,
      details: event.details,
    });

    // Auto-block IP for certain critical events
    if (event.ipAddress && (
      event.eventType === SecurityEventType.SUSPICIOUS_ACTIVITY ||
      event.eventType === SecurityEventType.UNAUTHORIZED_ACCESS
    )) {
      this.blockedIPs.add(event.ipAddress);
    }
  }
}