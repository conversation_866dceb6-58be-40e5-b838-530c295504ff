import { db } from '../db';
import { members } from '../db/schema';
import { eq } from 'drizzle-orm';

/**
 * Generates a unique user ID for new members
 * Format: 10-digit number (e.g., 1234567890)
 */
export async function generateUniqueUserId(): Promise<string> {
  let attempts = 0;
  const maxAttempts = 100;

  while (attempts < maxAttempts) {
    // Generate a random 10-digit number (1000000000 to 9999999999)
    const randomNumber = Math.floor(1000000000 + Math.random() * 9000000000);
    const userId = randomNumber.toString();

    // Check if this ID already exists
    const existingUser = await db.query.members.findFirst({
      where: eq(members.userId, userId),
      columns: { id: true }
    });

    if (!existingUser) {
      return userId;
    }

    attempts++;
  }

  throw new Error('Unable to generate unique user ID after maximum attempts');
}

/**
 * Validates a user ID format
 * @param userId - The user ID to validate
 * @returns boolean indicating if the format is valid
 */
export function isValidUserIdFormat(userId: string): boolean {
  // Must be exactly 10 digits
  const userIdRegex = /^\d{10}$/;
  return userIdRegex.test(userId);
}

/**
 * Checks if a string is a valid email format
 * @param input - The input string to check
 * @returns boolean indicating if it's a valid email
 */
export function isValidEmailFormat(input: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(input);
}