import type { Request, Response, NextFunction } from 'express';
import { ZodError } from 'zod';

export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public code?: string;
  public errors?: ValidationError[];

  constructor(message: string, statusCode: number, code?: string, errors?: ValidationError[]) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = true;
    this.code = code;
    this.errors = errors;

    Error.captureStackTrace(this, this.constructor);
  }
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

export interface StandardErrorResponse {
  status: 'error';
  message: string;
  errors?: ValidationError[];
  code?: string;
}

export interface StandardSuccessResponse<T = any> {
  status: 'success';
  message: string;
  data?: T;
}

export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    fn(req, res, next).catch(next);
  };
};

export const globalErrorHandler = (err: any, req: Request, res: Response, _next: NextFunction) => {
  // Set default error properties
  let statusCode = err.statusCode || 500;
  let status = err.status || 'error';
  let message = err.message || 'Something went wrong';

  // Handle Zod validation errors with enhanced field-specific messages
  if (err instanceof ZodError) {
    statusCode = 400;
    status = 'error';

    const errors: ValidationError[] = err.issues.map(issue => ({
      field: issue.path ? issue.path.join('.') : 'unknown',
      message: issue.message || 'Validation error',
      code: getValidationErrorCode(issue.code, issue.path?.join('.') || 'unknown')
    }));

    return res.status(statusCode).json({
      status,
      message: 'Validation failed',
      errors,
      code: 'VALIDATION_ERROR'
    });
  }

  // Handle AppError with custom validation errors
  if (err instanceof AppError && err.errors) {
    return res.status(err.statusCode).json({
      status: 'error',
      message: err.message,
      errors: err.errors,
      code: err.code || 'VALIDATION_ERROR'
    });
  }

  // Handle validation errors that have errors array but aren't ZodError instances
  if (err.errors && Array.isArray(err.errors)) {
    statusCode = 400;
    status = 'error';

    const errors: ValidationError[] = err.errors.map((error: any) => ({
      field: error.path ? error.path.join('.') : error.field || 'unknown',
      message: error.message || 'Validation error',
      code: error.code || 'VALIDATION_ERROR'
    }));

    return res.status(statusCode).json({
      status,
      message: 'Validation failed',
      errors,
      code: 'VALIDATION_ERROR'
    });
  }

  // Handle case where the error message is a stringified array (from Zod)
  if (typeof message === 'string' && message.startsWith('[') && message.includes('"path"')) {
    try {
      const parsedErrors = JSON.parse(message);
      if (Array.isArray(parsedErrors)) {
        const errors: ValidationError[] = parsedErrors.map((error: any) => ({
          field: error.path ? error.path.join('.') : 'unknown',
          message: error.message || 'Validation error',
          code: getValidationErrorCode(error.code, error.path?.join('.') || 'unknown')
        }));

        return res.status(400).json({
          status: 'error',
          message: 'Validation failed',
          errors,
          code: 'VALIDATION_ERROR'
        });
      }
    } catch {
      // If parsing fails, continue with original error handling
    }
  }

  // Handle duplicate key errors (PostgreSQL) with field-specific messages
  if (err.code === '23505') {
    const field = extractFieldFromDuplicateError(err.detail || err.message || '');
    const errors: ValidationError[] = [{
      field,
      message: `${field === 'email' ? 'Email address' : 'This value'} is already registered`,
      code: 'DUPLICATE_VALUE'
    }];

    return res.status(400).json({
      status: 'error',
      message: 'Duplicate entry detected',
      errors,
      code: 'DUPLICATE_ERROR'
    });
  }

  // Handle foreign key constraint errors (PostgreSQL)
  if (err.code === '23503') {
    const field = extractFieldFromForeignKeyError(err.detail || err.message || '');
    const errors: ValidationError[] = [{
      field,
      message: 'Selected option is not valid or does not exist',
      code: 'INVALID_REFERENCE'
    }];

    return res.status(400).json({
      status: 'error',
      message: 'Invalid reference detected',
      errors,
      code: 'REFERENCE_ERROR'
    });
  }

  // Handle not null constraint errors (PostgreSQL)
  if (err.code === '23502') {
    const field = extractFieldFromNotNullError(err.message || '');
    const errors: ValidationError[] = [{
      field,
      message: 'This field is required',
      code: 'REQUIRED_FIELD'
    }];

    return res.status(400).json({
      status: 'error',
      message: 'Required field missing',
      errors,
      code: 'REQUIRED_FIELD_ERROR'
    });
  }

  // Default error response
  res.status(statusCode).json({
    status,
    message,
    ...(err.code && { code: err.code }),
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

// Helper function to generate validation error codes
function getValidationErrorCode(zodCode: string, field: string): string {
  const codeMap: Record<string, string> = {
    'invalid_type': 'INVALID_TYPE',
    'invalid_string': 'INVALID_FORMAT',
    'too_small': 'TOO_SHORT',
    'too_big': 'TOO_LONG',
    'invalid_enum_value': 'INVALID_OPTION',
    'custom': field.includes('email') ? 'INVALID_EMAIL' :
      field.includes('phone') ? 'INVALID_PHONE' :
        field.includes('password') ? 'WEAK_PASSWORD' : 'INVALID_FORMAT'
  };

  return codeMap[zodCode] || 'VALIDATION_ERROR';
}

// Helper function to extract field name from PostgreSQL duplicate error
function extractFieldFromDuplicateError(detail: string): string {
  const match = detail.match(/Key \(([^)]+)\)/);
  return match && match[1] ? match[1] : 'unknown';
}

// Helper function to extract field name from PostgreSQL foreign key error
function extractFieldFromForeignKeyError(detail: string): string {
  const match = detail.match(/Key \(([^)]+)\)/);
  if (match && match[1]) {
    const field = match[1];
    // Convert database field names to more user-friendly names
    if (field.includes('branch')) return 'branchId';
    if (field.includes('role')) return 'roleId';
    if (field.includes('church')) return 'churchId';
    return field;
  }
  return 'unknown';
}

// Helper function to extract field name from PostgreSQL not null error
function extractFieldFromNotNullError(message: string): string {
  const match = message.match(/column "([^"]+)"/);
  if (match && match[1]) {
    const field = match[1];
    // Convert snake_case to camelCase
    return field.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }
  return 'unknown';
}