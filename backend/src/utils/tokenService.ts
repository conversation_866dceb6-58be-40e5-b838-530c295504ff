import type { Request, Response } from 'express';
import { db } from '../db';
import { refreshTokens } from '../db/schema';
import { eq, and, lt } from 'drizzle-orm';
import { 
  generateRefreshToken, 
  getTokenExpiration, 
  isTokenExpired 
} from './auth';

export interface TokenMetadata {
  ipAddress?: string;
  userAgent?: string;
}

export interface TokenRevocationOptions {
  reason?: string;
  revokedBy?: string;
}

export class TokenService {
  private static readonly REFRESH_TOKEN_COOKIE_NAME = 'refresh_token';
  private static readonly COOKIE_MAX_AGE = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

  /**
   * Set refresh token as httpOnly cookie
   */
  static setRefreshTokenCookie(res: Response, token: string): void {
    const isProduction = process.env.NODE_ENV === 'production';
    
    res.cookie(this.REFRESH_TOKEN_COOKIE_NAME, token, {
      httpOnly: true,
      secure: isProduction, // Only send over HTTPS in production
      sameSite: isProduction ? 'strict' : 'lax',
      maxAge: this.COOKIE_MAX_AGE,
      path: '/api/auth', // Restrict cookie to auth endpoints
    });
  }

  /**
   * Clear refresh token cookie
   */
  static clearRefreshTokenCookie(res: Response): void {
    res.clearCookie(this.REFRESH_TOKEN_COOKIE_NAME, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax',
      path: '/api/auth',
    });
  }

  /**
   * Get refresh token from cookie or request body
   */
  static getRefreshToken(req: Request): string | null {
    // First try to get from httpOnly cookie
    const cookieToken = req.cookies?.[this.REFRESH_TOKEN_COOKIE_NAME];
    if (cookieToken) {
      return cookieToken;
    }

    // Fallback to request body for backward compatibility
    return req.body.refreshToken || null;
  }

  /**
   * Create a new refresh token with metadata
   */
  static async createRefreshToken(
    memberId: string, 
    metadata: TokenMetadata = {}
  ): Promise<string> {
    const token = generateRefreshToken();
    const expiresAt = getTokenExpiration(7 * 24); // 7 days

    await db.insert(refreshTokens).values({
      memberId,
      token,
      expiresAt,
      ipAddress: metadata.ipAddress,
      userAgent: metadata.userAgent,
    });

    return token;
  }

  /**
   * Validate and use a refresh token
   */
  static async validateAndUseRefreshToken(
    token: string,
    _metadata: TokenMetadata = {}
  ): Promise<{ member: any; tokenRecord: any } | null> {
    const tokenRecord = await db.query.refreshTokens.findFirst({
      where: and(
        eq(refreshTokens.token, token),
        eq(refreshTokens.isRevoked, false)
      ),
      with: {
        member: {
          with: {
            role: true,
            church: true,
          },
        },
      },
    });

    if (!tokenRecord || isTokenExpired(tokenRecord.expiresAt)) {
      return null;
    }

    // Update last used timestamp
    await db.update(refreshTokens)
      .set({ 
        lastUsedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(refreshTokens.id, tokenRecord.id));

    return { member: tokenRecord.member, tokenRecord };
  }

  /**
   * Revoke a specific refresh token
   */
  static async revokeToken(
    token: string, 
    options: TokenRevocationOptions = {}
  ): Promise<boolean> {
    const result = await db.update(refreshTokens)
      .set({
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy: options.revokedBy || null,
        revokedReason: options.reason || 'manual_revocation',
        updatedAt: new Date(),
      })
      .where(eq(refreshTokens.token, token))
      .returning({ id: refreshTokens.id });

    return result.length > 0;
  }

  /**
   * Revoke all refresh tokens for a user
   */
  static async revokeAllUserTokens(
    memberId: string, 
    options: TokenRevocationOptions = {}
  ): Promise<number> {
    const result = await db.update(refreshTokens)
      .set({
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy: options.revokedBy || null,
        revokedReason: options.reason || 'revoke_all_sessions',
        updatedAt: new Date(),
      })
      .where(and(
        eq(refreshTokens.memberId, memberId),
        eq(refreshTokens.isRevoked, false)
      ))
      .returning({ id: refreshTokens.id });

    return result.length;
  }

  /**
   * Clean up expired tokens
   */
  static async cleanupExpiredTokens(): Promise<number> {
    const result = await db.update(refreshTokens)
      .set({
        isRevoked: true,
        revokedAt: new Date(),
        revokedReason: 'expired',
        updatedAt: new Date(),
      })
      .where(and(
        eq(refreshTokens.isRevoked, false),
        lt(refreshTokens.expiresAt, new Date())
      ))
      .returning({ id: refreshTokens.id });

    return result.length;
  }

  /**
   * Get active sessions for a user
   */
  static async getUserActiveSessions(memberId: string): Promise<any[]> {
    return await db.query.refreshTokens.findMany({
      where: and(
        eq(refreshTokens.memberId, memberId),
        eq(refreshTokens.isRevoked, false)
      ),
      columns: {
        id: true,
        createdAt: true,
        lastUsedAt: true,
        expiresAt: true,
        ipAddress: true,
        userAgent: true,
      },
      orderBy: (tokens, { desc }) => [desc(tokens.lastUsedAt)],
    });
  }

  /**
   * Revoke a specific session by ID
   */
  static async revokeSession(
    sessionId: string, 
    memberId: string,
    options: TokenRevocationOptions = {}
  ): Promise<boolean> {
    const result = await db.update(refreshTokens)
      .set({
        isRevoked: true,
        revokedAt: new Date(),
        revokedBy: options.revokedBy || null,
        revokedReason: options.reason || 'session_revoked',
        updatedAt: new Date(),
      })
      .where(and(
        eq(refreshTokens.id, sessionId),
        eq(refreshTokens.memberId, memberId),
        eq(refreshTokens.isRevoked, false)
      ))
      .returning({ id: refreshTokens.id });

    return result.length > 0;
  }

  /**
   * Extract metadata from request
   */
  static extractMetadata(req: Request): TokenMetadata {
    return {
      ipAddress: req.ip || req.connection.remoteAddress || undefined,
      userAgent: req.get('User-Agent') || undefined,
    };
  }
}