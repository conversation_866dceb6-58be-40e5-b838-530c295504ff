import { db } from '../db';
import { roles } from '../db/schema';
import { DEFAULT_ROLES } from './permissions';

export const seedRoles = async (churchId: string) => {
  const defaultRoles = Object.values(DEFAULT_ROLES).map(role => ({
    churchId,
    name: role.name,
    description: role.description,
    permissions: role.permissions,
    isSystem: role.isSystem,
  }));

  await db.insert(roles).values(defaultRoles);
};

export const createCustomRole = async (
  churchId: string,
  name: string,
  description: string,
  permissions: string[]
) => {
  const [role] = await db.insert(roles).values({
    churchId,
    name,
    description,
    permissions,
    isSystem: false,
  }).returning();

  return role;
};