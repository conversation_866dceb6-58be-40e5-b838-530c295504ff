import { drizzle as drizzlePg } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';

let db: ReturnType<typeof drizzlePg>;

if (process.env.NODE_ENV === 'test') {
  // Lazy import PGLite + drizzle for tests
  const setup = async () => {
    const { PGlite } = await import('@electric-sql/pglite');
    const { drizzle } = await import('drizzle-orm/pglite');
    const client = new PGlite();
    const d = drizzle(client, { schema });
    // Attempt to push schema using drizzle-kit api if available
    try {
      // eslint-disable-next-line @typescript-eslint/no-var-requires
      const { pushSchema } = require('drizzle-kit/api') as typeof import('drizzle-kit/api');
      const { apply } = await pushSchema(schema as unknown as Record<string, unknown>, d as unknown as any);
      await apply();
    } catch {
      // fallback: no pushSchema, continue
    }
    return d;
  };
  // @ts-expect-error assign after await
  db = await setup();
} else {
  const connectionString = process.env.DATABASE_URL!;
  const client = postgres(connectionString);
  db = drizzlePg(client, { schema });
}

export { db };
export * as schema from './schema';
