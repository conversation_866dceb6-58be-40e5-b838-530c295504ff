import { pgTable, uuid, varchar, text, timestamp, boolean, pgEnum, integer } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { branches } from './branches';
import { members } from './members';
import { events } from './events';

export const announcementTypeEnum = pgEnum('announcement_type', [
  'general',
  'urgent',
  'event_related',
  'service_update',
  'prayer_request',
  'community_news',
  'financial',
  'special_event',
  'ministry_update',
  'volunteer_opportunity',
  'other'
]);

export const priorityEnum = pgEnum('priority', ['low', 'normal', 'high', 'urgent']);

export const announcementStatusEnum = pgEnum('announcement_status', [
  'draft',
  'scheduled',
  'published',
  'archived',
  'expired'
]);

export const targetAudienceEnum = pgEnum('target_audience', [
  'all_members',
  'specific_branches',
  'specific_roles',
  'custom_group'
]);

export const announcements = pgTable('announcements', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'set null' }), // Optional link to event
  authorId: uuid('author_id').references(() => members.id, { onDelete: 'set null' }).notNull(),
  title: varchar('title', { length: 255 }).notNull(),
  content: text('content').notNull(),
  summary: varchar('summary', { length: 500 }), // Short summary for previews
  type: announcementTypeEnum('type').default('general').notNull(),
  priority: priorityEnum('priority').default('normal').notNull(),
  status: announcementStatusEnum('status').default('draft').notNull(),
  targetAudience: targetAudienceEnum('target_audience').default('all_members').notNull(),
  targetBranches: text('target_branches'), // JSON array of branch IDs
  targetRoles: text('target_roles'), // JSON array of role names
  imageUrl: varchar('image_url', { length: 500 }),
  attachments: text('attachments'), // JSON array of attachment objects
  externalLinks: text('external_links'), // JSON array of links
  tags: text('tags'), // JSON array of tags
  publishedAt: timestamp('published_at'),
  scheduledFor: timestamp('scheduled_for'), // For scheduled announcements
  expiresAt: timestamp('expires_at'), // Optional expiration
  isPinned: boolean('is_pinned').default(false), // Pin to top
  allowComments: boolean('allow_comments').default(true),
  requiresAcknowledgment: boolean('requires_acknowledgment').default(false), // Track who has read
  sendNotification: boolean('send_notification').default(true), // Send push/email notifications
  notificationChannels: text('notification_channels'), // JSON: email, sms, push, etc.
  viewCount: integer('view_count').default(0),
  metadata: text('metadata'), // JSON for additional custom fields
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Track announcement views/reads
export const announcementViews = pgTable('announcement_views', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  announcementId: uuid('announcement_id').references(() => announcements.id, { onDelete: 'cascade' }).notNull(),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  viewedAt: timestamp('viewed_at').defaultNow(),
  acknowledgedAt: timestamp('acknowledged_at'), // For announcements requiring acknowledgment
  deviceInfo: text('device_info'), // JSON with device/browser info
  createdAt: timestamp('created_at').defaultNow(),
});

// Comments on announcements
export const announcementComments = pgTable('announcement_comments', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  announcementId: uuid('announcement_id').references(() => announcements.id, { onDelete: 'cascade' }).notNull(),
  authorId: uuid('author_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  parentCommentId: uuid('parent_comment_id'), // Self-reference defined separately to avoid circular dependency
  content: text('content').notNull(),
  isEdited: boolean('is_edited').default(false),
  editedAt: timestamp('edited_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Announcement reactions/likes
export const announcementReactions = pgTable('announcement_reactions', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  announcementId: uuid('announcement_id').references(() => announcements.id, { onDelete: 'cascade' }).notNull(),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  reactionType: varchar('reaction_type', { length: 50 }).notNull(), // like, love, pray, amen, etc.
  createdAt: timestamp('created_at').defaultNow(),
});

// Relations
export const announcementsRelations = relations(announcements, ({ one, many }) => ({
  church: one(churches, {
    fields: [announcements.churchId],
    references: [churches.id],
  }),
  event: one(events, {
    fields: [announcements.eventId],
    references: [events.id],
  }),
  author: one(members, {
    fields: [announcements.authorId],
    references: [members.id],
  }),
  views: many(announcementViews),
  comments: many(announcementComments),
  reactions: many(announcementReactions),
}));

export const announcementViewsRelations = relations(announcementViews, ({ one }) => ({
  announcement: one(announcements, {
    fields: [announcementViews.announcementId],
    references: [announcements.id],
  }),
  member: one(members, {
    fields: [announcementViews.memberId],
    references: [members.id],
  }),
}));

export const announcementCommentsRelations = relations(announcementComments, ({ one, many }) => ({
  announcement: one(announcements, {
    fields: [announcementComments.announcementId],
    references: [announcements.id],
  }),
  author: one(members, {
    fields: [announcementComments.authorId],
    references: [members.id],
  }),
  parentComment: one(announcementComments, {
    fields: [announcementComments.parentCommentId],
    references: [announcementComments.id],
  }),
  replies: many(announcementComments),
}));

export const announcementReactionsRelations = relations(announcementReactions, ({ one }) => ({
  announcement: one(announcements, {
    fields: [announcementReactions.announcementId],
    references: [announcements.id],
  }),
  member: one(members, {
    fields: [announcementReactions.memberId],
    references: [members.id],
  }),
}));