import { pgTable, uuid, varchar, text, boolean, timestamp, pgEnum } from 'drizzle-orm/pg-core';
import { churches } from './churches';

export const paymentMethodTypeEnum = pgEnum('payment_method_type', [
  'mobile_money',
  'bank_card', 
  'bank_transfer',
  'cash'
]);

export const paymentMethodProviderEnum = pgEnum('payment_method_provider', [
  'airtel',
  'mtn',
  'zamtel',
  'visa',
  'mastercard'
]);

export const paymentMethods = pgTable('payment_methods', {
  id: uuid('id').primaryKey().defaultRandom(),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  type: paymentMethodTypeEnum('type').notNull(),
  provider: paymentMethodProviderEnum('provider'),
  accountNumber: varchar('account_number', { length: 100 }),
  accountName: varchar('account_name', { length: 255 }),
  bankName: varchar('bank_name', { length: 255 }),
  phoneNumber: varchar('phone_number', { length: 20 }),
  isActive: boolean('is_active').default(true).notNull(),
  isDefault: boolean('is_default').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export type PaymentMethod = typeof paymentMethods.$inferSelect;
export type NewPaymentMethod = typeof paymentMethods.$inferInsert;