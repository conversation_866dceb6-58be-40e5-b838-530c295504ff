import { pgTable, uuid, varchar, text, timestamp, boolean, jsonb } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { members } from './members';

export const branches = pgTable('branches', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull(), // Unique within church context
  description: text('description'),
  address: text('address'),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 255 }),
  coordinates: jsonb('coordinates'), // { lat: number, lng: number }
  capacity: varchar('capacity', { length: 100 }), // e.g., "200 seated, 300 standing"
  facilities: jsonb('facilities'), // Array of facility types like ["parking", "wheelchair_accessible", "sound_system"]
  branchLeader: uuid('branch_leader').references(() => members.id, { onDelete: 'set null' }),
  settings: jsonb('settings').default({}), // Branch-specific settings
  isActive: boolean('is_active').default(true),
  isMainBranch: boolean('is_main_branch').default(false), // One main branch per church
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const branchesRelations = relations(branches, ({ one, many }) => ({
  church: one(churches, {
    fields: [branches.churchId],
    references: [churches.id],
  }),
  leader: one(members, {
    fields: [branches.branchLeader],
    references: [members.id],
  }),
  members: many(members),
}));