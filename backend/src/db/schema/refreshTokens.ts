import { pgTable, uuid, varchar, timestamp, boolean, text } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { members } from './members';

export const refreshTokens = pgTable('refresh_tokens', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  expiresAt: timestamp('expires_at').notNull(),
  isRevoked: boolean('is_revoked').default(false),
  revokedAt: timestamp('revoked_at'),
  revokedBy: uuid('revoked_by').references(() => members.id),
  revokedReason: varchar('revoked_reason', { length: 100 }),
  ipAddress: varchar('ip_address', { length: 45 }), // Support IPv6
  userAgent: text('user_agent'),
  lastUsedAt: timestamp('last_used_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const refreshTokensRelations = relations(refreshTokens, ({ one }) => ({
  member: one(members, {
    fields: [refreshTokens.memberId],
    references: [members.id],
  }),
}));