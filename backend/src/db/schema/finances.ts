import { pgTable, uuid, varchar, text, timestamp, decimal, pgEnum, boolean } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { branches } from './branches';
import { members } from './members';
import { events } from './events';

export const financeTypeEnum = pgEnum('finance_type', [
  'tithe',
  'donation',
  'offering',
  'event_donation',
  'building_fund',
  'mission_support',
  'special_collection',
  'other'
]);

export const financeMethodEnum = pgEnum('finance_method', [
  'cash',
  'check',
  'credit_card',
  'debit_card',
  'bank_transfer',
  'mobile_payment',
  'online',
  'other'
]);

export const financeStatusEnum = pgEnum('finance_status', [
  'pending',
  'completed',
  'failed',
  'refunded',
  'cancelled'
]);

export const projectStatusEnum = pgEnum('project_status', [
  'planning',
  'active',
  'completed',
  'cancelled',
  'on_hold'
]);

export const finances = pgTable('finances', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  branchId: uuid('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'set null' }),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'set null' }),
  projectId: uuid('project_id').references(() => financeProjects.id, { onDelete: 'set null' }),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('ZMW').notNull(),
  type: financeTypeEnum('type').default('offering').notNull(),
  method: financeMethodEnum('method').default('cash').notNull(),
  status: financeStatusEnum('status').default('completed').notNull(),
  donorName: varchar('donor_name', { length: 255 }), // For anonymous or guest donations
  donorEmail: varchar('donor_email', { length: 255 }),
  donorPhone: varchar('donor_phone', { length: 20 }),
  description: text('description'),
  notes: text('notes'), // Internal notes
  receiptNumber: varchar('receipt_number', { length: 100 }),
  taxDeductible: boolean('tax_deductible').default(true),
  isAnonymous: boolean('is_anonymous').default(false),
  transactionId: varchar('transaction_id', { length: 255 }), // Payment processor transaction ID
  metadata: text('metadata'), // JSON for additional data (payment processor info, etc.)
  recordedAt: timestamp('recorded_at').defaultNow(),
  recordedBy: uuid('recorded_by').references(() => members.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const financeCategories = pgTable('finance_categories', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const financeProjects = pgTable('finance_projects', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  name: varchar('name', { length: 200 }).notNull(),
  description: text('description'),
  targetAmount: decimal('target_amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('ZMW').notNull(),
  status: projectStatusEnum('status').default('planning').notNull(),
  startDate: timestamp('start_date'),
  endDate: timestamp('end_date'),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const financesRelations = relations(finances, ({ one }) => ({
  church: one(churches, {
    fields: [finances.churchId],
    references: [churches.id],
  }),
  branch: one(branches, {
    fields: [finances.branchId],
    references: [branches.id],
  }),
  member: one(members, {
    fields: [finances.memberId],
    references: [members.id],
  }),
  event: one(events, {
    fields: [finances.eventId],
    references: [events.id],
  }),
  project: one(financeProjects, {
    fields: [finances.projectId],
    references: [financeProjects.id],
  }),
  recordedBy: one(members, {
    fields: [finances.recordedBy],
    references: [members.id],
  }),
}));

export const financeCategoriesRelations = relations(financeCategories, ({ one }) => ({
  church: one(churches, {
    fields: [financeCategories.churchId],
    references: [churches.id],
  }),
}));

export const financeProjectsRelations = relations(financeProjects, ({ one, many }) => ({
  church: one(churches, {
    fields: [financeProjects.churchId],
    references: [churches.id],
  }),
  finances: many(finances),
}));