import { pgTable, uuid, varchar, text, timestamp, decimal, pgEnum, boolean } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { branches } from './branches';
import { members } from './members';
import { events } from './events';

export const donationTypeEnum = pgEnum('donation_type', [
  'tithe',
  'offering',
  'event_donation',
  'building_fund',
  'mission_support',
  'special_collection',
  'other'
]);

export const donationMethodEnum = pgEnum('donation_method', [
  'cash',
  'check',
  'credit_card',
  'debit_card',
  'bank_transfer',
  'mobile_payment',
  'online',
  'other'
]);

export const donationStatusEnum = pgEnum('donation_status', [
  'pending',
  'completed',
  'failed',
  'refunded',
  'cancelled'
]);

export const donations = pgTable('donations', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  branchId: uuid('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  donorId: uuid('donor_id').references(() => members.id, { onDelete: 'set null' }),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'set null' }),
  amount: decimal('amount', { precision: 10, scale: 2 }).notNull(),
  currency: varchar('currency', { length: 3 }).default('USD').notNull(),
  type: donationTypeEnum('type').default('offering').notNull(),
  method: donationMethodEnum('method').default('cash').notNull(),
  status: donationStatusEnum('status').default('completed').notNull(),
  donorName: varchar('donor_name', { length: 255 }), // For anonymous or guest donations
  donorEmail: varchar('donor_email', { length: 255 }),
  donorPhone: varchar('donor_phone', { length: 20 }),
  description: text('description'),
  notes: text('notes'), // Internal notes
  receiptNumber: varchar('receipt_number', { length: 100 }),
  taxDeductible: boolean('tax_deductible').default(true),
  isAnonymous: boolean('is_anonymous').default(false),
  transactionId: varchar('transaction_id', { length: 255 }), // Payment processor transaction ID
  metadata: text('metadata'), // JSON for additional data (payment processor info, etc.)
  donatedAt: timestamp('donated_at').defaultNow(),
  recordedBy: uuid('recorded_by').references(() => members.id, { onDelete: 'set null' }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const donationCategories = pgTable('donation_categories', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  name: varchar('name', { length: 100 }).notNull(),
  description: text('description'),
  targetAmount: decimal('target_amount', { precision: 10, scale: 2 }),
  isActive: boolean('is_active').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const donationsRelations = relations(donations, ({ one }) => ({
  church: one(churches, {
    fields: [donations.churchId],
    references: [churches.id],
  }),
  branch: one(branches, {
    fields: [donations.branchId],
    references: [branches.id],
  }),
  donor: one(members, {
    fields: [donations.donorId],
    references: [members.id],
  }),
  event: one(events, {
    fields: [donations.eventId],
    references: [events.id],
  }),
  recordedBy: one(members, {
    fields: [donations.recordedBy],
    references: [members.id],
  }),
}));

export const donationCategoriesRelations = relations(donationCategories, ({ one }) => ({
  church: one(churches, {
    fields: [donationCategories.churchId],
    references: [churches.id],
  }),
}));