import { pgTable, uuid, varchar, text, timestamp, boolean, date, jsonb, pgEnum } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { branches } from './branches';
import { roles } from './roles';

export const memberStatusEnum = pgEnum('member_status', ['active', 'inactive', 'suspended']);
export const genderEnum = pgEnum('gender', ['male', 'female', 'other']);

export const members = pgTable('members', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  userId: varchar('user_id', { length: 10 }).notNull().unique(),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  branchId: uuid('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  roleId: uuid('role_id').references(() => roles.id, { onDelete: 'set null' }),
  firstName: varchar('first_name', { length: 100 }).notNull(),
  lastName: varchar('last_name', { length: 100 }).notNull(),
  email: varchar('email', { length: 255 }).unique().notNull(),
  phone: varchar('phone', { length: 20 }),
  password: varchar('password', { length: 255 }).notNull(),
  dateOfBirth: date('date_of_birth'),
  gender: genderEnum('gender'),
  address: text('address'),
  profileImage: varchar('profile_image', { length: 500 }),
  joinDate: date('join_date').defaultNow(),
  status: memberStatusEnum('status').default('active'),
  additionalInfo: jsonb('additional_info').default({}),
  isEmailVerified: boolean('is_email_verified').default(false),
  emailVerificationToken: varchar('email_verification_token', { length: 255 }),
  passwordResetToken: varchar('password_reset_token', { length: 255 }),
  passwordResetExpires: timestamp('password_reset_expires'),
  lastLoginAt: timestamp('last_login_at'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const membersRelations = relations(members, ({ one }) => ({
  church: one(churches, {
    fields: [members.churchId],
    references: [churches.id],
  }),
  branch: one(branches, {
    fields: [members.branchId],
    references: [branches.id],
  }),
  role: one(roles, {
    fields: [members.roleId],
    references: [roles.id],
  }),
}));