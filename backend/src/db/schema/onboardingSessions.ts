import { pgTable, uuid, text, timestamp, boolean, jsonb } from 'drizzle-orm/pg-core';
import { createId } from '@paralleldrive/cuid2';

export const onboardingSessions = pgTable('onboarding_sessions', {
  id: uuid('id').primaryKey().$defaultFn(() => createId()),
  churchData: jsonb('church_data').notNull(),
  adminData: jsonb('admin_data').notNull(),
  settingsData: jsonb('settings_data'),
  hashedPassword: text('hashed_password').notNull(),
  token: text('token').unique().notNull(),
  verificationToken: text('verification_token').unique(),
  expiresAt: timestamp('expires_at').notNull(),
  isCompleted: boolean('is_completed').default(false).notNull(),
  isVerified: boolean('is_verified').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});