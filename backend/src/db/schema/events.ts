import { pgTable, uuid, varchar, text, timestamp, boolean, decimal, integer, pgEnum } from 'drizzle-orm/pg-core';
import { sql } from 'drizzle-orm';
import { relations } from 'drizzle-orm';
import { churches } from './churches';
import { branches } from './branches';
import { members } from './members';

export const eventTypeEnum = pgEnum('event_type', [
  'sunday_service',
  'bible_study',
  'prayer_meeting',
  'youth_service',
  'choir_practice',
  'community_outreach',
  'fundraiser',
  'conference',
  'retreat',
  'wedding',
  'funeral',
  'baptism',
  'communion',
  'special_service',
  'social_event',
  'other'
]);

export const eventStatusEnum = pgEnum('event_status', ['draft', 'published', 'cancelled', 'completed']);

export const rsvpStatusEnum = pgEnum('rsvp_status', ['pending', 'attending', 'not_attending', 'maybe']);

export const events = pgTable('events', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  churchId: uuid('church_id').references(() => churches.id, { onDelete: 'cascade' }).notNull(),
  branchId: uuid('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  createdBy: uuid('created_by').references(() => members.id, { onDelete: 'set null' }),
  title: varchar('title', { length: 255 }).notNull(),
  description: text('description'),
  type: eventTypeEnum('type').default('other').notNull(),
  status: eventStatusEnum('status').default('draft').notNull(),
  startDate: timestamp('start_date').notNull(),
  endDate: timestamp('end_date'),
  location: varchar('location', { length: 500 }),
  virtualLink: varchar('virtual_link', { length: 500 }),
  maxAttendees: integer('max_attendees'),
  requiresRsvp: boolean('requires_rsvp').default(false),
  allowDonations: boolean('allow_donations').default(false),
  donationGoal: decimal('donation_goal', { precision: 10, scale: 2 }),
  donationDescription: text('donation_description'),
  imageUrl: text('image_url'),
  recurrencePattern: text('recurrence_pattern'), // JSON for recurring events
  tags: text('tags'), // JSON array of tags
  isPublic: boolean('is_public').default(true),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const eventRsvps = pgTable('event_rsvps', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'cascade' }).notNull(),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  status: rsvpStatusEnum('status').default('pending').notNull(),
  attendeeCount: integer('attendee_count').default(1),
  notes: text('notes'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const eventLikes = pgTable('event_likes', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'cascade' }).notNull(),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  createdAt: timestamp('created_at').defaultNow(),
});

export const eventComments = pgTable('event_comments', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  eventId: uuid('event_id').references(() => events.id, { onDelete: 'cascade' }).notNull(),
  memberId: uuid('member_id').references(() => members.id, { onDelete: 'cascade' }).notNull(),
  parentCommentId: uuid('parent_comment_id').references(() => eventComments.id, { onDelete: 'cascade' }),
  content: text('content').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const eventsRelations = relations(events, ({ one, many }) => ({
  church: one(churches, {
    fields: [events.churchId],
    references: [churches.id],
  }),
  branch: one(branches, {
    fields: [events.branchId],
    references: [branches.id],
  }),
  createdBy: one(members, {
    fields: [events.createdBy],
    references: [members.id],
  }),
  rsvps: many(eventRsvps),
  likes: many(eventLikes),
  comments: many(eventComments),
}));

export const eventRsvpsRelations = relations(eventRsvps, ({ one }) => ({
  event: one(events, {
    fields: [eventRsvps.eventId],
    references: [events.id],
  }),
  member: one(members, {
    fields: [eventRsvps.memberId],
    references: [members.id],
  }),
}));

export const eventLikesRelations = relations(eventLikes, ({ one }) => ({
  event: one(events, {
    fields: [eventLikes.eventId],
    references: [events.id],
  }),
  member: one(members, {
    fields: [eventLikes.memberId],
    references: [members.id],
  }),
}));

export const eventCommentsRelations = relations(eventComments, ({ one, many }) => ({
  event: one(events, {
    fields: [eventComments.eventId],
    references: [events.id],
  }),
  member: one(members, {
    fields: [eventComments.memberId],
    references: [members.id],
  }),
  parentComment: one(eventComments, {
    fields: [eventComments.parentCommentId],
    references: [eventComments.id],
    relationName: 'parentComment',
  }),
  replies: many(eventComments, {
    relationName: 'parentComment',
  }),
}));