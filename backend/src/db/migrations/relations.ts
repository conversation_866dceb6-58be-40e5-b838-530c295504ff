import { relations } from "drizzle-orm/relations";
import { churches, paymentMethods, donationCategories, roles, branches, members, donations, events, eventRsvps, refreshTokens, announcements, announcementComments, announcementReactions, announcementViews } from "./schema";

export const paymentMethodsRelations = relations(paymentMethods, ({one}) => ({
	church: one(churches, {
		fields: [paymentMethods.churchId],
		references: [churches.id]
	}),
}));

export const churchesRelations = relations(churches, ({many}) => ({
	paymentMethods: many(paymentMethods),
	donationCategories: many(donationCategories),
	roles: many(roles),
	branches: many(branches),
	donations: many(donations),
	events: many(events),
	members: many(members),
	announcements: many(announcements),
}));

export const donationCategoriesRelations = relations(donationCategories, ({one}) => ({
	church: one(churches, {
		fields: [donationCategories.churchId],
		references: [churches.id]
	}),
}));

export const rolesRelations = relations(roles, ({one, many}) => ({
	church: one(churches, {
		fields: [roles.churchId],
		references: [churches.id]
	}),
	members: many(members),
}));

export const branchesRelations = relations(branches, ({one, many}) => ({
	church: one(churches, {
		fields: [branches.churchId],
		references: [churches.id]
	}),
	member: one(members, {
		fields: [branches.branchLeader],
		references: [members.id],
		relationName: "branches_branchLeader_members_id"
	}),
	donations: many(donations),
	events: many(events),
	members: many(members, {
		relationName: "members_branchId_branches_id"
	}),
}));

export const membersRelations = relations(members, ({one, many}) => ({
	branches: many(branches, {
		relationName: "branches_branchLeader_members_id"
	}),
	donations_donorId: many(donations, {
		relationName: "donations_donorId_members_id"
	}),
	donations_recordedBy: many(donations, {
		relationName: "donations_recordedBy_members_id"
	}),
	eventRsvps: many(eventRsvps),
	events: many(events),
	refreshTokens_memberId: many(refreshTokens, {
		relationName: "refreshTokens_memberId_members_id"
	}),
	refreshTokens_revokedBy: many(refreshTokens, {
		relationName: "refreshTokens_revokedBy_members_id"
	}),
	church: one(churches, {
		fields: [members.churchId],
		references: [churches.id]
	}),
	branch: one(branches, {
		fields: [members.branchId],
		references: [branches.id],
		relationName: "members_branchId_branches_id"
	}),
	role: one(roles, {
		fields: [members.roleId],
		references: [roles.id]
	}),
	announcementComments: many(announcementComments),
	announcementReactions: many(announcementReactions),
	announcementViews: many(announcementViews),
	announcements: many(announcements),
}));

export const donationsRelations = relations(donations, ({one}) => ({
	church: one(churches, {
		fields: [donations.churchId],
		references: [churches.id]
	}),
	branch: one(branches, {
		fields: [donations.branchId],
		references: [branches.id]
	}),
	member_donorId: one(members, {
		fields: [donations.donorId],
		references: [members.id],
		relationName: "donations_donorId_members_id"
	}),
	event: one(events, {
		fields: [donations.eventId],
		references: [events.id]
	}),
	member_recordedBy: one(members, {
		fields: [donations.recordedBy],
		references: [members.id],
		relationName: "donations_recordedBy_members_id"
	}),
}));

export const eventsRelations = relations(events, ({one, many}) => ({
	donations: many(donations),
	eventRsvps: many(eventRsvps),
	church: one(churches, {
		fields: [events.churchId],
		references: [churches.id]
	}),
	branch: one(branches, {
		fields: [events.branchId],
		references: [branches.id]
	}),
	member: one(members, {
		fields: [events.createdBy],
		references: [members.id]
	}),
	announcements: many(announcements),
}));

export const eventRsvpsRelations = relations(eventRsvps, ({one}) => ({
	event: one(events, {
		fields: [eventRsvps.eventId],
		references: [events.id]
	}),
	member: one(members, {
		fields: [eventRsvps.memberId],
		references: [members.id]
	}),
}));

export const refreshTokensRelations = relations(refreshTokens, ({one}) => ({
	member_memberId: one(members, {
		fields: [refreshTokens.memberId],
		references: [members.id],
		relationName: "refreshTokens_memberId_members_id"
	}),
	member_revokedBy: one(members, {
		fields: [refreshTokens.revokedBy],
		references: [members.id],
		relationName: "refreshTokens_revokedBy_members_id"
	}),
}));

export const announcementCommentsRelations = relations(announcementComments, ({one}) => ({
	announcement: one(announcements, {
		fields: [announcementComments.announcementId],
		references: [announcements.id]
	}),
	member: one(members, {
		fields: [announcementComments.authorId],
		references: [members.id]
	}),
}));

export const announcementsRelations = relations(announcements, ({one, many}) => ({
	announcementComments: many(announcementComments),
	announcementReactions: many(announcementReactions),
	announcementViews: many(announcementViews),
	church: one(churches, {
		fields: [announcements.churchId],
		references: [churches.id]
	}),
	event: one(events, {
		fields: [announcements.eventId],
		references: [events.id]
	}),
	member: one(members, {
		fields: [announcements.authorId],
		references: [members.id]
	}),
}));

export const announcementReactionsRelations = relations(announcementReactions, ({one}) => ({
	announcement: one(announcements, {
		fields: [announcementReactions.announcementId],
		references: [announcements.id]
	}),
	member: one(members, {
		fields: [announcementReactions.memberId],
		references: [members.id]
	}),
}));

export const announcementViewsRelations = relations(announcementViews, ({one}) => ({
	announcement: one(announcements, {
		fields: [announcementViews.announcementId],
		references: [announcements.id]
	}),
	member: one(members, {
		fields: [announcementViews.memberId],
		references: [members.id]
	}),
}));