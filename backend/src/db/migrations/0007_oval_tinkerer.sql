ALTER TABLE "churches" ADD COLUMN IF NOT EXISTS "default_currency" varchar(3) DEFAULT 'USD' NOT NULL;--> statement-breakpoint
ALTER TABLE "members" ADD COLUMN IF NOT EXISTS "user_id" varchar(10) NOT NULL;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "members" ADD CONSTRAINT "members_user_id_unique" UNIQUE("user_id");
EXCEPTION WHEN duplicate_table THEN null; WHEN duplicate_object THEN null; END $$;