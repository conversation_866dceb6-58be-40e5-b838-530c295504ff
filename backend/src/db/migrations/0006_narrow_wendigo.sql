DO $$ BEGIN
  CREATE TYPE "public"."payment_method_provider" AS ENUM('airtel', 'mtn', 'zamtel', 'visa', 'mastercard');
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  CREATE TYPE "public"."payment_method_type" AS ENUM('mobile_money', 'bank_card', 'bank_transfer', 'cash');
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  CREATE TABLE "payment_methods" (
  	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  	"church_id" uuid NOT NULL,
  	"name" varchar(255) NOT NULL,
  	"type" "payment_method_type" NOT NULL,
  	"provider" "payment_method_provider",
  	"account_number" varchar(100),
  	"account_name" varchar(255),
  	"bank_name" varchar(255),
  	"phone_number" varchar(20),
  	"is_active" boolean DEFAULT true NOT NULL,
  	"is_default" boolean DEFAULT false NOT NULL,
  	"created_at" timestamp DEFAULT now() NOT NULL,
  	"updated_at" timestamp DEFAULT now() NOT NULL
  );
EXCEPTION WHEN duplicate_table THEN null; END $$;
--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "payment_methods" ADD CONSTRAINT "payment_methods_church_id_churches_id_fk" FOREIGN KEY ("church_id") REFERENCES "public"."churches"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;