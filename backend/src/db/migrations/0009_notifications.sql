CREATE TABLE IF NOT EXISTS "notifications" (
  "id" uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  "church_id" uuid NOT NULL REFERENCES "churches"("id") ON DELETE CASCADE,
  "member_id" uuid NOT NULL REFERENCES "members"("id") ON DELETE CASCADE,
  "type" varchar(100) NOT NULL,
  "title" varchar(255) NOT NULL,
  "message" text,
  "link" varchar(500),
  "metadata" text,
  "is_read" boolean DEFAULT false,
  "read_at" timestamp,
  "created_at" timestamp DEFAULT now()
);

CREATE INDEX IF NOT EXISTS idx_notifications_church_member ON "notifications" ("church_id", "member_id");
CREATE INDEX IF NOT EXISTS idx_notifications_unread ON "notifications" ("church_id", "member_id", "is_read");
