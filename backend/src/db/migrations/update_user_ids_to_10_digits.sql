-- Migration: Update existing user IDs to 10-digit format
-- This migration converts existing USR-prefixed user IDs to simple 10-digit numbers

-- Create a function to generate unique 10-digit user IDs
CREATE OR REPLACE FUNCTION generate_10_digit_user_id() RETURNS VARCHAR(10) AS $$
DECLARE
    new_user_id VARCHAR(10);
    random_number BIGINT;
BEGIN
    LOOP
        -- Generate a random 10-digit number (1000000000 to 9999999999)
        random_number := 1000000000 + floor(random() * 9000000000)::BIGINT;
        new_user_id := random_number::TEXT;
        
        -- Check if this ID already exists
        IF NOT EXISTS (SELECT 1 FROM members WHERE user_id = new_user_id) THEN
            RETURN new_user_id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Update all existing user IDs to 10-digit format
UPDATE members SET user_id = generate_10_digit_user_id();

-- Drop the function as it's no longer needed
DROP FUNCTION generate_10_digit_user_id();