-- Migration: Add userId field to members table
-- This migration adds a unique userId field to the members table for login authentication

-- Add the userId column (nullable initially to allow for data population)
ALTER TABLE members ADD COLUMN user_id VARCHAR(10);

-- Create a function to generate unique 10-digit user IDs
CREATE OR REPLACE FUNCTION generate_user_id() RETURNS VARCHAR(10) AS $$
DECLARE
    new_user_id VARCHAR(10);
    random_number BIGINT;
BEGIN
    LOOP
        -- Generate a random 10-digit number (1000000000 to 9999999999)
        random_number := 1000000000 + floor(random() * 9000000000)::BIGINT;
        new_user_id := random_number::TEXT;
        
        -- Check if this ID already exists
        IF NOT EXISTS (SELECT 1 FROM members WHERE user_id = new_user_id) THEN
            RETURN new_user_id;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Populate userId for existing members
UPDATE members SET user_id = generate_user_id() WHERE user_id IS NULL;

-- Make the userId column NOT NULL and UNIQUE
ALTER TABLE members ALTER COLUMN user_id SET NOT NULL;
ALTER TABLE members ADD CONSTRAINT members_user_id_unique UNIQUE (user_id);

-- Drop the function as it's no longer needed
DROP FUNCTION generate_user_id();

-- Create an index on user_id for faster lookups
CREATE INDEX idx_members_user_id ON members(user_id);