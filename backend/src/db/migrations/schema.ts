import { pgTable, unique, uuid, jsonb, text, timestamp, boolean, foreignKey, varchar, numeric, type AnyPgColumn, integer, date, pgEnum } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"

export const announcementStatus = pgEnum("announcement_status", ['draft', 'scheduled', 'published', 'archived', 'expired'])
export const announcementType = pgEnum("announcement_type", ['general', 'urgent', 'event_related', 'service_update', 'prayer_request', 'community_news', 'financial', 'special_event', 'ministry_update', 'volunteer_opportunity', 'other'])
export const donationMethod = pgEnum("donation_method", ['cash', 'check', 'credit_card', 'debit_card', 'bank_transfer', 'mobile_payment', 'online', 'other'])
export const donationStatus = pgEnum("donation_status", ['pending', 'completed', 'failed', 'refunded', 'cancelled'])
export const donationType = pgEnum("donation_type", ['tithe', 'offering', 'event_donation', 'building_fund', 'mission_support', 'special_collection', 'other'])
export const eventStatus = pgEnum("event_status", ['draft', 'published', 'cancelled', 'completed'])
export const eventType = pgEnum("event_type", ['sunday_service', 'bible_study', 'prayer_meeting', 'youth_service', 'choir_practice', 'community_outreach', 'fundraiser', 'conference', 'retreat', 'wedding', 'funeral', 'baptism', 'communion', 'special_service', 'social_event', 'other'])
export const gender = pgEnum("gender", ['male', 'female', 'other'])
export const memberStatus = pgEnum("member_status", ['active', 'inactive', 'suspended'])
export const paymentMethodProvider = pgEnum("payment_method_provider", ['airtel', 'mtn', 'zamtel', 'visa', 'mastercard'])
export const paymentMethodType = pgEnum("payment_method_type", ['mobile_money', 'bank_card', 'bank_transfer', 'cash'])
export const priority = pgEnum("priority", ['low', 'normal', 'high', 'urgent'])
export const rsvpStatus = pgEnum("rsvp_status", ['pending', 'attending', 'not_attending', 'maybe'])
export const targetAudience = pgEnum("target_audience", ['all_members', 'specific_branches', 'specific_roles', 'custom_group'])


export const onboardingSessions = pgTable("onboarding_sessions", {
	id: uuid().primaryKey().notNull(),
	churchData: jsonb("church_data").notNull(),
	adminData: jsonb("admin_data").notNull(),
	settingsData: jsonb("settings_data"),
	hashedPassword: text("hashed_password").notNull(),
	token: text().notNull(),
	verificationToken: text("verification_token"),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	isCompleted: boolean("is_completed").default(false).notNull(),
	isVerified: boolean("is_verified").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	unique("onboarding_sessions_token_unique").on(table.token),
	unique("onboarding_sessions_verification_token_unique").on(table.verificationToken),
]);

export const paymentMethods = pgTable("payment_methods", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	type: paymentMethodType().notNull(),
	provider: paymentMethodProvider(),
	accountNumber: varchar("account_number", { length: 100 }),
	accountName: varchar("account_name", { length: 255 }),
	bankName: varchar("bank_name", { length: 255 }),
	phoneNumber: varchar("phone_number", { length: 20 }),
	isActive: boolean("is_active").default(true).notNull(),
	isDefault: boolean("is_default").default(false).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "payment_methods_church_id_churches_id_fk"
		}).onDelete("cascade"),
]);

export const churches = pgTable("churches", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	name: varchar({ length: 255 }).notNull(),
	slug: varchar({ length: 100 }).notNull(),
	description: text(),
	address: text(),
	phone: varchar({ length: 20 }),
	email: varchar({ length: 255 }),
	website: varchar({ length: 255 }),
	logo: varchar({ length: 500 }),
	settings: jsonb().default({}),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	churchCode: varchar("church_code", { length: 10 }).notNull(),
	defaultCurrency: varchar("default_currency", { length: 3 }).default('USD').notNull(),
}, (table) => [
	unique("churches_slug_unique").on(table.slug),
	unique("churches_church_code_unique").on(table.churchCode),
]);

export const donationCategories = pgTable("donation_categories", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	description: text(),
	targetAmount: numeric("target_amount", { precision: 10, scale:  2 }),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "donation_categories_church_id_churches_id_fk"
		}).onDelete("cascade"),
]);

export const roles = pgTable("roles", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	name: varchar({ length: 100 }).notNull(),
	description: text(),
	permissions: jsonb().default([]),
	isSystem: boolean("is_system").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "roles_church_id_churches_id_fk"
		}).onDelete("cascade"),
]);

export const branches = pgTable("branches", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	name: varchar({ length: 255 }).notNull(),
	slug: varchar({ length: 100 }).notNull(),
	description: text(),
	address: text(),
	phone: varchar({ length: 20 }),
	email: varchar({ length: 255 }),
	coordinates: jsonb(),
	capacity: varchar({ length: 100 }),
	facilities: jsonb(),
	branchLeader: uuid("branch_leader"),
	settings: jsonb().default({}),
	isActive: boolean("is_active").default(true),
	isMainBranch: boolean("is_main_branch").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "branches_church_id_churches_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.branchLeader],
			foreignColumns: [members.id],
			name: "branches_branch_leader_members_id_fk"
		}).onDelete("set null"),
]);

export const donations = pgTable("donations", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	branchId: uuid("branch_id"),
	donorId: uuid("donor_id"),
	eventId: uuid("event_id"),
	amount: numeric({ precision: 10, scale:  2 }).notNull(),
	currency: varchar({ length: 3 }).default('USD').notNull(),
	type: donationType().default('offering').notNull(),
	method: donationMethod().default('cash').notNull(),
	status: donationStatus().default('completed').notNull(),
	donorName: varchar("donor_name", { length: 255 }),
	donorEmail: varchar("donor_email", { length: 255 }),
	donorPhone: varchar("donor_phone", { length: 20 }),
	description: text(),
	notes: text(),
	receiptNumber: varchar("receipt_number", { length: 100 }),
	taxDeductible: boolean("tax_deductible").default(true),
	isAnonymous: boolean("is_anonymous").default(false),
	transactionId: varchar("transaction_id", { length: 255 }),
	metadata: text(),
	donatedAt: timestamp("donated_at", { mode: 'string' }).defaultNow(),
	recordedBy: uuid("recorded_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "donations_church_id_churches_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.branchId],
			foreignColumns: [branches.id],
			name: "donations_branch_id_branches_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.donorId],
			foreignColumns: [members.id],
			name: "donations_donor_id_members_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "donations_event_id_events_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.recordedBy],
			foreignColumns: [members.id],
			name: "donations_recorded_by_members_id_fk"
		}).onDelete("set null"),
]);

export const eventRsvps = pgTable("event_rsvps", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	eventId: uuid("event_id").notNull(),
	memberId: uuid("member_id").notNull(),
	status: rsvpStatus().default('pending').notNull(),
	attendeeCount: integer("attendee_count").default(1),
	notes: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "event_rsvps_event_id_events_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.memberId],
			foreignColumns: [members.id],
			name: "event_rsvps_member_id_members_id_fk"
		}).onDelete("cascade"),
]);

export const events = pgTable("events", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	branchId: uuid("branch_id"),
	createdBy: uuid("created_by"),
	title: varchar({ length: 255 }).notNull(),
	description: text(),
	type: eventType().default('other').notNull(),
	status: eventStatus().default('draft').notNull(),
	startDate: timestamp("start_date", { mode: 'string' }).notNull(),
	endDate: timestamp("end_date", { mode: 'string' }),
	location: varchar({ length: 500 }),
	virtualLink: varchar("virtual_link", { length: 500 }),
	maxAttendees: integer("max_attendees"),
	requiresRsvp: boolean("requires_rsvp").default(false),
	allowDonations: boolean("allow_donations").default(false),
	donationGoal: numeric("donation_goal", { precision: 10, scale:  2 }),
	donationDescription: text("donation_description"),
	imageUrl: text("image_url"),
	recurrencePattern: text("recurrence_pattern"),
	tags: text(),
	isPublic: boolean("is_public").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "events_church_id_churches_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.branchId],
			foreignColumns: [branches.id],
			name: "events_branch_id_branches_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [members.id],
			name: "events_created_by_members_id_fk"
		}).onDelete("set null"),
]);

export const refreshTokens = pgTable("refresh_tokens", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	memberId: uuid("member_id").notNull(),
	token: varchar({ length: 255 }).notNull(),
	expiresAt: timestamp("expires_at", { mode: 'string' }).notNull(),
	isRevoked: boolean("is_revoked").default(false),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	revokedAt: timestamp("revoked_at", { mode: 'string' }),
	revokedBy: uuid("revoked_by"),
	revokedReason: varchar("revoked_reason", { length: 100 }),
	ipAddress: varchar("ip_address", { length: 45 }),
	userAgent: text("user_agent"),
	lastUsedAt: timestamp("last_used_at", { mode: 'string' }),
}, (table) => [
	foreignKey({
			columns: [table.memberId],
			foreignColumns: [members.id],
			name: "refresh_tokens_member_id_members_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.revokedBy],
			foreignColumns: [members.id],
			name: "refresh_tokens_revoked_by_members_id_fk"
		}),
	unique("refresh_tokens_token_unique").on(table.token),
]);

export const members = pgTable("members", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	branchId: uuid("branch_id"),
	roleId: uuid("role_id"),
	firstName: varchar("first_name", { length: 100 }).notNull(),
	lastName: varchar("last_name", { length: 100 }).notNull(),
	email: varchar({ length: 255 }).notNull(),
	phone: varchar({ length: 20 }),
	password: varchar({ length: 255 }).notNull(),
	dateOfBirth: date("date_of_birth"),
	gender: gender(),
	address: text(),
	profileImage: varchar("profile_image", { length: 500 }),
	joinDate: date("join_date").defaultNow(),
	status: memberStatus().default('active'),
	additionalInfo: jsonb("additional_info").default({}),
	isEmailVerified: boolean("is_email_verified").default(false),
	emailVerificationToken: varchar("email_verification_token", { length: 255 }),
	passwordResetToken: varchar("password_reset_token", { length: 255 }),
	passwordResetExpires: timestamp("password_reset_expires", { mode: 'string' }),
	lastLoginAt: timestamp("last_login_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	userId: varchar("user_id", { length: 10 }).notNull(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "members_church_id_churches_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.branchId],
			foreignColumns: [branches.id],
			name: "members_branch_id_branches_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.roleId],
			foreignColumns: [roles.id],
			name: "members_role_id_roles_id_fk"
		}).onDelete("set null"),
	unique("members_email_unique").on(table.email),
	unique("members_user_id_unique").on(table.userId),
]);

export const announcementComments = pgTable("announcement_comments", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	announcementId: uuid("announcement_id").notNull(),
	authorId: uuid("author_id").notNull(),
	parentCommentId: uuid("parent_comment_id"),
	content: text().notNull(),
	isEdited: boolean("is_edited").default(false),
	editedAt: timestamp("edited_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.announcementId],
			foreignColumns: [announcements.id],
			name: "announcement_comments_announcement_id_announcements_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.authorId],
			foreignColumns: [members.id],
			name: "announcement_comments_author_id_members_id_fk"
		}).onDelete("cascade"),
]);

export const announcementReactions = pgTable("announcement_reactions", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	announcementId: uuid("announcement_id").notNull(),
	memberId: uuid("member_id").notNull(),
	reactionType: varchar("reaction_type", { length: 50 }).notNull(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.announcementId],
			foreignColumns: [announcements.id],
			name: "announcement_reactions_announcement_id_announcements_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.memberId],
			foreignColumns: [members.id],
			name: "announcement_reactions_member_id_members_id_fk"
		}).onDelete("cascade"),
]);

export const announcementViews = pgTable("announcement_views", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	announcementId: uuid("announcement_id").notNull(),
	memberId: uuid("member_id").notNull(),
	viewedAt: timestamp("viewed_at", { mode: 'string' }).defaultNow(),
	acknowledgedAt: timestamp("acknowledged_at", { mode: 'string' }),
	deviceInfo: text("device_info"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.announcementId],
			foreignColumns: [announcements.id],
			name: "announcement_views_announcement_id_announcements_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.memberId],
			foreignColumns: [members.id],
			name: "announcement_views_member_id_members_id_fk"
		}).onDelete("cascade"),
]);

export const announcements = pgTable("announcements", {
	id: uuid().defaultRandom().primaryKey().notNull(),
	churchId: uuid("church_id").notNull(),
	eventId: uuid("event_id"),
	authorId: uuid("author_id").notNull(),
	title: varchar({ length: 255 }).notNull(),
	content: text().notNull(),
	summary: varchar({ length: 500 }),
	type: announcementType().default('general').notNull(),
	priority: priority().default('normal').notNull(),
	status: announcementStatus().default('draft').notNull(),
	targetAudience: targetAudience("target_audience").default('all_members').notNull(),
	targetBranches: text("target_branches"),
	targetRoles: text("target_roles"),
	imageUrl: varchar("image_url", { length: 500 }),
	attachments: text(),
	externalLinks: text("external_links"),
	tags: text(),
	publishedAt: timestamp("published_at", { mode: 'string' }),
	scheduledFor: timestamp("scheduled_for", { mode: 'string' }),
	expiresAt: timestamp("expires_at", { mode: 'string' }),
	isPinned: boolean("is_pinned").default(false),
	allowComments: boolean("allow_comments").default(true),
	requiresAcknowledgment: boolean("requires_acknowledgment").default(false),
	sendNotification: boolean("send_notification").default(true),
	notificationChannels: text("notification_channels"),
	viewCount: integer("view_count").default(0),
	metadata: text(),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.churchId],
			foreignColumns: [churches.id],
			name: "announcements_church_id_churches_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.eventId],
			foreignColumns: [events.id],
			name: "announcements_event_id_events_id_fk"
		}).onDelete("set null"),
	foreignKey({
			columns: [table.authorId],
			foreignColumns: [members.id],
			name: "announcements_author_id_members_id_fk"
		}).onDelete("set null"),
]);
