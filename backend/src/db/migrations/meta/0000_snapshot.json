{"id": "1e8bac55-79a8-4761-b493-21fea8c1fd18", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.branches": {"name": "branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "coordinates": {"name": "coordinates", "type": "jsonb", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "facilities": {"name": "facilities", "type": "jsonb", "primaryKey": false, "notNull": false}, "branch_leader": {"name": "branch_leader", "type": "uuid", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_main_branch": {"name": "is_main_branch", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"branches_church_id_churches_id_fk": {"name": "branches_church_id_churches_id_fk", "tableFrom": "branches", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "branches_branch_leader_members_id_fk": {"name": "branches_branch_leader_members_id_fk", "tableFrom": "branches", "tableTo": "members", "columnsFrom": ["branch_leader"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.churches": {"name": "churches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"churches_slug_unique": {"name": "churches_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donation_categories": {"name": "donation_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "target_amount": {"name": "target_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donation_categories_church_id_churches_id_fk": {"name": "donation_categories_church_id_churches_id_fk", "tableFrom": "donation_categories", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donations": {"name": "donations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "donor_id": {"name": "donor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "type": {"name": "type", "type": "donation_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'offering'"}, "method": {"name": "method", "type": "donation_method", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'cash'"}, "status": {"name": "status", "type": "donation_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'completed'"}, "donor_name": {"name": "donor_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "donor_email": {"name": "donor_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "donor_phone": {"name": "donor_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tax_deductible": {"name": "tax_deductible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "donated_at": {"name": "donated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "recorded_by": {"name": "recorded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donations_church_id_churches_id_fk": {"name": "donations_church_id_churches_id_fk", "tableFrom": "donations", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "donations_branch_id_branches_id_fk": {"name": "donations_branch_id_branches_id_fk", "tableFrom": "donations", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_donor_id_members_id_fk": {"name": "donations_donor_id_members_id_fk", "tableFrom": "donations", "tableTo": "members", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_event_id_events_id_fk": {"name": "donations_event_id_events_id_fk", "tableFrom": "donations", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_recorded_by_members_id_fk": {"name": "donations_recorded_by_members_id_fk", "tableFrom": "donations", "tableTo": "members", "columnsFrom": ["recorded_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_rsvps": {"name": "event_rsvps", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "rsvp_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "attendee_count": {"name": "attendee_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_rsvps_event_id_events_id_fk": {"name": "event_rsvps_event_id_events_id_fk", "tableFrom": "event_rsvps", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "event_rsvps_member_id_members_id_fk": {"name": "event_rsvps_member_id_members_id_fk", "tableFrom": "event_rsvps", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "event_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'other'"}, "status": {"name": "status", "type": "event_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "virtual_link": {"name": "virtual_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "max_attendees": {"name": "max_attendees", "type": "integer", "primaryKey": false, "notNull": false}, "requires_rsvp": {"name": "requires_rsvp", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "allow_donations": {"name": "allow_donations", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "donation_goal": {"name": "donation_goal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "donation_description": {"name": "donation_description", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "recurrence_pattern": {"name": "recurrence_pattern", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"events_church_id_churches_id_fk": {"name": "events_church_id_churches_id_fk", "tableFrom": "events", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_branch_id_branches_id_fk": {"name": "events_branch_id_branches_id_fk", "tableFrom": "events", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_created_by_members_id_fk": {"name": "events_created_by_members_id_fk", "tableFrom": "events", "tableTo": "members", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"roles_church_id_churches_id_fk": {"name": "roles_church_id_churches_id_fk", "tableFrom": "roles", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.members": {"name": "members", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender", "typeSchema": "public", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "profile_image": {"name": "profile_image", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "join_date": {"name": "join_date", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "member_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'active'"}, "additional_info": {"name": "additional_info", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"members_church_id_churches_id_fk": {"name": "members_church_id_churches_id_fk", "tableFrom": "members", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "members_branch_id_branches_id_fk": {"name": "members_branch_id_branches_id_fk", "tableFrom": "members", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "members_role_id_roles_id_fk": {"name": "members_role_id_roles_id_fk", "tableFrom": "members", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"members_email_unique": {"name": "members_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.refresh_tokens": {"name": "refresh_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"refresh_tokens_member_id_members_id_fk": {"name": "refresh_tokens_member_id_members_id_fk", "tableFrom": "refresh_tokens", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"refresh_tokens_token_unique": {"name": "refresh_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.donation_method": {"name": "donation_method", "schema": "public", "values": ["cash", "check", "credit_card", "debit_card", "bank_transfer", "mobile_payment", "online", "other"]}, "public.donation_status": {"name": "donation_status", "schema": "public", "values": ["pending", "completed", "failed", "refunded", "cancelled"]}, "public.donation_type": {"name": "donation_type", "schema": "public", "values": ["tithe", "offering", "event_donation", "building_fund", "mission_support", "special_collection", "other"]}, "public.event_status": {"name": "event_status", "schema": "public", "values": ["draft", "published", "cancelled", "completed"]}, "public.event_type": {"name": "event_type", "schema": "public", "values": ["sunday_service", "bible_study", "prayer_meeting", "youth_service", "choir_practice", "community_outreach", "fundraiser", "conference", "retreat", "wedding", "funeral", "baptism", "communion", "special_service", "social_event", "other"]}, "public.rsvp_status": {"name": "rsvp_status", "schema": "public", "values": ["pending", "attending", "not_attending", "maybe"]}, "public.gender": {"name": "gender", "schema": "public", "values": ["male", "female", "other"]}, "public.member_status": {"name": "member_status", "schema": "public", "values": ["active", "inactive", "suspended"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}