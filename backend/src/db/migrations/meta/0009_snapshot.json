{"id": "dd3f625c-925f-4574-833d-103923176249", "prevId": "ee092ff3-f97c-4da6-8d65-3e77d579fff6", "version": "7", "dialect": "postgresql", "tables": {"public.announcement_comments": {"name": "announcement_comments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "announcement_id": {"name": "announcement_id", "type": "uuid", "primaryKey": false, "notNull": true}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_comment_id": {"name": "parent_comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "is_edited": {"name": "is_edited", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "edited_at": {"name": "edited_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"announcement_comments_announcement_id_announcements_id_fk": {"name": "announcement_comments_announcement_id_announcements_id_fk", "tableFrom": "announcement_comments", "tableTo": "announcements", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "announcement_comments_author_id_members_id_fk": {"name": "announcement_comments_author_id_members_id_fk", "tableFrom": "announcement_comments", "tableTo": "members", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.announcement_reactions": {"name": "announcement_reactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "announcement_id": {"name": "announcement_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "reaction_type": {"name": "reaction_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"announcement_reactions_announcement_id_announcements_id_fk": {"name": "announcement_reactions_announcement_id_announcements_id_fk", "tableFrom": "announcement_reactions", "tableTo": "announcements", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "announcement_reactions_member_id_members_id_fk": {"name": "announcement_reactions_member_id_members_id_fk", "tableFrom": "announcement_reactions", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.announcement_views": {"name": "announcement_views", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "announcement_id": {"name": "announcement_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "viewed_at": {"name": "viewed_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "acknowledged_at": {"name": "acknowledged_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "device_info": {"name": "device_info", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"announcement_views_announcement_id_announcements_id_fk": {"name": "announcement_views_announcement_id_announcements_id_fk", "tableFrom": "announcement_views", "tableTo": "announcements", "columnsFrom": ["announcement_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "announcement_views_member_id_members_id_fk": {"name": "announcement_views_member_id_members_id_fk", "tableFrom": "announcement_views", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.announcements": {"name": "announcements", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "uuid", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "summary": {"name": "summary", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "announcement_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'general'"}, "priority": {"name": "priority", "type": "priority", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'normal'"}, "status": {"name": "status", "type": "announcement_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "target_audience": {"name": "target_audience", "type": "target_audience", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'all_members'"}, "target_branches": {"name": "target_branches", "type": "text", "primaryKey": false, "notNull": false}, "target_roles": {"name": "target_roles", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "external_links": {"name": "external_links", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scheduled_for": {"name": "scheduled_for", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_pinned": {"name": "is_pinned", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "allow_comments": {"name": "allow_comments", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "requires_acknowledgment": {"name": "requires_acknowledgment", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "send_notification": {"name": "send_notification", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "notification_channels": {"name": "notification_channels", "type": "text", "primaryKey": false, "notNull": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"announcements_church_id_churches_id_fk": {"name": "announcements_church_id_churches_id_fk", "tableFrom": "announcements", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "announcements_event_id_events_id_fk": {"name": "announcements_event_id_events_id_fk", "tableFrom": "announcements", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "announcements_author_id_members_id_fk": {"name": "announcements_author_id_members_id_fk", "tableFrom": "announcements", "tableTo": "members", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.branches": {"name": "branches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "coordinates": {"name": "coordinates", "type": "jsonb", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "facilities": {"name": "facilities", "type": "jsonb", "primaryKey": false, "notNull": false}, "branch_leader": {"name": "branch_leader", "type": "uuid", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_main_branch": {"name": "is_main_branch", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"branches_church_id_churches_id_fk": {"name": "branches_church_id_churches_id_fk", "tableFrom": "branches", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "branches_branch_leader_members_id_fk": {"name": "branches_branch_leader_members_id_fk", "tableFrom": "branches", "tableTo": "members", "columnsFrom": ["branch_leader"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.churches": {"name": "churches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "church_code": {"name": "church_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "default_currency": {"name": "default_currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"churches_slug_unique": {"name": "churches_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}, "churches_church_code_unique": {"name": "churches_church_code_unique", "nullsNotDistinct": false, "columns": ["church_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donation_categories": {"name": "donation_categories", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "target_amount": {"name": "target_amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donation_categories_church_id_churches_id_fk": {"name": "donation_categories_church_id_churches_id_fk", "tableFrom": "donation_categories", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.donations": {"name": "donations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "donor_id": {"name": "donor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "type": {"name": "type", "type": "donation_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'offering'"}, "method": {"name": "method", "type": "donation_method", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'cash'"}, "status": {"name": "status", "type": "donation_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'completed'"}, "donor_name": {"name": "donor_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "donor_email": {"name": "donor_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "donor_phone": {"name": "donor_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "tax_deductible": {"name": "tax_deductible", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "is_anonymous": {"name": "is_anonymous", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "donated_at": {"name": "donated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "recorded_by": {"name": "recorded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"donations_church_id_churches_id_fk": {"name": "donations_church_id_churches_id_fk", "tableFrom": "donations", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "donations_branch_id_branches_id_fk": {"name": "donations_branch_id_branches_id_fk", "tableFrom": "donations", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_donor_id_members_id_fk": {"name": "donations_donor_id_members_id_fk", "tableFrom": "donations", "tableTo": "members", "columnsFrom": ["donor_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_event_id_events_id_fk": {"name": "donations_event_id_events_id_fk", "tableFrom": "donations", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "donations_recorded_by_members_id_fk": {"name": "donations_recorded_by_members_id_fk", "tableFrom": "donations", "tableTo": "members", "columnsFrom": ["recorded_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_comments": {"name": "event_comments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "parent_comment_id": {"name": "parent_comment_id", "type": "uuid", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_comments_event_id_events_id_fk": {"name": "event_comments_event_id_events_id_fk", "tableFrom": "event_comments", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "event_comments_member_id_members_id_fk": {"name": "event_comments_member_id_members_id_fk", "tableFrom": "event_comments", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "event_comments_parent_comment_id_event_comments_id_fk": {"name": "event_comments_parent_comment_id_event_comments_id_fk", "tableFrom": "event_comments", "tableTo": "event_comments", "columnsFrom": ["parent_comment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_likes": {"name": "event_likes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_likes_event_id_events_id_fk": {"name": "event_likes_event_id_events_id_fk", "tableFrom": "event_likes", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "event_likes_member_id_members_id_fk": {"name": "event_likes_member_id_members_id_fk", "tableFrom": "event_likes", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.event_rsvps": {"name": "event_rsvps", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "event_id": {"name": "event_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "rsvp_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'pending'"}, "attendee_count": {"name": "attendee_count", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"event_rsvps_event_id_events_id_fk": {"name": "event_rsvps_event_id_events_id_fk", "tableFrom": "event_rsvps", "tableTo": "events", "columnsFrom": ["event_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "event_rsvps_member_id_members_id_fk": {"name": "event_rsvps_member_id_members_id_fk", "tableFrom": "event_rsvps", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.events": {"name": "events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "event_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'other'"}, "status": {"name": "status", "type": "event_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'draft'"}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "virtual_link": {"name": "virtual_link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "max_attendees": {"name": "max_attendees", "type": "integer", "primaryKey": false, "notNull": false}, "requires_rsvp": {"name": "requires_rsvp", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "allow_donations": {"name": "allow_donations", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "donation_goal": {"name": "donation_goal", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "donation_description": {"name": "donation_description", "type": "text", "primaryKey": false, "notNull": false}, "image_url": {"name": "image_url", "type": "text", "primaryKey": false, "notNull": false}, "recurrence_pattern": {"name": "recurrence_pattern", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "text", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"events_church_id_churches_id_fk": {"name": "events_church_id_churches_id_fk", "tableFrom": "events", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "events_branch_id_branches_id_fk": {"name": "events_branch_id_branches_id_fk", "tableFrom": "events", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "events_created_by_members_id_fk": {"name": "events_created_by_members_id_fk", "tableFrom": "events", "tableTo": "members", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"roles_church_id_churches_id_fk": {"name": "roles_church_id_churches_id_fk", "tableFrom": "roles", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.members": {"name": "members", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "branch_id": {"name": "branch_id", "type": "uuid", "primaryKey": false, "notNull": false}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "gender", "typeSchema": "public", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "profile_image": {"name": "profile_image", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "join_date": {"name": "join_date", "type": "date", "primaryKey": false, "notNull": false, "default": "now()"}, "status": {"name": "status", "type": "member_status", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'active'"}, "additional_info": {"name": "additional_info", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "email_verification_token": {"name": "email_verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"members_church_id_churches_id_fk": {"name": "members_church_id_churches_id_fk", "tableFrom": "members", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "members_branch_id_branches_id_fk": {"name": "members_branch_id_branches_id_fk", "tableFrom": "members", "tableTo": "branches", "columnsFrom": ["branch_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "members_role_id_roles_id_fk": {"name": "members_role_id_roles_id_fk", "tableFrom": "members", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"members_user_id_unique": {"name": "members_user_id_unique", "nullsNotDistinct": false, "columns": ["user_id"]}, "members_email_unique": {"name": "members_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.refresh_tokens": {"name": "refresh_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_revoked": {"name": "is_revoked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_by": {"name": "revoked_by", "type": "uuid", "primaryKey": false, "notNull": false}, "revoked_reason": {"name": "revoked_reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"refresh_tokens_member_id_members_id_fk": {"name": "refresh_tokens_member_id_members_id_fk", "tableFrom": "refresh_tokens", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "refresh_tokens_revoked_by_members_id_fk": {"name": "refresh_tokens_revoked_by_members_id_fk", "tableFrom": "refresh_tokens", "tableTo": "members", "columnsFrom": ["revoked_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"refresh_tokens_token_unique": {"name": "refresh_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_methods": {"name": "payment_methods", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "payment_method_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "payment_method_provider", "typeSchema": "public", "primaryKey": false, "notNull": false}, "account_number": {"name": "account_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "account_name": {"name": "account_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "bank_name": {"name": "bank_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_methods_church_id_churches_id_fk": {"name": "payment_methods_church_id_churches_id_fk", "tableFrom": "payment_methods", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.onboarding_sessions": {"name": "onboarding_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true}, "church_data": {"name": "church_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "admin_data": {"name": "admin_data", "type": "jsonb", "primaryKey": false, "notNull": true}, "settings_data": {"name": "settings_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "hashed_password": {"name": "hashed_password", "type": "text", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "verification_token": {"name": "verification_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_completed": {"name": "is_completed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"onboarding_sessions_token_unique": {"name": "onboarding_sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}, "onboarding_sessions_verification_token_unique": {"name": "onboarding_sessions_verification_token_unique", "nullsNotDistinct": false, "columns": ["verification_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "church_id": {"name": "church_id", "type": "uuid", "primaryKey": false, "notNull": true}, "member_id": {"name": "member_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": false}, "link": {"name": "link", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "read_at": {"name": "read_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_church_id_churches_id_fk": {"name": "notifications_church_id_churches_id_fk", "tableFrom": "notifications", "tableTo": "churches", "columnsFrom": ["church_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notifications_member_id_members_id_fk": {"name": "notifications_member_id_members_id_fk", "tableFrom": "notifications", "tableTo": "members", "columnsFrom": ["member_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.announcement_status": {"name": "announcement_status", "schema": "public", "values": ["draft", "scheduled", "published", "archived", "expired"]}, "public.announcement_type": {"name": "announcement_type", "schema": "public", "values": ["general", "urgent", "event_related", "service_update", "prayer_request", "community_news", "financial", "special_event", "ministry_update", "volunteer_opportunity", "other"]}, "public.priority": {"name": "priority", "schema": "public", "values": ["low", "normal", "high", "urgent"]}, "public.target_audience": {"name": "target_audience", "schema": "public", "values": ["all_members", "specific_branches", "specific_roles", "custom_group"]}, "public.donation_method": {"name": "donation_method", "schema": "public", "values": ["cash", "check", "credit_card", "debit_card", "bank_transfer", "mobile_payment", "online", "other"]}, "public.donation_status": {"name": "donation_status", "schema": "public", "values": ["pending", "completed", "failed", "refunded", "cancelled"]}, "public.donation_type": {"name": "donation_type", "schema": "public", "values": ["tithe", "offering", "event_donation", "building_fund", "mission_support", "special_collection", "other"]}, "public.event_status": {"name": "event_status", "schema": "public", "values": ["draft", "published", "cancelled", "completed"]}, "public.event_type": {"name": "event_type", "schema": "public", "values": ["sunday_service", "bible_study", "prayer_meeting", "youth_service", "choir_practice", "community_outreach", "fundraiser", "conference", "retreat", "wedding", "funeral", "baptism", "communion", "special_service", "social_event", "other"]}, "public.rsvp_status": {"name": "rsvp_status", "schema": "public", "values": ["pending", "attending", "not_attending", "maybe"]}, "public.gender": {"name": "gender", "schema": "public", "values": ["male", "female", "other"]}, "public.member_status": {"name": "member_status", "schema": "public", "values": ["active", "inactive", "suspended"]}, "public.payment_method_provider": {"name": "payment_method_provider", "schema": "public", "values": ["airtel", "mtn", "zamtel", "visa", "mastercard"]}, "public.payment_method_type": {"name": "payment_method_type", "schema": "public", "values": ["mobile_money", "bank_card", "bank_transfer", "cash"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}