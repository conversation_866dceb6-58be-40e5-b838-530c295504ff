CREATE TABLE "onboarding_sessions" (
	"id" uuid PRIMARY KEY NOT NULL,
	"church_data" jsonb NOT NULL,
	"admin_data" jsonb NOT NULL,
	"settings_data" jsonb,
	"hashed_password" text NOT NULL,
	"token" text NOT NULL,
	"verification_token" text,
	"expires_at" timestamp NOT NULL,
	"is_completed" boolean DEFAULT false NOT NULL,
	"is_verified" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "onboarding_sessions_token_unique" UNIQUE("token"),
	CONSTRAINT "onboarding_sessions_verification_token_unique" UNIQUE("verification_token")
);
