DO $$ BEGIN
  CREATE TABLE "event_comments" (
  	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  	"event_id" uuid NOT NULL,
  	"member_id" uuid NOT NULL,
  	"parent_comment_id" uuid,
  	"content" text NOT NULL,
  	"created_at" timestamp DEFAULT now(),
  	"updated_at" timestamp DEFAULT now()
  );
EXCEPTION WHEN duplicate_table THEN null; END $$;
--> statement-breakpoint
DO $$ BEGIN
  CREATE TABLE "event_likes" (
  	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  	"event_id" uuid NOT NULL,
  	"member_id" uuid NOT NULL,
  	"created_at" timestamp DEFAULT now()
  );
EXCEPTION WHEN duplicate_table THEN null; END $$;
--> statement-breakpoint
DO $$ BEGIN
  CREATE TABLE "notifications" (
  	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
  	"church_id" uuid NOT NULL,
  	"member_id" uuid NOT NULL,
  	"type" varchar(100) NOT NULL,
  	"title" varchar(255) NOT NULL,
  	"message" text,
  	"link" varchar(500),
  	"metadata" text,
  	"is_read" boolean DEFAULT false,
  	"read_at" timestamp,
  	"created_at" timestamp DEFAULT now()
  );
EXCEPTION WHEN duplicate_table THEN null; END $$;
--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "event_comments" ADD CONSTRAINT "event_comments_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "event_comments" ADD CONSTRAINT "event_comments_member_id_members_id_fk" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "event_comments" ADD CONSTRAINT "event_comments_parent_comment_id_event_comments_id_fk" FOREIGN KEY ("parent_comment_id") REFERENCES "public"."event_comments"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "event_likes" ADD CONSTRAINT "event_likes_event_id_events_id_fk" FOREIGN KEY ("event_id") REFERENCES "public"."events"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "event_likes" ADD CONSTRAINT "event_likes_member_id_members_id_fk" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "notifications" ADD CONSTRAINT "notifications_church_id_churches_id_fk" FOREIGN KEY ("church_id") REFERENCES "public"."churches"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;--> statement-breakpoint
DO $$ BEGIN
  ALTER TABLE "notifications" ADD CONSTRAINT "notifications_member_id_members_id_fk" FOREIGN KEY ("member_id") REFERENCES "public"."members"("id") ON DELETE cascade ON UPDATE no action;
EXCEPTION WHEN duplicate_object THEN null; END $$;