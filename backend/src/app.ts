import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import cookieParser from 'cookie-parser';
import dotenv from 'dotenv';
import swaggerUi from 'swagger-ui-express';
import routes from './routes';
import { globalErrorHandler } from './utils/errorHandler';
import { specs, swaggerUiOptions } from './config/swagger';
import { TokenCleanupService } from './utils/tokenCleanupService';
import { verifyMailConnection } from './config/mail';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3000;

// HTTPS enforcement middleware for production
if (process.env.NODE_ENV === 'production') {
  app.use((req, res, next) => {
    if (req.header('x-forwarded-proto') !== 'https') {
      return res.redirect(`https://${req.header('host')}${req.url}`);
    }
    next();
  });
}

app.use(helmet({
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  }
}));

app.use(cors({
  credentials: true, // Allow cookies to be sent
  origin: process.env.FRONTEND_URL || 'http://localhost:3002'
}));

app.use(morgan('combined'));
app.use(express.json());
app.use(express.urlencoded({ extended: true }));
app.use(cookieParser());

app.get('/health', async (req, res) => {
  const mailStatus = process.env.NODE_ENV === 'test' ? true : await verifyMailConnection().catch(() => false);
  
  res.json({ 
    status: 'OK', 
    timestamp: new Date().toISOString(),
    services: {
      mail: mailStatus ? 'connected' : 'disconnected'
    }
  });
});

// Swagger Documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, swaggerUiOptions));

app.use('/api', routes);

app.use(globalErrorHandler);

if (process.env.NODE_ENV !== 'test') {
  app.listen(PORT, async () => {
    console.log(`🚀 Server running on port ${PORT}`);
    console.log(`📱 Health check: http://localhost:${PORT}/health`);
    console.log(`📚 API Documentation: http://localhost:${PORT}/api-docs`);
    
    // Verify mail connection
    await verifyMailConnection();
    
    // Start token cleanup service
    const tokenCleanupService = TokenCleanupService.getInstance();
    tokenCleanupService.start();
  });
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully...');
  const tokenCleanupService = TokenCleanupService.getInstance();
  tokenCleanupService.stop();
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully...');
  const tokenCleanupService = TokenCleanupService.getInstance();
  tokenCleanupService.stop();
  process.exit(0);
});

export { app };
export default app;