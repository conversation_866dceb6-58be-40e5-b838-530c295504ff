import swaggerJsdoc from 'swagger-jsdoc';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'Church Management API',
      version: '1.0.0',
      description: 'A comprehensive API for managing churches, members, and roles with authentication',
    },
    servers: [
      {
        url: process.env.NODE_ENV === 'production' 
          ? 'https://your-production-url.com/api'
          : 'http://localhost:3000/api',
        description: process.env.NODE_ENV === 'production' ? 'Production server' : 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        Church: {
          type: 'object',
          required: ['name', 'slug'],
          properties: {
            id: {
              type: 'string',
              description: 'Unique identifier for the church',
            },
            name: {
              type: 'string',
              description: 'Name of the church',
              example: 'Grace Community Church',
            },
            slug: {
              type: 'string',
              pattern: '^[a-z0-9-]+$',
              description: 'URL-friendly identifier for the church',
              example: 'grace-community-church',
            },
            description: {
              type: 'string',
              description: 'Brief description of the church',
              example: 'A welcoming community church in downtown',
            },
            address: {
              type: 'string',
              description: 'Physical address of the church',
              example: '123 Main Street, City, State 12345',
            },
            phone: {
              type: 'string',
              description: 'Contact phone number',
              example: '(*************',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Contact email address',
              example: '<EMAIL>',
            },
            website: {
              type: 'string',
              format: 'uri',
              description: 'Church website URL',
              example: 'https://gracechurch.com',
            },
            isActive: {
              type: 'boolean',
              description: 'Whether the church is active',
              default: true,
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
              description: 'Timestamp when the church was created',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
              description: 'Timestamp when the church was last updated',
            },
          },
        },
        CreateChurchRequest: {
          type: 'object',
          required: ['name', 'slug'],
          properties: {
            name: {
              type: 'string',
              description: 'Name of the church',
              example: 'Grace Community Church',
            },
            slug: {
              type: 'string',
              pattern: '^[a-z0-9-]+$',
              description: 'URL-friendly identifier for the church',
              example: 'grace-community-church',
            },
            description: {
              type: 'string',
              description: 'Brief description of the church',
              example: 'A welcoming community church in downtown',
            },
            address: {
              type: 'string',
              description: 'Physical address of the church',
              example: '123 Main Street, City, State 12345',
            },
            phone: {
              type: 'string',
              description: 'Contact phone number',
              example: '(*************',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Contact email address',
              example: '<EMAIL>',
            },
            website: {
              type: 'string',
              format: 'uri',
              description: 'Church website URL',
              example: 'https://gracechurch.com',
            },
          },
        },
        ChurchResponse: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'success',
            },
            data: {
              type: 'object',
              properties: {
                church: {
                  $ref: '#/components/schemas/Church',
                },
                churchUrl: {
                  type: 'string',
                  description: 'Direct URL to access this church',
                  example: 'http://localhost:3000/api/churches/grace-community-church',
                },
                membersUrl: {
                  type: 'string',
                  description: 'URL to access church members',
                  example: 'http://localhost:3000/api/churches/grace-community-church/members',
                },
              },
            },
          },
        },
        ChurchListResponse: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'success',
            },
            data: {
              type: 'object',
              properties: {
                churches: {
                  type: 'array',
                  items: {
                    $ref: '#/components/schemas/Church',
                  },
                },
              },
            },
          },
        },
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'string',
              description: 'User ID',
            },
            firstName: {
              type: 'string',
              example: 'John',
            },
            lastName: {
              type: 'string',
              example: 'Doe',
            },
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>',
            },
            churchId: {
              type: 'string',
              description: 'Church ID the user belongs to',
            },
            isEmailVerified: {
              type: 'boolean',
              description: 'Whether the user has verified their email',
            },
          },
        },
        UserWithRole: {
          allOf: [
            { $ref: '#/components/schemas/User' },
            {
              type: 'object',
              properties: {
                church: {
                  type: 'object',
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    slug: { type: 'string' },
                  },
                },
                role: {
                  type: 'object',
                  nullable: true,
                  properties: {
                    id: { type: 'string' },
                    name: { type: 'string' },
                    permissions: {
                      type: 'array',
                      items: { type: 'string' },
                    },
                  },
                },
                lastLoginAt: {
                  type: 'string',
                  format: 'date-time',
                  nullable: true,
                },
              },
            },
          ],
        },
        UserProfile: {
          allOf: [
            { $ref: '#/components/schemas/UserWithRole' },
            {
              type: 'object',
              properties: {
                phone: { type: 'string', nullable: true },
                dateOfBirth: { type: 'string', format: 'date', nullable: true },
                gender: { type: 'string', enum: ['male', 'female', 'other'], nullable: true },
                address: { type: 'string', nullable: true },
                profileImage: { type: 'string', format: 'uri', nullable: true },
                joinDate: { type: 'string', format: 'date' },
                status: { type: 'string', enum: ['active', 'inactive', 'suspended'] },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
              },
            },
          ],
        },
        AuthTokens: {
          type: 'object',
          properties: {
            accessToken: {
              type: 'string',
              description: 'JWT access token',
              example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            },
            refreshToken: {
              type: 'string',
              description: 'Refresh token for obtaining new access tokens',
              example: 'abc123def456...',
            },
            expiresIn: {
              type: 'string',
              description: 'Access token expiration time',
              example: '15m',
            },
          },
        },
        Error: {
          type: 'object',
          properties: {
            status: {
              type: 'string',
              example: 'error',
            },
            message: {
              type: 'string',
              description: 'Error message',
              example: 'Church with this slug already exists',
            },
            errors: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  field: {
                    type: 'string',
                    description: 'Field that caused the error',
                  },
                  message: {
                    type: 'string',
                    description: 'Error message for the field',
                  },
                },
              },
            },
          },
        },
      },
    },
    tags: [
      {
        name: 'Authentication',
        description: 'User authentication and profile management',
      },
      {
        name: 'Onboarding',
        description: 'Church onboarding and setup process',
      },
      {
        name: 'Churches',
        description: 'Church management endpoints',
      },
      {
        name: 'Members', 
        description: 'Member management endpoints',
      },
      {
        name: 'Roles',
        description: 'Role and permission management endpoints',
      },
    ],
  },
  apis: ['./src/routes/*.ts', './src/controllers/*.ts'],
};

export const specs = swaggerJsdoc(options);
export const swaggerUiOptions = {
  explorer: true,
  customCss: '.swagger-ui .topbar { display: none }',
  customSiteTitle: 'Church Management API',
};