import nodemailer from 'nodemailer';
import { z } from 'zod';

// Email configuration schema
const emailConfigSchema = z.object({
  SMTP_HOST: z.string().default('localhost'),
  SMTP_PORT: z.string().default('2525').transform(Number),
  SMTP_SECURE: z.string().default('false').transform(val => val === 'true'),
  SMTP_USER: z.string().email().default('<EMAIL>'),
  SMTP_PASS: z.string().default('password'),
  FROM_EMAIL: z.string().email().default('<EMAIL>'),
  FROM_NAME: z.string().default('Test Suite'),
});

// Validate environment variables
const emailConfig = emailConfigSchema.parse({
  SMTP_HOST: process.env.SMTP_HOST,
  SMTP_PORT: process.env.SMTP_PORT,
  SMTP_SECURE: process.env.SMTP_SECURE,
  SMTP_USER: process.env.SMTP_USER,
  SMTP_PASS: process.env.SMTP_PASS,
  FROM_EMAIL: process.env.FROM_EMAIL,
  FROM_NAME: process.env.FROM_NAME,
});

// Create transporter
export const transporter = nodemailer.createTransport({
  host: emailConfig.SMTP_HOST,
  port: emailConfig.SMTP_PORT,
  secure: emailConfig.SMTP_SECURE,
  auth: {
    user: emailConfig.SMTP_USER,
    pass: emailConfig.SMTP_PASS,
  },
});

export const mailConfig = {
  from: {
    email: emailConfig.FROM_EMAIL,
    name: emailConfig.FROM_NAME,
  },
};

// Verify connection on startup
export const verifyMailConnection = async (): Promise<boolean> => {
  try {
    if (process.env.NODE_ENV === 'test') return true;
    await transporter.verify();
    console.log('✅ Mail server connection verified');
    return true;
  } catch (error) {
    if (process.env.NODE_ENV === 'test') return true;
    console.error('❌ Mail server connection failed:', error);
    return false;
  }
};