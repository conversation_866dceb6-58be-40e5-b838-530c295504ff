# Church Management System - Backend

A multi-tenant church management system built with TypeScript, Bun, and PostgreSQL.

## Features

- Multi-tenant architecture (multiple churches)
- Role-based access control
- Member management
- Church administration
- JWT authentication
- Database migrations with Drizzle ORM

## Prerequisites

- [Bun](https://bun.com) runtime
- PostgreSQL database

## Setup

1. Install dependencies:
```bash
bun install
```

2. Copy environment file:
```bash
cp .env.example .env
```

3. Update the `.env` file with your database URL and JWT secret:
```env
DATABASE_URL=postgresql://username:password@localhost:5432/church_management
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
```

4. Generate and run database migrations:
```bash
bun run db:generate
bun run db:migrate
```

## Available Scripts

- `bun run dev` - Start development server with hot reload
- `bun run start` - Start production server
- `bun run db:generate` - Generate database migrations
- `bun run db:migrate` - Run database migrations
- `bun run db:push` - Push schema changes to database
- `bun run db:studio` - Launch Drizzle Studio (database GUI)

## API Endpoints

### Churches
- `POST /api/v1/churches` - Create a new church
- `GET /api/v1/churches` - Get all active churches
- `GET /api/v1/churches/:id` - Get church by ID
- `GET /api/v1/churches/slug/:slug` - Get church by slug
- `PUT /api/v1/churches/:id` - Update church (requires auth)
- `DELETE /api/v1/churches/:id` - Delete church (requires auth)

### Members
- `POST /api/v1/members/register` - Register new member
- `POST /api/v1/members/login` - Member login
- `GET /api/v1/members` - Get all members (requires auth)
- `GET /api/v1/members/:id` - Get member by ID (requires auth)
- `PUT /api/v1/members/:id` - Update member (requires auth)
- `DELETE /api/v1/members/:id` - Delete member (requires auth)

### Roles
- `POST /api/v1/roles` - Create role (requires auth)
- `GET /api/v1/roles` - Get all roles (requires auth)
- `GET /api/v1/roles/:id` - Get role by ID (requires auth)
- `PUT /api/v1/roles/:id` - Update role (requires auth)
- `DELETE /api/v1/roles/:id` - Delete role (requires auth)

## Default Permissions

- `*` - Full access (Admin only)
- `manage_church` - Church settings management
- `manage_members` - Add, edit, delete members
- `view_members` - View member list and details
- `manage_roles` - Create, edit, delete roles
- `view_roles` - View roles and permissions
- `view_profile` - View own profile

## Project Structure

```
src/
├── controllers/     # Route handlers
├── middleware/      # Authentication & authorization
├── models/          # Database models (future use)
├── routes/          # API routes
├── db/
│   ├── schema/      # Database schema definitions
│   └── migrations/  # Database migrations
├── types/           # TypeScript type definitions
├── utils/           # Utility functions
└── config/          # Configuration files
```
