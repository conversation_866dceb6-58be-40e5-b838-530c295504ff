#!/usr/bin/env node

const API_BASE = 'http://localhost:3000/api';

async function testAuthFlow() {
  console.log('🔐 Testing Authentication Flow...\n');

  try {
    // Step 1: Test that finance endpoints require auth (should return 401)
    console.log('1. Testing finance endpoint without auth (should return 401)...');
    const unauthResponse = await fetch(`${API_BASE}/churches/test-church/finances/projects`);
    
    if (unauthResponse.status === 401) {
      console.log('✅ Finance endpoint correctly requires authentication');
      const errorData = await unauthResponse.json();
      console.log(`   Error: ${errorData.message}`);
    } else {
      console.log(`❌ Unexpected status: ${unauthResponse.status}`);
    }

    // Step 2: Test health endpoint (should work without auth)
    console.log('\n2. Testing health endpoint (should work without auth)...');
    const healthResponse = await fetch(`${API_BASE.replace('/api', '')}/health`);
    
    if (healthResponse.ok) {
      console.log('✅ Health endpoint working');
    } else {
      console.log('❌ Health endpoint failed');
    }

    // Step 3: Test public endpoints
    console.log('\n3. Testing public endpoints...');
    const publicEndpoints = [
      '/currencies',
      '/onboarding/churches/check-slug/test-slug'
    ];

    for (const endpoint of publicEndpoints) {
      try {
        const response = await fetch(`${API_BASE}${endpoint}`);
        console.log(`   ${endpoint}: ${response.status === 404 ? 'OK (404 expected)' : response.status}`);
      } catch (error) {
        console.log(`   ${endpoint}: Error - ${error.message}`);
      }
    }

    console.log('\n🎉 Authentication flow is working correctly!');
    console.log('\n📋 Next steps to test project creation:');
    console.log('1. Start frontend: cd frontend && bun run dev');
    console.log('2. Go to http://localhost:3002');
    console.log('3. Register a church and login');
    console.log('4. Navigate to finances and create a project');
    console.log('\nOR');
    console.log('1. Create test user: cd backend && bun run create-test-member.ts');
    console.log('2. Login via API to get auth token');
    console.log('3. Use token in Authorization header for API calls');

  } catch (error) {
    console.error('❌ Error testing auth flow:', error.message);
    console.log('\nMake sure the backend server is running on port 3000');
  }
}

testAuthFlow();