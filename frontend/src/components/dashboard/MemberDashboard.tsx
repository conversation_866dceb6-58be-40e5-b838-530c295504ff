'use client'

import { useDashboardData } from '@/hooks/useDashboardData'
import { useAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Calendar,
  Activity,
  AlertCircle,
  RefreshCw,
  BookOpen,
  MessageSquare,
  Clock
} from 'lucide-react'

export function MemberDashboard() {
  const { user, church } = useAuth()
  const { 
    events, 
    announcements,
    loading, 
    error, 
    refetch 
  } = useDashboardData('30d')

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error}
            <button 
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Welcome, {user?.firstName}!</h1>
          <p className="text-muted-foreground">
            Stay connected with your church community
          </p>
        </div>
        {loading && (
          <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
        )}
      </div>

      {/* Quick Info - Member friendly (no analytics) */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{events?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {events?.length ? 'Don\'t miss what\'s coming up' : 'No events scheduled'}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Announcements</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{announcements?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Stay updated with church news
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">My Engagement</CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{(events?.filter(e => e.requiresRsvp).length) || 0}</div>
                <p className="text-xs text-muted-foreground">
                  Events that may need RSVP
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Member-specific sections */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              Upcoming Events
            </CardTitle>
            <CardDescription>Worship services, studies, and community</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {loading ? (
              <div className="space-y-3">
                {[1,2,3].map(i => (
                  <div key={i} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                    <Skeleton className="h-5 w-16" />
                  </div>
                ))}
              </div>
            ) : events?.length ? (
              events.slice(0, 5).map((event) => (
                <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                  <div>
                    <p className="font-medium">{event.title}</p>
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(event.startDate).toLocaleDateString()} • {event.type.replace('_', ' ')}
                    </p>
                  </div>
                  <Badge variant="outline">{event.requiresRsvp ? 'RSVP' : 'Open'}</Badge>
                </div>
              ))
            ) : (
              <div className="text-center py-6">
                <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No events scheduled</p>
                <p className="text-xs text-muted-foreground mt-1">Check back soon for updates</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Church Announcements
            </CardTitle>
            <CardDescription>Latest updates and news</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {loading ? (
              <div className="space-y-3">
                {[1,2,3].map(i => (
                  <div key={i} className="p-3 bg-muted/50 rounded-lg">
                    <Skeleton className="h-4 w-40 mb-1" />
                    <Skeleton className="h-3 w-60" />
                  </div>
                ))}
              </div>
            ) : announcements?.length ? (
              announcements.slice(0, 5).map((a) => (
                <div key={a.id} className="p-3 bg-muted/50 rounded-lg">
                  <p className="font-medium">{a.title}</p>
                  <p className="text-sm text-muted-foreground line-clamp-2">{a.summary || a.content}</p>
                  <p className="text-xs text-muted-foreground mt-1">{a.publishedAt ? new Date(a.publishedAt).toLocaleDateString() : ''}</p>
                </div>
              ))
            ) : (
              <div className="text-center py-6">
                <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No announcements yet</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Member-focused Tabs */}
      <Tabs defaultValue="events" className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="community">Announcements</TabsTrigger>
        </TabsList>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Church Events</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : events?.length ? (
                  events.slice(0, 8).map((event) => (
                    <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {new Date(event.startDate).toLocaleDateString()} • {event.type.replace('_', ' ')}
                        </p>
                      </div>
                      <Badge variant="outline">{event.requiresRsvp ? 'RSVP' : 'Open'}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No events scheduled</p>
                    <p className="text-xs text-muted-foreground mt-1">Check back soon for updates</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Announcements Tab */}
        <TabsContent value="community" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-1">
            <Card>
              <CardHeader>
                <CardTitle>Church Announcements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1,2,3].map(i => (
                      <div key={i} className="p-3 bg-muted/50 rounded-lg">
                        <Skeleton className="h-4 w-40 mb-1" />
                        <Skeleton className="h-3 w-60" />
                      </div>
                    ))}
                  </div>
                ) : announcements?.length ? (
                  announcements.slice(0, 8).map((a) => (
                    <div key={a.id} className="p-3 bg-muted/50 rounded-lg">
                      <p className="font-medium">{a.title}</p>
                      <p className="text-sm text-muted-foreground line-clamp-2">{a.summary || a.content}</p>
                      <p className="text-xs text-muted-foreground mt-1">{a.publishedAt ? new Date(a.publishedAt).toLocaleDateString() : ''}</p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <MessageSquare className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No announcements yet</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Recent Activity intentionally omitted for members to avoid implied tracking */}
    </div>
  )
}