'use client'

import { useDashboardData } from '@/hooks/useDashboardData'
import { useAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { 
  Calendar,
  Users,
  Heart,
  AlertCircle,
  RefreshCw,
  BookOpen,
  MapPin,
  Clock,
  Phone,
  Mail,
  Globe
} from 'lucide-react'

export function VisitorDashboard() {
  const { user, church } = useAuth()
  const { 
    overview, 
    events, 
    branches, 
    loading, 
    error, 
    refetch 
  } = useDashboardData('30d')

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load information: {error}
            <button 
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Welcome to {church?.name}!</h1>
          <p className="text-muted-foreground">
            We&apos;re glad you&apos;re here. Discover our community and upcoming events.
          </p>
        </div>
        {loading && (
          <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
        )}
      </div>

      {/* Welcome Message */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-blue-900">
            <Heart className="h-5 w-5" />
            Welcome to Our Church Family
          </CardTitle>
          <CardDescription className="text-blue-700">
            Thank you for visiting us! We believe everyone has a place in God&apos;s family, and we&apos;d love to help you find yours.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
              Plan Your Visit
            </Button>
            <Button size="sm" variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-50">
              Learn About Us
            </Button>
            <Button size="sm" variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-50">
              Connect With Us
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick Info - Visitor focused */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Services</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {events?.filter(e => e.type === 'worship_service').length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  {events?.length ? (
                    'Join us for worship this week'
                  ) : (
                    'Check back for service times'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Church Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{branches?.length || 1}</div>
                <p className="text-xs text-muted-foreground">
                  {branches?.length ? (
                    'Convenient locations to visit'
                  ) : (
                    'Find us in your community'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Community Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{overview?.totalMembers || 0}+</div>
                <p className="text-xs text-muted-foreground">
                  {overview?.totalMembers ? (
                    'Welcoming church family'
                  ) : (
                    'Growing community'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Visitor Information Sections */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              This Week&apos;s Events
            </CardTitle>
            <CardDescription>Join us for worship and fellowship</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {loading ? (
              <div className="space-y-3">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex justify-between items-center p-2 border rounded">
                    <div>
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-20" />
                    </div>
                    <Skeleton className="h-5 w-16" />
                  </div>
                ))}
              </div>
            ) : events?.length ? (
              events.slice(0, 4).map((event) => (
                <div key={event.id} className="flex justify-between items-center p-3 bg-muted/30 rounded-lg">
                  <div>
                    <p className="font-medium">{event.title}</p>
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {new Date(event.startDate).toLocaleDateString()} at {new Date(event.startDate).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                    </p>
                    {event.location && (
                      <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3" />
                        {event.location}
                      </p>
                    )}
                  </div>
                  <Badge variant="outline" className="bg-white">
                    {event.isPublic ? 'Open' : 'Members'}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="text-center py-6">
                <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No events scheduled</p>
                <p className="text-xs text-muted-foreground mt-1">Check back soon for updates</p>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              What to Expect
            </CardTitle>
            <CardDescription>Your first visit with us</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="font-medium text-green-900">Warm Welcome</p>
              <p className="text-sm text-green-700">Our greeters will help you feel at home from the moment you arrive</p>
            </div>
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="font-medium text-blue-900">Casual Atmosphere</p>
              <p className="text-sm text-blue-700">Come as you are - we value authenticity over formality</p>
            </div>
            <div className="p-3 bg-purple-50 border border-purple-200 rounded-lg">
              <p className="font-medium text-purple-900">Meaningful Worship</p>
              <p className="text-sm text-purple-700">Experience uplifting music and practical, life-changing messages</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Church Information */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Visit Us
            </CardTitle>
            <CardDescription>Find us and plan your visit</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {branches?.length ? (
              branches.slice(0, 2).map((branch) => (
                <div key={branch.id} className="p-3 border rounded-lg">
                  <p className="font-medium">{branch.name}</p>
                  {branch.address && (
                    <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                      <MapPin className="h-3 w-3" />
                      {branch.address}
                    </p>
                  )}
                  {branch.phone && (
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <Phone className="h-3 w-3" />
                      {branch.phone}
                    </p>
                  )}
                  <Badge variant={branch.isMainBranch ? 'default' : 'outline'} className="mt-2">
                    {branch.isMainBranch ? 'Main Campus' : 'Branch Location'}
                  </Badge>
                </div>
              ))
            ) : (
              <div className="p-3 border rounded-lg">
                <p className="font-medium">{church?.name}</p>
                {church?.address && (
                  <p className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                    <MapPin className="h-3 w-3" />
                    {church.address}
                  </p>
                )}
                {church?.phone && (
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <Phone className="h-3 w-3" />
                    {church.phone}
                  </p>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5" />
              Connect With Us
            </CardTitle>
            <CardDescription>Get in touch and stay connected</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              {church?.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{church.email}</span>
                </div>
              )}
              {church?.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{church.phone}</span>
                </div>
              )}
              {church?.website && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">{church.website}</span>
                </div>
              )}
            </div>
            
            <div className="pt-3 border-t">
              <p className="text-sm font-medium mb-2">Next Steps:</p>
              <div className="space-y-1">
                <Button size="sm" variant="outline" className="w-full justify-start">
                  Request Prayer
                </Button>
                <Button size="sm" variant="outline" className="w-full justify-start">
                  Schedule a Visit
                </Button>
                <Button size="sm" variant="outline" className="w-full justify-start">
                  Learn About Membership
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* About the Church */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BookOpen className="h-5 w-5" />
            About {church?.name}
          </CardTitle>
          <CardDescription>Our mission and values</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none">
            {church?.description ? (
              <p className="text-muted-foreground">{church.description}</p>
            ) : (
              <div className="space-y-4">
                <p className="text-muted-foreground">
                  We are a community of believers committed to following Jesus Christ and making a positive impact in our community. 
                  Our mission is to love God, love people, and serve our world with the hope and truth of the Gospel.
                </p>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center p-4 bg-muted/30 rounded-lg">
                    <Heart className="h-8 w-8 text-red-500 mx-auto mb-2" />
                    <h3 className="font-medium">Love</h3>
                    <p className="text-xs text-muted-foreground">Showing God&apos;s love to all</p>
                  </div>
                  <div className="text-center p-4 bg-muted/30 rounded-lg">
                    <Users className="h-8 w-8 text-blue-500 mx-auto mb-2" />
                    <h3 className="font-medium">Community</h3>
                    <p className="text-xs text-muted-foreground">Building authentic relationships</p>
                  </div>
                  <div className="text-center p-4 bg-muted/30 rounded-lg">
                    <BookOpen className="h-8 w-8 text-green-500 mx-auto mb-2" />
                    <h3 className="font-medium">Growth</h3>
                    <p className="text-xs text-muted-foreground">Growing in faith together</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}