'use client'

import { useAuth } from '@/lib/auth'
import { SuperAdminDashboard } from './SuperAdminDashboard'
import { PastorDashboard } from './PastorDashboard'
import { DeaconDashboard } from './DeaconDashboard'
import { MemberDashboard } from './MemberDashboard'
import { VisitorDashboard } from './VisitorDashboard'

export function RoleBasedDashboard() {
  const { user } = useAuth()

  // Handle case where user or role is not loaded yet
  if (!user) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-sm text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  const roleName = user.role?.name || 'Member'

  switch (roleName) {
    case 'Super Admin':
      return <SuperAdminDashboard />
    case 'Pastor':
    case 'Elder':
      return <PastorDashboard />
    case 'Deacon':
      return <DeaconDashboard />
    case 'Member':
      return <MemberDashboard />
    case 'Visitor':
      return <VisitorDashboard />
    default:
      return <MemberDashboard />
  }
}