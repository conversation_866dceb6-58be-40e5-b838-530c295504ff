'use client'

import { useDashboardData } from '@/hooks/useDashboardData'
import { useAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/currency'
import { 
  Calendar,
  Users,
  DollarSign,
  TrendingUp,
  Activity,
  AlertCircle,
  RefreshCw,
  Heart,
  HandHeart,
  MessageSquare
} from 'lucide-react'
import { useState } from 'react'

export function DeaconDashboard() {
  const { user, church } = useAuth()
  const [period, setPeriod] = useState('30d')
  const { 
    overview, 
    events, 
    branches, 
    memberAnalytics, 
    eventAnalytics, 
    financialAnalytics,
    chartData,
    loading, 
    error, 
    refetch 
  } = useDashboardData(period)

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error}
            <button 
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Deacon Dashboard</h1>
          <p className="text-muted-foreground">
            Ministry support and community care overview
          </p>
        </div>
        <div className="flex items-center gap-4">
          {loading && (
            <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Stats - Limited for Deacon role */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Community Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{overview?.totalMembers || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {overview?.totalMembers ? (
                    `Active congregation members`
                  ) : (
                    'No members registered yet'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Ministry Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{events?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {events?.length ? (
                    `${events?.filter(e => e.type === 'worship_service').length || 0} worship services`
                  ) : (
                    'No events scheduled'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Community Giving</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold">
                  {formatCurrency(overview?.totalRevenue || 0, church?.defaultCurrency || 'ZMW')}
                </div>
                <p className="text-xs text-muted-foreground">
                  {overview?.totalRevenue ? (
                    'Church giving this month'
                  ) : (
                    'No donations received yet'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Deacon-specific Ministry Areas */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HandHeart className="h-5 w-5" />
              Community Care
            </CardTitle>
            <CardDescription>Member support and pastoral care</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Active Members</span>
              <Badge variant="default">
                {memberAnalytics?.membersByStatus?.find(s => s.status === 'active')?.count || 0}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>New Members</span>
              <Badge variant="secondary">{memberAnalytics?.memberGrowthTrend?.slice(-1)[0]?.count || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Follow-up Needed</span>
              <Badge variant="outline">{memberAnalytics?.membersByStatus?.find(s => s.status === 'inactive')?.count || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Prayer Requests</span>
              <Badge variant="default">3</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Ministry Activities
            </CardTitle>
            <CardDescription>Events and community engagement</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Upcoming Events</span>
              <Badge variant="default">{events?.length || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Community Outreach</span>
              <Badge variant="secondary">
                {events?.filter(e => e.type === 'outreach').length || 0}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Bible Studies</span>
              <Badge variant="outline">
                {events?.filter(e => e.type === 'bible_study').length || 0}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Average Attendance</span>
              <Badge variant="default">
                {eventAnalytics?.attendanceMetrics?.averageAttendanceRate || 0}%
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Simplified Analytics Tabs for Deacon */}
      <Tabs defaultValue="members" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="giving">Giving</TabsTrigger>
        </TabsList>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Member Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByStatus?.length ? (
                  memberAnalytics.membersByStatus.map((item) => (
                    <div key={item.status} className="flex justify-between items-center">
                      <span className="capitalize">{item.status.replace('_', ' ')}</span>
                      <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No member data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Community Demographics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByGender?.length ? (
                  memberAnalytics.membersByGender.map((item) => (
                    <div key={item.gender} className="flex justify-between items-center">
                      <span className="capitalize">{item.gender}</span>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No demographic data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Ministry Events</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : events?.length ? (
                  events.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(event.startDate).toLocaleDateString()} • {event.type.replace('_', ' ')}
                        </p>
                      </div>
                      <Badge variant="outline">{(event as any).rsvpStats?.attending || 0} attending</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No events scheduled</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Event Engagement</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>This Week</span>
                  <Badge variant="default">{events?.length || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Worship Services</span>
                  <Badge variant="secondary">
                    {events?.filter(e => e.type === 'worship_service').length || 0}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Bible Studies</span>
                  <Badge variant="outline">
                    {events?.filter(e => e.type === 'bible_study').length || 0}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Community Events</span>
                  <Badge variant="default">
                    {events?.filter(e => e.type === 'community').length || 0}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Giving Tab */}
        <TabsContent value="giving" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Donation Overview</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>This Month</span>
                  <Badge variant="default">
                    {formatCurrency(overview?.totalRevenue || 0, church?.defaultCurrency || 'ZMW')}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Total Donors</span>
                  <Badge variant="secondary">{financialAnalytics?.topDonors?.length || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Average Gift</span>
                  <Badge variant="outline">
                    {formatCurrency((financialAnalytics?.donationsByType?.length ? (financialAnalytics.totalRevenue / Math.max(1, financialAnalytics.donationsByType.reduce((s, d) => s + d.count, 0))) : 0), church?.defaultCurrency || 'ZMW')}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Giving Categories</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.donationsByType?.length ? (
                  financialAnalytics.donationsByType.slice(0, 4).map((item) => (
                    <div key={item.type} className="flex justify-between items-center">
                      <span className="capitalize">{item.type.replace('_', ' ')}</span>
                      <Badge variant="outline">
                        {formatCurrency(item.totalAmount, church?.defaultCurrency || 'ZMW')}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No giving data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Recent Activity - Limited view for Deacon */}
      {overview?.recentActivity && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Ministry Activity
            </CardTitle>
            <CardDescription>Latest updates in your areas of service</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <Skeleton className="h-4 w-4" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                  </div>
                ))}
              </div>
            ) : overview.recentActivity.length > 0 ? (
              <div className="space-y-4">
                {overview.recentActivity.slice(0, 3).map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    {activity.type === 'member_joined' && <Users className="h-4 w-4 text-blue-500" />}
                    {activity.type === 'event_created' && <Calendar className="h-4 w-4 text-green-500" />}
                    {activity.type === 'donation_received' && <DollarSign className="h-4 w-4 text-yellow-500" />}
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">{activity.description}</p>
                    </div>
                    <span className="text-xs text-muted-foreground">{new Date(activity.timestamp).toLocaleString()}</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-6">
                <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No recent activity</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}