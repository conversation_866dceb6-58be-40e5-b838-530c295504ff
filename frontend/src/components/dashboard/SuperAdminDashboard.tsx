'use client'

import { useDashboardData } from '@/hooks/useDashboardData'
import { useAuth } from '@/lib/auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency, formatCurrencyParts } from '@/lib/currency'
import { 
  Calendar,
  Users,
  DollarSign,
  MapPin,
  TrendingUp,
  Activity,
  AlertCircle,
  RefreshCw,
  BarChart3,
  PieChart,
  LineChart,
  Settings,
  Shield,
  Database
} from 'lucide-react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  <PERSON><PERSON><PERSON>, 
  Legend, 
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import { useState } from 'react'

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

export function SuperAdminDashboard() {
  const { user, church } = useAuth()
  const [period, setPeriod] = useState('30d')
  const { 
    overview, 
    events, 
    branches, 
    memberAnalytics, 
    eventAnalytics, 
    financialAnalytics,
    chartData,
    loading, 
    error, 
    refetch 
  } = useDashboardData(period)

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error}
            <button 
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Super Admin Dashboard</h1>
          <p className="text-muted-foreground">
            Complete church management and analytics overview
          </p>
        </div>
        <div className="flex items-center gap-4">
          {loading && (
            <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Members</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{overview?.totalMembers || 0}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {overview?.totalMembers ? (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                      {overview?.memberGrowthRate ? `+${overview.memberGrowthRate}%` : 'No growth data'} from last month
                    </>
                  ) : (
                    <span className="text-muted-foreground">No members registered yet</span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold leading-none">
                  {(() => {
                    const parts = formatCurrencyParts(overview?.totalRevenue || 0, church?.defaultCurrency || 'ZMW')
                    return (
                      <span>
                        <span className="align-top text-base mr-1 opacity-80">{parts.symbol}</span>
                        <span className="tracking-tight">{parts.number}</span>
                      </span>
                    )
                  })()}
                </div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {overview?.totalRevenue ? (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                      {overview?.donationGrowthRate ? `+${overview.donationGrowthRate}%` : 'No growth data'} from last month
                    </>
                  ) : (
                    <span className="text-muted-foreground">No donations received yet</span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Events</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{overview?.totalEvents || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {overview?.totalEvents ? (
                    `${events?.length || 0} upcoming this week`
                  ) : (
                    'No events scheduled'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Church Branches</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <>
                <div className="text-2xl font-bold">{branches?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {branches?.length ? (
                    `${branches?.filter(b => b.isMainBranch).length || 0} main, ${branches?.filter(b => !b.isMainBranch).length || 0} satellite`
                  ) : (
                    'No branches configured'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Admin-specific Quick Actions */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              System Management
            </CardTitle>
            <CardDescription>Church configuration and settings</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Church Settings</span>
              <Badge variant="outline">Active</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>User Roles</span>
              <Badge variant="default">{memberAnalytics?.totalRoles || 5}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Payment Methods</span>
              <Badge variant="default">{financialAnalytics?.paymentMethods || 3}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Security Overview
            </CardTitle>
            <CardDescription>User access and permissions</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Active Sessions</span>
              <Badge variant="default">{memberAnalytics?.activeSessions || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Admin Users</span>
              <Badge variant="secondary">{memberAnalytics?.adminUsers || 1}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Last Login</span>
              <Badge variant="outline">Today</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              Data Insights
            </CardTitle>
            <CardDescription>System performance metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Total Records</span>
              <Badge variant="default">{(overview?.totalMembers || 0) + (overview?.totalEvents || 0)}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Storage Used</span>
              <Badge variant="outline">2.3 GB</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Backup Status</span>
              <Badge variant="default">Current</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Member Growth Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Member Growth Trends
                </CardTitle>
                <CardDescription>Monthly member registration patterns</CardDescription>
              </CardHeader>
              <CardContent>
                {chartData?.memberGrowth?.some(item => item.members > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={chartData.memberGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="members" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <LineChart className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">No Member Growth Data</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Add members to track growth trends
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Financial Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Revenue Analytics
                </CardTitle>
                <CardDescription>Monthly donation and revenue trends</CardDescription>
              </CardHeader>
              <CardContent>
                {chartData?.financeTrends?.some(item => item.amount && item.amount > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={chartData.financeTrends}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => formatCurrency(Number(value), church?.defaultCurrency || 'ZMW')} />
                      <Bar dataKey="amount" fill="#0088FE" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">No Revenue Data</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Set up donation tracking to see financial trends
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Member Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByStatus?.length ? (
                  memberAnalytics.membersByStatus.map((item) => (
                    <div key={item.status} className="flex justify-between items-center">
                      <span className="capitalize">{item.status.replace('_', ' ')}</span>
                      <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No member data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Role Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByRole?.length ? (
                  memberAnalytics.membersByRole.map((item) => (
                    <div key={item.role} className="flex justify-between items-center">
                      <span>{item.role}</span>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Shield className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No role data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Branch Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : branches?.length ? (
                  branches.slice(0, 5).map((branch) => (
                    <div key={branch.id} className="flex justify-between items-center">
                      <span className="truncate">{branch.name}</span>
                      <Badge variant={branch.isMainBranch ? 'default' : 'outline'}>
                        {branch.memberCount || 0}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <MapPin className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No branch data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Event Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : events?.length ? (
                  events.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(event.startDate).toLocaleDateString()} • {event.type}
                        </p>
                      </div>
                      <Badge variant="outline">{event.rsvpStats?.attending || 0} attending</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No events scheduled</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Event Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Total Events</span>
                  <Badge variant="default">{overview?.totalEvents || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Average Attendance</span>
                  <Badge variant="outline">
                    {eventAnalytics?.attendanceMetrics?.averageAttendanceRate || 0}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Most Popular Type</span>
                  <Badge variant="secondary">
                    {eventAnalytics?.attendanceMetrics?.mostPopularEventType || 'Worship'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Peak Day</span>
                  <Badge variant="outline">
                    {eventAnalytics?.attendanceMetrics?.peakAttendanceDay || 'Sunday'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        <TabsContent value="financial" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Revenue Breakdown</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.donationsByType?.length ? (
                  financialAnalytics.donationsByType.map((item) => (
                    <div key={item.type} className="flex justify-between items-center">
                      <span className="capitalize">{item.type.replace('_', ' ')}</span>
                      <Badge variant="default">
                        {formatCurrency(item.totalAmount, church?.defaultCurrency || 'ZMW')}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No revenue data</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Payment Analytics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.donationsByMethod?.length ? (
                  financialAnalytics.donationsByMethod.map((item) => (
                    <div key={item.method} className="flex justify-between items-center">
                      <span className="capitalize">{item.method.replace('_', ' ')}</span>
                      <Badge variant="outline">{item.percentage}%</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No payment data</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Top Contributors</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="text-sm">
                        <Skeleton className="h-4 w-32 mb-1" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.topDonors?.length ? (
                  financialAnalytics.topDonors.slice(0, 3).map((donor) => (
                    <div key={donor.donorId || 'anonymous'} className="text-sm">
                      <p className="font-medium">{donor.donorName}</p>
                      <p className="text-muted-foreground">
                        {formatCurrency(donor.totalDonations, church?.defaultCurrency || 'ZMW')}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No donor data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* System Tab - Super Admin Only */}
        <TabsContent value="system" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  System Configuration
                </CardTitle>
                <CardDescription>Church settings and configuration</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>Church Status</span>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Features Enabled</span>
                  <Badge variant="outline">
                    {Object.values(church?.settings?.features || {}).filter(Boolean).length || 0}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Default Currency</span>
                  <Badge variant="secondary">{church?.defaultCurrency || 'ZMW'}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Timezone</span>
                  <Badge variant="outline">{church?.settings?.timezone || 'UTC'}</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5" />
                  System Health
                </CardTitle>
                <CardDescription>Performance and maintenance status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span>Database Status</span>
                  <Badge variant="default">Healthy</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Last Backup</span>
                  <Badge variant="outline">2 hours ago</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>System Load</span>
                  <Badge variant="secondary">Normal</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Uptime</span>
                  <Badge variant="default">99.9%</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}