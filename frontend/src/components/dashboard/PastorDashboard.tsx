'use client'

import { useDashboardData } from '@/hooks/useDashboardData'
import { useAuth } from '@/lib/auth'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency, formatCurrencyParts } from '@/lib/currency'
import {
  Calendar,
  Users,
  DollarSign,
  MapPin,
  TrendingUp,
  Activity,
  AlertCircle,
  RefreshCw,
  BarChart3,
  Heart,
  BookOpen,
  MessageSquare
} from 'lucide-react'
import {
  <PERSON><PERSON>hart,
  Bar,
  <PERSON><PERSON><PERSON>s,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts'
import { useState } from 'react'

export function PastorDashboard() {
  const { user, church } = useAuth()
  const [period, setPeriod] = useState('30d')
  const {
    overview,
    events,
    branches,
    memberAnalytics,
    eventAnalytics,
    financialAnalytics,
    chartData,
    loading,
    error,
    refetch
  } = useDashboardData(period)

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error}
            <button
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  const canViewFinancial = hasPermission(user, PERMISSIONS.VIEW_FINANCIAL_REPORTS)

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Pastor Dashboard</h1>
          <p className="text-muted-foreground">
            Church leadership and ministry overview
          </p>
        </div>
        <div className="flex items-center gap-4">
          {loading && (
            <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Congregation Size</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{overview?.totalMembers || 0}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {overview?.totalMembers ? (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                      {overview?.memberGrowthRate ? `+${overview.memberGrowthRate}%` : 'No growth data'} growth
                    </>
                  ) : (
                    <span className="text-muted-foreground">Start building your congregation</span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Giving</CardTitle>
            <Heart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-20" />
            ) : (
              <>
                <div className="text-2xl font-bold leading-none">
                  {(() => {
                    const parts = formatCurrencyParts(overview?.totalRevenue || 0, church?.defaultCurrency || 'ZMW')
                    return (
                      <span>
                        <span className="align-top text-base mr-1 opacity-80">{parts.symbol}</span>
                        <span className="tracking-tight">{parts.number}</span>
                      </span>
                    )
                  })()}
                </div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {overview?.totalRevenue ? (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                      {overview?.donationGrowthRate ? `+${overview.donationGrowthRate}%` : 'Steady'} from last month
                    </>
                  ) : (
                    <span className="text-muted-foreground">Set up giving to track donations</span>
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Upcoming Services</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-16" />
            ) : (
              <>
                <div className="text-2xl font-bold">{events?.filter(e => e.type === 'worship_service').length || 0}</div>
                <p className="text-xs text-muted-foreground">
                  {events?.length ? (
                    `${events?.length || 0} total events this week`
                  ) : (
                    'No services scheduled'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Church Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            {loading ? (
              <Skeleton className="h-8 w-12" />
            ) : (
              <>
                <div className="text-2xl font-bold">{branches?.length || 0}</div>
                <p className="text-xs text-muted-foreground">
                                      {branches?.length ? (
                      `${branches.filter(b => b.isMain || b.isMainBranch).length} main campus`
                    ) : (
                    'Set up your first location'
                  )}
                </p>
              </>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Ministry Focus Areas */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Pastoral Care
            </CardTitle>
            <CardDescription>Member engagement and care</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Active Members</span>
              <Badge variant="default">
                {memberAnalytics?.membersByStatus?.find(s => s.status === 'active')?.count || 0}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>New This Month</span>
              <Badge variant="secondary">{memberAnalytics?.newMembersThisMonth || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Needs Follow-up</span>
              <Badge variant="outline">{memberAnalytics?.needsFollowUp || 0}</Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Ministry Events
            </CardTitle>
            <CardDescription>Upcoming church activities</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>This Week</span>
              <Badge variant="default">{events?.length || 0}</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Worship Services</span>
              <Badge variant="secondary">
                {events?.filter(e => e.type === 'worship_service').length || 0}
              </Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Bible Studies</span>
              <Badge variant="outline">
                {events?.filter(e => e.type === 'bible_study').length || 0}
              </Badge>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Communication
            </CardTitle>
            <CardDescription>Church announcements and updates</CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <div className="flex justify-between items-center">
              <span>Recent Announcements</span>
              <Badge variant="default">3</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Prayer Requests</span>
              <Badge variant="secondary">5</Badge>
            </div>
            <div className="flex justify-between items-center">
              <span>Pending Approvals</span>
              <Badge variant="outline">2</Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="ministry" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4 md:grid-cols-3 lg:grid-cols-4">
          <TabsTrigger value="ministry">Ministry</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          {canViewFinancial && <TabsTrigger value="giving">Giving</TabsTrigger>}
        </TabsList>

        {/* Ministry Tab */}
        <TabsContent value="ministry" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Congregation Growth */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Congregation Growth
                </CardTitle>
                <CardDescription>Monthly membership trends</CardDescription>
              </CardHeader>
              <CardContent>
                {chartData?.memberGrowth?.some(item => item.members > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={chartData.memberGrowth}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="members" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <TrendingUp className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">Building Your Congregation</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Add members to track growth and engagement
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Ministry Activities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Ministry Activities
                </CardTitle>
                <CardDescription>Weekly church engagement</CardDescription>
              </CardHeader>
              <CardContent>
                {chartData?.weeklyActivity?.some(item => item.events > 0 || item.attendance > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={chartData.weeklyActivity}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="events" fill="#00C49F" name="Events" />
                      <Bar dataKey="attendance" fill="#FFBB28" name="Attendance" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <Activity className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">Ministry Activities</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Schedule events and track attendance<br />to see engagement patterns
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Member Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByStatus?.length ? (
                  memberAnalytics.membersByStatus.map((item) => (
                    <div key={item.status} className="flex justify-between items-center">
                      <span className="capitalize">{item.status.replace('_', ' ')}</span>
                      <Badge variant={item.status === 'active' ? 'default' : 'secondary'}>
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No member data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Demographics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : memberAnalytics?.membersByGender?.length ? (
                  memberAnalytics.membersByGender.map((item) => (
                    <div key={item.gender} className="flex justify-between items-center">
                      <span className="capitalize">{item.gender}</span>
                      <Badge variant="outline">{item.count}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No demographic data</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Branch Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                  {loading ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map(i => (
                        <div key={i} className="flex justify-between items-center">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-5 w-12" />
                        </div>
                      ))}
                    </div>
                  ) : branches?.length ? (
                    branches.slice(0, 5).map((branch) => (
                      <div key={branch.id} className="flex justify-between items-center">
                        <span className="truncate">{branch.name}</span>
                        <Badge variant={branch.isMain || branch.isMainBranch ? 'default' : 'outline'}>
                          {branch.memberCount || 0}
                        </Badge>
                      </div>
                    ))
                  ) : (
                  <div className="text-center py-4">
                    <MapPin className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No branch data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Services & Events</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : events?.length ? (
                  events.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-muted-foreground">
                          {new Date(event.startDate).toLocaleDateString()} • {event.type.replace('_', ' ')}
                        </p>
                      </div>
                      <Badge variant="outline">{event.rsvpStats?.attending || 0} attending</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No events scheduled</p>
                    <p className="text-xs text-muted-foreground mt-1">Plan your first service or event</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Ministry Engagement</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Average Attendance</span>
                  <Badge variant="default">
                    {eventAnalytics?.attendanceMetrics?.averageAttendanceRate || 0}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Most Attended</span>
                  <Badge variant="secondary">
                    {eventAnalytics?.attendanceMetrics?.mostPopularEventType?.replace('_', ' ') || 'Worship Service'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Peak Day</span>
                  <Badge variant="outline">
                    {eventAnalytics?.attendanceMetrics?.peakAttendanceDay || 'Sunday'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Total Events</span>
                  <Badge variant="default">{overview?.totalEvents || 0}</Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Giving Tab */}
        {canViewFinancial && (
        <TabsContent value="giving" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Giving Categories</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.donationsByType?.length ? (
                  financialAnalytics.donationsByType.map((item) => (
                    <div key={item.type} className="flex justify-between items-center">
                      <span className="capitalize">{item.type.replace('_', ' ')}</span>
                      <Badge variant="default">
                        {formatCurrency(item.totalAmount, church?.defaultCurrency || 'ZMW')}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Heart className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No giving data</p>
                    <p className="text-xs text-muted-foreground mt-1">Set up donation tracking</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Giving Methods</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-5 w-12" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.donationsByMethod?.length ? (
                  financialAnalytics.donationsByMethod.map((item) => (
                    <div key={item.method} className="flex justify-between items-center">
                      <span className="capitalize">{item.method.replace('_', ' ')}</span>
                      <Badge variant="outline">{item.percentage}%</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No payment data</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Faithful Givers</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="space-y-2">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="text-sm">
                        <Skeleton className="h-4 w-32 mb-1" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    ))}
                  </div>
                ) : financialAnalytics?.topDonors?.length ? (
                  financialAnalytics.topDonors.slice(0, 3).map((donor) => (
                    <div key={donor.donorId || 'anonymous'} className="text-sm">
                      <p className="font-medium">{donor.donorName}</p>
                      <p className="text-muted-foreground">
                        {formatCurrency(donor.totalDonations, church?.defaultCurrency || 'ZMW')}
                      </p>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No donor data</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
