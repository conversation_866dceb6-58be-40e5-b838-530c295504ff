'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { churchSettingsSchema, type ChurchSettingsForm } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { 
  Settings, 
  Users, 
  Mail, 
  Globe, 
  Palette, 
  Zap, 
  Calendar, 
  DollarSign,
  Shield,
  Clock,
  Languages,
  Eye,
  UserPlus,
  CheckCircle,
  Image,
  CreditCard,
  Receipt,
  Repeat
} from 'lucide-react'
import { PaymentMethodsSettings } from './PaymentMethodsSettings'

interface ChurchData {
  id: string
  name: string
  slug: string
  churchCode: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  logo?: string
  settings: any
  createdAt: string
  updatedAt: string
}

interface ChurchSettingsFormProps {
  church: ChurchData
  onSubmit: (data: Partial<ChurchSettingsForm>) => Promise<void>
  section: 'general' | 'theme' | 'features' | 'events' | 'donations'
}

const timezones = [
  { value: 'UTC', label: 'UTC (Coordinated Universal Time)' },
  { value: 'America/New_York', label: 'Eastern Time (ET)' },
  { value: 'America/Chicago', label: 'Central Time (CT)' },
  { value: 'America/Denver', label: 'Mountain Time (MT)' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },
  { value: 'America/Phoenix', label: 'Arizona Time (MST)' },
  { value: 'America/Anchorage', label: 'Alaska Time (AKST)' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HST)' },
]

const locales = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'de', label: 'German' },
]

const currencies = [
  { value: 'USD', label: 'US Dollar (USD)' },
  { value: 'EUR', label: 'Euro (EUR)' },
  { value: 'GBP', label: 'British Pound (GBP)' },
  { value: 'CAD', label: 'Canadian Dollar (CAD)' },
  { value: 'AUD', label: 'Australian Dollar (AUD)' },
]

const eventTypes = [
  { value: 'sunday_service', label: 'Sunday Service' },
  { value: 'bible_study', label: 'Bible Study' },
  { value: 'prayer_meeting', label: 'Prayer Meeting' },
  { value: 'youth_service', label: 'Youth Service' },
  { value: 'community_outreach', label: 'Community Outreach' },
  { value: 'other', label: 'Other' },
]

export function ChurchSettingsForm({ church, onSubmit, section }: ChurchSettingsFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm({
    resolver: zodResolver(churchSettingsSchema),
    defaultValues: {
      allowSelfRegistration: church.settings?.allowSelfRegistration ?? true,
      requireEmailVerification: church.settings?.requireEmailVerification ?? true,
      timezone: church.settings?.timezone ?? 'UTC',
      locale: church.settings?.locale ?? 'en',
      theme: {
        primaryColor: church.settings?.theme?.primaryColor ?? '#3B82F6',
        secondaryColor: church.settings?.theme?.secondaryColor ?? '#64748B',
        logo: church.settings?.theme?.logo ?? '',
      },
      features: {
        events: church.settings?.features?.events ?? true,
        donations: church.settings?.features?.donations ?? false,
        messaging: church.settings?.features?.messaging ?? true,
        calendar: church.settings?.features?.calendar ?? true,
        onlineGiving: church.settings?.features?.onlineGiving ?? false,
        memberDirectory: church.settings?.features?.memberDirectory ?? true,
        eventRsvp: church.settings?.features?.eventRsvp ?? true,
        recurringEvents: church.settings?.features?.recurringEvents ?? true,
      },
      eventSettings: {
        defaultEventType: church.settings?.eventSettings?.defaultEventType ?? 'other',
        requireApprovalForPublicEvents: church.settings?.eventSettings?.requireApprovalForPublicEvents ?? false,
        allowMemberEventCreation: church.settings?.eventSettings?.allowMemberEventCreation ?? false,
        maxEventsPerMonth: church.settings?.eventSettings?.maxEventsPerMonth ?? undefined,
        eventImageRequired: church.settings?.eventSettings?.eventImageRequired ?? false,
      },
      donationSettings: {
        defaultCurrency: church.settings?.donationSettings?.defaultCurrency ?? 'USD',
        enableAnonymousDonations: church.settings?.donationSettings?.enableAnonymousDonations ?? true,
        requireReceiptNumbers: church.settings?.donationSettings?.requireReceiptNumbers ?? true,
        taxDeductibleByDefault: church.settings?.donationSettings?.taxDeductibleByDefault ?? true,
        enableEventDonations: church.settings?.donationSettings?.enableEventDonations ?? true,
        minimumDonationAmount: church.settings?.donationSettings?.minimumDonationAmount ?? 1,
        enableRecurringDonations: church.settings?.donationSettings?.enableRecurringDonations ?? false,
      },
    },
  })

  const handleSubmit = async (data: any) => {
    try {
      setIsSubmitting(true)
      
      // Only submit the section being edited
      let sectionData: Partial<ChurchSettingsForm> = {}
      
      switch (section) {
        case 'general':
          sectionData = {
            allowSelfRegistration: data.allowSelfRegistration,
            requireEmailVerification: data.requireEmailVerification,
            timezone: data.timezone,
            locale: data.locale,
          }
          break
        case 'theme':
          sectionData = { theme: data.theme }
          break
        case 'features':
          sectionData = { features: data.features }
          break
        case 'events':
          sectionData = { eventSettings: data.eventSettings }
          break
        case 'donations':
          sectionData = { donationSettings: data.donationSettings }
          break
      }
      
      await onSubmit(sectionData)
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsSubmitting(false)
    }
  }

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Member Registration
          </CardTitle>
          <CardDescription>
            Control how new members can join your church
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Allow Self Registration
              </Label>
              <p className="text-sm text-muted-foreground">
                Let people register as members without admin approval
              </p>
            </div>
            <Switch
              checked={form.watch('allowSelfRegistration')}
              onCheckedChange={(checked) => form.setValue('allowSelfRegistration', checked)}
            />
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Require Email Verification
              </Label>
              <p className="text-sm text-muted-foreground">
                New members must verify their email address
              </p>
            </div>
            <Switch
              checked={form.watch('requireEmailVerification')}
              onCheckedChange={(checked) => form.setValue('requireEmailVerification', checked)}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Localization
          </CardTitle>
          <CardDescription>
            Set your church&apos;s timezone and language preferences
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Timezone
              </Label>
              <Select
                value={form.watch('timezone')}
                onValueChange={(value) => form.setValue('timezone', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Languages className="h-4 w-4" />
                Language
              </Label>
              <Select
                value={form.watch('locale')}
                onValueChange={(value) => form.setValue('locale', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {locales.map((locale) => (
                    <SelectItem key={locale.value} value={locale.value}>
                      {locale.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderThemeSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          Theme & Branding
        </CardTitle>
        <CardDescription>
          Customize your church&apos;s visual appearance
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="primaryColor">Primary Color</Label>
            <div className="flex items-center gap-2">
              <Input
                id="primaryColor"
                type="color"
                {...form.register('theme.primaryColor')}
                className="w-16 h-10 p-1 border rounded"
              />
              <Input
                {...form.register('theme.primaryColor')}
                placeholder="#3B82F6"
                className="flex-1"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="secondaryColor">Secondary Color</Label>
            <div className="flex items-center gap-2">
              <Input
                id="secondaryColor"
                type="color"
                {...form.register('theme.secondaryColor')}
                className="w-16 h-10 p-1 border rounded"
              />
              <Input
                {...form.register('theme.secondaryColor')}
                placeholder="#64748B"
                className="flex-1"
              />
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="logo" className="flex items-center gap-2">
            <Image className="h-4 w-4" />
            Logo URL
          </Label>
          <Input
            id="logo"
            type="url"
            {...form.register('theme.logo')}
            placeholder="https://example.com/logo.png"
          />
          <p className="text-xs text-muted-foreground">
            Enter a URL to your church logo image
          </p>
        </div>
      </CardContent>
    </Card>
  )

  const renderFeatureSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Feature Settings
        </CardTitle>
        <CardDescription>
          Enable or disable features for your church
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Events
              </Label>
              <p className="text-xs text-muted-foreground">Event management system</p>
            </div>
            <Switch
              checked={form.watch('features.events')}
              onCheckedChange={(checked) => form.setValue('features.events', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Donations
              </Label>
              <p className="text-xs text-muted-foreground">Donation tracking</p>
            </div>
            <Switch
              checked={form.watch('features.donations')}
              onCheckedChange={(checked) => form.setValue('features.donations', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Messaging
              </Label>
              <p className="text-xs text-muted-foreground">Internal messaging system</p>
            </div>
            <Switch
              checked={form.watch('features.messaging')}
              onCheckedChange={(checked) => form.setValue('features.messaging', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Calendar
              </Label>
              <p className="text-xs text-muted-foreground">Church calendar view</p>
            </div>
            <Switch
              checked={form.watch('features.calendar')}
              onCheckedChange={(checked) => form.setValue('features.calendar', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Online Giving
              </Label>
              <p className="text-xs text-muted-foreground">Online donation processing</p>
            </div>
            <Switch
              checked={form.watch('features.onlineGiving')}
              onCheckedChange={(checked) => form.setValue('features.onlineGiving', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Member Directory
              </Label>
              <p className="text-xs text-muted-foreground">Searchable member directory</p>
            </div>
            <Switch
              checked={form.watch('features.memberDirectory')}
              onCheckedChange={(checked) => form.setValue('features.memberDirectory', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Event RSVP
              </Label>
              <p className="text-xs text-muted-foreground">Event RSVP system</p>
            </div>
            <Switch
              checked={form.watch('features.eventRsvp')}
              onCheckedChange={(checked) => form.setValue('features.eventRsvp', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Repeat className="h-4 w-4" />
                Recurring Events
              </Label>
              <p className="text-xs text-muted-foreground">Support for recurring events</p>
            </div>
            <Switch
              checked={form.watch('features.recurringEvents')}
              onCheckedChange={(checked) => form.setValue('features.recurringEvents', checked)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderEventSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Event Settings
        </CardTitle>
        <CardDescription>
          Configure how events work in your church
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Default Event Type</Label>
            <Select
              value={form.watch('eventSettings.defaultEventType')}
              onValueChange={(value) => form.setValue('eventSettings.defaultEventType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select default type" />
              </SelectTrigger>
              <SelectContent>
                {eventTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxEventsPerMonth">Max Events Per Month</Label>
            <Input
              id="maxEventsPerMonth"
              type="number"
              min="1"
              {...form.register('eventSettings.maxEventsPerMonth', { valueAsNumber: true })}
              placeholder="No limit"
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Require Approval for Public Events
              </Label>
              <p className="text-xs text-muted-foreground">
                Public events need admin approval before being published
              </p>
            </div>
            <Switch
              checked={form.watch('eventSettings.requireApprovalForPublicEvents')}
              onCheckedChange={(checked) => form.setValue('eventSettings.requireApprovalForPublicEvents', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <UserPlus className="h-4 w-4" />
                Allow Member Event Creation
              </Label>
              <p className="text-xs text-muted-foreground">
                Let regular members create events
              </p>
            </div>
            <Switch
              checked={form.watch('eventSettings.allowMemberEventCreation')}
              onCheckedChange={(checked) => form.setValue('eventSettings.allowMemberEventCreation', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Image className="h-4 w-4" />
                Event Image Required
              </Label>
              <p className="text-xs text-muted-foreground">
                Require an image for all events
              </p>
            </div>
            <Switch
              checked={form.watch('eventSettings.eventImageRequired')}
              onCheckedChange={(checked) => form.setValue('eventSettings.eventImageRequired', checked)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderDonationSettings = () => (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          Donation Settings
        </CardTitle>
        <CardDescription>
          Configure donation tracking and processing
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label>Default Currency</Label>
            <Select
              value={form.watch('donationSettings.defaultCurrency')}
              onValueChange={(value) => form.setValue('donationSettings.defaultCurrency', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select currency" />
              </SelectTrigger>
              <SelectContent>
                {currencies.map((currency) => (
                  <SelectItem key={currency.value} value={currency.value}>
                    {currency.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="minimumDonationAmount">Minimum Donation Amount</Label>
            <Input
              id="minimumDonationAmount"
              type="number"
              min="0.01"
              step="0.01"
              {...form.register('donationSettings.minimumDonationAmount', { valueAsNumber: true })}
            />
          </div>
        </div>

        <Separator />

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Eye className="h-4 w-4" />
                Enable Anonymous Donations
              </Label>
              <p className="text-xs text-muted-foreground">
                Allow donors to give anonymously
              </p>
            </div>
            <Switch
              checked={form.watch('donationSettings.enableAnonymousDonations')}
              onCheckedChange={(checked) => form.setValue('donationSettings.enableAnonymousDonations', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Receipt className="h-4 w-4" />
                Require Receipt Numbers
              </Label>
              <p className="text-xs text-muted-foreground">
                Generate receipt numbers for all donations
              </p>
            </div>
            <Switch
              checked={form.watch('donationSettings.requireReceiptNumbers')}
              onCheckedChange={(checked) => form.setValue('donationSettings.requireReceiptNumbers', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4" />
                Tax Deductible by Default
              </Label>
              <p className="text-xs text-muted-foreground">
                Mark donations as tax deductible by default
              </p>
            </div>
            <Switch
              checked={form.watch('donationSettings.taxDeductibleByDefault')}
              onCheckedChange={(checked) => form.setValue('donationSettings.taxDeductibleByDefault', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Enable Event Donations
              </Label>
              <p className="text-xs text-muted-foreground">
                Allow donations to be linked to specific events
              </p>
            </div>
            <Switch
              checked={form.watch('donationSettings.enableEventDonations')}
              onCheckedChange={(checked) => form.setValue('donationSettings.enableEventDonations', checked)}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="flex items-center gap-2">
                <Repeat className="h-4 w-4" />
                Enable Recurring Donations
              </Label>
              <p className="text-xs text-muted-foreground">
                Support for recurring donation schedules
              </p>
            </div>
            <Switch
              checked={form.watch('donationSettings.enableRecurringDonations')}
              onCheckedChange={(checked) => form.setValue('donationSettings.enableRecurringDonations', checked)}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )

  const renderSection = () => {
    switch (section) {
      case 'general':
        return renderGeneralSettings()
      case 'theme':
        return renderThemeSettings()
      case 'features':
        return renderFeatureSettings()
      case 'events':
        return renderEventSettings()
      case 'donations':
        return (
          <div className="space-y-6">
            {renderDonationSettings()}
            <PaymentMethodsSettings />
          </div>
        )
      default:
        return null
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {renderSection()}
      
      <div className="flex justify-end pt-4">
        <Button 
          type="submit" 
          disabled={isSubmitting || !form.formState.isDirty}
          className="min-w-[120px]"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </form>
  )
}