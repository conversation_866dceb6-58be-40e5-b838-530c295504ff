'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle, Smartphone, CreditCard, Building2 } from 'lucide-react'
import { PaymentMethod } from '@/types'

const paymentMethodSchema = z.object({
    name: z.string().min(1, 'Name is required'),
    type: z.enum(['mobile_money', 'bank_card', 'bank_transfer', 'cash']),
    provider: z.string().optional(),
    accountNumber: z.string().optional(),
    accountName: z.string().optional(),
    bankName: z.string().optional(),
    phoneNumber: z.string().optional(),
    isDefault: z.boolean().default(false),
})

type PaymentMethodFormData = z.infer<typeof paymentMethodSchema>

interface PaymentMethodFormProps {
    onSubmit: (data: PaymentMethodFormData) => Promise<void>
    onCancel: () => void
    initialData?: Partial<PaymentMethod>
}

const paymentTypes = [
    { value: 'mobile_money', label: 'Mobile Money', icon: Smartphone },
    { value: 'bank_card', label: 'Bank Card', icon: CreditCard },
    { value: 'bank_transfer', label: 'Bank Transfer', icon: Building2 },
    { value: 'cash', label: 'Cash', icon: Building2 },
]

const mobileProviders = [
    { value: 'airtel', label: 'Airtel Money' },
    { value: 'mtn', label: 'MTN Mobile Money' },
    { value: 'zamtel', label: 'Zamtel Kwacha' },
]

const cardProviders = [
    { value: 'visa', label: 'Visa' },
    { value: 'mastercard', label: 'Mastercard' },
]

export function PaymentMethodForm({
    onSubmit,
    onCancel,
    initialData
}: PaymentMethodFormProps) {
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState<string | null>(null)

    const {
        register,
        handleSubmit,
        setValue,
        watch,
        formState: { errors }
    } = useForm<PaymentMethodFormData>({
        resolver: zodResolver(paymentMethodSchema),
        defaultValues: {
            name: initialData?.name || '',
            type: initialData?.type || 'mobile_money',
            provider: initialData?.provider || '',
            accountNumber: initialData?.accountNumber || '',
            accountName: initialData?.accountName || '',
            bankName: initialData?.bankName || '',
            phoneNumber: initialData?.phoneNumber || '',
            isDefault: initialData?.isDefault || false,
        }
    })

    const watchedValues = watch()
    const selectedType = watchedValues.type

    const handleFormSubmit = async (data: PaymentMethodFormData) => {
        try {
            setLoading(true)
            setError(null)
            await onSubmit(data)
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to save payment method')
        } finally {
            setLoading(false)
        }
    }

    const getProviderOptions = () => {
        switch (selectedType) {
            case 'mobile_money':
                return mobileProviders
            case 'bank_card':
                return cardProviders
            default:
                return []
        }
    }

    const renderTypeSpecificFields = () => {
        switch (selectedType) {
            case 'mobile_money':
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="provider">Provider *</Label>
                            <Select
                                value={watchedValues.provider}
                                onValueChange={(value) => setValue('provider', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select mobile money provider" />
                                </SelectTrigger>
                                <SelectContent>
                                    {mobileProviders.map((provider) => (
                                        <SelectItem key={provider.value} value={provider.value}>
                                            {provider.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phoneNumber">Phone Number *</Label>
                            <Input
                                id="phoneNumber"
                                placeholder="e.g., +************"
                                {...register('phoneNumber')}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="accountName">Account Name</Label>
                            <Input
                                id="accountName"
                                placeholder="Account holder name"
                                {...register('accountName')}
                            />
                        </div>
                    </>
                )

            case 'bank_card':
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="provider">Card Type *</Label>
                            <Select
                                value={watchedValues.provider}
                                onValueChange={(value) => setValue('provider', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select card type" />
                                </SelectTrigger>
                                <SelectContent>
                                    {cardProviders.map((provider) => (
                                        <SelectItem key={provider.value} value={provider.value}>
                                            {provider.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="accountNumber">Card Number (Last 4 digits) *</Label>
                            <Input
                                id="accountNumber"
                                placeholder="****1234"
                                maxLength={4}
                                {...register('accountNumber')}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="accountName">Cardholder Name</Label>
                            <Input
                                id="accountName"
                                placeholder="Name on card"
                                {...register('accountName')}
                            />
                        </div>
                    </>
                )

            case 'bank_transfer':
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="bankName">Bank Name *</Label>
                            <Input
                                id="bankName"
                                placeholder="e.g., Zanaco, FNB, Stanbic"
                                {...register('bankName')}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="accountNumber">Account Number *</Label>
                            <Input
                                id="accountNumber"
                                placeholder="Bank account number"
                                {...register('accountNumber')}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="accountName">Account Name *</Label>
                            <Input
                                id="accountName"
                                placeholder="Account holder name"
                                {...register('accountName')}
                            />
                        </div>
                    </>
                )

            default:
                return null
        }
    }

    return (
        <Dialog open onOpenChange={onCancel}>
            <DialogContent className="sm:max-w-[500px]">
                <DialogHeader>
                    <DialogTitle>
                        {initialData ? 'Edit Payment Method' : 'Add Payment Method'}
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
                    {error && (
                        <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{error}</AlertDescription>
                        </Alert>
                    )}

                    <div className="space-y-2">
                        <Label htmlFor="name">Method Name *</Label>
                        <Input
                            id="name"
                            placeholder="e.g., Church Airtel Money, Main Bank Account"
                            {...register('name')}
                        />
                        {errors.name && (
                            <p className="text-sm text-destructive">{errors.name.message}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor="type">Payment Type *</Label>
                        <Select
                            value={watchedValues.type}
                            onValueChange={(value) => setValue('type', value as any)}
                        >
                            <SelectTrigger>
                                <SelectValue placeholder="Select payment type" />
                            </SelectTrigger>
                            <SelectContent>
                                {paymentTypes.map((type) => (
                                    <SelectItem key={type.value} value={type.value}>
                                        <div className="flex items-center gap-2">
                                            <type.icon className="h-4 w-4" />
                                            {type.label}
                                        </div>
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors.type && (
                            <p className="text-sm text-destructive">{errors.type.message}</p>
                        )}
                    </div>

                    {renderTypeSpecificFields()}

                    <div className="flex items-center space-x-2">
                        <Checkbox
                            id="isDefault"
                            checked={watchedValues.isDefault}
                            onCheckedChange={(checked) => setValue('isDefault', checked as boolean)}
                        />
                        <Label htmlFor="isDefault">Set as default payment method</Label>
                    </div>

                    <DialogFooter>
                        <Button type="button" variant="outline" onClick={onCancel}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={loading}>
                            {loading ? 'Saving...' : initialData ? 'Update Method' : 'Add Method'}
                        </Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}