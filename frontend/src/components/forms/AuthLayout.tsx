'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

interface AuthLayoutProps {
  children: React.ReactNode
  title: string
  description?: string
  showBackToHome?: boolean
  footer?: React.ReactNode
}

export function AuthLayout({ 
  children, 
  title, 
  description, 
  showBackToHome = true,
  footer 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-6">
        {showBackToHome && (
          <div className="text-center">
            <Link 
              href="/" 
              className="text-sm text-muted-foreground hover:text-primary transition-colors"
            >
              ← Back to Home
            </Link>
          </div>
        )}
        
        <Card>
          <CardHeader className="space-y-1">
            <CardTitle className="text-2xl text-center">{title}</CardTitle>
            {description && (
              <CardDescription className="text-center">
                {description}
              </CardDescription>
            )}
          </CardHeader>
          
          <CardContent>
            {children}
          </CardContent>
          
          {footer && (
            <CardFooter>
              {footer}
            </CardFooter>
          )}
        </Card>
      </div>
    </div>
  )
}