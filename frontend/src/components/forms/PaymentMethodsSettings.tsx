'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { api } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Plus,
  CreditCard,
  Smartphone,
  Building2,
  DollarSign,
  Edit,
  Trash2,
  Star,
  AlertCircle
} from 'lucide-react'
import { PaymentMethod } from '@/types'
import { PaymentMethodForm } from './PaymentMethodForm'

export function PaymentMethodsSettings() {
  const params = useParams()
  const slug = params.slug as string

  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showPaymentMethodForm, setShowPaymentMethodForm] = useState(false)
  const [editingItem, setEditingItem] = useState<any>(null)

  useEffect(() => {
    loadPaymentMethods()
  }, [slug])

  const loadPaymentMethods = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await api.getPaymentMethods(slug)
      setPaymentMethods((response.data as any)?.paymentMethods || [])
    } catch (err) {
      console.warn('Failed to load payment methods:', err)
      setPaymentMethods([])
    } finally {
      setLoading(false)
    }
  }

  const handleCreatePaymentMethod = async (data: any) => {
    try {
      await api.createPaymentMethod(slug, data)
      await loadPaymentMethods()
      setShowPaymentMethodForm(false)
      setEditingItem(null)
    } catch (err) {
      console.error('Error creating payment method:', err)
      setError('Failed to create payment method. Please try again.')
    }
  }

  const handleUpdatePaymentMethod = async (data: any) => {
    try {
      await api.updatePaymentMethod(slug, editingItem.id, data)
      await loadPaymentMethods()
      setShowPaymentMethodForm(false)
      setEditingItem(null)
    } catch (err) {
      console.error('Error updating payment method:', err)
      setError('Failed to update payment method. Please try again.')
    }
  }

  const handleDeletePaymentMethod = async (methodId: string) => {
    if (!confirm('Are you sure you want to delete this payment method?')) return

    try {
      await api.deletePaymentMethod(slug, methodId)
      await loadPaymentMethods()
    } catch (err) {
      console.error('Error deleting payment method:', err)
      setError('Failed to delete payment method. Please try again.')
    }
  }

  const handleSetDefaultPaymentMethod = async (methodId: string) => {
    try {
      await api.setDefaultPaymentMethod(slug, methodId)
      await loadPaymentMethods()
    } catch (err) {
      console.error('Error setting default payment method:', err)
      setError('Failed to set default payment method. Please try again.')
    }
  }

  const getPaymentMethodIcon = (type: string, provider?: string) => {
    switch (type) {
      case 'mobile_money':
        return <Smartphone className="h-4 w-4" />
      case 'bank_card':
        return <CreditCard className="h-4 w-4" />
      case 'bank_transfer':
        return <Building2 className="h-4 w-4" />
      default:
        return <DollarSign className="h-4 w-4" />
    }
  }

  const getProviderBadgeColor = (provider?: string) => {
    switch (provider) {
      case 'airtel':
        return 'bg-red-100 text-red-800'
      case 'mtn':
        return 'bg-yellow-100 text-yellow-800'
      case 'zamtel':
        return 'bg-green-100 text-green-800'
      case 'visa':
        return 'bg-blue-100 text-blue-800'
      case 'mastercard':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Payment Methods
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Payment Methods
              </CardTitle>
              <CardDescription>
                Configure payment methods for donations and transactions
              </CardDescription>
            </div>
            <Button onClick={() => setShowPaymentMethodForm(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Method
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid gap-4 md:grid-cols-2">
            {paymentMethods.map((method) => (
              <Card key={method.id} className="border-2">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getPaymentMethodIcon(method.type, method.provider)}
                      <CardTitle className="text-lg">{method.name}</CardTitle>
                      {method.isDefault && (
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => {
                          setEditingItem(method)
                          setShowPaymentMethodForm(true)
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeletePaymentMethod(method.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  {method.provider && (
                    <Badge className={getProviderBadgeColor(method.provider)}>
                      {method.provider.toUpperCase()}
                    </Badge>
                  )}
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    {method.accountNumber && (
                      <p>
                        <span className="font-medium">Account:</span>{' '}
                        {method.accountNumber}
                      </p>
                    )}
                    {method.accountName && (
                      <p>
                        <span className="font-medium">Name:</span>{' '}
                        {method.accountName}
                      </p>
                    )}
                    {method.phoneNumber && (
                      <p>
                        <span className="font-medium">Phone:</span>{' '}
                        {method.phoneNumber}
                      </p>
                    )}
                    {method.bankName && (
                      <p>
                        <span className="font-medium">Bank:</span>{' '}
                        {method.bankName}
                      </p>
                    )}
                    {!method.isDefault && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleSetDefaultPaymentMethod(method.id)}
                      >
                        Set as Default
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {paymentMethods.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12 text-center">
              <CreditCard className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No payment methods configured</h3>
              <p className="text-muted-foreground mb-4">
                Add payment methods to enable online donations and track payment preferences
              </p>
              <Button onClick={() => setShowPaymentMethodForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add First Payment Method
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payment Method Form Modal */}
      {showPaymentMethodForm && (
        <PaymentMethodForm
          onSubmit={editingItem ? handleUpdatePaymentMethod : handleCreatePaymentMethod}
          onCancel={() => {
            setShowPaymentMethodForm(false)
            setEditingItem(null)
          }}
          initialData={editingItem}
        />
      )}
    </>
  )
}