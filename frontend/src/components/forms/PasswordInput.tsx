'use client'

import { useState } from 'react'
import { Eye, EyeOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { FormControl, FormItem, FormLabel, FormMessage } from '@/components/ui/form'

interface PasswordInputProps {
  placeholder?: string
  label?: string
  field: {
    name: string
    value: string
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void
    onBlur: () => void
  }
  showToggle?: boolean
}

export function PasswordInput({ 
  placeholder = "Enter password", 
  label = "Password", 
  field, 
  showToggle = true 
}: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false)

  return (
    <FormItem>
      <FormLabel>{label}</FormLabel>
      <FormControl>
        <div className="relative">
          <Input
            type={showPassword ? 'text' : 'password'}
            placeholder={placeholder}
            {...field}
          />
          {showToggle && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>
      </FormControl>
      <FormMessage />
    </FormItem>
  )
}