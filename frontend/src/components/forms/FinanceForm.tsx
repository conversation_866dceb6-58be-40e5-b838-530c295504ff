'use client'

import React, { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/lib/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { FinanceProject, Currency } from '@/types'

const financeTypes = [
  'tithe', 'donation', 'offering', 'event_donation', 'building_fund', 
  'mission_support', 'special_collection', 'other'
] as const;

const financeMethods = [
  'cash', 'check', 'credit_card', 'debit_card', 'bank_transfer', 
  'mobile_payment', 'online', 'other'
] as const;

const financeSchema = z.object({
  amount: z.number().positive('Amount must be positive'),
  currency: z.string().min(3, 'Currency must be at least 3 characters'),
  type: z.enum(financeTypes),
  method: z.enum(financeMethods),
  isAnonymous: z.boolean().default(false),
  notes: z.string().optional(),
  eventId: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
  projectId: z.string().optional().or(z.literal('')).transform(val => val === '' ? undefined : val),
})

type FinanceFormData = z.infer<typeof financeSchema>

interface FinanceFormProps {
  onSubmit: (data: FinanceFormData) => Promise<void>
  onCancel: () => void
  projects: FinanceProject[]
  currencies: Currency[]
  initialData?: Partial<FinanceFormData>
}

const financeTypeOptions = [
  { value: 'tithe', label: 'Tithe' },
  { value: 'donation', label: 'Donation' },
  { value: 'offering', label: 'Offering' },
  { value: 'event_donation', label: 'Event Donation' },
  { value: 'building_fund', label: 'Building Fund' },
  { value: 'mission_support', label: 'Mission Support' },
  { value: 'special_collection', label: 'Special Collection' },
  { value: 'other', label: 'Other' },
]

const paymentMethodOptions = [
  { value: 'cash', label: 'Cash' },
  { value: 'check', label: 'Check' },
  { value: 'credit_card', label: 'Credit Card' },
  { value: 'debit_card', label: 'Debit Card' },
  { value: 'bank_transfer', label: 'Bank Transfer' },
  { value: 'mobile_payment', label: 'Mobile Payment' },
  { value: 'online', label: 'Online' },
  { value: 'other', label: 'Other' },
]

export function FinanceForm({
  onSubmit,
  onCancel,
  projects,
  currencies,
  initialData
}: FinanceFormProps) {
  const { church } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<FinanceFormData>({
    resolver: zodResolver(financeSchema),
    defaultValues: {
      amount: initialData?.amount || 0,
      currency: initialData?.currency || church?.defaultCurrency || currencies[0]?.code || 'ZMW',
      type: initialData?.type || 'offering',
      method: initialData?.method || 'cash',
      isAnonymous: initialData?.isAnonymous || false,
      notes: initialData?.notes || '',
      eventId: initialData?.eventId || '',
      projectId: initialData?.projectId || '',
    }
  })

  const watchedValues = watch()

  const handleFormSubmit = async (data: FinanceFormData) => {
    try {
      setLoading(true)
      setError(null)
      
      // Clean up the data before sending
      const cleanData = {
        ...data,
        eventId: data.eventId || undefined,
        projectId: data.projectId || undefined,
        notes: data.notes || undefined,
      }
      
      console.log('Submitting finance data:', cleanData)
      await onSubmit(cleanData)
    } catch (err: any) {
      console.error('Finance submission error:', err)
      
      // Handle detailed validation errors
      if (err?.details?.errors && Array.isArray(err.details.errors)) {
        const validationErrors = err.details.errors
          .map((e: any) => `${e.field}: ${e.message}`)
          .join(', ')
        setError(`Validation failed: ${validationErrors}`)
      } else {
        const errorMessage = err?.message || 'Failed to create finance record'
        setError(errorMessage)
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Record Finance</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount">Amount</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0.01"
                {...register('amount', { valueAsNumber: true })}
                placeholder="0.00"
              />
              {errors.amount && (
                <p className="text-sm text-red-500 mt-1">{errors.amount.message}</p>
              )}
            </div>

            <div>
              <Label htmlFor="currency">Currency</Label>
              <Select
                value={watchedValues.currency}
                onValueChange={(value) => setValue('currency', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.code} value={currency.code}>
                      {currency.symbol} {currency.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.currency && (
                <p className="text-sm text-red-500 mt-1">{errors.currency.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="type">Type</Label>
            <Select
              value={watchedValues.type}
              onValueChange={(value) => setValue('type', value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select finance type" />
              </SelectTrigger>
              <SelectContent>
                {financeTypeOptions.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.type && (
              <p className="text-sm text-red-500 mt-1">{errors.type.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="method">Payment Method</Label>
            <Select
              value={watchedValues.method}
              onValueChange={(value) => setValue('method', value as any)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                {paymentMethodOptions.map((method) => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.method && (
              <p className="text-sm text-red-500 mt-1">{errors.method.message}</p>
            )}
          </div>

          {projects.length > 0 && (
            <div>
              <Label htmlFor="projectId">Project (Optional)</Label>
              <Select
                value={watchedValues.projectId}
                onValueChange={(value) => setValue('projectId', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select project" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">No project</SelectItem>
                  {projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          <div>
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              {...register('notes')}
              placeholder="Additional notes..."
              rows={3}
            />
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isAnonymous"
              checked={watchedValues.isAnonymous}
              onCheckedChange={(checked) => setValue('isAnonymous', checked as boolean)}
            />
            <Label htmlFor="isAnonymous">Anonymous finance record</Label>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Recording...' : 'Record Finance'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}