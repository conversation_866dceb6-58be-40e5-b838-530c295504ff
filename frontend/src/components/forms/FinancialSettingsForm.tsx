'use client'

import React, { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Checkbox } from '@/components/ui/checkbox'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle } from 'lucide-react'
import { Currency } from '@/types'

const financialSettingsSchema = z.object({
  defaultCurrency: z.string().min(1, 'Default currency is required'),
  enableOnlineGiving: z.boolean().default(true),
  enableAnonymousDonations: z.boolean().default(true),
  requireReceiptGeneration: z.boolean().default(true),
  enableDonationCategories: z.boolean().default(true),
  enableEventDonations: z.boolean().default(true),
  enableRecurringDonations: z.boolean().default(false),
  minimumDonationAmount: z.number().min(0, 'Minimum amount must be 0 or greater').default(0),
})

type FinancialSettingsFormData = z.infer<typeof financialSettingsSchema>

interface FinancialSettingsFormProps {
  onSubmit: (data: FinancialSettingsFormData) => Promise<void>
  onCancel: () => void
  currencies: Currency[]
  initialData?: Partial<FinancialSettingsFormData>
}

export function FinancialSettingsForm({
  onSubmit,
  onCancel,
  currencies,
  initialData
}: FinancialSettingsFormProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors }
  } = useForm<FinancialSettingsFormData>({
    resolver: zodResolver(financialSettingsSchema),
    defaultValues: {
      defaultCurrency: initialData?.defaultCurrency || 'ZMW',
      enableOnlineGiving: initialData?.enableOnlineGiving ?? true,
      enableAnonymousDonations: initialData?.enableAnonymousDonations ?? true,
      requireReceiptGeneration: initialData?.requireReceiptGeneration ?? true,
      enableDonationCategories: initialData?.enableDonationCategories ?? true,
      enableEventDonations: initialData?.enableEventDonations ?? true,
      enableRecurringDonations: initialData?.enableRecurringDonations ?? false,
      minimumDonationAmount: initialData?.minimumDonationAmount || 0,
    }
  })

  const watchedValues = watch()

  const handleFormSubmit = async (data: FinancialSettingsFormData) => {
    try {
      setLoading(true)
      setError(null)
      await onSubmit(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onCancel}>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Financial Settings</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Currency Settings</CardTitle>
              <CardDescription>
                Configure the default currency for your church donations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="defaultCurrency">Default Currency *</Label>
                <Select
                  value={watchedValues.defaultCurrency}
                  onValueChange={(value) => setValue('defaultCurrency', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select default currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.code} value={currency.code}>
                        {currency.symbol} {currency.name} ({currency.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.defaultCurrency && (
                  <p className="text-sm text-destructive">{errors.defaultCurrency.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Donation Features</CardTitle>
              <CardDescription>
                Enable or disable specific donation features
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableOnlineGiving"
                  checked={watchedValues.enableOnlineGiving}
                  onCheckedChange={(checked) => setValue('enableOnlineGiving', checked as boolean)}
                />
                <Label htmlFor="enableOnlineGiving">Enable online giving</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableAnonymousDonations"
                  checked={watchedValues.enableAnonymousDonations}
                  onCheckedChange={(checked) => setValue('enableAnonymousDonations', checked as boolean)}
                />
                <Label htmlFor="enableAnonymousDonations">Allow anonymous donations</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="requireReceiptGeneration"
                  checked={watchedValues.requireReceiptGeneration}
                  onCheckedChange={(checked) => setValue('requireReceiptGeneration', checked as boolean)}
                />
                <Label htmlFor="requireReceiptGeneration">Require receipt generation</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableDonationCategories"
                  checked={watchedValues.enableDonationCategories}
                  onCheckedChange={(checked) => setValue('enableDonationCategories', checked as boolean)}
                />
                <Label htmlFor="enableDonationCategories">Enable donation categories</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableEventDonations"
                  checked={watchedValues.enableEventDonations}
                  onCheckedChange={(checked) => setValue('enableEventDonations', checked as boolean)}
                />
                <Label htmlFor="enableEventDonations">Enable event-specific donations</Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableRecurringDonations"
                  checked={watchedValues.enableRecurringDonations}
                  onCheckedChange={(checked) => setValue('enableRecurringDonations', checked as boolean)}
                />
                <Label htmlFor="enableRecurringDonations">Enable recurring donations (Coming Soon)</Label>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Donation Limits</CardTitle>
              <CardDescription>
                Set minimum donation amounts and other limits
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="minimumDonationAmount">
                  Minimum Donation Amount ({watchedValues.defaultCurrency})
                </Label>
                <input
                  id="minimumDonationAmount"
                  type="number"
                  step="0.01"
                  min="0"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  {...register('minimumDonationAmount', { valueAsNumber: true })}
                />
                {errors.minimumDonationAmount && (
                  <p className="text-sm text-destructive">{errors.minimumDonationAmount.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : 'Save Settings'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}