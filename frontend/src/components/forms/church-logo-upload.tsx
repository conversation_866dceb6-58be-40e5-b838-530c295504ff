'use client'

import { useState, useRef } from 'react'
import { useParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Image, Upload, X, Camera } from 'lucide-react'
import { toast } from 'sonner'
import { apiClient } from '@/lib/api'
import { compressImage, validateImageFile } from '@/lib/imageUtils'

// Helper function to get base64 size in KB
function getBase64SizeKB(base64String: string): number {
  const base64Data = base64String.split(',')[1] || base64String
  const sizeInBytes = (base64Data.length * 3) / 4
  return sizeInBytes / 1024
}

interface ChurchLogoUploadProps {
  currentLogo?: string
  churchName: string
  onLogoUpdate?: (logo: string) => void
}

export function ChurchLogoUpload({ currentLogo, churchName, onLogoUpdate }: ChurchLogoUploadProps) {
  const params = useParams()
  const slug = params.slug as string
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentLogo || null)
  const [urlInput, setUrlInput] = useState('')

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file
    const validation = validateImageFile(file)
    if (!validation.valid) {
      toast.error(validation.error)
      return
    }

    try {
      setIsUploading(true)

      // Compress image for church logo (extremely aggressive compression for database storage)
      const compressedImage = await compressImage(file, {
        maxWidth: 150,
        maxHeight: 150,
        quality: 0.2,
        maxSizeKB: 30 // Very small for database storage
      })

      // Update preview immediately
      setPreviewUrl(compressedImage)

      // Check final size and provide fallback for very large images
      const sizeKB = getBase64SizeKB(compressedImage)
      
      if (sizeKB > 50) {
        // Image is still too large even after compression
        toast.error(`Image is too large (${Math.round(sizeKB)}KB). Please use a smaller image or enter an image URL instead. For database storage, logos should be under 50KB.`)
        setPreviewUrl(currentLogo || null)
        return
      } else if (sizeKB > 30) {
        toast.warning(`Image compressed to ${Math.round(sizeKB)}KB. This may cause slow uploads.`)
      } else {
        toast.success(`Image compressed successfully (${Math.round(sizeKB)}KB)`)
      }

      // Upload to server
      await apiClient.updateChurchLogo(slug, compressedImage)
      
      // Notify parent component
      onLogoUpdate?.(compressedImage)
      
      toast.success('Church logo updated successfully')
    } catch (error) {
      console.error('Error uploading logo:', error)
      const errorMessage = error instanceof Error && error.message.includes('too large') 
        ? 'Image is too large. Please use a smaller image or try entering an image URL instead.'
        : 'Failed to update church logo'
      toast.error(errorMessage)
      // Revert preview on error
      setPreviewUrl(currentLogo || null)
    } finally {
      setIsUploading(false)
      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleRemoveLogo = async () => {
    try {
      setIsUploading(true)
      
      // Remove logo by setting it to null/empty
      await apiClient.updateChurchLogo(slug, '')
      
      setPreviewUrl(null)
      onLogoUpdate?.('')
      
      toast.success('Church logo removed successfully')
    } catch (error) {
      console.error('Error removing logo:', error)
      toast.error('Failed to remove church logo')
    } finally {
      setIsUploading(false)
    }
  }

  const handleUrlSubmit = async () => {
    if (!urlInput.trim()) {
      toast.error('Please enter a valid URL')
      return
    }

    if (!urlInput.match(/^https?:\/\/.+/)) {
      toast.error('Please enter a valid URL starting with http:// or https://')
      return
    }

    try {
      setIsUploading(true)
      
      // Update preview immediately
      setPreviewUrl(urlInput)
      
      // Upload to server
      await apiClient.updateChurchLogo(slug, urlInput)
      
      // Notify parent component
      onLogoUpdate?.(urlInput)
      
      // Clear URL input
      setUrlInput('')
      
      toast.success('Church logo updated successfully')
    } catch (error) {
      console.error('Error uploading logo:', error)
      toast.error('Failed to update church logo')
      // Revert preview on error
      setPreviewUrl(currentLogo || null)
    } finally {
      setIsUploading(false)
    }
  }

  const triggerFileSelect = () => {
    fileInputRef.current?.click()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Camera className="h-5 w-5" />
          Church Logo
        </CardTitle>
        <CardDescription>
          Upload your church logo. This will appear in the sidebar and throughout the application.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Logo Preview */}
        <div className="flex items-center gap-4">
          <div className="relative">
            <div className="w-24 h-24 rounded-lg border-2 border-dashed border-border bg-muted flex items-center justify-center overflow-hidden">
              {previewUrl ? (
                <img
                  src={previewUrl}
                  alt={`${churchName} logo`}
                  className="w-full h-full object-cover rounded-lg"
                />
              ) : (
                <div className="text-center">
                  <Image className="h-8 w-8 text-muted-foreground mx-auto mb-1" />
                  <p className="text-xs text-muted-foreground">No logo</p>
                </div>
              )}
            </div>
          </div>

          <div className="flex-1 space-y-2">
            <Label className="text-sm font-medium">Current Logo</Label>
            <p className="text-sm text-muted-foreground">
              {previewUrl ? 'Your church logo is displayed above' : 'No logo uploaded yet'}
            </p>
          </div>
        </div>

        {/* Upload Controls */}
        <div className="flex gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={triggerFileSelect}
            disabled={isUploading}
            className="flex items-center gap-2"
          >
            {isUploading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                Uploading...
              </>
            ) : (
              <>
                <Upload className="h-4 w-4" />
                {previewUrl ? 'Change Logo' : 'Upload Logo'}
              </>
            )}
          </Button>

          {previewUrl && (
            <Button
              type="button"
              variant="outline"
              onClick={handleRemoveLogo}
              disabled={isUploading}
              className="flex items-center gap-2 text-destructive hover:text-destructive"
            >
              <X className="h-4 w-4" />
              Remove
            </Button>
          )}
        </div>

        {/* URL Input Alternative */}
        <div className="space-y-4">
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or enter image URL
              </span>
            </div>
          </div>

          <div className="flex gap-2">
            <Input
              placeholder="https://example.com/logo.jpg"
              value={urlInput}
              onChange={(e) => setUrlInput(e.target.value)}
              disabled={isUploading}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault()
                  handleUrlSubmit()
                }
              }}
            />
            <Button
              type="button"
              variant="outline"
              onClick={handleUrlSubmit}
              disabled={isUploading || !urlInput.trim()}
              className="flex items-center gap-2"
            >
              <Upload className="h-4 w-4" />
              Use URL
            </Button>
          </div>
        </div>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />

        {/* Upload guidelines */}
        <div className="text-xs text-muted-foreground space-y-1">
          <p>• Supported formats: JPG, PNG, GIF, WebP</p>
          <p>• Maximum file size: 10MB (will be compressed automatically)</p>
          <p>• Square images work best (1:1 aspect ratio)</p>
          <p>• Recommended minimum size: 200x200 pixels</p>
          <p>• For large files, consider using an image URL instead</p>
        </div>
      </CardContent>
    </Card>
  )
}