'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { churchProfileSchema, type ChurchProfileForm } from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Building2, Mail, Phone, Globe, MapPin, FileText } from 'lucide-react'

interface ChurchData {
  id: string
  name: string
  slug: string
  churchCode: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  logo?: string
  settings: any
  createdAt: string
  updatedAt: string
}

interface ChurchProfileFormProps {
  church: ChurchData
  onSubmit: (data: ChurchProfileForm) => Promise<void>
}

export function ChurchProfileForm({ church, onSubmit }: ChurchProfileFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const form = useForm<ChurchProfileForm>({
    resolver: zodResolver(churchProfileSchema),
    defaultValues: {
      name: church.name || '',
      description: church.description || '',
      address: church.address || '',
      phone: church.phone || '',
      email: church.email || '',
      website: church.website || '',
    },
  })

  const handleSubmit = async (data: ChurchProfileForm) => {
    try {
      setIsSubmitting(true)
      await onSubmit(data)
      // Don't reset form as we want to keep the updated values
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Church Name */}
        <div className="space-y-2">
          <Label htmlFor="name" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Church Name *
          </Label>
          <Input
            id="name"
            {...form.register('name')}
            placeholder="Enter church name"
            className={form.formState.errors.name ? 'border-destructive' : ''}
          />
          {form.formState.errors.name && (
            <p className="text-sm text-destructive">{form.formState.errors.name.message}</p>
          )}
        </div>

        {/* Church Code (Read-only) */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Church Code
          </Label>
          <Input
            value={church.churchCode}
            disabled
            className="bg-muted"
          />
          <p className="text-xs text-muted-foreground">
            This is your unique church identifier used for member login
          </p>
        </div>

        {/* Email */}
        <div className="space-y-2">
          <Label htmlFor="email" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Email Address
          </Label>
          <Input
            id="email"
            type="email"
            {...form.register('email')}
            placeholder="<EMAIL>"
            className={form.formState.errors.email ? 'border-destructive' : ''}
          />
          {form.formState.errors.email && (
            <p className="text-sm text-destructive">{form.formState.errors.email.message}</p>
          )}
        </div>

        {/* Phone */}
        <div className="space-y-2">
          <Label htmlFor="phone" className="flex items-center gap-2">
            <Phone className="h-4 w-4" />
            Phone Number
          </Label>
          <Input
            id="phone"
            {...form.register('phone')}
            placeholder="+****************"
            className={form.formState.errors.phone ? 'border-destructive' : ''}
          />
          {form.formState.errors.phone && (
            <p className="text-sm text-destructive">{form.formState.errors.phone.message}</p>
          )}
        </div>

        {/* Website */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="website" className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            Website
          </Label>
          <Input
            id="website"
            type="url"
            {...form.register('website')}
            placeholder="https://www.yourchurch.com"
            className={form.formState.errors.website ? 'border-destructive' : ''}
          />
          {form.formState.errors.website && (
            <p className="text-sm text-destructive">{form.formState.errors.website.message}</p>
          )}
        </div>

        {/* Address */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="address" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Address
          </Label>
          <Textarea
            id="address"
            {...form.register('address')}
            placeholder="123 Church Street, City, State, ZIP"
            rows={2}
            className={form.formState.errors.address ? 'border-destructive' : ''}
          />
          {form.formState.errors.address && (
            <p className="text-sm text-destructive">{form.formState.errors.address.message}</p>
          )}
        </div>

        {/* Description */}
        <div className="space-y-2 md:col-span-2">
          <Label htmlFor="description" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Description
          </Label>
          <Textarea
            id="description"
            {...form.register('description')}
            placeholder="Tell people about your church, mission, and values..."
            rows={4}
            className={form.formState.errors.description ? 'border-destructive' : ''}
          />
          {form.formState.errors.description && (
            <p className="text-sm text-destructive">{form.formState.errors.description.message}</p>
          )}
          <p className="text-xs text-muted-foreground">
            This description will be visible to visitors and potential members
          </p>
        </div>
      </div>

      {/* Church Slug (Read-only info) */}
      <Card className="bg-muted/50">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Church URL</CardTitle>
          <CardDescription className="text-xs">
            Your church can be accessed at this URL
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="flex items-center gap-2 text-sm font-mono bg-background p-2 rounded border">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">https://churchcode.app/</span>
            <span className="font-semibold">{church.slug}</span>
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end pt-4">
        <Button 
          type="submit" 
          disabled={isSubmitting || !form.formState.isDirty}
          className="min-w-[120px]"
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </Button>
      </div>
    </form>
  )
}