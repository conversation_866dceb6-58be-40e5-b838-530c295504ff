'use client'

import { Member } from '@/types'
import { Dialog, Dialog<PERSON>ontent, Dialog<PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  User, 
  Building2,
  CheckCircle,
  XCircle,
  Clock
} from 'lucide-react'

interface MemberDetailsDialogProps {
  member: Member
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function MemberDetailsDialog({ member, open, onOpenChange }: MemberDetailsDialogProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            <div className="h-12 w-12 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-lg font-medium">
              {member.firstName[0]}{member.lastName[0]}
            </div>
            <div>
              <div className="text-xl font-semibold">{member.firstName} {member.lastName}</div>
              <div className="text-sm text-muted-foreground">{member.email}</div>
            </div>
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Status and Role */}
          <div className="flex items-center gap-4">
            <Badge variant={member.isActive ? 'default' : 'secondary'} className="flex items-center gap-1">
              {member.isActive ? (
                <CheckCircle className="h-3 w-3" />
              ) : (
                <XCircle className="h-3 w-3" />
              )}
              {member.isActive ? 'Active' : 'Inactive'}
            </Badge>
            
            {member.role && (
              <Badge variant="outline" className="flex items-center gap-1">
                <User className="h-3 w-3" />
                {member.role.name}
              </Badge>
            )}

            <Badge variant={member.isEmailVerified ? 'default' : 'destructive'} className="flex items-center gap-1">
              {member.isEmailVerified ? (
                <CheckCircle className="h-3 w-3" />
              ) : (
                <Clock className="h-3 w-3" />
              )}
              {member.isEmailVerified ? 'Email Verified' : 'Email Pending'}
            </Badge>
          </div>

          <Separator />

          {/* Contact Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Contact Information</h3>
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span>{member.email}</span>
              </div>
              
              {member.phone && (
                <div className="flex items-center gap-3">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <span>{member.phone}</span>
                </div>
              )}

              {member.address && (
                <div className="flex items-start gap-3">
                  <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                  <span className="flex-1">{member.address}</span>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Personal Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Personal Information</h3>
            <div className="grid grid-cols-2 gap-4">
              {member.dateOfBirth && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Date of Birth</p>
                  <p className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    {formatDate(member.dateOfBirth)}
                  </p>
                </div>
              )}

              {member.gender && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Gender</p>
                  <p className="capitalize">{member.gender}</p>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Church Information */}
          <div>
            <h3 className="text-lg font-semibold mb-3">Church Information</h3>
            <div className="space-y-3">
              {member.branch && (
                <div className="flex items-center gap-3">
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                  <span>{member.branch.name}</span>
                  {member.branch.isMain && (
                    <Badge variant="outline" className="text-xs">Main Branch</Badge>
                  )}
                </div>
              )}

              <div>
                <p className="text-sm font-medium text-muted-foreground">Member Since</p>
                <p className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  {formatDate(member.createdAt)}
                </p>
              </div>

              {member.lastLoginAt && (
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Last Login</p>
                  <p className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    {formatDate(member.lastLoginAt)}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Permissions (if user has role with permissions) */}
          {member.role?.permissions && member.role.permissions.length > 0 && (
            <>
              <Separator />
              <div>
                <h3 className="text-lg font-semibold mb-3">Permissions</h3>
                <div className="flex flex-wrap gap-2">
                  {member.role.permissions.map((permission, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {permission.replace(/_/g, ' ').toLowerCase()}
                    </Badge>
                  ))}
                </div>
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}