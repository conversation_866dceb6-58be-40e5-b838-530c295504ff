'use client'

import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { OnboardingStep, OnboardingData } from '@/app/(public)/onboarding/page'
import { 
  checkAvailabilitySchema, 
  initiateOnboardingSchema, 
  completeOnboardingSchema, 
  setupInitialDataSchema,
  CheckAvailabilityForm,
  InitiateOnboardingForm,
  CompleteOnboardingForm,
  SetupInitialDataForm
} from '@/lib/validations'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Separator } from '@/components/ui/separator'
import { LoadingButton } from '@/components/forms/LoadingButton'
import { Check, ChevronLeft, ChevronRight, Building, User, Settings, Zap, CheckCircle } from 'lucide-react'

interface OnboardingStepsProps {
  currentStep: OnboardingStep
  data: OnboardingData
  loading: boolean
  onStepComplete: (data: Partial<OnboardingData>) => Promise<void>
  onPrevious: () => void
}

export function OnboardingSteps({ currentStep, data, loading, onStepComplete, onPrevious }: OnboardingStepsProps) {
  const steps = [
    { key: 'availability', title: 'Check Availability', icon: Check },
    { key: 'initiation', title: 'Church Setup', icon: Building },
    { key: 'completion', title: 'Complete Setup', icon: User },
    { key: 'verification', title: 'Email Verification', icon: Settings },
    { key: 'setup', title: 'Initial Setup', icon: Zap },
    { key: 'finished', title: 'Finished', icon: CheckCircle },
  ]

  const getCurrentStepIndex = () => steps.findIndex(step => step.key === currentStep)

  return (
    <div className="space-y-6">
      {/* Progress Indicator */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isActive = step.key === currentStep
          const isCompleted = index < getCurrentStepIndex()
          const Icon = step.icon

          return (
            <div key={step.key} className="flex items-center">
              <div className={`
                flex items-center justify-center w-10 h-10 rounded-full border-2 
                ${isActive ? 'border-primary bg-primary text-primary-foreground' : 
                  isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                  'border-gray-300 bg-gray-100 text-gray-400'}
              `}>
                <Icon className="w-5 h-5" />
              </div>
              <div className="ml-2 hidden sm:block">
                <p className={`text-sm font-medium ${isActive ? 'text-primary' : isCompleted ? 'text-green-600' : 'text-gray-500'}`}>
                  {step.title}
                </p>
              </div>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
              )}
            </div>
          )
        })}
      </div>

      {/* Step Content */}
      <Card>
        <CardContent className="pt-6">
          {currentStep === 'availability' && (
            <AvailabilityStep data={data} onNext={onStepComplete} loading={loading} />
          )}
          {currentStep === 'initiation' && (
            <InitiationStep data={data} onNext={onStepComplete} onPrevious={onPrevious} loading={loading} />
          )}
          {currentStep === 'completion' && (
            <CompletionStep data={data} onNext={onStepComplete} onPrevious={onPrevious} loading={loading} />
          )}
          {currentStep === 'verification' && (
            <VerificationStep data={data} onNext={onStepComplete} loading={loading} />
          )}
          {currentStep === 'setup' && (
            <SetupStep data={data} onNext={onStepComplete} onPrevious={onPrevious} loading={loading} />
          )}
          {currentStep === 'finished' && (
            <FinishedStep data={data} />
          )}
        </CardContent>
      </Card>
    </div>
  )
}

function AvailabilityStep({ data, onNext, loading }: {
  data: OnboardingData
  onNext: (data: Partial<OnboardingData>) => Promise<void>
  loading: boolean
}) {
  const form = useForm<CheckAvailabilityForm>({
    resolver: zodResolver(checkAvailabilitySchema),
    defaultValues: {
      churchSlug: data.churchSlug,
      adminEmail: data.adminEmail,
    },
  })

  const onSubmit = async (values: CheckAvailabilityForm) => {
    await onNext(values)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Check Availability</h3>
        <p className="text-sm text-muted-foreground">
          First, let&apos;s check if your desired church slug and admin email are available.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="churchSlug"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Church Slug</FormLabel>
                <FormControl>
                  <Input placeholder="my-church" {...field} />
                </FormControl>
                <FormDescription>
                  This will be your church&apos;s unique identifier in URLs (lowercase letters, numbers, and hyphens only)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="adminEmail"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Admin Email</FormLabel>
                <FormControl>
                  <Input type="email" placeholder="<EMAIL>" {...field} />
                </FormControl>
                <FormDescription>
                  This email will be used for the church administrator account
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <LoadingButton type="submit" loading={loading} className="w-full">
            Check Availability
            <ChevronRight className="w-4 h-4 ml-2" />
          </LoadingButton>
        </form>
      </Form>
    </div>
  )
}

function InitiationStep({ data, onNext, onPrevious, loading }: {
  data: OnboardingData
  onNext: (data: Partial<OnboardingData>) => Promise<void>
  onPrevious: () => void
  loading: boolean
}) {
  const form = useForm<InitiateOnboardingForm>({
    resolver: zodResolver(initiateOnboardingSchema),
    defaultValues: data,
  })

  const onSubmit = async (values: InitiateOnboardingForm) => {
    await onNext(values)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Church & Admin Setup</h3>
        <p className="text-sm text-muted-foreground">
          Provide your church information and create the administrator account.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Church Information */}
          <div className="space-y-4">
                  Church Information
            
            <FormField
              control={form.control}
              name="church.name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Church Name</FormLabel>
                  <FormControl>
                    <Input placeholder="First Baptist Church" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="church.slug"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Church Slug</FormLabel>
                  <FormControl>
                    <Input placeholder="first-baptist" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="church.description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Brief description of your church" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="church.email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Church Email (Optional)</FormLabel>
                    <FormControl>
                      <Input type="email" placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="church.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Church Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="(*************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="church.address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="123 Main St, City, State 12345" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="church.website"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Website (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="https://www.church.com" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <Separator />

          {/* Admin Information */}
          <div className="space-y-4">
            <h4 className="font-medium">Administrator Account</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="admin.firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>First Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="admin.lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Last Name</FormLabel>
                    <FormControl>
                      <Input placeholder="Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="admin.email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input type="email" placeholder="<EMAIL>" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="admin.password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Password</FormLabel>
                  <FormControl>
                    <Input type="password" placeholder="********" {...field} />
                  </FormControl>
                  <FormDescription>
                    Password must be at least 8 characters long
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="admin.phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="(*************" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="admin.gender"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Gender (Optional)</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select gender" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="male">Male</SelectItem>
                        <SelectItem value="female">Female</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                        <SelectItem value="prefer_not_to_say">Prefer not to say</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={onPrevious}>
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            <LoadingButton type="submit" loading={loading} className="flex-1">
              Create Church
              <ChevronRight className="w-4 h-4 ml-2" />
            </LoadingButton>
          </div>
        </form>
      </Form>
    </div>
  )
}

function CompletionStep({ data, onNext, onPrevious, loading }: {
  data: OnboardingData
  onNext: (data: Partial<OnboardingData>) => Promise<void>
  onPrevious: () => void
  loading: boolean
}) {
  const form = useForm<CompleteOnboardingForm>({
    resolver: zodResolver(completeOnboardingSchema),
    defaultValues: {
      onboardingToken: data.onboardingToken || '',
      confirmPassword: '',
      agreeToTerms: false,
      subscribeToUpdates: false,
    },
  })

  const onSubmit = async (values: CompleteOnboardingForm) => {
    await onNext(values)
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Complete Setup</h3>
        <p className="text-sm text-muted-foreground">
          Confirm your password and agree to our terms to complete the setup.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Confirm Password</FormLabel>
                <FormControl>
                  <Input type="password" placeholder="********" {...field} />
                </FormControl>
                <FormDescription>
                  Re-enter your admin password to confirm
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="agreeToTerms"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>
                  I agree to the Terms of Service and Privacy Policy
                  </FormLabel>
                  <FormDescription>
                    You must agree to our terms to continue
                  </FormDescription>
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="subscribeToUpdates"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                <FormControl>
                  <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Subscribe to product updates</FormLabel>
                  <FormDescription>
                    Receive occasional emails about new features and improvements
                  </FormDescription>
                </div>
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={onPrevious}>
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            <LoadingButton type="submit" loading={loading} className="flex-1">
              Complete Setup
              <ChevronRight className="w-4 h-4 ml-2" />
            </LoadingButton>
          </div>
        </form>
      </Form>
    </div>
  )
}

function VerificationStep({ data, onNext, loading }: {
  data: OnboardingData
  onNext: (data: Partial<OnboardingData>) => Promise<void>
  loading: boolean
}) {
  const handleVerify = async () => {
    await onNext({ verificationToken: data.verificationToken })
  }

  return (
    <div className="space-y-4 text-center">
      <div>
        <h3 className="text-lg font-semibold">Email Verification</h3>
        <p className="text-sm text-muted-foreground">
          Please verify your email address to complete the setup.
        </p>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <p className="text-sm">
          A verification email has been sent to <strong>{data.admin.email}</strong>
        </p>
        <p className="text-xs text-muted-foreground mt-2">
          Click the verification link in your email to continue.
        </p>
      </div>

      {data.verificationToken && (
        <LoadingButton onClick={handleVerify} loading={loading} className="w-full">
          Verify Email
          <ChevronRight className="w-4 h-4 ml-2" />
        </LoadingButton>
      )}
    </div>
  )
}

function SetupStep({ data, onNext, onPrevious, loading }: {
  data: OnboardingData
  onNext: (data: Partial<OnboardingData>) => Promise<void>
  onPrevious: () => void
  loading: boolean
}) {
  const form = useForm<SetupInitialDataForm>({
    resolver: zodResolver(setupInitialDataSchema),
    defaultValues: {
      createSampleData: false,
      setupBranches: [],
      setupRoles: [],
    },
  })

  const onSubmit = async (values: SetupInitialDataForm) => {
    await onNext(values)
  }

  const skipSetup = async () => {
    await onNext({ createSampleData: false, setupBranches: [], setupRoles: [] })
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">Initial Setup (Optional)</h3>
        <p className="text-sm text-muted-foreground">
          Set up additional branches, roles, or sample data for your church.
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <FormField
            control={form.control}
            name="createSampleData"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Create Sample Data</FormLabel>
                  <FormDescription>
                    Add sample members, events, and other data to help you get started
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch checked={field.value} onCheckedChange={field.onChange} />
                </FormControl>
              </FormItem>
            )}
          />

          <div className="flex gap-2">
            <Button type="button" variant="outline" onClick={onPrevious}>
              <ChevronLeft className="w-4 h-4 mr-2" />
              Previous
            </Button>
            <Button type="button" variant="outline" onClick={skipSetup} className="flex-1">
              Skip Setup
            </Button>
            <LoadingButton type="submit" loading={loading} className="flex-1">
              Complete Setup
              <ChevronRight className="w-4 h-4 ml-2" />
            </LoadingButton>
          </div>
        </form>
      </Form>
    </div>
  )
}

 
function FinishedStep({ data }: { data: OnboardingData }) {
  return (
    <div className="space-y-4 text-center">
      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
        <CheckCircle className="w-8 h-8 text-green-600" />
      </div>
      
      <div>
        <h3 className="text-lg font-semibold">Setup Complete!</h3>
        <p className="text-sm text-muted-foreground">
          Your church management system is ready to use.
        </p>
      </div>

      <div className="bg-muted p-4 rounded-lg">
        <p className="text-sm font-medium">{data.church.name}</p>
        <p className="text-xs text-muted-foreground">/{data.church.slug}</p>
      </div>

      <p className="text-sm text-muted-foreground">
        Redirecting to your dashboard...
      </p>
    </div>
  )
}