'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/lib/auth'
import { apiClient } from '@/lib/api'
import { Event, Branch } from '@/types'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from '@/components/ui/form'
import { ImageUpload } from '@/components/ui/image-upload'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'

const updateEventSchema = z.object({
  title: z.string().min(1, 'Event title is required'),
  description: z.string().optional(),
  type: z.string().min(1, 'Event type is required'),
  startDate: z.string().min(1, 'Start date is required'),
  startTime: z.string().min(1, 'Start time is required'),
  endDate: z.string().min(1, 'End date is required'),
  endTime: z.string().min(1, 'End time is required'),
  location: z.string().optional(),
  virtualLink: z.string().url().optional().or(z.literal('')),
  maxAttendees: z.number().min(1).optional(),
  isPublic: z.boolean(),
  allowRsvp: z.boolean(),
  allowDonations: z.boolean(),
  branchId: z.string().optional(),
  imageUrl: z.string()
    .refine((url) => {
      if (!url) return true; // Optional field
      // Allow regular URLs or data URLs
      return url.match(/^https?:\/\/.+/) || url.match(/^data:image\/(jpeg|jpg|png|gif|webp);base64,/);
    }, {
      message: "Must be a valid URL or image file"
    })
    .optional()
    .or(z.literal('')),
})

type UpdateEventFormData = z.infer<typeof updateEventSchema>

interface EditEventFormProps {
  event: Event
  branches: Branch[]
  onSuccess: () => void
}

export function EditEventForm({ event, branches, onSuccess }: EditEventFormProps) {
  const { church } = useAuth()
  const [loading, setLoading] = useState(false)

  const isInvalidEvent = !event || !event.id

  // Convert ISO datetime to date and time strings with robust parsing
  const getDateString = (isoString: string | null | undefined) => {
    if (!isoString) return ''
    
    // Handle different date formats
    let date: Date
    if (typeof isoString === 'string') {
      // Try parsing as ISO string first
      date = new Date(isoString)
      
      // If invalid, try parsing as a simpler date format
      if (isNaN(date.getTime())) {
        // Try parsing without timezone info if it's causing issues
        const cleanedString = isoString.replace(/\.\d{3}Z?$/, '')
        date = new Date(cleanedString)
      }
    } else {
      date = new Date(isoString)
    }
    
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string:', isoString)
      return ''
    }
    
    return date.toISOString().split('T')[0]
  }

  const getTimeString = (isoString: string | null | undefined) => {
    if (!isoString) return ''
    
    // Handle different date formats
    let date: Date
    if (typeof isoString === 'string') {
      // Try parsing as ISO string first
      date = new Date(isoString)
      
      // If invalid, try parsing as a simpler date format
      if (isNaN(date.getTime())) {
        // Try parsing without timezone info if it's causing issues
        const cleanedString = isoString.replace(/\.\d{3}Z?$/, '')
        date = new Date(cleanedString)
      }
    } else {
      date = new Date(isoString)
    }
    
    if (isNaN(date.getTime())) {
      console.warn('Invalid date string:', isoString)
      return ''
    }
    
    return date.toTimeString().slice(0, 5)
  }

  // Convert backend format to display format
  const backendToDisplayType = (backendType: string) => {
    const reverseMapping: Record<string, string> = {
      'sunday_service': 'Sunday Service',
      'bible_study': 'Bible Study',
      'prayer_meeting': 'Prayer Meeting',
      'youth_service': 'Youth Service',
      'choir_practice': 'Choir Practice',
      'community_outreach': 'Community Outreach',
      'fundraiser': 'Fundraiser',
      'conference': 'Conference',
      'retreat': 'Retreat',
      'wedding': 'Wedding',
      'funeral': 'Funeral',
      'baptism': 'Baptism',
      'communion': 'Communion',
      'special_service': 'Special Service',
      'social_event': 'Social Event',
      'other': 'Other'
    }
    return reverseMapping[backendType] || 'Other'
  }

  const form = useForm<UpdateEventFormData>({
    resolver: zodResolver(updateEventSchema),
    defaultValues: {
      title: event?.title || '',
      description: event?.description || '',
      type: backendToDisplayType(event?.type || ''),
      startDate: getDateString(event?.startDate),
      startTime: getTimeString(event?.startDate),
      endDate: getDateString(event?.endDate),
      endTime: getTimeString(event?.endDate),
      location: event?.location || '',
      virtualLink: event?.virtualLink || '',
      maxAttendees: event?.maxAttendees || undefined,
      isPublic: event?.isPublic ?? true,
      allowRsvp: event?.requiresRsvp ?? false,
      allowDonations: event?.allowDonations ?? false,
      branchId: event?.branchId || 'none',
      imageUrl: event?.imageUrl || '',
    },
  })

  // Reset form when event changes
  useEffect(() => {
    if (event) {
      form.reset({
        title: event.title || '',
        description: event.description || '',
        type: backendToDisplayType(event.type || ''),
        startDate: getDateString(event.startDate),
        startTime: getTimeString(event.startDate),
        endDate: getDateString(event.endDate),
        endTime: getTimeString(event.endDate),
        location: event.location || '',
        virtualLink: event.virtualLink || '',
        maxAttendees: event.maxAttendees || undefined,
        isPublic: event.isPublic,
        allowRsvp: event.requiresRsvp || false,
        allowDonations: event.allowDonations,
        branchId: event.branchId || 'none',
        imageUrl: event.imageUrl || '',
      })
    }
  }, [event, form])

  const onSubmit = async (data: UpdateEventFormData) => {
    if (!church?.slug || !event?.id) {
      toast.error('Missing required information')
      return
    }

    try {
      setLoading(true)
      
      // Combine date and time into ISO strings
      const startDateTime = new Date(`${data.startDate}T${data.startTime}`).toISOString()
      const endDateTime = new Date(`${data.endDate}T${data.endTime}`).toISOString()

      // Convert display format to backend format  
      const typeMapping: Record<string, string> = {
        'Sunday Service': 'sunday_service',
        'Bible Study': 'bible_study', 
        'Prayer Meeting': 'prayer_meeting',
        'Youth Service': 'youth_service',
        'Choir Practice': 'choir_practice',
        'Community Outreach': 'community_outreach',
        'Fundraiser': 'fundraiser',
        'Conference': 'conference',
        'Retreat': 'retreat',
        'Wedding': 'wedding',
        'Funeral': 'funeral',
        'Baptism': 'baptism',
        'Communion': 'communion',
        'Special Service': 'special_service',
        'Social Event': 'social_event',
        'Other': 'other'
      }

      // Prepare data for API
      const eventData = {
        title: data.title,
        description: data.description || undefined,
        type: typeMapping[data.type] || 'other',
        startDate: startDateTime,
        endDate: endDateTime,
        location: data.location || undefined,
        virtualLink: data.virtualLink || undefined,
        maxAttendees: data.maxAttendees || undefined,
        isPublic: data.isPublic,
        requiresRsvp: data.allowRsvp,
        allowDonations: data.allowDonations,
        branchId: data.branchId === 'none' ? undefined : data.branchId || undefined,
        imageUrl: data.imageUrl || undefined,
      }

      await apiClient.updateEvent(church.slug, event.id!, eventData)
      
      toast.success('Event updated successfully!')
      onSuccess()
    } catch (error: any) {
      console.error('Error updating event:', error)
      
      // Handle specific error types
      if (error.status === 413 || error.code === 'REQUEST_TOO_LARGE' || error.message?.includes('Request too large')) {
        toast.error('Image is too large. Please try a smaller image or use an image URL instead.')
        // Clear the image field to allow user to try again
        form.setValue('imageUrl', '')
      } else if (error.message?.includes('400') && error.message?.includes('image')) {
        toast.error('Invalid image format. Please use JPG, PNG, GIF, or WebP.')
      } else {
        toast.error(error.message || 'Failed to update event. Please try again.')
      }
    } finally {
      setLoading(false)
    }
  }

  if (isInvalidEvent) {
    return (
      <div className="text-center p-4">
        <p className="text-muted-foreground">No valid event data provided</p>
      </div>
    )
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium border-b pb-2">Basic Information</h3>
          
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Event Title *</FormLabel>
                <FormControl>
                  <Input placeholder="Sunday Morning Service" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Brief description of the event..." 
                    className="min-h-[100px]"
                    {...field} 
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Type *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select event type" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Sunday Service">Sunday Service</SelectItem>
                      <SelectItem value="Bible Study">Bible Study</SelectItem>
                      <SelectItem value="Prayer Meeting">Prayer Meeting</SelectItem>
                      <SelectItem value="Youth Service">Youth Service</SelectItem>
                      <SelectItem value="Choir Practice">Choir Practice</SelectItem>
                      <SelectItem value="Community Outreach">Community Outreach</SelectItem>
                      <SelectItem value="Fundraiser">Fundraiser</SelectItem>
                      <SelectItem value="Conference">Conference</SelectItem>
                      <SelectItem value="Retreat">Retreat</SelectItem>
                      <SelectItem value="Wedding">Wedding</SelectItem>
                      <SelectItem value="Funeral">Funeral</SelectItem>
                      <SelectItem value="Baptism">Baptism</SelectItem>
                      <SelectItem value="Communion">Communion</SelectItem>
                      <SelectItem value="Special Service">Special Service</SelectItem>
                      <SelectItem value="Social Event">Social Event</SelectItem>
                      <SelectItem value="Other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="branchId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location/Branch</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select branch (optional)" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                       <SelectItem value="none">No specific branch</SelectItem>
                      {branches.map((branch) => (
                        <SelectItem key={branch.id} value={branch.id}>
                          {branch.name} {branch.isMain && '(Main)'}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Date and Time */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium border-b pb-2">Date and Time</h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Date *</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="startTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Time *</FormLabel>
                  <FormControl>
                    <Input type="time" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Date *</FormLabel>
                  <FormControl>
                    <Input type="date" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="endTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Time *</FormLabel>
                  <FormControl>
                    <Input type="time" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Location and Virtual */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium border-b pb-2">Location Details</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Physical Location</FormLabel>
                  <FormControl>
                    <Input placeholder="Main Sanctuary, Fellowship Hall, etc." {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="virtualLink"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Virtual Link</FormLabel>
                  <FormControl>
                    <Input placeholder="https://zoom.us/j/..." {...field} />
                  </FormControl>
                  <FormDescription>
                    Link for online participation (Zoom, YouTube Live, etc.)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Event Image */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium border-b pb-2">Event Image</h3>
          
          <FormField
            control={form.control}
            name="imageUrl"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <ImageUpload
                    value={field.value}
                    onChange={field.onChange}
                    onRemove={() => field.onChange('')}
                    disabled={loading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Event Settings */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium border-b pb-2">Event Settings</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <FormField
                control={form.control}
                name="maxAttendees"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Maximum Attendees</FormLabel>
                    <FormControl>
                      <Input 
                        type="number" 
                        placeholder="Leave empty for unlimited" 
                        {...field}
                        onChange={(e) => field.onChange(e.target.value ? parseInt(e.target.value) : undefined)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-4">
              <FormField
                control={form.control}
                name="isPublic"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Public Event</FormLabel>
                      <FormDescription>
                        Allow non-members to view and participate
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="allowRsvp"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Allow RSVPs</FormLabel>
                      <FormDescription>
                        Let people register their attendance
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="allowDonations"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Allow Event Donations</FormLabel>
                      <FormDescription>
                        Enable donations specific to this event
                      </FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end pt-6 border-t">
          <Button type="submit" disabled={loading} size="lg">
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Update Event
          </Button>
        </div>
      </form>
    </Form>
  )
}