'use client'

import { AlertCircle, CheckCircle, Info, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { cn } from '@/lib/utils'

interface FormAlertProps {
  type: 'error' | 'success' | 'info' | 'warning'
  message: string
  className?: string
}

const alertConfig = {
  error: {
    icon: AlertCircle,
    variant: 'destructive' as const,
    className: 'border-red-200 bg-red-50 text-red-800'
  },
  success: {
    icon: CheckCircle,
    variant: 'default' as const,
    className: 'border-green-200 bg-green-50 text-green-800'
  },
  info: {
    icon: Info,
    variant: 'default' as const,
    className: 'border-blue-200 bg-blue-50 text-blue-800'
  },
  warning: {
    icon: AlertTriangle,
    variant: 'default' as const,
    className: 'border-yellow-200 bg-yellow-50 text-yellow-800'
  }
}

export function FormAlert({ type, message, className }: FormAlertProps) {
  const config = alertConfig[type]
  const Icon = config.icon

  return (
    <Alert variant={config.variant} className={cn(config.className, className)}>
      <Icon className="h-4 w-4" />
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  )
}