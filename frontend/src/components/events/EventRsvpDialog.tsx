'use client'

import { useState } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Input } from '@/components/ui/input'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Event, RSVP } from '@/types'
import { api } from '@/lib/api'
import { useAuth } from '@/lib/auth'
import { toast } from 'sonner'
import { Loader2 } from 'lucide-react'

interface EventRsvpDialogProps {
  event: Event
  currentRsvp?: RSVP
  onClose: () => void
  onRsvpUpdate: (rsvp: RSVP | null) => void
}

export default function EventRsvpDialog({
  event,
  currentRsvp,
  onClose,
  onRsvpUpdate
}: EventRsvpDialogProps) {
  const { church } = useAuth()
  const [status, setStatus] = useState<string>(currentRsvp?.status || 'attending')
  const [attendeeCount, setAttendeeCount] = useState(currentRsvp?.attendeeCount || 1)
  const [notes, setNotes] = useState(currentRsvp?.notes || '')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleSubmit = async () => {
    if (!church) return

    setIsSubmitting(true)
    try {
      const data = {
        status: status as 'pending' | 'attending' | 'not_attending' | 'maybe',
        attendeeCount,
        notes: notes.trim() || undefined
      }

      let response
      if (currentRsvp) {
        response = await api.updateEventRsvp(church.slug, event.id, data)
      } else {
        response = await api.createEventRsvp(church.slug, event.id, data)
      }

      onRsvpUpdate(response.data.rsvp)
      toast.success(currentRsvp ? 'RSVP updated successfully!' : 'RSVP created successfully!')
      onClose()
    } catch (error) {
      console.error('Error submitting RSVP:', error)
      toast.error('Failed to submit RSVP')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDelete = async () => {
    if (!church || !currentRsvp) return

    setIsDeleting(true)
    try {
      await api.deleteEventRsvp(church.slug, event.id)
      onRsvpUpdate(null)
      toast.success('RSVP removed successfully!')
      onClose()
    } catch (error) {
      console.error('Error deleting RSVP:', error)
      toast.error('Failed to remove RSVP')
    } finally {
      setIsDeleting(false)
    }
  }

  const getStatusLabel = (value: string) => {
    switch (value) {
      case 'attending':
        return 'Yes, I\'ll be there'
      case 'maybe':
        return 'Maybe'
      case 'not_attending':
        return 'Can\'t make it'
      default:
        return 'Pending'
    }
  }

  const getStatusDescription = (value: string) => {
    switch (value) {
      case 'attending':
        return 'I plan to attend this event'
      case 'maybe':
        return 'I might attend, not sure yet'
      case 'not_attending':
        return 'I won\'t be able to attend'
      default:
        return 'Haven\'t decided yet'
    }
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>
            {currentRsvp ? 'Update RSVP' : 'RSVP to Event'}
          </DialogTitle>
          <DialogDescription>
            Let us know if you'll be attending "{event.title}"
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <Label className="text-base font-medium">Will you attend?</Label>
            <RadioGroup
              value={status}
              onValueChange={setStatus}
              className="mt-3"
            >
              {['attending', 'maybe', 'not_attending'].map((value) => (
                <div key={value} className="flex items-start space-x-3">
                  <RadioGroupItem value={value} id={value} className="mt-1" />
                  <div className="flex-1">
                    <Label 
                      htmlFor={value} 
                      className="text-sm font-medium cursor-pointer"
                    >
                      {getStatusLabel(value)}
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      {getStatusDescription(value)}
                    </p>
                  </div>
                </div>
              ))}
            </RadioGroup>
          </div>

          {status === 'attending' && (
            <div>
              <Label htmlFor="attendeeCount">Number of attendees</Label>
              <Input
                id="attendeeCount"
                type="number"
                min="1"
                max="10"
                value={attendeeCount}
                onChange={(e) => setAttendeeCount(parseInt(e.target.value) || 1)}
                className="mt-1"
              />
              <p className="text-xs text-muted-foreground mt-1">
                Including yourself, how many people will attend?
              </p>
            </div>
          )}

          <div>
            <Label htmlFor="notes">Additional notes (optional)</Label>
            <Textarea
              id="notes"
              placeholder="Any special requirements, questions, or comments..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              className="mt-1"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          {currentRsvp && (
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting || isSubmitting}
              className="w-full sm:w-auto"
            >
              {isDeleting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Remove RSVP
            </Button>
          )}
          
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting || isDeleting}
              className="flex-1 sm:flex-none"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || isDeleting}
              className="flex-1 sm:flex-none"
            >
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              {currentRsvp ? 'Update RSVP' : 'Submit RSVP'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}