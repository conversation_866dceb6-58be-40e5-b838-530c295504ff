'use client'
import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Heart,
  MessageCircle,
  Calendar,
  MapPin,
  Users,
  Clock,
  ExternalLink,
  ChevronRight
} from 'lucide-react'
import { Event, RSVP } from '@/types'
import { formatDate, formatTime } from '@/lib/utils'
import { api } from '@/lib/api'
import { useAuth } from '@/lib/auth'
import { toast } from 'sonner'
import EventRsvpDialog from './EventRsvpDialog'
import EventCommentsDialog from './EventCommentsDialog'

interface EventCardProps {
  event: Event
  onEventUpdate?: (updatedEvent: Event) => void
  showActions?: boolean
}

export default function EventCard({
  event,
  onEventUpdate,
  showActions = true
}: EventCardProps) {
  const { user, church } = useAuth()
  const [isLiked, setIsLiked] = useState(event.socialStats?.userLiked || false)
  const [likesCount, setLikesCount] = useState(event.socialStats?.likesCount || 0)
  const [isLiking, setIsLiking] = useState(false)
  const [showRsvpDialog, setShowRsvpDialog] = useState(false)
  const [showCommentsDialog, setShowCommentsDialog] = useState(false)
  const [userRsvp, setUserRsvp] = useState<RSVP | undefined>(event.socialStats?.userRsvp)

  const handleLike = async () => {
    if (!church || !user || isLiking) return
    setIsLiking(true)
    try {
      const response = await api.toggleEventLike(church.slug, event.id)
      const liked = (response.data as any).liked
      setIsLiked(liked)
      setLikesCount(prev => liked ? prev + 1 : prev - 1)
      toast.success(liked ? 'Event liked!' : 'Event unliked!')
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to update like')
    } finally {
      setIsLiking(false)
    }
  }

  const handleRsvpUpdate = (rsvp: RSVP | null) => {
    setUserRsvp(rsvp || undefined)
    if (onEventUpdate) {
      const updatedEvent = {
        ...event,
        socialStats: {
          likesCount: event.socialStats?.likesCount || 0,
          commentsCount: event.socialStats?.commentsCount || 0,
          userLiked: event.socialStats?.userLiked || false,
          userRsvp: rsvp || undefined
        }
      }
      onEventUpdate(updatedEvent)
    }
  }

  const getRsvpStatusColor = (status: string) => {
    switch (status) {
      case 'attending':
        return 'bg-emerald-500/10 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-400 border border-emerald-500/20'
      case 'maybe':
        return 'bg-amber-500/10 text-amber-600 dark:bg-amber-500/20 dark:text-amber-400 border border-amber-500/20'
      case 'not_attending':
        return 'bg-rose-500/10 text-rose-600 dark:bg-rose-500/20 dark:text-rose-400 border border-rose-500/20'
      default:
        return 'bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-300 border border-slate-200 dark:border-slate-700'
    }
  }

  const getRsvpStatusText = (status: string) => {
    switch (status) {
      case 'attending':
        return 'Going'
      case 'maybe':
        return 'Maybe'
      case 'not_attending':
        return 'Not Going'
      default:
        return 'RSVP'
    }
  }

  return (
    <>
      <Card className="group relative overflow-hidden border border-border/50 hover:shadow-xl hover:shadow-primary/5 transition-all duration-300 bg-card rounded-xl pt-0">
        {/* Enhanced Image Container */}
        <div className="relative">
          <div className="relative overflow-hidden rounded-t-xl">
            {event.imageUrl ? (
              <div className="relative w-full h-full">
                <img
                  src={event.imageUrl}
                  alt={event.title}
                  className="w-full h-full object-cover object-center transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent pointer-events-none" />

                {/* Date Badge Overlay */}
                <div className="absolute bottom-4 left-4 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-lg px-3 py-1.5 shadow-lg border border-white/20 dark:border-slate-700/50">
                  <div className="text-sm font-semibold text-primary">
                    {new Date(event.startDate).getDate()}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    {formatDate(event.startDate).split(' ')[1]?.slice(0, 3)}
                  </div>
                </div>
              </div>
            ) : (
              <div className="h-full bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-800/50 dark:to-slate-900 flex items-center justify-center">
                <div className="text-center">
                  <div className="bg-slate-200 dark:bg-slate-700/50 p-4 rounded-full inline-block mb-2">
                    <Calendar className="w-8 h-8 text-slate-600 dark:text-slate-300" />
                  </div>
                  <p className="text-sm font-medium text-slate-600 dark:text-slate-400">No image available</p>
                </div>
              </div>
            )}
          </div>

          {/* Badges Container */}
          <div className="absolute top-3 right-3 flex flex-col gap-2">
            {event.requiresRsvp && (
              <Badge
                className="bg-white/10 text-primary border border-primary/20 px-2.5 py-1 h-auto whitespace-nowrap"
                variant="secondary"
              >
                RSVP Required
              </Badge>
            )}
            {userRsvp && (
              <Badge
                className={`px-2.5 py-1 h-auto whitespace-nowrap ${getRsvpStatusColor(userRsvp.status)}`}
                variant="secondary"
              >
                {getRsvpStatusText(userRsvp.status)}
              </Badge>
            )}
          </div>

          <Badge
            className="absolute top-3 left-3 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border border-white/20 dark:border-slate-700/50 px-2.5 py-1 h-auto whitespace-nowrap shadow-sm"
            variant="secondary"
          >
            {event.type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        </div>

        {/* Content */}
        <CardContent className="ps-5 pt-0 pe-5 ">
          {/* Title */}
          <h3 className="font-bold text-lg leading-tight mb-3 line-clamp-2 group-hover:text-primary transition-colors duration-200 min-h-[2.4rem]">
            {event.title}
          </h3>

          {/* Event Details */}
          <div className="space-y-2.5 mb-4">
            {/* Date and Time */}
            <div className="flex items-center gap-2.5 text-sm">
              <div className="w-8 h-8 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                <Calendar className="w-4 h-4 text-primary" />
              </div>
              <div>
                <p className="font-medium text-foreground">{formatDate(event.startDate)}</p>
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <Clock className="w-3.5 h-3.5" /> {formatTime(event.startDate)}
                </p>
              </div>
            </div>

            {/* Location */}
            {event.location && (
              <div className="flex items-center gap-2.5 text-sm">
                <div className="w-8 h-8 rounded-lg bg-emerald-500/10 flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-4 h-4 text-emerald-600" />
                </div>
                <span className="text-muted-foreground line-clamp-1">{event.location}</span>
              </div>
            )}

            {/* Attendance Stats */}
            {event.rsvpStats && (
              <div className="flex items-center gap-2.5 text-sm pl-0.5">
                <div className="w-7 h-7 rounded-lg bg-orange-500/10 flex items-center justify-center flex-shrink-0">
                  <Users className="w-3.5 h-3.5 text-orange-600" />
                </div>
                <div className="text-muted-foreground flex flex-wrap gap-x-3">
                  <span className="flex items-center gap-1">
                    <span className="font-semibold text-emerald-600">{event.rsvpStats.attending}</span>
                    <span>Going</span>
                  </span>
                  {event.rsvpStats.maybe > 0 && (
                    <span className="flex items-center gap-1">
                      <span className="font-semibold text-amber-600">{event.rsvpStats.maybe}</span>
                      <span>Maybe</span>
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* Virtual Link */}
            {event.virtualLink && (
              <div className="flex items-center gap-2.5 text-sm pl-0.5">
                <div className="w-7 h-7 rounded-lg bg-purple-500/10 flex items-center justify-center flex-shrink-0">
                  <ExternalLink className="w-3.5 h-3.5 text-purple-600" />
                </div>
                <a
                  href={event.virtualLink}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-purple-600 hover:text-purple-700 font-medium hover:underline flex items-center gap-1 transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  Join Virtual Event
                  <ChevronRight className="w-3.5 h-3.5" />
                </a>
              </div>
            )}
          </div>

          {/* Description */}
          {event.description && (
            <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed mb-4 min-h-[2.4rem]">
              {event.description}
            </p>
          )}

          {/* Actions */}
          {showActions && (
            <div className="pt-3 border-t border-border/50">
              <div className="flex items-center justify-between">
                {/* Social Actions */}
                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      handleLike()
                    }}
                    disabled={isLiking}
                    className={`h-9 px-3 rounded-lg transition-all ${isLiked
                        ? 'bg-red-50 text-red-600 hover:bg-red-100 dark:bg-red-950 dark:text-red-400'
                        : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}
                  >
                    <Heart
                      className={`w-4 h-4 transition-all ${isLiked ? 'fill-current scale-110' : ''
                        }`}
                    />
                    <span className="ml-1.5 font-medium text-sm">{likesCount}</span>
                  </Button>

                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      setShowCommentsDialog(true)
                    }}
                    className="h-9 px-3 rounded-lg text-muted-foreground hover:text-foreground hover:bg-muted"
                  >
                    <MessageCircle className="w-4 h-4" />
                    <span className="ml-1.5 font-medium text-sm">{event.socialStats?.commentsCount || 0}</span>
                  </Button>
                </div>

                {/* RSVP Button */}
                {event.requiresRsvp && (
                  <Button
                    variant={userRsvp ? "outline" : "default"}
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      setShowRsvpDialog(true)
                    }}
                    className={`h-9 px-4 rounded-lg font-medium transition-all ${userRsvp ? 'text-sm' : 'font-semibold'
                      }`}
                  >
                    {userRsvp ? 'Update RSVP' : 'RSVP Now'}
                  </Button>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {showRsvpDialog && (
        <EventRsvpDialog
          event={event}
          currentRsvp={userRsvp}
          onClose={() => setShowRsvpDialog(false)}
          onRsvpUpdate={handleRsvpUpdate}
        />
      )}

      {showCommentsDialog && (
        <EventCommentsDialog
          event={event}
          onClose={() => setShowCommentsDialog(false)}
        />
      )}
    </>
  )
}