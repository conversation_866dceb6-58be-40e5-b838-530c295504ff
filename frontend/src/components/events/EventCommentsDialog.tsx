'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Event, EventComment } from '@/types'
import { api } from '@/lib/api'
import { useAuth } from '@/lib/auth'
import { toast } from 'sonner'
import { formatDistanceToNow } from 'date-fns'
import { Loader2, MessageCircle, MoreHorizontal, Reply, Edit, Trash2 } from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface EventCommentsDialogProps {
  event: Event
  onClose: () => void
}

interface CommentItemProps {
  comment: EventComment
  onReply: (parentId: string) => void
  onEdit: (comment: EventComment) => void
  onDelete: (commentId: string) => void
  isReply?: boolean
}

function CommentItem({ comment, onReply, onEdit, onDelete, isReply = false }: CommentItemProps) {
  const { user } = useAuth()
  const isOwner = user?.id === comment.memberId

  return (
    <div className={`flex gap-3 ${isReply ? 'ml-8 mt-2' : ''}`}>
      <Avatar className="w-8 h-8 flex-shrink-0">
        <AvatarImage src={comment.member.profileImage} />
        <AvatarFallback>
          {comment.member.firstName[0]}{comment.member.lastName[0]}
        </AvatarFallback>
      </Avatar>
      
      <div className="flex-1 min-w-0">
        <div className="bg-muted rounded-lg p-3">
          <div className="flex items-center justify-between mb-1">
            <p className="text-sm font-medium">
              {comment.member.firstName} {comment.member.lastName}
            </p>
            {isOwner && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreHorizontal className="w-3 h-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit(comment)}>
                    <Edit className="w-4 h-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem 
                    onClick={() => onDelete(comment.id)}
                    className="text-destructive"
                  >
                    <Trash2 className="w-4 h-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}
          </div>
          <p className="text-sm">{comment.content}</p>
        </div>
        
        <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
          <span>{formatDistanceToNow(new Date(comment.createdAt), { addSuffix: true })}</span>
          {!isReply && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onReply(comment.id)}
              className="h-auto p-0 text-xs text-muted-foreground hover:text-foreground"
            >
              <Reply className="w-3 h-3 mr-1" />
              Reply
            </Button>
          )}
        </div>

        {comment.replies && comment.replies.length > 0 && (
          <div className="mt-3 space-y-2">
            {comment.replies.map((reply) => (
              <CommentItem
                key={reply.id}
                comment={reply}
                onReply={onReply}
                onEdit={onEdit}
                onDelete={onDelete}
                isReply
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default function EventCommentsDialog({ event, onClose }: EventCommentsDialogProps) {
  const { user, church } = useAuth()
  const [comments, setComments] = useState<EventComment[]>([])
  const [newComment, setNewComment] = useState('')
  const [replyingTo, setReplyingTo] = useState<string | null>(null)
  const [editingComment, setEditingComment] = useState<EventComment | null>(null)
  const [editContent, setEditContent] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)

  useEffect(() => {
    loadComments()
  }, [])

  const loadComments = async () => {
    if (!church) return

    try {
      const response = await api.getEventComments(church.slug, event.id)
      setComments(response.data.comments)
    } catch (error) {
      console.error('Error loading comments:', error)
      toast.error('Failed to load comments')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmitComment = async () => {
    if (!church || !newComment.trim()) return

    setIsSubmitting(true)
    try {
      const response = await api.createEventComment(church.slug, event.id, {
        content: newComment.trim(),
        parentCommentId: replyingTo || undefined
      })

      if (replyingTo) {
        // Add reply to existing comment
        setComments(prev => prev.map(comment => {
          if (comment.id === replyingTo) {
            return {
              ...comment,
              replies: [...(comment.replies || []), response.data.comment]
            }
          }
          return comment
        }))
      } else {
        // Add new top-level comment
        setComments(prev => [response.data.comment, ...prev])
      }

      setNewComment('')
      setReplyingTo(null)
      toast.success('Comment added!')
    } catch (error) {
      console.error('Error submitting comment:', error)
      toast.error('Failed to add comment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleEditComment = async () => {
    if (!church || !editingComment || !editContent.trim()) return

    setIsSubmitting(true)
    try {
      await api.updateEventComment(church.slug, event.id, editingComment.id, {
        content: editContent.trim()
      })

      // Update comment in state
      const updateCommentInList = (comments: EventComment[]): EventComment[] => {
        return comments.map(comment => {
          if (comment.id === editingComment.id) {
            return { ...comment, content: editContent.trim() }
          }
          if (comment.replies) {
            return { ...comment, replies: updateCommentInList(comment.replies) }
          }
          return comment
        })
      }

      setComments(prev => updateCommentInList(prev))
      setEditingComment(null)
      setEditContent('')
      toast.success('Comment updated!')
    } catch (error) {
      console.error('Error updating comment:', error)
      toast.error('Failed to update comment')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleDeleteComment = async (commentId: string) => {
    if (!church) return

    try {
      await api.deleteEventComment(church.slug, event.id, commentId)

      // Remove comment from state
      const removeCommentFromList = (comments: EventComment[]): EventComment[] => {
        return comments.filter(comment => {
          if (comment.id === commentId) return false
          if (comment.replies) {
            comment.replies = removeCommentFromList(comment.replies)
          }
          return true
        })
      }

      setComments(prev => removeCommentFromList(prev))
      toast.success('Comment deleted!')
    } catch (error) {
      console.error('Error deleting comment:', error)
      toast.error('Failed to delete comment')
    }
  }

  const handleReply = (parentId: string) => {
    setReplyingTo(parentId)
    setNewComment('')
  }

  const handleEdit = (comment: EventComment) => {
    setEditingComment(comment)
    setEditContent(comment.content)
  }

  const cancelReply = () => {
    setReplyingTo(null)
    setNewComment('')
  }

  const cancelEdit = () => {
    setEditingComment(null)
    setEditContent('')
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageCircle className="w-5 h-5" />
            Comments - {event.title}
          </DialogTitle>
        </DialogHeader>

        <div className="flex-1 flex flex-col min-h-0">
          <ScrollArea className="flex-1 pr-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="w-6 h-6 animate-spin" />
              </div>
            ) : comments.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No comments yet. Be the first to comment!</p>
              </div>
            ) : (
              <div className="space-y-4">
                {comments.map((comment) => (
                  <CommentItem
                    key={comment.id}
                    comment={comment}
                    onReply={handleReply}
                    onEdit={handleEdit}
                    onDelete={handleDeleteComment}
                  />
                ))}
              </div>
            )}
          </ScrollArea>

          <div className="border-t pt-4 mt-4">
            {editingComment ? (
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Edit className="w-4 h-4" />
                  Editing comment
                </div>
                <Textarea
                  value={editContent}
                  onChange={(e) => setEditContent(e.target.value)}
                  placeholder="Edit your comment..."
                  rows={3}
                />
                <div className="flex gap-2">
                  <Button
                    onClick={handleEditComment}
                    disabled={isSubmitting || !editContent.trim()}
                    size="sm"
                  >
                    {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    Update
                  </Button>
                  <Button variant="outline" onClick={cancelEdit} size="sm">
                    Cancel
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-3">
                {replyingTo && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Reply className="w-4 h-4" />
                    Replying to comment
                    <Button variant="ghost" onClick={cancelReply} size="sm" className="h-auto p-0 text-xs">
                      Cancel
                    </Button>
                  </div>
                )}
                <div className="flex gap-3">
                  <Avatar className="w-8 h-8 flex-shrink-0">
                    <AvatarImage src={user?.profileImage} />
                    <AvatarFallback>
                      {user?.firstName?.[0]}{user?.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <Textarea
                      value={newComment}
                      onChange={(e) => setNewComment(e.target.value)}
                      placeholder={replyingTo ? "Write a reply..." : "Write a comment..."}
                      rows={3}
                    />
                  </div>
                </div>
                <div className="flex justify-end">
                  <Button
                    onClick={handleSubmitComment}
                    disabled={isSubmitting || !newComment.trim()}
                    size="sm"
                  >
                    {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                    {replyingTo ? 'Reply' : 'Comment'}
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}