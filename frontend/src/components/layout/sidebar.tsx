'use client'

import React, { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet'
import { Badge } from '@/components/ui/badge'
import { ChurchLogo } from '@/components/ui/church-logo'
import { useNotificationCount } from '@/hooks/useNotificationCount'
import {
  Menu,
  Home,
  Users,
  Calendar,
  DollarSign,
  Settings,
  ChevronRight,
  MapPin,
  BarChart3,
  MessageSquare,
  Shield,
  LogOut,
  Bell
} from 'lucide-react'

interface NavigationItem {
  name: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  roles: string[] // Roles that can see this item
  badge?: string
  description?: string
}

const getNavigationItems = (churchSlug: string): NavigationItem[] => [
  {
    name: 'Dashboard',
    href: `/${churchSlug}/dashboard`,
    icon: Home,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member', 'Visitor'],
    description: 'Overview and quick access'
  },
  {
    name: 'Members',
    href: `/${churchSlug}/members`,
    icon: Users,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon'],
    description: 'Manage church members'
  },
  {
    name: 'Events',
    href: `/${churchSlug}/events`,
    icon: Calendar,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member'],
    description: 'Church events and RSVPs'
  },
  {
    name: 'Finances',
    href: `/${churchSlug}/finances`,
    icon: DollarSign,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon'],
    description: 'Financial contributions'
  },
  {
    name: 'Branches',
    href: `/${churchSlug}/branches`,
    icon: MapPin,
    roles: ['Super Admin', 'Pastor', 'Elder'],
    description: 'Church locations'
  },
  {
    name: 'Analytics',
    href: `/${churchSlug}/analytics`,
    icon: BarChart3,
    roles: ['Super Admin', 'Pastor', 'Elder'],
    description: 'Reports and insights'
  },
  {
    name: 'Announcements',
    href: `/${churchSlug}/announcements`,
    icon: MessageSquare,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon'],
    description: 'Church communications'
  },
  {
    name: 'Notifications',
    href: `/${churchSlug}/notifications`,
    icon: Bell,
    roles: ['Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member', 'Visitor'],
    description: 'Your in-app notifications'
  },
  {
    name: 'Roles',
    href: `/${churchSlug}/roles`,
    icon: Shield,
    roles: ['Super Admin', 'Pastor'],
    description: 'User permissions'
  },
  {
    name: 'Settings',
    href: `/${churchSlug}/settings`,
    icon: Settings,
    roles: ['Super Admin', 'Pastor'],
    description: 'Church configuration'
  }
]

interface SidebarProps {
  className?: string
}

 export function Sidebar({ className }: SidebarProps) {
  function NotificationsBadge() {
    const { unreadCount } = useNotificationCount()
    if (unreadCount <= 0) return null
    return (
      <Badge variant="destructive" className="h-5 text-xs rounded-full min-w-5 px-1 text-center">
        {unreadCount}
      </Badge>
    )
  }  const { user, church, logout } = useAuth()
  const pathname = usePathname()
  const [isOpen, setIsOpen] = useState(false)

  if (!user || !church) {
    return null
  }

  const userRole = user.role?.name || 'Member'
  const navigationItems = getNavigationItems(church.slug).filter(item =>
    item.roles.includes(userRole)
  )

  const SidebarContent = () => (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex h-16 items-center border-b px-6">
        <Link href={`/${church.slug}/dashboard`} className="flex items-center space-x-3">
          <ChurchLogo 
            logo={church.logo} 
            churchName={church.name} 
            size="sm"
          />
          <div className="flex flex-col">
            <span className="text-sm font-semibold">{church.name}</span>
            <span className="text-xs text-muted-foreground">{userRole}</span>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <div className="flex-1 overflow-auto py-4">
        <nav className="grid gap-1 px-4">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsOpen(false)}
                className={cn(
                  'flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent',
                  isActive
                    ? 'bg-accent text-accent-foreground'
                    : 'text-muted-foreground hover:text-accent-foreground'
                )}
              >
                <item.icon className="h-4 w-4" />
                <span className="flex-1">{item.name}</span>
                {item.name === 'Notifications' ? (
                  <NotificationsBadge />
                ) : item.badge ? (
                  <Badge variant="secondary" className="h-5 text-xs">
                    {item.badge}
                  </Badge>
                ) : null}
                <ChevronRight className="h-3 w-3 opacity-50" />
              </Link>
            )
          })}        </nav>
      </div>

      {/* User Profile & Actions */}
      <div className="border-t p-4">
        <div className="flex items-center gap-3 px-3 py-2 text-sm">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground">
            {user.firstName.charAt(0)}{user.lastName.charAt(0)}
          </div>
          <div className="flex-1">
            <p className="font-medium">{user.firstName} {user.lastName}</p>
            <p className="text-xs text-muted-foreground">{user.email}</p>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={logout}
          className="w-full justify-start gap-2 mt-2"
        >
          <LogOut className="h-4 w-4" />
          Sign Out
        </Button>
      </div>
    </div>
  )

  return (
    <>
      {/* Mobile Sidebar */}
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="ghost" size="icon" className="md:hidden">
            <Menu className="h-5 w-5" />
            <span className="sr-only">Toggle sidebar</span>
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-64 p-0">
          <SidebarContent />
        </SheetContent>
      </Sheet>

      {/* Desktop Sidebar */}
      <div className={cn('hidden md:flex h-screen w-64 flex-col border-r bg-background fixed left-0 top-0 z-30', className)}>
        <SidebarContent />
      </div>
    </>
  )
}

export function MobileSidebarTrigger() {
  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle sidebar</span>
        </Button>
      </SheetTrigger>
    </Sheet>
  )
}