'use client'

import { useAuth } from '@/lib/auth'
import { Sidebar } from '@/components/layout/sidebar'
import { EmailVerificationBanner } from '@/components/ui/email-verification-banner'
// import { NotificationBell } from '@/components/layout/notification-bell'

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { user, church, loading } = useAuth()

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p>Please log in to access the dashboard.</p>
        </div>
      </div>
    )
  }

  if (!church) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <p>Loading church information...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-background">
      {/* Sidebar */}
      <Sidebar />

      {/* Main Content Area */}
      <div className="md:ml-64 flex flex-col h-full">
        {/* Mobile Header */}
        <header className="md:hidden flex items-center justify-between p-4 shrink-0">
          <div className="flex items-center gap-2">
            <Sidebar />
            <h1 className="font-semibold">Dashboard</h1>
          </div>
        
        </header>

        {/* Email Verification Banner */}
        {user && !user.isEmailVerified && (
          <div className="p-4 border-b">
            <EmailVerificationBanner 
              userEmail={user.email}
              onResendVerification={async () => {
                const { apiClient } = await import('@/lib/api')
                await apiClient.resendVerificationEmail()
              }}
            />
          </div>
        )}

        {/* Page Content */}
        <main className="flex-1 overflow-auto">
          {/* Top bar for desktop */}
          {/* <div className="hidden md:flex items-center justify-end p-4 border-b" /> */}
          {children}
        </main>
      </div>
    </div>
  )
}