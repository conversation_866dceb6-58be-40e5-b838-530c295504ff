"use client"

import { useEffect, useMemo, useState } from 'react'
import { Bell, CheckCheck } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/lib/auth'
import { api } from '@/lib/api'
import { cn } from '@/lib/utils'

interface NotificationItem {
  id: string
  title: string
  message?: string
  link?: string
  isRead: boolean
  createdAt: string
}

export function NotificationBell() {
  const { church } = useAuth()
  const [items, setItems] = useState<NotificationItem[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [open, setOpen] = useState(false)
  const slug = church?.slug

  const refresh = async () => {
    if (!slug) return
    const [list, count] = await Promise.all([
      api.getNotifications(slug, { limit: 10, unreadOnly: false }).catch(() => ({ data: { notifications: [], pagination: { page: 1, limit: 10, total: 0, totalPages: 0 } } })),
      api.getNotificationsUnreadCount(slug).catch(() => ({ data: { count: 0 } }))
    ])
    setItems(list.data.notifications as NotificationItem[])
    setUnreadCount(count.data.count)
  }

  useEffect(() => {
    refresh()
    const i = setInterval(refresh, 120000)
    return () => clearInterval(i)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [slug])

  const handleMarkAllRead = async () => {
    if (!slug) return
    await api.markNotificationsRead(slug).catch(() => {})
    await refresh()
  }

  const handleClickItem = async (n: NotificationItem) => {
    if (!slug) return
    if (!n.isRead) {
      await api.markNotificationsRead(slug, { notificationIds: [n.id] }).catch(() => {})
      await refresh()
    }
    if (n.link) {
      window.location.href = `/${slug}${n.link.startsWith('/') ? '' : '/'}${n.link}`
    }
  }

  const formattedItems = useMemo(() => items, [items])

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-1 -right-1 h-5 min-w-5 px-1 text-[10px] rounded-full" variant="destructive">{unreadCount}</Badge>
          )}
          <span className="sr-only">Open notifications</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 p-0">
        <div className="flex items-center justify-between p-3 border-b">
          <DropdownMenuLabel className="p-0">Notifications</DropdownMenuLabel>
          {unreadCount > 0 && (
            <Button variant="ghost" size="sm" onClick={handleMarkAllRead} className="h-7 gap-2">
              <CheckCheck className="h-4 w-4" /> Mark all read
            </Button>
          )}
        </div>
        <ScrollArea className="h-80">
          {formattedItems.length === 0 ? (
            <div className="p-4 text-sm text-muted-foreground">No notifications</div>
          ) : (
            <div className="p-1">
              {formattedItems.map((n) => (
                <button key={n.id} onClick={() => handleClickItem(n)} className={cn("w-full text-left p-3 rounded-md hover:bg-accent", !n.isRead && 'bg-accent/30') }>
                  <div className="flex items-start gap-2">
                    <div className={cn("mt-1 h-2 w-2 rounded-full", n.isRead ? 'bg-muted' : 'bg-primary')} />
                    <div className="flex-1">
                      <div className="text-sm font-medium line-clamp-1">{n.title}</div>
                      {n.message && <div className="text-xs text-muted-foreground line-clamp-2">{n.message}</div>}
                      <div className="mt-1 text-[10px] text-muted-foreground">{new Date(n.createdAt).toLocaleString()}</div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
