/**
 * Enhanced error display components with user-friendly messaging and recovery options
 * Implements requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

'use client'

import React from 'react'
import { AlertCircle, RefreshCw, Mail, Phone, ExternalLink, CheckCircle, Info, AlertTriangle } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { AuthError, ErrorUtils } from '@/lib/errors'

interface ErrorDisplayProps {
  error: AuthError
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
  variant?: 'inline' | 'card' | 'banner'
  showDetails?: boolean
  showActions?: boolean
}

/**
 * Main error display component with multiple variants
 */
export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss,
  className,
  variant = 'inline',
  showDetails = true,
  showActions = true
}: ErrorDisplayProps) {
  const isRetryable = ErrorUtils.isRetryable(error)
  const shouldContactSupport = ErrorUtils.shouldContactSupport(error)

  if (variant === 'card') {
    return (
      <Card className={cn('border-red-200 bg-red-50', className)}>
        <CardHeader className="pb-3">
          <div className="flex items-center space-x-2">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <CardTitle className="text-red-800">Error</CardTitle>
          </div>
          <CardDescription className="text-red-700">
            {error.userMessage}
          </CardDescription>
        </CardHeader>
        {(showDetails || showActions) && (
          <CardContent className="pt-0">
            {showDetails && <ErrorDetails error={error} />}
            {showActions && (
              <ErrorActions 
                error={error}
                onRetry={onRetry}
                onDismiss={onDismiss}
                isRetryable={isRetryable}
                shouldContactSupport={shouldContactSupport}
              />
            )}
          </CardContent>
        )}
      </Card>
    )
  }

  if (variant === 'banner') {
    return (
      <div className={cn('border-l-4 border-red-500 bg-red-50 p-4', className)}>
        <div className="flex items-start">
          <AlertCircle className="h-5 w-5 text-red-600 mt-0.5" />
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-red-800">
              Authentication Error
            </h3>
            <p className="mt-1 text-sm text-red-700">
              {error.userMessage}
            </p>
            {showDetails && <ErrorDetails error={error} className="mt-3" />}
            {showActions && (
              <ErrorActions 
                error={error}
                onRetry={onRetry}
                onDismiss={onDismiss}
                isRetryable={isRetryable}
                shouldContactSupport={shouldContactSupport}
                className="mt-3"
              />
            )}
          </div>
        </div>
      </div>
    )
  }

  // Default inline variant
  return (
    <Alert variant="destructive" className={className}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Error</AlertTitle>
      <AlertDescription className="mt-2">
        {error.userMessage}
        {showDetails && <ErrorDetails error={error} className="mt-3" />}
        {showActions && (
          <ErrorActions 
            error={error}
            onRetry={onRetry}
            onDismiss={onDismiss}
            isRetryable={isRetryable}
            shouldContactSupport={shouldContactSupport}
            className="mt-3"
          />
        )}
      </AlertDescription>
    </Alert>
  )
}

/**
 * Error details component showing suggested actions
 */
function ErrorDetails({ error, className }: { error: AuthError; className?: string }) {
  if (!error.suggestedActions?.length) return null

  return (
    <div className={cn('space-y-2', className)}>
      <Separator />
      <div>
        <h4 className="text-sm font-medium text-gray-800 mb-2">What you can try:</h4>
        <ul className="text-sm text-gray-600 space-y-1">
          {error.suggestedActions.map((action, index) => (
            <li key={index} className="flex items-start space-x-2">
              <CheckCircle className="h-3 w-3 text-green-600 mt-0.5 flex-shrink-0" />
              <span>{action}</span>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}

/**
 * Error actions component with retry and support options
 */
function ErrorActions({ 
  error, 
  onRetry, 
  onDismiss,
  isRetryable,
  shouldContactSupport,
  className 
}: {
  error: AuthError
  onRetry?: () => void
  onDismiss?: () => void
  isRetryable: boolean
  shouldContactSupport: boolean
  className?: string
}) {
  if (!isRetryable && !shouldContactSupport && !onDismiss) return null

  return (
    <div className={cn('flex flex-wrap gap-2', className)}>
      {isRetryable && onRetry && (
        <Button
          variant="outline"
          size="sm"
          onClick={onRetry}
          className="text-red-700 border-red-300 hover:bg-red-100"
        >
          <RefreshCw className="h-3 w-3 mr-1" />
          Try Again
        </Button>
      )}
      
      {shouldContactSupport && (
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open('mailto:<EMAIL>', '_blank')}
          className="text-red-700 border-red-300 hover:bg-red-100"
        >
          <Mail className="h-3 w-3 mr-1" />
          Contact Support
        </Button>
      )}
      
      {onDismiss && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onDismiss}
          className="text-red-600 hover:bg-red-100"
        >
          Dismiss
        </Button>
      )}
    </div>
  )
}

/**
 * Specialized error display for authentication forms
 */
export function AuthErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss,
  className 
}: {
  error: AuthError
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
}) {
  return (
    <ErrorDisplay
      error={error}
      onRetry={onRetry}
      onDismiss={onDismiss}
      className={className}
      variant="inline"
      showDetails={true}
      showActions={true}
    />
  )
}

/**
 * Specialized error display for network issues
 */
export function NetworkErrorDisplay({ 
  onRetry, 
  className 
}: {
  onRetry?: () => void
  className?: string
}) {
  const networkError: AuthError = {
    code: 'NETWORK_ERROR',
    message: 'Network connection failed',
    userMessage: 'Unable to connect to the server. Please check your internet connection.',
    retryable: true,
    recoverable: true,
    actionable: true,
    suggestedActions: [
      'Check your internet connection',
      'Try refreshing the page',
      'Wait a moment and try again'
    ]
  }

  return (
    <ErrorDisplay
      error={networkError}
      onRetry={onRetry}
      className={className}
      variant="card"
    />
  )
}

/**
 * Specialized error display for church not found
 */
export function ChurchNotFoundDisplay({ 
  churchSlug, 
  className 
}: {
  churchSlug: string
  className?: string
}) {
  const churchError: AuthError = {
    code: 'CHURCH_NOT_FOUND',
    message: `Church "${churchSlug}" not found`,
    userMessage: `The church "${churchSlug}" could not be found.`,
    retryable: false,
    recoverable: true,
    actionable: true,
    suggestedActions: [
      'Check the church URL for typos',
      'Contact the church administrator',
      'Try the general login page'
    ]
  }

  return (
    <div className={cn('text-center', className)}>
      <ErrorDisplay
        error={churchError}
        variant="card"
        showActions={false}
      />
      <div className="mt-4 space-y-2">
        <Button variant="outline" asChild>
          <a href="/login">
            <ExternalLink className="h-4 w-4 mr-2" />
            Try General Login
          </a>
        </Button>
        <Button variant="outline" asChild>
          <a href="/register-church">
            Register New Church
          </a>
        </Button>
      </div>
    </div>
  )
}

/**
 * Success message display for consistency
 */
export function SuccessDisplay({ 
  message, 
  onDismiss,
  className 
}: {
  message: string
  onDismiss?: () => void
  className?: string
}) {
  return (
    <Alert className={cn('border-green-200 bg-green-50', className)}>
      <CheckCircle className="h-4 w-4 text-green-600" />
      <AlertTitle className="text-green-800">Success</AlertTitle>
      <AlertDescription className="text-green-700">
        {message}
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="ml-2 text-green-600 hover:bg-green-100"
          >
            Dismiss
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

/**
 * Info message display for consistency
 */
export function InfoDisplay({ 
  message, 
  onDismiss,
  className 
}: {
  message: string
  onDismiss?: () => void
  className?: string
}) {
  return (
    <Alert className={cn('border-blue-200 bg-blue-50', className)}>
      <Info className="h-4 w-4 text-blue-600" />
      <AlertTitle className="text-blue-800">Information</AlertTitle>
      <AlertDescription className="text-blue-700">
        {message}
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="ml-2 text-blue-600 hover:bg-blue-100"
          >
            Dismiss
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}

/**
 * Warning message display for consistency
 */
export function WarningDisplay({ 
  message, 
  onDismiss,
  className 
}: {
  message: string
  onDismiss?: () => void
  className?: string
}) {
  return (
    <Alert className={cn('border-yellow-200 bg-yellow-50', className)}>
      <AlertTriangle className="h-4 w-4 text-yellow-600" />
      <AlertTitle className="text-yellow-800">Warning</AlertTitle>
      <AlertDescription className="text-yellow-700">
        {message}
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="ml-2 text-yellow-600 hover:bg-yellow-100"
          >
            Dismiss
          </Button>
        )}
      </AlertDescription>
    </Alert>
  )
}