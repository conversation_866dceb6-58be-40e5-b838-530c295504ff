'use client'

import { useState } from 'react'
import { Mail, X, RefreshCw } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { toast } from 'sonner'

interface EmailVerificationBannerProps {
  userEmail: string
  onResendVerification?: () => Promise<void>
  onDismiss?: () => void
}

export function EmailVerificationBanner({ 
  userEmail, 
  onResendVerification,
  onDismiss 
}: EmailVerificationBannerProps) {
  const [isResending, setIsResending] = useState(false)
  const [isDismissed, setIsDismissed] = useState(false)

  const handleResend = async () => {
    if (!onResendVerification) return

    try {
      setIsResending(true)
      await onResendVerification()
      toast.success('Verification email sent! Please check your inbox.')
    } catch (error) {
      toast.error('Failed to send verification email. Please try again.')
    } finally {
      setIsResending(false)
    }
  }

  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }

  if (isDismissed) {
    return null
  }

  return (
    <Alert className="border-amber-200 bg-amber-50 text-amber-800">
      <Mail className="h-4 w-4" />
      <AlertDescription className="flex items-center justify-between w-full">
        <div className="flex-1">
          <strong>Email verification required.</strong> Please check your email ({userEmail}) and click the verification link to complete your account setup.
        </div>
        <div className="flex items-center gap-2 ml-4">
          {onResendVerification && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleResend}
              disabled={isResending}
              className="border-amber-300 text-amber-800 hover:bg-amber-100"
            >
              {isResending ? (
                <>
                  <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Mail className="h-3 w-3 mr-1" />
                  Resend
                </>
              )}
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={handleDismiss}
            className="text-amber-800 hover:bg-amber-100 p-1"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </AlertDescription>
    </Alert>
  )
}