'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Upload, X, Image as ImageIcon, AlertCircle } from 'lucide-react'
import { cn } from '@/lib/utils'
import { compressImage, validateImageFile } from '@/lib/imageUtils'
import { toast } from 'sonner'

// Helper function to get base64 size in KB
function getBase64SizeKB(base64String: string): number {
  const base64Data = base64String.split(',')[1] || base64String
  const sizeInBytes = (base64Data.length * 3) / 4
  return sizeInBytes / 1024
}

interface ImageUploadProps {
  value?: string
  onChange: (value: string) => void
  onRemove: () => void
  disabled?: boolean
  className?: string
  label?: string
  description?: string
}

export function ImageUpload({
  value,
  onChange,
  onRemove,
  disabled,
  className,
  label = "Event Image",
  description = "Upload an image for your event (optional)"
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    // Validate the file first
    const validation = validateImageFile(file)
    if (!validation.valid) {
      toast.error(validation.error || 'Invalid file')
      return
    }

    setIsUploading(true)
    
    try {
      // Compress the image to reduce payload size
      const compressedImage = await compressImage(file, {
        maxWidth: 600,
        maxHeight: 400,
        quality: 0.4,
        maxSizeKB: 100 // Very aggressive compression to avoid server issues
      })
      
      // Check final size and provide fallback for very large images
      const sizeKB = getBase64SizeKB(compressedImage)
      
      if (sizeKB > 150) {
        // Image is still too large even after compression
        toast.error(`Image is too large (${Math.round(sizeKB)}KB) and may cause upload issues. Please use a smaller image or enter an image URL instead.`)
        return
      } else if (sizeKB > 100) {
        toast.warning(`Image compressed to ${Math.round(sizeKB)}KB. This may cause slow uploads.`)
      } else {
        toast.success(`Image compressed successfully (${Math.round(sizeKB)}KB)`)
      }
      
      onChange(compressedImage)
    } catch (error) {
      console.error('Error processing image:', error)
      toast.error('Failed to process image. Please try a different image.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (disabled) return
    
    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }

  const handleUrlInput = (url: string) => {
    if (url && !url.match(/^https?:\/\/.+/)) {
      toast.error('Please enter a valid URL starting with http:// or https://')
      return
    }
    onChange(url)
    if (url) {
      toast.success('Image URL added successfully')
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div>
        <Label>{label}</Label>
        {description && (
          <p className="text-sm text-muted-foreground mt-1">{description}</p>
        )}
      </div>

      {value ? (
        <div className="relative">
          <div className="relative w-full h-48 rounded-lg overflow-hidden border">
            <img
              src={value}
              alt="Event preview"
              className="w-full h-full object-cover"
            />
            <Button
              type="button"
              variant="destructive"
              size="sm"
              className="absolute top-2 right-2"
              onClick={onRemove}
              disabled={disabled}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      ) : (
        <div className="space-y-4">
          {/* Drag and Drop Area */}
          <div
            className={cn(
              "border-2 border-dashed rounded-lg p-6 text-center transition-colors",
              dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
              disabled && "opacity-50 cursor-not-allowed"
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <div className="space-y-2">
              <p className="text-sm font-medium">
                {isUploading ? 'Processing and compressing image...' : 'Drag and drop an image here'}
              </p>
              <p className="text-xs text-muted-foreground">
                {isUploading ? 'Please wait while we optimize your image' : 'or click to browse files'}
              </p>
            </div>
            <Button
              type="button"
              variant="outline"
              className="mt-4"
              onClick={() => fileInputRef.current?.click()}
              disabled={disabled || isUploading}
            >
              <Upload className="mr-2 h-4 w-4" />
              Choose File
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleInputChange}
              className="hidden"
              disabled={disabled}
            />
          </div>

          {/* URL Input Alternative */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">
                Or enter image URL
              </span>
            </div>
          </div>

          <div className="flex gap-2">
            <Input
              placeholder="https://example.com/image.jpg"
              onChange={(e) => handleUrlInput(e.target.value)}
              disabled={disabled}
            />
          </div>
        </div>
      )}

      <p className="text-xs text-muted-foreground">
        Supported formats: JPG, PNG, GIF, WebP. Max size: 10MB (automatically compressed)
      </p>
    </div>
  )
}