'use client'

import { Church } from 'lucide-react'
import { cn } from '@/lib/utils'

interface ChurchLogoProps {
  logo?: string
  churchName: string
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function ChurchLogo({ logo, churchName, size = 'md', className }: ChurchLogoProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-16 h-16'
  }

  const iconSizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6', 
    lg: 'w-8 h-8'
  }

  if (logo) {
    return (
      <div className={cn('relative overflow-hidden rounded-lg border bg-background', sizeClasses[size], className)}>
        <img
          src={logo}
          alt={`${churchName} logo`}
          className="w-full h-full object-cover"
        />
      </div>
    )
  }

  // Fallback to church icon if no logo
  return (
    <div className={cn(
      'flex items-center justify-center rounded-lg border bg-muted text-muted-foreground',
      sizeClasses[size],
      className
    )}>
      <Church className={iconSizeClasses[size]} />
    </div>
  )
}