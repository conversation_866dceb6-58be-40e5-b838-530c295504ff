/**
 * Tests for error display components
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { describe, it, expect, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { 
  ErrorDisplay, 
  AuthErrorDisplay, 
  NetworkErrorDisplay, 
  ChurchNotFoundDisplay,
  SuccessDisplay,
  InfoDisplay,
  WarningDisplay
} from '../error-display'
import { AuthError } from '@/lib/errors'

// Mock window.open for support contact tests
const mockWindowOpen = vi.fn()
Object.defineProperty(window, 'open', {
  value: mockWindowOpen,
  writable: true
})

describe('ErrorDisplay', () => {
  const mockError: AuthError = {
    code: 'NETWORK_ERROR',
    message: 'Network connection failed',
    userMessage: 'Unable to connect to the server. Please check your internet connection.',
    retryable: true,
    recoverable: true,
    actionable: true,
    suggestedActions: [
      'Check your internet connection',
      'Try refreshing the page',
      'Wait a moment and try again'
    ]
  }

  const mockNonRetryableError: AuthError = {
    code: 'PERMISSION_DENIED',
    message: 'Access denied',
    userMessage: 'You do not have permission to perform this action.',
    retryable: false,
    recoverable: false,
    actionable: true,
    contactSupport: true,
    suggestedActions: ['Contact your church administrator for access']
  }

  describe('inline variant (default)', () => {
    it('should render error message correctly', () => {
      render(<ErrorDisplay error={mockError} />)
      
      expect(screen.getByText('Error')).toBeInTheDocument()
      expect(screen.getByText(mockError.userMessage)).toBeInTheDocument()
    })

    it('should show suggested actions when showDetails is true', () => {
      render(<ErrorDisplay error={mockError} showDetails={true} />)
      
      expect(screen.getByText('What you can try:')).toBeInTheDocument()
      expect(screen.getByText('Check your internet connection')).toBeInTheDocument()
      expect(screen.getByText('Try refreshing the page')).toBeInTheDocument()
    })

    it('should hide suggested actions when showDetails is false', () => {
      render(<ErrorDisplay error={mockError} showDetails={false} />)
      
      expect(screen.queryByText('What you can try:')).not.toBeInTheDocument()
    })

    it('should show retry button for retryable errors', () => {
      const onRetry = vi.fn()
      render(<ErrorDisplay error={mockError} onRetry={onRetry} />)
      
      const retryButton = screen.getByText('Try Again')
      expect(retryButton).toBeInTheDocument()
      
      fireEvent.click(retryButton)
      expect(onRetry).toHaveBeenCalledTimes(1)
    })

    it('should not show retry button for non-retryable errors', () => {
      render(<ErrorDisplay error={mockNonRetryableError} />)
      
      expect(screen.queryByText('Try Again')).not.toBeInTheDocument()
    })

    it('should show contact support button when contactSupport is true', () => {
      render(<ErrorDisplay error={mockNonRetryableError} />)
      
      const supportButton = screen.getByText('Contact Support')
      expect(supportButton).toBeInTheDocument()
      
      fireEvent.click(supportButton)
      expect(mockWindowOpen).toHaveBeenCalledWith('mailto:<EMAIL>', '_blank')
    })

    it('should show dismiss button when onDismiss is provided', () => {
      const onDismiss = vi.fn()
      render(<ErrorDisplay error={mockError} onDismiss={onDismiss} />)
      
      const dismissButton = screen.getByText('Dismiss')
      expect(dismissButton).toBeInTheDocument()
      
      fireEvent.click(dismissButton)
      expect(onDismiss).toHaveBeenCalledTimes(1)
    })

    it('should hide actions when showActions is false', () => {
      const onRetry = vi.fn()
      render(<ErrorDisplay error={mockError} onRetry={onRetry} showActions={false} />)
      
      expect(screen.queryByText('Try Again')).not.toBeInTheDocument()
    })
  })

  describe('card variant', () => {
    it('should render as card with proper styling', () => {
      render(<ErrorDisplay error={mockError} variant="card" />)
      
      expect(screen.getByText('Error')).toBeInTheDocument()
      expect(screen.getByText(mockError.userMessage)).toBeInTheDocument()
    })

    it('should show all content sections in card variant', () => {
      const onRetry = vi.fn()
      render(
        <ErrorDisplay 
          error={mockError} 
          variant="card" 
          onRetry={onRetry}
          showDetails={true}
          showActions={true}
        />
      )
      
      expect(screen.getByText('Error')).toBeInTheDocument()
      expect(screen.getByText('What you can try:')).toBeInTheDocument()
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })
  })

  describe('banner variant', () => {
    it('should render as banner with proper styling', () => {
      render(<ErrorDisplay error={mockError} variant="banner" />)
      
      expect(screen.getByText('Authentication Error')).toBeInTheDocument()
      expect(screen.getByText(mockError.userMessage)).toBeInTheDocument()
    })
  })

  describe('custom className', () => {
    it('should apply custom className', () => {
      const { container } = render(
        <ErrorDisplay error={mockError} className="custom-error-class" />
      )
      
      expect(container.firstChild).toHaveClass('custom-error-class')
    })
  })
})

describe('AuthErrorDisplay', () => {
  it('should render with inline variant and all features enabled', () => {
    const mockError: AuthError = {
      code: 'INVALID_CREDENTIALS',
      message: 'Invalid credentials',
      userMessage: 'Invalid email, password, or church code. Please check your credentials and try again.',
      retryable: true,
      recoverable: true,
      actionable: true,
      suggestedActions: ['Double-check your email address', 'Verify your password is correct']
    }

    const onRetry = vi.fn()
    render(<AuthErrorDisplay error={mockError} onRetry={onRetry} />)
    
    expect(screen.getByText('Error')).toBeInTheDocument()
    expect(screen.getByText(mockError.userMessage)).toBeInTheDocument()
    expect(screen.getByText('What you can try:')).toBeInTheDocument()
    expect(screen.getByText('Try Again')).toBeInTheDocument()
  })
})

describe('NetworkErrorDisplay', () => {
  it('should render network error with retry option', () => {
    const onRetry = vi.fn()
    render(<NetworkErrorDisplay onRetry={onRetry} />)
    
    expect(screen.getByText('Error')).toBeInTheDocument()
    expect(screen.getByText('Unable to connect to the server. Please check your internet connection.')).toBeInTheDocument()
    expect(screen.getByText('Check your internet connection')).toBeInTheDocument()
    expect(screen.getByText('Try Again')).toBeInTheDocument()
    
    fireEvent.click(screen.getByText('Try Again'))
    expect(onRetry).toHaveBeenCalledTimes(1)
  })
})

describe('ChurchNotFoundDisplay', () => {
  it('should render church not found error with action buttons', () => {
    render(<ChurchNotFoundDisplay churchSlug="test-church" />)
    
    expect(screen.getByText('The church "test-church" could not be found.')).toBeInTheDocument()
    expect(screen.getByText('Check the church URL for typos')).toBeInTheDocument()
    expect(screen.getByText('Try General Login')).toBeInTheDocument()
    expect(screen.getByText('Register New Church')).toBeInTheDocument()
  })

  it('should have correct links for action buttons', () => {
    render(<ChurchNotFoundDisplay churchSlug="test-church" />)
    
    const generalLoginLink = screen.getByText('Try General Login').closest('a')
    const registerChurchLink = screen.getByText('Register New Church').closest('a')
    
    expect(generalLoginLink).toHaveAttribute('href', '/login')
    expect(registerChurchLink).toHaveAttribute('href', '/register-church')
  })
})

describe('SuccessDisplay', () => {
  it('should render success message correctly', () => {
    const message = 'Operation completed successfully!'
    render(<SuccessDisplay message={message} />)
    
    expect(screen.getByText('Success')).toBeInTheDocument()
    expect(screen.getByText(message)).toBeInTheDocument()
  })

  it('should show dismiss button when onDismiss is provided', () => {
    const onDismiss = vi.fn()
    render(<SuccessDisplay message="Success!" onDismiss={onDismiss} />)
    
    const dismissButton = screen.getByText('Dismiss')
    expect(dismissButton).toBeInTheDocument()
    
    fireEvent.click(dismissButton)
    expect(onDismiss).toHaveBeenCalledTimes(1)
  })
})

describe('InfoDisplay', () => {
  it('should render info message correctly', () => {
    const message = 'This is an informational message.'
    render(<InfoDisplay message={message} />)
    
    expect(screen.getByText('Information')).toBeInTheDocument()
    expect(screen.getByText(message)).toBeInTheDocument()
  })

  it('should show dismiss button when onDismiss is provided', () => {
    const onDismiss = vi.fn()
    render(<InfoDisplay message="Info!" onDismiss={onDismiss} />)
    
    const dismissButton = screen.getByText('Dismiss')
    expect(dismissButton).toBeInTheDocument()
    
    fireEvent.click(dismissButton)
    expect(onDismiss).toHaveBeenCalledTimes(1)
  })
})

describe('WarningDisplay', () => {
  it('should render warning message correctly', () => {
    const message = 'This is a warning message.'
    render(<WarningDisplay message={message} />)
    
    expect(screen.getByText('Warning')).toBeInTheDocument()
    expect(screen.getByText(message)).toBeInTheDocument()
  })

  it('should show dismiss button when onDismiss is provided', () => {
    const onDismiss = vi.fn()
    render(<WarningDisplay message="Warning!" onDismiss={onDismiss} />)
    
    const dismissButton = screen.getByText('Dismiss')
    expect(dismissButton).toBeInTheDocument()
    
    fireEvent.click(dismissButton)
    expect(onDismiss).toHaveBeenCalledTimes(1)
  })
})