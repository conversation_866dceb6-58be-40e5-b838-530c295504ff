import { cn } from '@/lib/utils'

interface LogoProps {
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  showTagline?: boolean
}

export function Logo({ 
  className, 
  size = 'md', 
  showText = true, 
  showTagline = false
}: LogoProps) {
  const sizeClasses = {
    sm: 'text-base',
    md: 'text-xl', 
    lg: 'text-3xl'
  }

  const emojiSizeClasses = {
    sm: 'text-lg',
    md: 'text-2xl',
    lg: 'text-4xl'
  }

  const taglineSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  return (
    <div className={cn('flex flex-col items-center', className)}>
      <div className="flex items-center space-x-2">
        <div className="relative">
          <span className={cn('font-bold', emojiSizeClasses[size])}>⛪</span>
          <div className="absolute -top-1 -right-1">
            {/* Shiny sparkle effect */}
            <div className="relative">
              <div className="w-2 h-2 bg-yellow-300 rounded-full animate-pulse"></div>
              <div className="absolute inset-0 w-2 h-2 bg-yellow-100 rounded-full animate-ping opacity-30"></div>
              <div className="absolute -inset-0.5 w-3 h-3 border border-yellow-300 rounded-full opacity-20 animate-pulse"></div>
            </div>
          </div>
        </div>
        
        {showText && (
          <span className={cn('font-bold bg-gradient-to-r from-amber-600 to-orange-600 bg-clip-text text-transparent', sizeClasses[size])}>
            ichengelo
          </span>
        )}
      </div>
      
      {showTagline && showText && (
        <span className={cn('text-gray-600 mt-1 font-medium', taglineSizeClasses[size])}>
          Bringing Light to Church Communities
        </span>
      )}
    </div>
  )
}