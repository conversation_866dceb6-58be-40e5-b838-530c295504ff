'use client'

import { useAuth } from '@/lib/auth'
import { useRouter, usePathname } from 'next/navigation'
import { useEffect, useState, useCallback } from 'react'
import { ErrorDisplay, NetworkErrorDisplay } from '@/components/ui/error-display'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Shield, RefreshCw, AlertCircle } from 'lucide-react'
import { AuthError } from '@/lib/errors'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
  fallbackComponent?: React.ComponentType<{ error?: AuthError; onRetry?: () => void }>
  showErrorDetails?: boolean
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  redirectTo,
  fallbackComponent: FallbackComponent,
  showErrorDetails = true
}: AuthGuardProps) {
  const { isAuthenticated, loading, isInitialized, error, checkAuthStatus, clearError } = useAuth()
  const router = useRouter()
  const pathname = usePathname()
  const [redirecting, setRedirecting] = useState(false)
  const [retryCount, setRetryCount] = useState(0)

  // Handle authentication redirect logic
  const handleAuthRedirect = useCallback(() => {
    if (!requireAuth || isAuthenticated || redirecting) return

    setRedirecting(true)
    
    try {
      // Always redirect to the main login page since users can login from anywhere
      const loginUrl = redirectTo || '/login'
      
      // Add current path as redirect parameter
      const redirectUrl = new URL(loginUrl, window.location.origin)
      redirectUrl.searchParams.set('redirect', pathname)
      
      router.push(redirectUrl.pathname + redirectUrl.search)
    } catch (error) {
      console.error('Failed to redirect to login:', error)
      setRedirecting(false)
    }
  }, [isAuthenticated, requireAuth, pathname, redirectTo, router, redirecting])

  // Handle retry logic for authentication errors
  const handleRetry = useCallback(async () => {
    if (retryCount >= 3) {
      console.warn('Max retry attempts reached for authentication')
      return
    }

    setRetryCount(prev => prev + 1)
    clearError()
    
    try {
      await checkAuthStatus()
    } catch (error) {
      console.error('Retry authentication failed:', error)
    }
  }, [retryCount, clearError, checkAuthStatus])

  // Reset retry count when authentication succeeds
  useEffect(() => {
    if (isAuthenticated) {
      setRetryCount(0)
      setRedirecting(false)
    }
  }, [isAuthenticated])

  // Handle redirect when authentication is required but user is not authenticated
  useEffect(() => {
    if (!isInitialized) return

    if (requireAuth && !isAuthenticated && !error) {
      handleAuthRedirect()
    }
  }, [isAuthenticated, isInitialized, requireAuth, error, handleAuthRedirect])

  // Show loading while authentication is being checked
  if (!isInitialized || loading) {
    return <AuthLoadingScreen />
  }

  // Show error state if there's an authentication error
  if (error && requireAuth) {
    // Use custom fallback component if provided
    if (FallbackComponent) {
      return <FallbackComponent error={error} onRetry={handleRetry} />
    }

    return (
      <AuthErrorScreen 
        error={error} 
        onRetry={handleRetry}
        onRedirectToLogin={handleAuthRedirect}
        showDetails={showErrorDetails}
        retryCount={retryCount}
      />
    )
  }

  // Show redirecting state
  if (redirecting) {
    return <AuthRedirectingScreen />
  }

  // Don't render children if auth is required but user is not authenticated
  if (requireAuth && !isAuthenticated) {
    return null
  }

  return <>{children}</>
}

/**
 * Loading screen component for authentication checks
 */
function AuthLoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Checking authentication...</h3>
        <p className="text-sm text-gray-600">Please wait while we verify your session</p>
      </div>
    </div>
  )
}

/**
 * Error screen component for authentication failures
 */
function AuthErrorScreen({ 
  error, 
  onRetry, 
  onRedirectToLogin,
  showDetails,
  retryCount 
}: {
  error: AuthError
  onRetry: () => void
  onRedirectToLogin: () => void
  showDetails: boolean
  retryCount: number
}) {
  const isNetworkError = error.code === 'NETWORK_ERROR'
  const canRetry = error.retryable && retryCount < 3
  const shouldRedirectToLogin = ['TOKEN_EXPIRED', 'INVALID_CREDENTIALS', 'REFRESH_FAILED'].includes(error.code)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Authentication Error</CardTitle>
          <CardDescription>
            {shouldRedirectToLogin 
              ? 'Your session has expired or is invalid'
              : 'There was a problem verifying your authentication'
            }
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {isNetworkError ? (
            <NetworkErrorDisplay onRetry={canRetry ? onRetry : undefined} />
          ) : (
            <ErrorDisplay 
              error={error}
              onRetry={canRetry ? onRetry : undefined}
              showDetails={showDetails}
              variant="inline"
            />
          )}

          {retryCount >= 3 && (
            <div className="text-center text-sm text-gray-600 bg-gray-50 p-3 rounded-md">
              Maximum retry attempts reached. Please try logging in again.
            </div>
          )}

          <div className="flex flex-col gap-2">
            {shouldRedirectToLogin && (
              <Button onClick={onRedirectToLogin} className="w-full">
                <Shield className="mr-2 h-4 w-4" />
                Go to Login
              </Button>
            )}
            
            {canRetry && !shouldRedirectToLogin && (
              <Button variant="outline" onClick={onRetry} className="w-full">
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again ({3 - retryCount} attempts left)
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Redirecting screen component
 */
function AuthRedirectingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Redirecting to login...</h3>
        <p className="text-sm text-gray-600">Please wait while we redirect you</p>
      </div>
    </div>
  )
}