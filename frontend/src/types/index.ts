// User Types
export interface User {
  id: string
  userId: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  address?: string
  profileImage?: string
  role?: {
    id: string
    name: string
    permissions: string[]
  }
  church?: Church
  churchId: string
  branchId?: string
  isActive: boolean
  isEmailVerified: boolean
  lastLoginAt?: string
  joinDate?: string
  createdAt: string
  updatedAt: string
}

// Member Types
export interface Member extends User {
  branch?: Branch
  status?: 'active' | 'inactive' | 'suspended'
}

// Church Types
export interface Church {
  id: string
  name: string
  slug: string
  churchCode: string
  email: string
  phone?: string
  address?: string
  website?: string
  description?: string
  logo?: string
  defaultCurrency: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  settings: ChurchSettings
}

export interface ChurchSettings {
  allowSelfRegistration: boolean
  requireEmailVerification: boolean
  timezone: string
  theme: Record<string, unknown>
  features: {
    events: boolean
    donations: boolean
    messaging: boolean
    calendar: boolean
    onlineGiving: boolean
    memberDirectory: boolean
    eventRsvp: boolean
    recurringEvents: boolean
  }
  eventSettings: Record<string, unknown>
  donationSettings: Record<string, unknown>
}

// Event Types
export interface Event {
  id: string
  title: string
  description?: string
  type: string
  startDate: string
  endDate: string
  location?: string
  virtualLink?: string
  maxAttendees?: number
  isPublic: boolean
  requiresRsvp: boolean
  allowDonations: boolean
  imageUrl?: string
  churchId: string
  branchId?: string
  createdBy: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  rsvpStats?: {
    total: number
    attending: number
    notAttending: number
    maybe: number
    pending: number
  }
  socialStats?: {
    likesCount: number
    commentsCount: number
    userLiked: boolean
    userRsvp?: RSVP
  }
  likes?: EventLike[]
  comments?: EventComment[]
}

// RSVP Types
export interface RSVP {
  id: string
  eventId: string
  memberId: string
  status: 'pending' | 'attending' | 'not_attending' | 'maybe'
  attendeeCount?: number
  notes?: string
  createdAt: string
  updatedAt: string
  member?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
}

// Event Social Types
export interface EventLike {
  id: string
  eventId: string
  memberId: string
  createdAt: string
  member: {
    id: string
    firstName: string
    lastName: string
    profileImage?: string
  }
}

export interface EventComment {
  id: string
  eventId: string
  memberId: string
  parentCommentId?: string
  content: string
  createdAt: string
  updatedAt: string
  member: {
    id: string
    firstName: string
    lastName: string
    profileImage?: string
  }
  replies?: EventComment[]
}

// Donation Types
export interface Finance {
  id: string
  amount: number
  currency: string
  type: string
  method: string
  status: 'pending' | 'completed' | 'failed' | 'refunded' | 'cancelled'
  isAnonymous: boolean
  notes?: string
  receiptNumber: string
  eventId?: string
  projectId?: string
  memberId?: string
  recordedBy: string
  churchId: string
  branchId?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  member?: {
    id: string
    firstName: string
    lastName: string
    email: string
  }
  event?: {
    id: string
    title: string
  }
  project?: {
    id: string
    name: string
  }
}

// Payment Method Types
export interface PaymentMethod {
  id: string
  name: string
  type: 'mobile_money' | 'bank_card' | 'bank_transfer' | 'cash'
  provider?: 'airtel' | 'mtn' | 'zamtel' | 'visa' | 'mastercard'
  accountNumber?: string
  accountName?: string
  bankName?: string
  phoneNumber?: string
  isActive: boolean
  isDefault: boolean
  churchId: string
  createdAt: string
  updatedAt: string
}

// Donation Category Types
export interface FinanceCategory {
  id: string
  name: string
  description?: string
  isActive: boolean
  churchId: string
  createdAt: string
  updatedAt: string
}

// Currency Types
export interface FinanceProject {
  id: string
  name: string
  description?: string
  targetAmount: number
  currency: string
  status: 'planning' | 'active' | 'completed' | 'cancelled' | 'on_hold'
  startDate?: string
  endDate?: string
  isActive: boolean
  churchId: string
  createdAt: string
  updatedAt: string
  totalRaised?: number
  progress?: number
}

export interface Currency {
  code: string
  name: string
  symbol: string
  isActive: boolean
}

// Church Financial Settings
export interface ChurchFinancialSettings {
  defaultCurrency: string
  enableOnlineGiving: boolean
  enableAnonymousDonations: boolean
  requireReceiptGeneration: boolean
  financeCategories: string[]
  paymentMethods: PaymentMethod[]
}

// Role Types
export interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
  isSystem: boolean
  churchId: string
  createdAt: string
  updatedAt: string
}

// Branch Types
export interface Branch {
  id: string
  name: string
  slug: string
  description?: string
  address?: string
  phone?: string
  email?: string
  coordinates?: {
    lat: number
    lng: number
  }
  capacity?: string
  isMainBranch: boolean
  branchLeader?: string
  churchId: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  leader?: User
  memberCount?: number
}

// Member Types
export interface Member extends User {
  branch?: Branch
}

// API Response Types
export interface ApiResponse<T> {
  status: 'success' | 'error'
  data: T
  message?: string
}

export interface PaginatedResponse<T> {
  status: 'success'
  data: {
    items: T[]
    pagination: {
      page: number
      limit: number
      total: number
      pages: number
    }
  }
}

export interface Notification {
  id: string
  churchId: string
  memberId: string
  type: string
  title: string
  message?: string
  link?: string
  metadata?: Record<string, unknown>
  isRead: boolean
  readAt?: string
  createdAt: string
}

// Auth Context Types
export interface AuthContextType {
  user: User | null
  church: Church | null
  login: (emailOrUserId: string, password: string) => Promise<void>
  logout: () => void
  refreshTokens: () => Promise<void>
  checkAuthStatus: () => Promise<void>
  loading: boolean
  isInitialized: boolean
  isAuthenticated: boolean
  error: import('@/lib/errors').AuthError | null
  clearError: () => void
}

// Auth Error Types - moved to @/lib/errors for better organization
// This interface is kept for backward compatibility
export interface AuthError {
  code: 'INVALID_CREDENTIALS' | 'TOKEN_EXPIRED' | 'REFRESH_FAILED' | 'NETWORK_ERROR' | 'CHURCH_NOT_FOUND' | 'ACCOUNT_INACTIVE' | 'UNKNOWN_ERROR'
  message: string
  details?: Record<string, unknown>
  retryable: boolean
}

// Announcement Types
export interface Announcement {
  id: string
  churchId: string
  eventId?: string
  authorId: string
  title: string
  content: string
  summary?: string
  type: 'general' | 'urgent' | 'event_related' | 'service_update' | 'prayer_request' | 'community_news' | 'financial' | 'special_event' | 'ministry_update' | 'volunteer_opportunity' | 'other'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  status: 'draft' | 'scheduled' | 'published' | 'archived' | 'expired'
  targetAudience: 'all_members' | 'specific_branches' | 'specific_roles' | 'custom_group'
  targetBranches?: string[]
  targetRoles?: string[]
  imageUrl?: string
  attachments?: AnnouncementAttachment[]
  externalLinks?: AnnouncementLink[]
  tags?: string[]
  publishedAt?: string
  scheduledFor?: string
  expiresAt?: string
  isPinned: boolean
  allowComments: boolean
  requiresAcknowledgment: boolean
  sendNotification: boolean
  notificationChannels?: {
    email: boolean
    sms: boolean
    push: boolean
    inApp: boolean
  }
  viewCount: number
  metadata?: Record<string, unknown>
  createdAt: string
  updatedAt: string
  author?: {
    id: string
    firstName: string
    lastName: string
    email: string
    profileImage?: string
  }
  event?: {
    id: string
    title: string
    startDate: string
    type: string
  }
  stats?: {
    viewCount: number
    commentCount: number
    reactionCount: number
    acknowledgmentCount?: number
    isViewed?: boolean
    isAcknowledged?: boolean
    userReaction?: 'like' | 'love' | 'pray' | 'amen' | 'heart' | 'thumbs_up' | null
  }
}

export interface AnnouncementAttachment {
  name: string
  url: string
  type: string
  size?: number
}

export interface AnnouncementLink {
  title: string
  url: string
  description?: string
}

export interface AnnouncementComment {
  id: string
  announcementId: string
  parentCommentId?: string
  authorId: string
  content: string
  createdAt: string
  updatedAt: string
  author: {
    id: string
    firstName: string
    lastName: string
    profileImage?: string
  }
  replies?: AnnouncementComment[]
}

export interface AnnouncementReaction {
  id: string
  announcementId: string
  memberId: string
  reactionType: 'like' | 'love' | 'pray' | 'amen' | 'heart' | 'thumbs_up'
  createdAt: string
}