// Currency constants
export const CURRENCIES = [
  { code: 'ZM<PERSON>', name: 'Zambian Kwacha', symbol: '<PERSON>' },
  { code: 'USD', name: 'US Dollar', symbol: '$' },
  { code: 'EUR', name: 'Euro', symbol: '€' },
  { code: 'GBP', name: 'British Pound', symbol: '£' },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R' },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh' },
  { code: 'UGX', name: 'Ugandan Shilling', symbol: 'USh' },
  { code: 'TZS', name: 'Tanzanian Shilling', symbol: 'TSh' },
]

// Mobile Money Providers
export const MOBILE_MONEY_PROVIDERS = [
  { value: 'airtel', label: 'Airtel Money', color: 'red' },
  { value: 'mtn', label: 'MTN Mobile Money', color: 'yellow' },
  { value: 'zamtel', label: 'Zamtel Kwacha', color: 'green' },
]

// Card Providers
export const CARD_PROVIDERS = [
  { value: 'visa', label: 'Visa', color: 'blue' },
  { value: 'mastercard', label: 'Mastercard', color: 'orange' },
]

// Donation Types
export const DONATION_TYPES = [
  { value: 'tithe', label: 'Tithe' },
  { value: 'offering', label: 'Offering' },
  { value: 'special_offering', label: 'Special Offering' },
  { value: 'building_fund', label: 'Building Fund' },
  { value: 'missions', label: 'Missions' },
  { value: 'charity', label: 'Charity' },
  { value: 'thanksgiving', label: 'Thanksgiving' },
  { value: 'first_fruit', label: 'First Fruit' },
  { value: 'seed_offering', label: 'Seed Offering' },
  { value: 'other', label: 'Other' },
]

// Payment Method Types
export const PAYMENT_METHOD_TYPES = [
  { value: 'mobile_money', label: 'Mobile Money', icon: 'Smartphone' },
  { value: 'bank_card', label: 'Bank Card', icon: 'CreditCard' },
  { value: 'bank_transfer', label: 'Bank Transfer', icon: 'Building2' },
  { value: 'cash', label: 'Cash', icon: 'Banknote' },
]

// Donation Status
export const DONATION_STATUS = [
  { value: 'pending', label: 'Pending', color: 'yellow' },
  { value: 'completed', label: 'Completed', color: 'green' },
  { value: 'failed', label: 'Failed', color: 'red' },
  { value: 'refunded', label: 'Refunded', color: 'orange' },
  { value: 'cancelled', label: 'Cancelled', color: 'gray' },
]