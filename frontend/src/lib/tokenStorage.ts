/**
 * Enhanced Token Storage Service
 * 
 * Provides dual storage mechanism for localStorage and cookies with:
 * - Event-based token change notifications
 * - Fallback mechanisms for storage failures
 * - Cross-tab synchronization
 */

export interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresAt: number
  issuedAt: number
}

export interface StoredAuthData {
  tokens: TokenPair
  user?: any
  church?: any
  lastRefresh: number
}

export type TokenChangeCallback = (tokens: TokenPair | null) => void

class TokenStorageService {
  private static instance: TokenStorageService
  private callbacks: Set<TokenChangeCallback> = new Set()
  private storageAvailable = {
    localStorage: false,
    cookies: false
  }
  private pollingInterval: NodeJS.Timeout | null = null
  private lastKnownTokens: TokenPair | null = null
  private storageEventsSupported = false

  private constructor() {
    this.checkStorageAvailability()
    this.setupCrossTabSynchronization()
  }

  public static getInstance(): TokenStorageService {
    if (!TokenStorageService.instance) {
      TokenStorageService.instance = new TokenStorageService()
    }
    return TokenStorageService.instance
  }

  /**
   * Check availability of storage mechanisms
   */
  private checkStorageAvailability(): void {
    // Check localStorage availability
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const testKey = '__storage_test__'
        localStorage.setItem(testKey, 'test')
        localStorage.removeItem(testKey)
        this.storageAvailable.localStorage = true
      }
    } catch (error) {
      console.warn('localStorage not available:', error)
      this.storageAvailable.localStorage = false
    }

    // Check cookies availability
    try {
      if (typeof document !== 'undefined') {
        // Test if we can set and read cookies
        const testCookie = '__cookie_test__'
        document.cookie = `${testCookie}=test; path=/`
        this.storageAvailable.cookies = document.cookie.includes(testCookie)
        // Clean up test cookie
        document.cookie = `${testCookie}=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/`
      }
    } catch (error) {
      console.warn('Cookies not available:', error)
      this.storageAvailable.cookies = false
    }
  }

  /**
   * Dynamically check if localStorage is available (for testing)
   */
  private isLocalStorageAvailable(): boolean {
    try {
      if (typeof window !== 'undefined' && window.localStorage) {
        const testKey = '__storage_test__'
        localStorage.setItem(testKey, 'test')
        localStorage.removeItem(testKey)
        return true
      }
    } catch (error) {
      return false
    }
    return false
  }

  /**
   * Dynamically check if cookies are available (for testing)
   */
  private isCookiesAvailable(): boolean {
    try {
      if (typeof document !== 'undefined') {
        return true // Basic check - assume cookies work if document exists
      }
    } catch (error) {
      return false
    }
    return false
  }

  /**
   * Set up cross-tab synchronization with storage events and polling fallback
   */
  private setupCrossTabSynchronization(): void {
    if (typeof window === 'undefined') return

    // Try to set up storage event listener
    try {
      window.addEventListener('storage', this.handleStorageEvent.bind(this))
      this.storageEventsSupported = true
      console.debug('Storage events supported - using event-based synchronization')
    } catch (error) {
      console.warn('Storage events not supported:', error)
      this.storageEventsSupported = false
    }

    // Set up polling fallback for browsers that don't support storage events
    // or as a backup mechanism
    if (!this.storageEventsSupported) {
      this.startPolling()
      console.debug('Using polling-based synchronization')
    }

    // Initialize last known tokens
    this.lastKnownTokens = this.getTokens()
  }

  /**
   * Handle storage events for cross-tab synchronization
   */
  private handleStorageEvent(event: StorageEvent): void {
    if (event.key === 'auth_tokens' && event.newValue !== event.oldValue) {
      const tokens = event.newValue ? this.parseTokens(event.newValue) : null
      this.updateLastKnownTokens(tokens)
      this.notifyCallbacks(tokens)
    }
  }

  /**
   * Start polling for token changes (fallback for unsupported browsers)
   */
  private startPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
    }

    // Poll every 1 second for token changes
    this.pollingInterval = setInterval(() => {
      this.checkForTokenChanges()
    }, 1000)
  }

  /**
   * Stop polling for token changes
   */
  private stopPolling(): void {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval)
      this.pollingInterval = null
    }
  }

  /**
   * Check for token changes via polling
   */
  private checkForTokenChanges(): void {
    const currentTokens = this.getTokens()
    
    // Compare with last known tokens
    if (!this.tokensEqual(currentTokens, this.lastKnownTokens)) {
      this.updateLastKnownTokens(currentTokens)
      this.notifyCallbacks(currentTokens)
    }
  }

  /**
   * Update last known tokens and handle cleanup
   */
  private updateLastKnownTokens(tokens: TokenPair | null): void {
    this.lastKnownTokens = tokens
  }

  /**
   * Compare two token pairs for equality
   */
  private tokensEqual(tokens1: TokenPair | null, tokens2: TokenPair | null): boolean {
    if (tokens1 === null && tokens2 === null) return true
    if (tokens1 === null || tokens2 === null) return false
    
    return (
      tokens1.accessToken === tokens2.accessToken &&
      tokens1.refreshToken === tokens2.refreshToken &&
      tokens1.expiresAt === tokens2.expiresAt &&
      tokens1.issuedAt === tokens2.issuedAt
    )
  }

  /**
   * Store tokens in both localStorage and cookies
   */
  public setTokens(accessToken: string, refreshToken: string): void {
    const now = Date.now()
    const tokenPair: TokenPair = {
      accessToken,
      refreshToken,
      expiresAt: now + (15 * 60 * 1000), // 15 minutes for access token
      issuedAt: now
    }

    const authData: StoredAuthData = {
      tokens: tokenPair,
      lastRefresh: now
    }

    let success = false

    // Try localStorage first (use dynamic check for better test compatibility)
    if (this.storageAvailable.localStorage || this.isLocalStorageAvailable()) {
      try {
        localStorage.setItem('auth_tokens', JSON.stringify(authData))
        localStorage.setItem('token', accessToken) // Legacy support
        localStorage.setItem('refreshToken', refreshToken) // Legacy support
        success = true
        this.storageAvailable.localStorage = true
      } catch (error) {
        console.warn('Failed to store tokens in localStorage:', error)
        this.storageAvailable.localStorage = false
      }
    }

    // Try cookies as backup or primary if localStorage failed
    if (this.storageAvailable.cookies || this.isCookiesAvailable()) {
      try {
        this.setCookie('auth_tokens', JSON.stringify(authData), 7 * 24 * 60) // 7 days
        this.setCookie('token', accessToken, 15) // 15 minutes
        this.setCookie('refreshToken', refreshToken, 7 * 24 * 60) // 7 days
        success = true
        this.storageAvailable.cookies = true
      } catch (error) {
        console.warn('Failed to store tokens in cookies:', error)
        this.storageAvailable.cookies = false
      }
    }

    if (!success) {
      throw new Error('Failed to store tokens: No storage mechanism available')
    }

    // Notify callbacks about token change
    this.notifyCallbacks(tokenPair)
  }

  /**
   * Get access token from storage
   */
  public getAccessToken(): string | null {
    const authData = this.getStoredAuthData()
    if (!authData) {
      // Fallback to legacy storage
      return this.getLegacyToken('token')
    }

    // Check if token is expired
    if (authData.tokens.expiresAt <= Date.now()) {
      return null
    }

    return authData.tokens.accessToken
  }

  /**
   * Get refresh token from storage
   */
  public getRefreshToken(): string | null {
    const authData = this.getStoredAuthData()
    if (!authData) {
      // Fallback to legacy storage
      return this.getLegacyToken('refreshToken')
    }

    return authData.tokens.refreshToken
  }

  /**
   * Get complete token pair from storage
   */
  public getTokens(): TokenPair | null {
    const authData = this.getStoredAuthData()
    if (!authData) {
      // Try to construct from legacy storage
      const accessToken = this.getLegacyToken('token')
      const refreshToken = this.getLegacyToken('refreshToken')
      
      if (accessToken && refreshToken) {
        return {
          accessToken,
          refreshToken,
          expiresAt: Date.now() + (15 * 60 * 1000), // Assume 15 minutes
          issuedAt: Date.now()
        }
      }
      return null
    }

    return authData.tokens
  }

  /**
   * Clear all stored tokens
   */
  public clearTokens(): void {
    // Clear from localStorage (use dynamic check for better test compatibility)
    if (this.storageAvailable.localStorage || this.isLocalStorageAvailable()) {
      try {
        localStorage.removeItem('auth_tokens')
        localStorage.removeItem('token') // Legacy cleanup
        localStorage.removeItem('refreshToken') // Legacy cleanup
      } catch (error) {
        console.warn('Failed to clear tokens from localStorage:', error)
      }
    }

    // Clear from cookies
    if (this.storageAvailable.cookies || this.isCookiesAvailable()) {
      try {
        this.deleteCookie('auth_tokens')
        this.deleteCookie('token') // Legacy cleanup
        this.deleteCookie('refreshToken') // Legacy cleanup
      } catch (error) {
        console.warn('Failed to clear tokens from cookies:', error)
      }
    }

    // Notify callbacks about token removal
    this.notifyCallbacks(null)
  }

  /**
   * Check if tokens are available and valid
   */
  public hasValidTokens(): boolean {
    const tokens = this.getTokens()
    if (!tokens) return false

    // Check if access token is not expired (with 1 minute buffer)
    return tokens.expiresAt > (Date.now() + 60 * 1000)
  }

  /**
   * Subscribe to token changes
   */
  public onTokensChanged(callback: TokenChangeCallback): () => void {
    this.callbacks.add(callback)
    
    // Return unsubscribe function
    return () => {
      this.callbacks.delete(callback)
    }
  }

  /**
   * Get storage availability status
   */
  public getStorageStatus() {
    return { ...this.storageAvailable }
  }

  /**
   * Get synchronization status
   */
  public getSynchronizationStatus() {
    return {
      storageEventsSupported: this.storageEventsSupported,
      pollingActive: this.pollingInterval !== null,
      lastKnownTokens: this.lastKnownTokens
    }
  }

  /**
   * Cleanup resources (for testing and component unmounting)
   */
  public cleanup(): void {
    this.stopPolling()
    this.callbacks.clear()
    
    if (typeof window !== 'undefined' && this.storageEventsSupported) {
      try {
        window.removeEventListener('storage', this.handleStorageEvent.bind(this))
      } catch (error) {
        console.warn('Failed to remove storage event listener:', error)
      }
    }
  }

  // Private helper methods

  private getStoredAuthData(): StoredAuthData | null {
    // Try localStorage first (use dynamic check for better test compatibility)
    if (this.storageAvailable.localStorage || this.isLocalStorageAvailable()) {
      try {
        const stored = localStorage.getItem('auth_tokens')
        if (stored) {
          return JSON.parse(stored)
        }
      } catch (error) {
        console.warn('Failed to parse auth data from localStorage:', error)
      }
    }

    // Fallback to cookies
    if (this.storageAvailable.cookies || this.isCookiesAvailable()) {
      try {
        const stored = this.getCookie('auth_tokens')
        if (stored) {
          return JSON.parse(stored)
        }
      } catch (error) {
        console.warn('Failed to parse auth data from cookies:', error)
      }
    }

    return null
  }

  private getLegacyToken(key: string): string | null {
    // Try localStorage first (use dynamic check for better test compatibility)
    if (this.storageAvailable.localStorage || this.isLocalStorageAvailable()) {
      try {
        const token = localStorage.getItem(key)
        if (token) return token
      } catch (error) {
        console.warn(`Failed to get ${key} from localStorage:`, error)
      }
    }

    // Fallback to cookies
    if (this.storageAvailable.cookies || this.isCookiesAvailable()) {
      try {
        return this.getCookie(key)
      } catch (error) {
        console.warn(`Failed to get ${key} from cookies:`, error)
      }
    }

    return null
  }

  private setCookie(name: string, value: string, minutes: number): void {
    if (typeof document === 'undefined') return

    const expires = new Date()
    expires.setTime(expires.getTime() + minutes * 60 * 1000)
    
    // Use secure and httpOnly flags in production
    const isProduction = process.env.NODE_ENV === 'production'
    const secureFlag = isProduction ? '; Secure' : ''
    const sameSiteFlag = '; SameSite=Strict'
    
    document.cookie = `${name}=${encodeURIComponent(value)}; expires=${expires.toUTCString()}; path=/${secureFlag}${sameSiteFlag}`
  }

  private getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null

    const nameEQ = name + '='
    const ca = document.cookie.split(';')
    
    for (let i = 0; i < ca.length; i++) {
      let c = ca[i]
      while (c.charAt(0) === ' ') c = c.substring(1, c.length)
      if (c.indexOf(nameEQ) === 0) {
        return decodeURIComponent(c.substring(nameEQ.length, c.length))
      }
    }
    return null
  }

  private deleteCookie(name: string): void {
    if (typeof document === 'undefined') return
    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:01 GMT; path=/`
  }

  private parseTokens(tokenString: string): TokenPair | null {
    try {
      const authData: StoredAuthData = JSON.parse(tokenString)
      return authData.tokens
    } catch (error) {
      console.warn('Failed to parse token string:', error)
      return null
    }
  }

  private notifyCallbacks(tokens: TokenPair | null): void {
    this.callbacks.forEach(callback => {
      try {
        callback(tokens)
      } catch (error) {
        console.error('Error in token change callback:', error)
      }
    })
  }
}

// Export singleton instance
export const tokenStorage = TokenStorageService.getInstance()