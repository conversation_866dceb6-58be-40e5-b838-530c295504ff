/**
 * Integration test for cross-tab authentication synchronization
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { tokenStorage } from '../tokenStorage'

describe('Cross-Tab Authentication Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue(null),
      setItem: vi.fn(),
      removeItem: vi.fn(),
    }

    // Mock window
    Object.defineProperty(global, 'window', {
      value: {
        localStorage: mockLocalStorage,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      },
      writable: true,
    })

    // Mock document
    Object.defineProperty(global, 'document', {
      value: {
        cookie: '',
      },
      writable: true,
    })

    // Reset token storage state
    tokenStorage.clearTokens()
  })

  afterEach(() => {
    // Cleanup
    tokenStorage.cleanup()
  })

  describe('Token Storage Cross-Tab Synchronization', () => {
    it('should provide callback subscription mechanism', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      expect(typeof unsubscribe).toBe('function')
      
      // Test that callback is called when tokens are set
      tokenStorage.setTokens('test-access', 'test-refresh')
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        accessToken: 'test-access',
        refreshToken: 'test-refresh',
      }))
      
      // Test that callback is called when tokens are cleared
      tokenStorage.clearTokens()
      expect(callback).toHaveBeenCalledWith(null)
      
      // Clean up
      unsubscribe()
    })

    it('should support multiple subscribers', () => {
      const callback1 = vi.fn()
      const callback2 = vi.fn()
      
      const unsubscribe1 = tokenStorage.onTokensChanged(callback1)
      const unsubscribe2 = tokenStorage.onTokensChanged(callback2)
      
      // Set tokens
      tokenStorage.setTokens('test-access', 'test-refresh')
      
      // Both callbacks should be called
      expect(callback1).toHaveBeenCalled()
      expect(callback2).toHaveBeenCalled()
      
      // Clean up
      unsubscribe1()
      unsubscribe2()
    })

    it('should handle unsubscription correctly', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Unsubscribe
      unsubscribe()
      
      // Set tokens after unsubscribing
      tokenStorage.setTokens('test-access', 'test-refresh')
      
      // Callback should not be called
      expect(callback).not.toHaveBeenCalled()
    })

    it('should provide synchronization status', () => {
      const status = tokenStorage.getSynchronizationStatus()
      
      expect(status).toHaveProperty('storageEventsSupported')
      expect(status).toHaveProperty('pollingActive')
      expect(status).toHaveProperty('lastKnownTokens')
      
      expect(typeof status.storageEventsSupported).toBe('boolean')
      expect(typeof status.pollingActive).toBe('boolean')
    })

    it('should handle callback errors gracefully', () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Test callback error')
      })
      const normalCallback = vi.fn()
      
      const unsubscribe1 = tokenStorage.onTokensChanged(errorCallback)
      const unsubscribe2 = tokenStorage.onTokensChanged(normalCallback)
      
      // Setting tokens should not throw despite error callback
      expect(() => {
        tokenStorage.setTokens('test-access', 'test-refresh')
      }).not.toThrow()
      
      // Both callbacks should have been called
      expect(errorCallback).toHaveBeenCalled()
      expect(normalCallback).toHaveBeenCalled()
      
      // Clean up
      unsubscribe1()
      unsubscribe2()
    })
  })

  describe('Token Management', () => {
    it('should store and retrieve tokens correctly', () => {
      // Set tokens
      tokenStorage.setTokens('access-token-123', 'refresh-token-456')
      
      // Retrieve tokens
      const accessToken = tokenStorage.getAccessToken()
      const refreshToken = tokenStorage.getRefreshToken()
      const tokenPair = tokenStorage.getTokens()
      
      expect(accessToken).toBe('access-token-123')
      expect(refreshToken).toBe('refresh-token-456')
      expect(tokenPair).toMatchObject({
        accessToken: 'access-token-123',
        refreshToken: 'refresh-token-456',
      })
    })

    it('should clear tokens correctly', () => {
      // Set tokens first
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Verify tokens exist
      expect(tokenStorage.getAccessToken()).toBe('access-token')
      
      // Clear tokens
      tokenStorage.clearTokens()
      
      // Verify tokens are cleared
      expect(tokenStorage.getAccessToken()).toBeNull()
      expect(tokenStorage.getRefreshToken()).toBeNull()
      expect(tokenStorage.getTokens()).toBeNull()
    })

    it('should validate token expiration', () => {
      // Mock current time
      const now = Date.now()
      vi.spyOn(Date, 'now').mockReturnValue(now)
      
      // Set tokens
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Should have valid tokens initially
      expect(tokenStorage.hasValidTokens()).toBe(true)
      
      // Mock time advancement to expire tokens
      vi.spyOn(Date, 'now').mockReturnValue(now + 20 * 60 * 1000) // 20 minutes later
      
      // Should not have valid tokens after expiration
      expect(tokenStorage.hasValidTokens()).toBe(false)
      
      // Restore Date.now
      vi.restoreAllMocks()
    })
  })

  describe('Cross-Tab Scenarios', () => {
    it('should simulate cross-tab logout scenario', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Simulate user logged in
      tokenStorage.setTokens('access-token', 'refresh-token')
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        accessToken: 'access-token',
      }))
      
      // Reset callback calls
      callback.mockClear()
      
      // Simulate logout in another tab
      tokenStorage.clearTokens()
      expect(callback).toHaveBeenCalledWith(null)
      
      unsubscribe()
    })

    it('should simulate cross-tab token refresh scenario', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Simulate initial tokens
      tokenStorage.setTokens('old-access-token', 'old-refresh-token')
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        accessToken: 'old-access-token',
      }))
      
      // Reset callback calls
      callback.mockClear()
      
      // Simulate token refresh in another tab
      tokenStorage.setTokens('new-access-token', 'new-refresh-token')
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
      }))
      
      unsubscribe()
    })
  })

  describe('Error Handling', () => {
    it('should handle storage failures gracefully', () => {
      // Mock localStorage to throw errors
      const mockWindow = global.window as any
      mockWindow.localStorage.setItem.mockImplementation(() => {
        throw new Error('Storage quota exceeded')
      })
      
      // Should throw error when no storage is available
      expect(() => {
        tokenStorage.setTokens('access-token', 'refresh-token')
      }).toThrow('Failed to store tokens: No storage mechanism available')
    })

    it('should handle malformed stored data gracefully', () => {
      const mockWindow = global.window as any
      mockWindow.localStorage.getItem.mockReturnValue('invalid-json')
      
      // Should return null for malformed data
      expect(tokenStorage.getAccessToken()).toBeNull()
      expect(tokenStorage.getRefreshToken()).toBeNull()
      expect(tokenStorage.getTokens()).toBeNull()
    })
  })
})