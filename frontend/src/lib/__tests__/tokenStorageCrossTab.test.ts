/**
 * Cross-tab synchronization tests for TokenStorageService
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { tokenStorage, TokenPair } from '../tokenStorage'

describe('TokenStorageService Cross-Tab Synchronization', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock localStorage
    const mockLocalStorage = {
      getItem: vi.fn().mockReturnValue(null),
      setItem: vi.fn(),
      removeItem: vi.fn(),
    }

    // Mock window
    Object.defineProperty(global, 'window', {
      value: {
        localStorage: mockLocalStorage,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      },
      writable: true,
    })

    // Mock document
    Object.defineProperty(global, 'document', {
      value: {
        cookie: '',
      },
      writable: true,
    })

    // Reset token storage state
    tokenStorage.clearTokens()
  })

  afterEach(() => {
    // Cleanup
    tokenStorage.cleanup()
  })

  describe('Token Change Callbacks', () => {
    it('should support subscribing to token changes', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      expect(typeof unsubscribe).toBe('function')
      
      // Clean up
      unsubscribe()
    })

    it('should notify callbacks when tokens are set', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Set tokens
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Verify callback was called
      expect(callback).toHaveBeenCalledWith(expect.objectContaining({
        accessToken: 'access-token',
        refreshToken: 'refresh-token',
      }))
      
      unsubscribe()
    })

    it('should notify callbacks when tokens are cleared', () => {
      const callback = vi.fn()
      
      // First set some tokens
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Then subscribe to changes
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Clear tokens
      tokenStorage.clearTokens()
      
      // Verify callback was called with null
      expect(callback).toHaveBeenCalledWith(null)
      
      unsubscribe()
    })

    it('should support multiple callbacks', () => {
      const callback1 = vi.fn()
      const callback2 = vi.fn()
      
      const unsubscribe1 = tokenStorage.onTokensChanged(callback1)
      const unsubscribe2 = tokenStorage.onTokensChanged(callback2)
      
      // Set tokens
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Both callbacks should be called
      expect(callback1).toHaveBeenCalled()
      expect(callback2).toHaveBeenCalled()
      
      unsubscribe1()
      unsubscribe2()
    })

    it('should remove callbacks when unsubscribed', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Unsubscribe immediately
      unsubscribe()
      
      // Set tokens
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Callback should not be called
      expect(callback).not.toHaveBeenCalled()
    })

    it('should handle callback errors gracefully', () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Callback error')
      })
      const normalCallback = vi.fn()
      
      const unsubscribe1 = tokenStorage.onTokensChanged(errorCallback)
      const unsubscribe2 = tokenStorage.onTokensChanged(normalCallback)
      
      // Set tokens - should not throw despite error callback
      expect(() => {
        tokenStorage.setTokens('access-token', 'refresh-token')
      }).not.toThrow()
      
      // Both callbacks should have been called
      expect(errorCallback).toHaveBeenCalled()
      expect(normalCallback).toHaveBeenCalled()
      
      unsubscribe1()
      unsubscribe2()
    })
  })

  describe('Synchronization Status', () => {
    it('should report synchronization status', () => {
      const status = tokenStorage.getSynchronizationStatus()
      
      expect(status).toHaveProperty('storageEventsSupported')
      expect(status).toHaveProperty('pollingActive')
      expect(status).toHaveProperty('lastKnownTokens')
      expect(typeof status.storageEventsSupported).toBe('boolean')
      expect(typeof status.pollingActive).toBe('boolean')
    })
  })

  describe('Storage Mechanisms', () => {
    it('should store tokens in localStorage when available', () => {
      const mockSetItem = vi.fn()
      const mockWindow = global.window as any
      mockWindow.localStorage.setItem = mockSetItem
      
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      expect(mockSetItem).toHaveBeenCalledWith('auth_tokens', expect.any(String))
      expect(mockSetItem).toHaveBeenCalledWith('token', 'access-token')
      expect(mockSetItem).toHaveBeenCalledWith('refreshToken', 'refresh-token')
    })

    it('should retrieve tokens from localStorage when available', () => {
      const mockGetItem = vi.fn()
      const mockWindow = global.window as any
      mockWindow.localStorage.getItem = mockGetItem
      
      // Mock stored auth data
      const authData = {
        tokens: {
          accessToken: 'stored-access-token',
          refreshToken: 'stored-refresh-token',
          expiresAt: Date.now() + 900000,
          issuedAt: Date.now(),
        },
        lastRefresh: Date.now(),
      }
      
      mockGetItem.mockReturnValue(JSON.stringify(authData))
      
      const accessToken = tokenStorage.getAccessToken()
      expect(accessToken).toBe('stored-access-token')
      
      const refreshToken = tokenStorage.getRefreshToken()
      expect(refreshToken).toBe('stored-refresh-token')
    })

    it('should handle localStorage failures gracefully', () => {
      const mockSetItem = vi.fn(() => {
        throw new Error('localStorage not available')
      })
      const mockWindow = global.window as any
      mockWindow.localStorage.setItem = mockSetItem
      
      // Should not throw error
      expect(() => {
        tokenStorage.setTokens('access-token', 'refresh-token')
      }).toThrow('Failed to store tokens: No storage mechanism available')
    })
  })

  describe('Token Validation', () => {
    it('should validate token expiration', () => {
      // Mock expired tokens
      const mockGetItem = vi.fn()
      const mockWindow = global.window as any
      mockWindow.localStorage.getItem = mockGetItem
      
      const expiredAuthData = {
        tokens: {
          accessToken: 'expired-token',
          refreshToken: 'refresh-token',
          expiresAt: Date.now() - 1000, // Expired
          issuedAt: Date.now() - 900000,
        },
        lastRefresh: Date.now(),
      }
      
      mockGetItem.mockReturnValue(JSON.stringify(expiredAuthData))
      
      const accessToken = tokenStorage.getAccessToken()
      expect(accessToken).toBeNull()
      
      const hasValidTokens = tokenStorage.hasValidTokens()
      expect(hasValidTokens).toBe(false)
    })

    it('should validate non-expired tokens', () => {
      // Mock valid tokens
      const mockGetItem = vi.fn()
      const mockWindow = global.window as any
      mockWindow.localStorage.getItem = mockGetItem
      
      const validAuthData = {
        tokens: {
          accessToken: 'valid-token',
          refreshToken: 'refresh-token',
          expiresAt: Date.now() + 900000, // Valid for 15 minutes
          issuedAt: Date.now(),
        },
        lastRefresh: Date.now(),
      }
      
      mockGetItem.mockReturnValue(JSON.stringify(validAuthData))
      
      const accessToken = tokenStorage.getAccessToken()
      expect(accessToken).toBe('valid-token')
      
      const hasValidTokens = tokenStorage.hasValidTokens()
      expect(hasValidTokens).toBe(true)
    })
  })

  describe('Cleanup', () => {
    it('should cleanup resources properly', () => {
      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      
      // Cleanup should remove all callbacks
      tokenStorage.cleanup()
      
      // Set tokens after cleanup
      tokenStorage.setTokens('access-token', 'refresh-token')
      
      // Callback should not be called
      expect(callback).not.toHaveBeenCalled()
    })
  })
})