/**
 * Authentication Context Tests
 * 
 * Tests for the enhanced AuthProvider with:
 * - Token storage integration
 * - Cross-tab synchronization
 * - Error handling
 * - Loading states
 * - Automatic authentication status checking
 */

import React from 'react'
import { render, screen, waitFor, act, renderHook } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { AuthProvider, useAuth } from '../auth'
import { tokenStorage } from '../tokenStorage'
import { apiClient } from '../api'
import { User, Church, AuthError } from '@/types'

import { vi } from 'vitest'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('../tokenStorage', () => ({
  tokenStorage: {
    hasValidTokens: vi.fn(),
    getAccessToken: vi.fn(),
    getRefreshToken: vi.fn(),
    setTokens: vi.fn(),
    clearTokens: vi.fn(),
    onTokensChanged: vi.fn(),
  },
}))

vi.mock('../api', () => ({
  apiClient: {
    getProfile: vi.fn(),
    login: vi.fn(),
  },
}))

// Mock fetch for token refresh
global.fetch = vi.fn()

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  churchId: 'church-1',
  isActive: true,
  isEmailVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  church: {
    id: 'church-1',
    name: 'Test Church',
    slug: 'test-church',
    churchCode: 'TEST123',
    email: '<EMAIL>',
    isActive: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    settings: {
      allowSelfRegistration: false,
      requireEmailVerification: true,
      timezone: 'UTC',
      theme: {},
      features: {
        events: true,
        donations: true,
        messaging: true,
        calendar: true,
        onlineGiving: true,
        memberDirectory: true,
        eventRsvp: true,
        recurringEvents: true,
      },
      eventSettings: {},
      donationSettings: {},
    },
  },
}

const mockTokens = {
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
  expiresAt: Date.now() + 15 * 60 * 1000,
  issuedAt: Date.now(),
}

describe('AuthProvider', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(tokenStorage.onTokensChanged as any).mockReturnValue(() => {})
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/test-church/dashboard',
        href: 'http://localhost:3000/test-church/dashboard',
      },
      writable: true,
    })
  })

  describe('Initialization', () => {
    it('should show loading screen during initialization', async () => {
      // Mock a slow API call to catch the loading state
      let resolveProfile: (value: any) => void
      const profilePromise = new Promise(resolve => {
        resolveProfile = resolve
      })
      
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockImplementation(() => profilePromise)
      
      render(
        <AuthProvider>
          <div>Test Content</div>
        </AuthProvider>
      )

      // Should show loading initially
      expect(screen.getByText('Loading...')).toBeInTheDocument()
      expect(screen.queryByText('Test Content')).not.toBeInTheDocument()
      
      // Resolve the profile call
      act(() => {
        resolveProfile({ data: { user: mockUser } })
      })
      
      // Wait for initialization to complete
      await waitFor(() => {
        expect(screen.getByText('Test Content')).toBeInTheDocument()
      })
    })

    it('should initialize with no user when no valid tokens exist', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)

      const TestComponent = () => {
        const { user, isAuthenticated, isInitialized } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? 'authenticated' : 'not authenticated'}</div>
            <div data-testid="initialized">{isInitialized ? 'initialized' : 'not initialized'}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('initialized')).toHaveTextContent('initialized')
      })

      expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
    })

    it('should initialize with user when valid tokens exist', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      const TestComponent = () => {
        const { user, church, isAuthenticated, isInitialized } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <div data-testid="initialized">{isInitialized ? 'initialized' : 'not initialized'}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('initialized')).toHaveTextContent('initialized')
      })

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      expect(screen.getByTestId('church')).toHaveTextContent('Test Church')
    })

    it('should handle profile fetch failure during initialization', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockRejectedValue(new Error('Network error'))

      const TestComponent = () => {
        const { user, error, isInitialized } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? 'authenticated' : 'not authenticated'}</div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <div data-testid="initialized">{isInitialized ? 'initialized' : 'not initialized'}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('initialized')).toHaveTextContent('initialized')
      })

      expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      expect(screen.getByTestId('error')).toHaveTextContent('Network error')
      expect(tokenStorage.clearTokens).toHaveBeenCalled()
    })
  })

  describe('Cross-tab synchronization', () => {
    it('should handle token changes from other tabs', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      
      let tokenChangeCallback: (tokens: any) => void = () => {}
      ;(tokenStorage.onTokensChanged as any).mockImplementation((callback) => {
        tokenChangeCallback = callback
        return () => {}
      })

      const TestComponent = () => {
        const { user, isAuthenticated } = useAuth()
        return (
          <div data-testid="user">{user ? 'authenticated' : 'not authenticated'}</div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      })

      // Simulate token change from another tab
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      act(() => {
        tokenChangeCallback(mockTokens)
      })

      await waitFor(() => {
        expect(apiClient.getProfile).toHaveBeenCalled()
      })
    })

    it('should handle logout from other tabs', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      let tokenChangeCallback: (tokens: any) => void = () => {}
      ;(tokenStorage.onTokensChanged as any).mockImplementation((callback) => {
        tokenChangeCallback = callback
        return () => {}
      })

      const TestComponent = () => {
        const { user } = useAuth()
        return (
          <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      // Simulate logout from another tab
      act(() => {
        tokenChangeCallback(null)
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      })

      expect(mockRouter.push).toHaveBeenCalledWith('/test-church/login?redirect=%2Ftest-church%2Fdashboard')
    })
  })

  describe('Login functionality', () => {
    it('should login successfully and redirect to church dashboard', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockResolvedValue({
        data: {
          user: mockUser,
          tokens: {
            accessToken: 'new-access-token',
            refreshToken: 'new-refresh-token',
          }
        }
      })

      const TestComponent = () => {
        const { login, user, loading } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="loading">{loading ? 'loading' : 'not loading'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'TEST123')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('loading')).toHaveTextContent('not loading')
      })

      act(() => {
        screen.getByText('Login').click()
      })

      await waitFor(() => {
        expect(tokenStorage.setTokens).toHaveBeenCalledWith('new-access-token', 'new-refresh-token')
      })

      expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      expect(mockRouter.push).toHaveBeenCalledWith('/test-church/dashboard')
    })

    it('should handle login failure with proper error', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockRejectedValue({
        response: { status: 401, data: { message: 'Invalid credentials' } }
      })

      const TestComponent = () => {
        const { login, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={() => login('<EMAIL>', 'wrong-password', 'TEST123')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('no error')
      })

      await act(async () => {
        try {
          await screen.getByText('Login').click()
        } catch (error) {
          // Expected to throw
        }
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Your session has expired. Please log in again.')
      })
    })
  })

  describe('Logout functionality', () => {
    it('should logout and redirect to appropriate login page', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      const TestComponent = () => {
        const { logout, user } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <button onClick={logout}>Logout</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      act(() => {
        screen.getByText('Logout').click()
      })

      expect(tokenStorage.clearTokens).toHaveBeenCalled()
      expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      expect(mockRouter.push).toHaveBeenCalledWith('/test-church/login')
    })
  })

  describe('Token refresh functionality', () => {
    it('should refresh tokens successfully', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(tokenStorage.getRefreshToken as any).mockReturnValue('refresh-token')
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      const mockFetch = fetch as any
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve({
          data: {
            tokens: {
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            }
          }
        })
      } as Response)

      const TestComponent = () => {
        const { refreshTokens, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={refreshTokens}>Refresh</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('no error')
      })

      await act(async () => {
        screen.getByText('Refresh').click()
      })

      await waitFor(() => {
        expect(tokenStorage.setTokens).toHaveBeenCalledWith('new-access-token', 'new-refresh-token')
      })

      expect(screen.getByTestId('error')).toHaveTextContent('no error')
    })

    it('should handle refresh failure', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(tokenStorage.getRefreshToken as any).mockReturnValue('refresh-token')
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      const mockFetch = fetch as any
      mockFetch.mockResolvedValue({
        ok: false,
        json: () => Promise.resolve({ message: 'Refresh token expired' })
      } as Response)

      const TestComponent = () => {
        const { refreshTokens, error, user } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <button onClick={refreshTokens}>Refresh</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      await act(async () => {
        screen.getByText('Refresh').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Refresh token expired')
      })

      expect(tokenStorage.clearTokens).toHaveBeenCalled()
      expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
    })

    it('should handle missing refresh token', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(tokenStorage.getRefreshToken as any).mockReturnValue(null)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser }
      })

      const TestComponent = () => {
        const { refreshTokens, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={refreshTokens}>Refresh</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('no error')
      })

      await act(async () => {
        screen.getByText('Refresh').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('No refresh token available')
      })
    })
  })

  describe('Error handling', () => {
    it('should clear errors when clearError is called', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockRejectedValue(new Error('Test error'))

      const TestComponent = () => {
        const { error, clearError } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={clearError}>Clear Error</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Test error')
      })

      act(() => {
        screen.getByText('Clear Error').click()
      })

      expect(screen.getByTestId('error')).toHaveTextContent('no error')
    })

    it('should create appropriate error for 404 church not found', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockRejectedValue({
        response: { 
          status: 404, 
          data: { message: 'Church not found' } 
        }
      })

      const TestComponent = () => {
        const { error } = useAuth()
        return (
          <div data-testid="error">{error ? error.message : 'no error'}</div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Church not found. Please check your church code.')
      })
    })

    it('should create appropriate error for network issues', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockRejectedValue({
        code: 'NETWORK_ERROR',
        message: 'Network connection failed'
      })

      const TestComponent = () => {
        const { error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <div data-testid="retryable">{error ? (error.retryable ? 'retryable' : 'not retryable') : 'no error'}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Network error. Please check your connection and try again.')
        expect(screen.getByTestId('retryable')).toHaveTextContent('retryable')
      })
    })
  })

  describe('Route protection', () => {
    it('should redirect to login for protected routes when not authenticated', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockRejectedValue({
        response: { status: 401 }
      })

      // Mock protected route
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/test-church/members',
          href: 'http://localhost:3000/test-church/members',
        },
        writable: true,
      })

      render(
        <AuthProvider>
          <div>Test Content</div>
        </AuthProvider>
      )

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/login?redirect=%2Ftest-church%2Fmembers')
      })
    })

    it('should not redirect for public routes when not authenticated', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)

      // Mock public route
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/login',
          href: 'http://localhost:3000/login',
        },
        writable: true,
      })

      render(
        <AuthProvider>
          <div>Test Content</div>
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByText('Test Content')).toBeInTheDocument()
      })

      expect(mockRouter.push).not.toHaveBeenCalled()
    })
  })

  describe('useAuth hook', () => {
    it('should throw error when used outside AuthProvider', () => {
      const TestComponent = () => {
        useAuth()
        return <div>Test</div>
      }

      // Suppress console.error for this test
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => render(<TestComponent />)).toThrow('useAuth must be used within an AuthProvider')

      consoleSpy.mockRestore()
    })
  })

  describe('Memory leak prevention', () => {
    it('should cleanup subscriptions on unmount', () => {
      const unsubscribeMock = vi.fn()
      ;(tokenStorage.onTokensChanged as any).mockReturnValue(unsubscribeMock)
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)

      const { unmount } = render(
        <AuthProvider>
          <div>Test Content</div>
        </AuthProvider>
      )

      unmount()

      expect(unsubscribeMock).toHaveBeenCalled()
    })

    it('should not update state after component unmount', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      
      // Mock a delayed API response
      ;(apiClient.getProfile as any).mockImplementation(() => 
        new Promise(resolve => 
          setTimeout(() => resolve({ data: { user: mockUser } }), 100)
        )
      )

      const TestComponent = () => {
        const { user } = useAuth()
        return <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
      }

      const { unmount } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Unmount before API response
      unmount()

      // Wait for the delayed response
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 150))
      })

      // No errors should be thrown due to state updates after unmount
    })
  })
})