/**
 * Token Refresh Interceptor Tests
 * 
 * Tests for automatic token refresh functionality including:
 * - Request/response interception
 * - Token refresh logic
 * - Request queuing during refresh
 * - Error handling for refresh failures
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest'
import { TokenRefreshInterceptor, RequestConfig } from '../tokenRefreshInterceptor'
import { tokenStorage, TokenPair } from '../tokenStorage'

// Mock the tokenStorage module
vi.mock('../tokenStorage', () => ({
  tokenStorage: {
    getAccessToken: vi.fn(),
    getRefreshToken: vi.fn(),
    setTokens: vi.fn(),
    clearTokens: vi.fn(),
    getTokens: vi.fn(),
  },
}))

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

// Mock window.location
const mockLocation = {
  pathname: '/test-church/dashboard',
  href: '',
}
Object.defineProperty(window, 'location', {
  value: mockLocation,
  writable: true,
})

describe('TokenRefreshInterceptor', () => {
  let interceptor: TokenRefreshInterceptor
  const mockTokenStorage = tokenStorage as {
    getAccessToken: Mock
    getRefreshToken: Mock
    setTokens: Mock
    clearTokens: Mock
    getTokens: Mock
  }

  beforeEach(() => {
    interceptor = new TokenRefreshInterceptor()
    vi.clearAllMocks()
    mockFetch.mockClear()
    mockLocation.href = ''
    
    // Set default environment
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api'
  })

  afterEach(() => {
    interceptor.clearRefreshState()
  })

  describe('interceptRequest', () => {
    it('should add Authorization header when access token is available', async () => {
      const accessToken = 'valid-access-token'
      mockTokenStorage.getAccessToken.mockReturnValue(accessToken)

      const config: RequestConfig = {
        url: 'http://localhost:3000/api/test',
        options: {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        },
      }

      const result = await interceptor.interceptRequest(config)

      expect(result.options.headers).toEqual(
        new Headers({
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`,
        })
      )
    })

    it('should not add Authorization header when no access token is available', async () => {
      mockTokenStorage.getAccessToken.mockReturnValue(null)

      const config: RequestConfig = {
        url: 'http://localhost:3000/api/test',
        options: {
          method: 'GET',
          headers: { 'Content-Type': 'application/json' },
        },
      }

      const result = await interceptor.interceptRequest(config)

      const headers = result.options.headers as Headers
      expect(headers).toBeInstanceOf(Headers)
      expect(headers.has('Authorization')).toBe(false)
      expect(headers.get('Content-Type')).toBe('application/json')
    })

    it('should preserve existing headers', async () => {
      const accessToken = 'valid-access-token'
      mockTokenStorage.getAccessToken.mockReturnValue(accessToken)

      const config: RequestConfig = {
        url: 'http://localhost:3000/api/test',
        options: {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-Custom-Header': 'custom-value',
          },
        },
      }

      const result = await interceptor.interceptRequest(config)

      expect(result.options.headers).toEqual(
        new Headers({
          'Content-Type': 'application/json',
          'X-Custom-Header': 'custom-value',
          'Authorization': `Bearer ${accessToken}`,
        })
      )
    })
  })

  describe('interceptResponse', () => {
    it('should return response as-is when status is not 401', async () => {
      const mockResponse = new Response('{"data": "success"}', { status: 200 })
      const config: RequestConfig = {
        url: 'http://localhost:3000/api/test',
        options: { method: 'GET' },
      }

      const result = await interceptor.interceptResponse(mockResponse, config)

      expect(result).toBe(mockResponse)
    })

    it('should return response as-is for auth endpoints to avoid infinite loops', async () => {
      const mockResponse = new Response('{"error": "Unauthorized"}', { status: 401 })
      const config: RequestConfig = {
        url: 'http://localhost:3000/api/auth/login',
        options: { method: 'POST' },
      }

      const result = await interceptor.interceptResponse(mockResponse, config)

      expect(result).toBe(mockResponse)
    })

    it('should attempt token refresh on 401 response for non-auth endpoints', async () => {
      const mockResponse = new Response('{"error": "Unauthorized"}', { status: 401 })
      const config: RequestConfig = {
        url: 'http://localhost:3000/api/churches/test/members',
        options: { method: 'GET' },
      }

      // Mock successful token refresh
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      mockTokenStorage.getAccessToken.mockReturnValue('new-access-token')
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 15 * 60 * 1000,
        issuedAt: Date.now(),
      })

      // Mock refresh API call
      const refreshResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: {
            tokens: {
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            },
          },
        }),
      }
      
      // Mock retry request
      const retryResponse = new Response('{"data": "success"}', { status: 200 })

      mockFetch
        .mockResolvedValueOnce(refreshResponse) // Refresh call
        .mockResolvedValueOnce(retryResponse) // Retry original request

      const result = await interceptor.interceptResponse(mockResponse, config)

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: 'valid-refresh-token' }),
      })
      expect(mockTokenStorage.setTokens).toHaveBeenCalledWith('new-access-token', 'new-refresh-token')
      expect(result).toBe(retryResponse)
    })

    it('should clear tokens and redirect on refresh failure', async () => {
      const mockResponse = new Response('{"error": "Unauthorized"}', { status: 401 })
      const config: RequestConfig = {
        url: 'http://localhost:3000/api/churches/test/members',
        options: { method: 'GET' },
      }

      mockTokenStorage.getRefreshToken.mockReturnValue('invalid-refresh-token')

      // Mock failed refresh API call
      const refreshResponse = {
        ok: false,
        status: 401,
        json: () => Promise.resolve({ message: 'Invalid refresh token' }),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      const result = await interceptor.interceptResponse(mockResponse, config)

      expect(mockTokenStorage.clearTokens).toHaveBeenCalled()
      expect(mockLocation.href).toBe('/test-church/login')
      expect(result).toBe(mockResponse)
    })

    it('should queue requests when refresh is already in progress', async () => {
      const mockResponse1 = new Response('{"error": "Unauthorized"}', { status: 401 })
      const mockResponse2 = new Response('{"error": "Unauthorized"}', { status: 401 })
      
      const config1: RequestConfig = {
        url: 'http://localhost:3000/api/churches/test/members',
        options: { method: 'GET' },
      }
      
      const config2: RequestConfig = {
        url: 'http://localhost:3000/api/churches/test/events',
        options: { method: 'GET' },
      }

      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      mockTokenStorage.getAccessToken.mockReturnValue('new-access-token')
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 15 * 60 * 1000,
        issuedAt: Date.now(),
      })

      // Mock successful refresh
      const refreshResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: {
            tokens: {
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            },
          },
        }),
      }

      const retryResponse1 = new Response('{"data": "members"}', { status: 200 })
      const retryResponse2 = new Response('{"data": "events"}', { status: 200 })

      mockFetch
        .mockResolvedValueOnce(refreshResponse) // Refresh call
        .mockResolvedValueOnce(retryResponse1) // Retry first request
        .mockResolvedValueOnce(retryResponse2) // Retry second request

      // Start both requests simultaneously
      const promise1 = interceptor.interceptResponse(mockResponse1, config1)
      const promise2 = interceptor.interceptResponse(mockResponse2, config2)

      const [result1, result2] = await Promise.all([promise1, promise2])

      expect(result1).toBe(retryResponse1)
      expect(result2).toBe(retryResponse2)
      expect(mockFetch).toHaveBeenCalledTimes(3) // 1 refresh + 2 retries
    })
  })

  describe('handleTokenRefresh', () => {
    it('should successfully refresh tokens', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 15 * 60 * 1000,
        issuedAt: Date.now(),
      })

      const refreshResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: {
            tokens: {
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            },
          },
        }),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      const result = await interceptor.handleTokenRefresh()

      expect(mockFetch).toHaveBeenCalledWith('http://localhost:3000/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: 'valid-refresh-token' }),
      })
      expect(mockTokenStorage.setTokens).toHaveBeenCalledWith('new-access-token', 'new-refresh-token')
      expect(result).toEqual({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: expect.any(Number),
        issuedAt: expect.any(Number),
      })
    })

    it('should throw error when no refresh token is available', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue(null)

      await expect(interceptor.handleTokenRefresh()).rejects.toThrow('No refresh token available')
    })

    it('should throw error when refresh API call fails', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('invalid-refresh-token')

      const refreshResponse = {
        ok: false,
        status: 401,
        json: () => Promise.resolve({ message: 'Invalid refresh token' }),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      await expect(interceptor.handleTokenRefresh()).rejects.toThrow('Invalid refresh token')
    })

    it('should return same promise for concurrent refresh attempts', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 15 * 60 * 1000,
        issuedAt: Date.now(),
      })

      const refreshResponse = {
        ok: true,
        json: () => Promise.resolve({
          data: {
            tokens: {
              accessToken: 'new-access-token',
              refreshToken: 'new-refresh-token',
            },
          },
        }),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      // Start multiple refresh attempts
      const promise1 = interceptor.handleTokenRefresh()
      const promise2 = interceptor.handleTokenRefresh()
      const promise3 = interceptor.handleTokenRefresh()

      const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3])

      // Should only make one API call
      expect(mockFetch).toHaveBeenCalledTimes(1)
      expect(result1).toEqual(result2)
      expect(result2).toEqual(result3)
    })
  })

  describe('getRefreshState', () => {
    it('should return current refresh state', () => {
      const initialState = interceptor.getRefreshState()
      
      expect(initialState).toEqual({
        isRefreshing: false,
        queueLength: 0,
        hasRefreshPromise: false,
      })
    })

    it('should reflect refresh state during token refresh', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      
      // Mock a slow refresh response
      const refreshResponse = {
        ok: true,
        json: () => new Promise(resolve => 
          setTimeout(() => resolve({
            data: {
              tokens: {
                accessToken: 'new-access-token',
                refreshToken: 'new-refresh-token',
              },
            },
          }), 100)
        ),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      // Start refresh but don't await
      const refreshPromise = interceptor.handleTokenRefresh()
      
      // Check state during refresh
      const duringRefreshState = interceptor.getRefreshState()
      expect(duringRefreshState.isRefreshing).toBe(true)
      expect(duringRefreshState.hasRefreshPromise).toBe(true)

      // Wait for completion
      await refreshPromise

      // Check state after refresh
      const afterRefreshState = interceptor.getRefreshState()
      expect(afterRefreshState.isRefreshing).toBe(false)
      expect(afterRefreshState.hasRefreshPromise).toBe(false)
    })
  })

  describe('clearRefreshState', () => {
    it('should clear all refresh state', async () => {
      // Set up some state
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')
      
      const refreshResponse = {
        ok: true,
        json: () => new Promise(() => {}), // Never resolves
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      // Start refresh to set state
      interceptor.handleTokenRefresh()
      
      const beforeClear = interceptor.getRefreshState()
      expect(beforeClear.isRefreshing).toBe(true)

      // Clear state
      interceptor.clearRefreshState()

      const afterClear = interceptor.getRefreshState()
      expect(afterClear).toEqual({
        isRefreshing: false,
        queueLength: 0,
        hasRefreshPromise: false,
      })
    })
  })

  describe('edge cases', () => {
    it('should handle malformed refresh response', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')

      const refreshResponse = {
        ok: false,
        status: 500,
        json: () => Promise.reject(new Error('Invalid JSON')),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      await expect(interceptor.handleTokenRefresh()).rejects.toThrow('Token refresh failed')
    })

    it('should handle network errors during refresh', async () => {
      mockTokenStorage.getRefreshToken.mockReturnValue('valid-refresh-token')

      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      await expect(interceptor.handleTokenRefresh()).rejects.toThrow('Network error')
    })

    it('should redirect to general login when not in church-specific path', async () => {
      mockLocation.pathname = '/general-page'
      
      const mockResponse = new Response('{"error": "Unauthorized"}', { status: 401 })
      const config: RequestConfig = {
        url: 'http://localhost:3000/api/test',
        options: { method: 'GET' },
      }

      mockTokenStorage.getRefreshToken.mockReturnValue('invalid-refresh-token')

      const refreshResponse = {
        ok: false,
        status: 401,
        json: () => Promise.resolve({ message: 'Invalid refresh token' }),
      }

      mockFetch.mockResolvedValueOnce(refreshResponse)

      await interceptor.interceptResponse(mockResponse, config)

      expect(mockLocation.href).toBe('/login')
    })
  })
})