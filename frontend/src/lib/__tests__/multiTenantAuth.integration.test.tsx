/**
 * Multi-Tenant Authentication Integration Tests
 * 
 * Comprehensive integration tests for multi-tenant authentication:
 * - Church-specific login flows
 * - Tenant isolation verification
 * - Cross-tenant access prevention
 * - Church-scoped resource access
 * - Multi-tenant session management
 */

import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import { useRouter } from 'next/navigation'
import { AuthProvider, useAuth } from '../auth'
import { tokenStorage } from '../tokenStorage'
import { apiClient } from '../api'
import { vi } from 'vitest'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
}))

vi.mock('../tokenStorage', () => ({
  tokenStorage: {
    hasValidTokens: vi.fn(),
    getAccessToken: vi.fn(),
    getRefreshToken: vi.fn(),
    setTokens: vi.fn(),
    clearTokens: vi.fn(),
    onTokensChanged: vi.fn(),
  },
}))

vi.mock('../api', () => ({
  apiClient: {
    getProfile: vi.fn(),
    login: vi.fn(),
    get: vi.fn(),
    post: vi.fn(),
  },
}))

global.fetch = vi.fn()

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

// Mock church data
const mockChurch1 = {
  id: 'church-1',
  name: 'First Baptist Church',
  slug: 'first-baptist',
  churchCode: 'FBC123',
  email: '<EMAIL>',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  settings: {
    allowSelfRegistration: true,
    requireEmailVerification: true,
    timezone: 'America/New_York',
    theme: {},
    features: {
      events: true,
      donations: true,
      messaging: true,
      calendar: true,
      onlineGiving: true,
      memberDirectory: true,
      eventRsvp: true,
      recurringEvents: true,
    },
    eventSettings: {},
    donationSettings: {},
  },
}

const mockChurch2 = {
  id: 'church-2',
  name: 'Grace Community Church',
  slug: 'grace-community',
  churchCode: 'GCC456',
  email: '<EMAIL>',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  settings: {
    allowSelfRegistration: false,
    requireEmailVerification: true,
    timezone: 'America/Los_Angeles',
    theme: {},
    features: {
      events: true,
      donations: true,
      messaging: false,
      calendar: true,
      onlineGiving: false,
      memberDirectory: true,
      eventRsvp: true,
      recurringEvents: false,
    },
    eventSettings: {},
    donationSettings: {},
  },
}

const mockUser1 = {
  id: 'user-1',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  churchId: 'church-1',
  isActive: true,
  isEmailVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  church: mockChurch1,
}

const mockUser2 = {
  id: 'user-2',
  email: '<EMAIL>',
  firstName: 'Jane',
  lastName: 'Smith',
  churchId: 'church-2',
  isActive: true,
  isEmailVerified: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  church: mockChurch2,
}

describe('Multi-Tenant Authentication Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(tokenStorage.onTokensChanged as any).mockReturnValue(() => {})
  })

  describe('Church-Specific Login Flows', () => {
    it('should handle login through church-specific URL', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockResolvedValue({
        data: {
          user: mockUser1,
          tokens: {
            accessToken: 'church1-access-token',
            refreshToken: 'church1-refresh-token',
          }
        }
      })

      // Mock church-specific URL
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/first-baptist/login',
          href: 'http://localhost:3000/first-baptist/login',
        },
        writable: true,
      })

      const TestComponent = () => {
        const { login, user, church } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'FBC123')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      })

      await act(async () => {
        screen.getByText('Login').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('First Baptist Church')
      })

      expect(mockRouter.push).toHaveBeenCalledWith('/first-baptist/dashboard')
    })

    it('should handle login through general login with church code', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockResolvedValue({
        data: {
          user: mockUser2,
          tokens: {
            accessToken: 'church2-access-token',
            refreshToken: 'church2-refresh-token',
          }
        }
      })

      // Mock general login URL
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/login',
          href: 'http://localhost:3000/login',
        },
        writable: true,
      })

      const TestComponent = () => {
        const { login, user, church } = useAuth()
        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'GCC456')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await act(async () => {
        screen.getByText('Login').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('Grace Community Church')
      })

      expect(mockRouter.push).toHaveBeenCalledWith('/grace-community/dashboard')
    })

    it('should prevent login with wrong church code', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockRejectedValue({
        response: { 
          status: 401, 
          data: { message: 'Invalid church code for this user' } 
        }
      })

      const TestComponent = () => {
        const { login, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'WRONG123')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await act(async () => {
        try {
          await screen.getByText('Login').click()
        } catch (error) {
          // Expected to throw
        }
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Your session has expired. Please log in again.')
      })
    })
  })

  describe('Tenant Isolation Verification', () => {
    it('should isolate user data between churches', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser1 }
      })

      // Mock API call to get church members
      ;(apiClient.get as any).mockImplementation((url: string) => {
        if (url.includes('/first-baptist/members')) {
          return Promise.resolve({
            data: {
              members: [
                { id: '1', email: '<EMAIL>', churchId: 'church-1' },
                { id: '2', email: '<EMAIL>', churchId: 'church-1' },
              ]
            }
          })
        }
        if (url.includes('/grace-community/members')) {
          return Promise.reject({
            response: { status: 403, data: { message: 'Access denied' } }
          })
        }
        return Promise.reject(new Error('Not found'))
      })

      const TestComponent = () => {
        const { user } = useAuth()
        const [members, setMembers] = React.useState<any[]>([])
        const [error, setError] = React.useState<string>('')

        const fetchMembers = async (churchSlug: string) => {
          try {
            const response = await apiClient.get(`/churches/${churchSlug}/members`)
            setMembers(response.data.members)
            setError('')
          } catch (err: any) {
            setError(err.response?.data?.message || 'Error fetching members')
            setMembers([])
          }
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="members-count">{members.length}</div>
            <div data-testid="error">{error}</div>
            <button onClick={() => fetchMembers('first-baptist')}>
              Get First Baptist Members
            </button>
            <button onClick={() => fetchMembers('grace-community')}>
              Get Grace Community Members
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      // Should be able to access own church members
      await act(async () => {
        screen.getByText('Get First Baptist Members').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('members-count')).toHaveTextContent('2')
        expect(screen.getByTestId('error')).toHaveTextContent('')
      })

      // Should NOT be able to access other church members
      await act(async () => {
        screen.getByText('Get Grace Community Members').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('members-count')).toHaveTextContent('0')
        expect(screen.getByTestId('error')).toHaveTextContent('Access denied')
      })
    })

    it('should prevent cross-tenant token usage', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(tokenStorage.getAccessToken as any).mockReturnValue('church1-token')

      // Mock API to reject church1 token for church2 resources
      ;(apiClient.get as any).mockImplementation((url: string) => {
        if (url.includes('/grace-community/')) {
          return Promise.reject({
            response: { status: 403, data: { message: 'Token not valid for this church' } }
          })
        }
        return Promise.resolve({ data: {} })
      })

      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser1 }
      })

      const TestComponent = () => {
        const { user } = useAuth()
        const [error, setError] = React.useState<string>('')

        const accessOtherChurch = async () => {
          try {
            await apiClient.get('/churches/grace-community/settings')
            setError('')
          } catch (err: any) {
            setError(err.response?.data?.message || 'Access error')
          }
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={accessOtherChurch}>
              Access Other Church
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      await act(async () => {
        screen.getByText('Access Other Church').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Token not valid for this church')
      })
    })
  })

  describe('Church-Scoped Resource Access', () => {
    it('should automatically scope API requests to user church', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser1 }
      })

      let capturedUrl = ''
      ;(apiClient.get as any).mockImplementation((url: string) => {
        capturedUrl = url
        return Promise.resolve({
          data: {
            events: [
              { id: '1', title: 'Sunday Service', churchId: 'church-1' },
              { id: '2', title: 'Bible Study', churchId: 'church-1' },
            ]
          }
        })
      })

      const TestComponent = () => {
        const { user } = useAuth()
        const [events, setEvents] = React.useState<any[]>([])

        const fetchEvents = async () => {
          const response = await apiClient.get('/events')
          setEvents(response.data.events)
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="events-count">{events.length}</div>
            <div data-testid="captured-url">{capturedUrl}</div>
            <button onClick={fetchEvents}>Fetch Events</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      await act(async () => {
        screen.getByText('Fetch Events').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('events-count')).toHaveTextContent('2')
        // Verify URL was automatically scoped to user's church
        expect(capturedUrl).toContain('/churches/first-baptist/events')
      })
    })

    it('should handle church-specific feature availability', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser2 } // Grace Community Church user
      })

      const TestComponent = () => {
        const { user, church } = useAuth()

        const hasFeature = (feature: string) => {
          return church?.settings?.features?.[feature] || false
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <div data-testid="messaging-enabled">{hasFeature('messaging') ? 'yes' : 'no'}</div>
            <div data-testid="online-giving-enabled">{hasFeature('onlineGiving') ? 'yes' : 'no'}</div>
            <div data-testid="recurring-events-enabled">{hasFeature('recurringEvents') ? 'yes' : 'no'}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('Grace Community Church')
      })

      // Verify church-specific feature settings
      expect(screen.getByTestId('messaging-enabled')).toHaveTextContent('no')
      expect(screen.getByTestId('online-giving-enabled')).toHaveTextContent('no')
      expect(screen.getByTestId('recurring-events-enabled')).toHaveTextContent('no')
    })
  })

  describe('Multi-Tenant Session Management', () => {
    it('should handle switching between church contexts', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)

      let currentUser = mockUser1
      ;(apiClient.login as any).mockImplementation(() => {
        return Promise.resolve({
          data: {
            user: currentUser,
            tokens: {
              accessToken: `${currentUser.church.slug}-token`,
              refreshToken: `${currentUser.church.slug}-refresh`,
            }
          }
        })
      })

      ;(apiClient.getProfile as any).mockImplementation(() => {
        return Promise.resolve({ data: { user: currentUser } })
      })

      const TestComponent = () => {
        const { login, logout, user, church } = useAuth()

        const loginAsUser1 = () => {
          currentUser = mockUser1
          login('<EMAIL>', 'password', 'FBC123')
        }

        const loginAsUser2 = () => {
          currentUser = mockUser2
          login('<EMAIL>', 'password', 'GCC456')
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <button onClick={loginAsUser1}>Login as User 1</button>
            <button onClick={loginAsUser2}>Login as User 2</button>
            <button onClick={logout}>Logout</button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Login as first user
      await act(async () => {
        screen.getByText('Login as User 1').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('First Baptist Church')
      })

      // Logout
      await act(async () => {
        screen.getByText('Logout').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('not authenticated')
      })

      // Login as second user
      await act(async () => {
        screen.getByText('Login as User 2').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('Grace Community Church')
      })
    })

    it('should maintain separate session state for different churches', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser1 }
      })

      // Mock session storage for different churches
      const sessionData: { [key: string]: any } = {}

      const TestComponent = () => {
        const { user, church } = useAuth()
        const [sessionInfo, setSessionInfo] = React.useState<string>('')

        React.useEffect(() => {
          if (church) {
            // Simulate church-specific session data
            const churchSessionKey = `session_${church.slug}`
            if (!sessionData[churchSessionKey]) {
              sessionData[churchSessionKey] = {
                lastActivity: Date.now(),
                preferences: { theme: church.slug === 'first-baptist' ? 'blue' : 'green' },
                permissions: church.slug === 'first-baptist' ? ['admin'] : ['member'],
              }
            }
            setSessionInfo(JSON.stringify(sessionData[churchSessionKey]))
          }
        }, [church])

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="church">{church ? church.name : 'no church'}</div>
            <div data-testid="session-info">{sessionInfo}</div>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
        expect(screen.getByTestId('church')).toHaveTextContent('First Baptist Church')
      })

      const sessionInfo = screen.getByTestId('session-info').textContent
      expect(sessionInfo).toContain('blue') // First Baptist theme
      expect(sessionInfo).toContain('admin') // First Baptist permissions
    })
  })

  describe('Error Handling in Multi-Tenant Context', () => {
    it('should handle church not found errors', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockRejectedValue({
        response: { 
          status: 404, 
          data: { message: 'Church not found' } 
        }
      })

      const TestComponent = () => {
        const { login, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'INVALID')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await act(async () => {
        try {
          await screen.getByText('Login').click()
        } catch (error) {
          // Expected to throw
        }
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Church not found. Please check your church code.')
      })
    })

    it('should handle inactive church errors', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(false)
      ;(apiClient.login as any).mockRejectedValue({
        response: { 
          status: 403, 
          data: { message: 'Church account is inactive' } 
        }
      })

      const TestComponent = () => {
        const { login, error } = useAuth()
        return (
          <div>
            <div data-testid="error">{error ? error.message : 'no error'}</div>
            <button onClick={() => login('<EMAIL>', 'password', 'INACTIVE')}>
              Login
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await act(async () => {
        try {
          await screen.getByText('Login').click()
        } catch (error) {
          // Expected to throw
        }
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Your account is inactive. Please contact your administrator.')
      })
    })

    it('should handle church-specific permission errors', async () => {
      ;(tokenStorage.hasValidTokens as any).mockReturnValue(true)
      ;(apiClient.getProfile as any).mockResolvedValue({
        data: { user: mockUser1 }
      })

      ;(apiClient.get as any).mockRejectedValue({
        response: { 
          status: 403, 
          data: { message: 'Insufficient permissions for this church resource' } 
        }
      })

      const TestComponent = () => {
        const { user } = useAuth()
        const [error, setError] = React.useState<string>('')

        const accessRestrictedResource = async () => {
          try {
            await apiClient.get('/churches/first-baptist/admin/settings')
          } catch (err: any) {
            setError(err.response?.data?.message || 'Access error')
          }
        }

        return (
          <div>
            <div data-testid="user">{user ? user.email : 'not authenticated'}</div>
            <div data-testid="error">{error}</div>
            <button onClick={accessRestrictedResource}>
              Access Restricted Resource
            </button>
          </div>
        )
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      await waitFor(() => {
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      await act(async () => {
        screen.getByText('Access Restricted Resource').click()
      })

      await waitFor(() => {
        expect(screen.getByTestId('error')).toHaveTextContent('Insufficient permissions for this church resource')
      })
    })
  })
});