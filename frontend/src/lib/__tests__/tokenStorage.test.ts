/**
 * Unit tests for TokenStorageService
 */

import { vi, beforeEach, describe, it, expect } from 'vitest'

// We need to mock the module before importing it
vi.mock('../tokenStorage', async () => {
  const actual = await vi.importActual('../tokenStorage')
  return actual
})

import { tokenStorage, TokenPair } from '../tokenStorage'

describe('TokenStorageService', () => {
  beforeEach(() => {
    // Clear localStorage and cookies before each test
    localStorage.clear()
    // Clear cookies by setting them to expire
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
    })
    
    vi.clearAllMocks()
  })

  describe('setTokens', () => {
    it('should store tokens and make them retrievable', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)

      expect(tokenStorage.getAccessToken()).toBe(accessToken)
      expect(tokenStorage.getRefreshToken()).toBe(refreshToken)
    })

    it('should notify callbacks when tokens are set', async () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'
      
      return new Promise<void>((resolve) => {
        const unsubscribe = tokenStorage.onTokensChanged((tokens) => {
          expect(tokens).not.toBeNull()
          expect(tokens!.accessToken).toBe(accessToken)
          expect(tokens!.refreshToken).toBe(refreshToken)
          unsubscribe()
          resolve()
        })

        tokenStorage.setTokens(accessToken, refreshToken)
      })
    })

    it('should create tokens with proper expiration', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      const tokens = tokenStorage.getTokens()
      expect(tokens).not.toBeNull()
      expect(tokens!.expiresAt).toBeGreaterThan(Date.now())
      expect(tokens!.issuedAt).toBeLessThanOrEqual(Date.now())
    })
  })

  describe('getAccessToken', () => {
    it('should retrieve access token after setting', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      expect(tokenStorage.getAccessToken()).toBe(accessToken)
    })

    it('should return null when no tokens exist', () => {
      expect(tokenStorage.getAccessToken()).toBeNull()
    })

    it('should return null for expired tokens', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      // Mock Date.now to simulate expired token
      const originalNow = Date.now
      const mockNow = vi.fn()
      Date.now = mockNow

      // Set token with current time
      mockNow.mockReturnValue(1000000)
      tokenStorage.setTokens(accessToken, refreshToken)

      // Simulate time passing (16 minutes later)
      mockNow.mockReturnValue(1000000 + (16 * 60 * 1000))
      
      expect(tokenStorage.getAccessToken()).toBeNull()

      // Restore Date.now
      Date.now = originalNow
    })
  })

  describe('getRefreshToken', () => {
    it('should retrieve refresh token after setting', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      expect(tokenStorage.getRefreshToken()).toBe(refreshToken)
    })

    it('should return null when no tokens exist', () => {
      expect(tokenStorage.getRefreshToken()).toBeNull()
    })
  })

  describe('getTokens', () => {
    it('should return complete token pair', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      const tokens = tokenStorage.getTokens()
      expect(tokens).not.toBeNull()
      expect(tokens!.accessToken).toBe(accessToken)
      expect(tokens!.refreshToken).toBe(refreshToken)
      expect(tokens!.expiresAt).toBeGreaterThan(Date.now())
      expect(tokens!.issuedAt).toBeLessThanOrEqual(Date.now())
    })

    it('should return null when no tokens exist', () => {
      expect(tokenStorage.getTokens()).toBeNull()
    })
  })

  describe('clearTokens', () => {
    it('should clear all tokens', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      tokenStorage.clearTokens()

      expect(tokenStorage.getAccessToken()).toBeNull()
      expect(tokenStorage.getRefreshToken()).toBeNull()
      expect(tokenStorage.getTokens()).toBeNull()
    })

    it('should notify callbacks when tokens are cleared', async () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      return new Promise<void>((resolve) => {
        const unsubscribe = tokenStorage.onTokensChanged((tokens) => {
          if (tokens === null) {
            unsubscribe()
            resolve()
          }
        })

        tokenStorage.clearTokens()
      })
    })
  })

  describe('hasValidTokens', () => {
    it('should return true for valid tokens', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      tokenStorage.setTokens(accessToken, refreshToken)
      
      expect(tokenStorage.hasValidTokens()).toBe(true)
    })

    it('should return false for expired tokens', () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'

      // Mock Date.now to simulate expired token
      const originalNow = Date.now
      const mockNow = vi.fn()
      Date.now = mockNow

      // Set token with current time
      mockNow.mockReturnValue(1000000)
      tokenStorage.setTokens(accessToken, refreshToken)

      // Simulate time passing (16 minutes later)
      mockNow.mockReturnValue(1000000 + (16 * 60 * 1000))
      
      expect(tokenStorage.hasValidTokens()).toBe(false)

      // Restore Date.now
      Date.now = originalNow
    })

    it('should return false when no tokens exist', () => {
      expect(tokenStorage.hasValidTokens()).toBe(false)
    })
  })

  describe('onTokensChanged', () => {
    it('should allow subscribing to token changes', async () => {
      const accessToken = 'access-token-123'
      const refreshToken = 'refresh-token-456'
      
      return new Promise<void>((resolve) => {
        const unsubscribe = tokenStorage.onTokensChanged((tokens) => {
          expect(tokens).not.toBeNull()
          expect(tokens!.accessToken).toBe(accessToken)
          unsubscribe()
          resolve()
        })

        tokenStorage.setTokens(accessToken, refreshToken)
      })
    })

    it('should allow unsubscribing from token changes', () => {
      const callback = vi.fn()
      
      const unsubscribe = tokenStorage.onTokensChanged(callback)
      unsubscribe()

      tokenStorage.setTokens('access-token', 'refresh-token')
      
      expect(callback).not.toHaveBeenCalled()
    })

    it('should handle callback errors gracefully', () => {
      const errorCallback = vi.fn(() => {
        throw new Error('Callback error')
      })
      const normalCallback = vi.fn()
      
      tokenStorage.onTokensChanged(errorCallback)
      tokenStorage.onTokensChanged(normalCallback)

      // Should not throw despite error in first callback
      expect(() => {
        tokenStorage.setTokens('access-token', 'refresh-token')
      }).not.toThrow()

      expect(errorCallback).toHaveBeenCalled()
      expect(normalCallback).toHaveBeenCalled()
    })
  })

  describe('getStorageStatus', () => {
    it('should return storage availability status', () => {
      const status = tokenStorage.getStorageStatus()
      
      expect(status).toHaveProperty('localStorage')
      expect(status).toHaveProperty('cookies')
      expect(typeof status.localStorage).toBe('boolean')
      expect(typeof status.cookies).toBe('boolean')
    })
  })

  describe('error handling', () => {
    it('should handle malformed JSON in storage gracefully', () => {
      // Set malformed JSON directly
      localStorage.setItem('auth_tokens', 'invalid-json')
      
      expect(tokenStorage.getTokens()).toBeNull()
      expect(tokenStorage.getAccessToken()).toBeNull()
      expect(tokenStorage.getRefreshToken()).toBeNull()
    })

    it('should handle storage failures gracefully', () => {
      // Mock localStorage to fail
      const originalSetItem = localStorage.setItem
      localStorage.setItem = vi.fn(() => {
        throw new Error('Storage failed')
      })

      // Should not throw when storage fails
      expect(() => {
        tokenStorage.setTokens('access-token', 'refresh-token')
      }).not.toThrow()

      // Restore localStorage
      localStorage.setItem = originalSetItem
    })
  })

  describe('integration tests', () => {
    it('should maintain token persistence across operations', () => {
      const accessToken = 'persistent-access-token'
      const refreshToken = 'persistent-refresh-token'

      // Set tokens
      tokenStorage.setTokens(accessToken, refreshToken)
      
      // Verify they can be retrieved
      expect(tokenStorage.getAccessToken()).toBe(accessToken)
      expect(tokenStorage.getRefreshToken()).toBe(refreshToken)
      expect(tokenStorage.hasValidTokens()).toBe(true)
      
      // Clear tokens
      tokenStorage.clearTokens()
      
      // Verify they are cleared
      expect(tokenStorage.getAccessToken()).toBeNull()
      expect(tokenStorage.getRefreshToken()).toBeNull()
      expect(tokenStorage.hasValidTokens()).toBe(false)
    })

    it('should handle token expiration correctly', () => {
      const accessToken = 'expiring-access-token'
      const refreshToken = 'expiring-refresh-token'

      // Mock Date.now to simulate expired token
      const originalNow = Date.now
      const mockNow = vi.fn()
      Date.now = mockNow

      // Set token with current time
      mockNow.mockReturnValue(1000000)
      tokenStorage.setTokens(accessToken, refreshToken)
      
      // Verify tokens are valid initially
      expect(tokenStorage.hasValidTokens()).toBe(true)

      // Simulate time passing (16 minutes later)
      mockNow.mockReturnValue(1000000 + (16 * 60 * 1000))
      
      // Verify tokens are now expired
      expect(tokenStorage.hasValidTokens()).toBe(false)
      expect(tokenStorage.getAccessToken()).toBeNull()

      // Restore Date.now
      Date.now = originalNow
    })
  })
})