/**
 * Tests for error classification system
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { describe, it, expect } from 'vitest'
import { ErrorClassifier, ErrorUtils } from '../errors'
import { ApiError } from '../api'

describe('ErrorClassifier', () => {
  describe('classifyError', () => {
    it('should classify ApiError with UNAUTHORIZED status as TOKEN_EXPIRED for non-login context', () => {
      const apiError = new ApiError('Unauthorized', 401, 'UNAUTHORIZED')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('TOKEN_EXPIRED')
      expect(result.userMessage).toBe('Your session has expired. Please log in again.')
      expect(result.retryable).toBe(false)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify ApiError with UNAUTHORIZED status as INVALID_CREDENTIALS for login context', () => {
      const apiError = new ApiError('Unauthorized', 401, 'UNAUTHORIZED')
      const result = ErrorClassifier.classifyError(apiError, 'login')

      expect(result.code).toBe('INVALID_CREDENTIALS')
      expect(result.userMessage).toBe('Invalid email, password, or church code. Please check your credentials and try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
      expect(result.suggestedActions).toContain('Double-check your email address')
    })

    it('should classify ApiError with FORBIDDEN status as PERMISSION_DENIED', () => {
      const apiError = new ApiError('Forbidden', 403, 'FORBIDDEN')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('PERMISSION_DENIED')
      expect(result.userMessage).toBe('You do not have permission to perform this action.')
      expect(result.retryable).toBe(false)
      expect(result.recoverable).toBe(false)
      expect(result.actionable).toBe(true)
    })

    it('should classify ApiError with NOT_FOUND status as CHURCH_NOT_FOUND for church context', () => {
      const apiError = new ApiError('Church not found', 404, 'NOT_FOUND')
      const result = ErrorClassifier.classifyError(apiError, 'church')

      expect(result.code).toBe('CHURCH_NOT_FOUND')
      expect(result.userMessage).toBe('Church not found. Please check your church code or URL.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
      expect(result.suggestedActions).toContain('Verify the church code is correct')
    })

    it('should classify ApiError with VALIDATION_ERROR status correctly', () => {
      const apiError = new ApiError('Validation failed', 422, 'VALIDATION_ERROR')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('VALIDATION_ERROR')
      expect(result.userMessage).toBe('Please check your input and try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify ApiError with RATE_LIMITED status correctly', () => {
      const apiError = new ApiError('Too many requests', 429, 'RATE_LIMITED')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('RATE_LIMITED')
      expect(result.userMessage).toBe('Too many attempts. Please wait a moment and try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify ApiError with NETWORK_ERROR status correctly', () => {
      const apiError = new ApiError('Network error', 0, 'NETWORK_ERROR')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('NETWORK_ERROR')
      expect(result.userMessage).toBe('Unable to connect to the server. Please check your internet connection.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
      expect(result.suggestedActions).toContain('Check your internet connection')
    })

    it('should classify server errors as SERVER_ERROR', () => {
      const apiError = new ApiError('Internal server error', 500, 'INTERNAL_SERVER_ERROR')
      const result = ErrorClassifier.classifyError(apiError)

      expect(result.code).toBe('SERVER_ERROR')
      expect(result.userMessage).toBe('A server error occurred. Please try again in a few moments.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
      expect(result.contactSupport).toBe(true)
    })

    it('should classify standard Error with network message as NETWORK_ERROR', () => {
      const error = new Error('Network connection failed')
      const result = ErrorClassifier.classifyError(error)

      expect(result.code).toBe('NETWORK_ERROR')
      expect(result.userMessage).toBe('Network connection failed. Please check your internet connection and try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify standard Error with token message as TOKEN_EXPIRED', () => {
      const error = new Error('Token expired')
      const result = ErrorClassifier.classifyError(error)

      expect(result.code).toBe('TOKEN_EXPIRED')
      expect(result.userMessage).toBe('Your session has expired. Please log in again.')
      expect(result.retryable).toBe(false)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify error response with ACCOUNT_INACTIVE code correctly', () => {
      const errorResponse = {
        status: 403,
        code: 'ACCOUNT_INACTIVE',
        message: 'Account is inactive'
      }
      const result = ErrorClassifier.classifyError(errorResponse)

      expect(result.code).toBe('ACCOUNT_INACTIVE')
      expect(result.userMessage).toBe('Your account is inactive. Please contact your church administrator.')
      expect(result.retryable).toBe(false)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify error response with EMAIL_NOT_VERIFIED code correctly', () => {
      const errorResponse = {
        status: 401,
        code: 'EMAIL_NOT_VERIFIED',
        message: 'Email not verified'
      }
      const result = ErrorClassifier.classifyError(errorResponse)

      expect(result.code).toBe('EMAIL_NOT_VERIFIED')
      expect(result.userMessage).toBe('Please verify your email address before logging in.')
      expect(result.retryable).toBe(false)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
      expect(result.suggestedActions).toContain('Check your email for a verification link')
    })

    it('should classify error response with INVALID_CHURCH_CODE correctly', () => {
      const errorResponse = {
        status: 400,
        code: 'INVALID_CHURCH_CODE',
        message: 'Invalid church code'
      }
      const result = ErrorClassifier.classifyError(errorResponse)

      expect(result.code).toBe('INVALID_CHURCH_CODE')
      expect(result.userMessage).toBe('Invalid church code. Please check the code and try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should classify string errors correctly', () => {
      const result = ErrorClassifier.classifyError('Network error occurred')

      expect(result.code).toBe('NETWORK_ERROR')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(true)
      expect(result.actionable).toBe(true)
    })

    it('should create unknown error for unrecognized error types', () => {
      const result = ErrorClassifier.classifyError({ unknown: 'error' })

      expect(result.code).toBe('UNKNOWN_ERROR')
      expect(result.userMessage).toBe('An unexpected error occurred. Please try again.')
      expect(result.retryable).toBe(true)
      expect(result.recoverable).toBe(false)
      expect(result.actionable).toBe(true)
      expect(result.contactSupport).toBe(true)
    })
  })
})

describe('ErrorUtils', () => {
  const mockError = {
    code: 'NETWORK_ERROR' as const,
    message: 'Network error',
    userMessage: 'Network connection failed',
    retryable: true,
    recoverable: true,
    actionable: true,
    contactSupport: false
  }

  const mockNonRetryableError = {
    code: 'PERMISSION_DENIED' as const,
    message: 'Permission denied',
    userMessage: 'Access denied',
    retryable: false,
    recoverable: false,
    actionable: true,
    contactSupport: true
  }

  describe('isRetryable', () => {
    it('should return true for retryable errors', () => {
      expect(ErrorUtils.isRetryable(mockError)).toBe(true)
    })

    it('should return false for non-retryable errors', () => {
      expect(ErrorUtils.isRetryable(mockNonRetryableError)).toBe(false)
    })
  })

  describe('isRecoverable', () => {
    it('should return true for recoverable errors', () => {
      expect(ErrorUtils.isRecoverable(mockError)).toBe(true)
    })

    it('should return false for non-recoverable errors', () => {
      expect(ErrorUtils.isRecoverable(mockNonRetryableError)).toBe(false)
    })
  })

  describe('isActionable', () => {
    it('should return true for actionable errors', () => {
      expect(ErrorUtils.isActionable(mockError)).toBe(true)
      expect(ErrorUtils.isActionable(mockNonRetryableError)).toBe(true)
    })
  })

  describe('shouldContactSupport', () => {
    it('should return false when contactSupport is false', () => {
      expect(ErrorUtils.shouldContactSupport(mockError)).toBe(false)
    })

    it('should return true when contactSupport is true', () => {
      expect(ErrorUtils.shouldContactSupport(mockNonRetryableError)).toBe(true)
    })
  })

  describe('getRetryDelay', () => {
    it('should return 0 for non-retryable errors', () => {
      expect(ErrorUtils.getRetryDelay(mockNonRetryableError)).toBe(0)
    })

    it('should return exponential backoff for network errors', () => {
      const networkError = { ...mockError, code: 'NETWORK_ERROR' as const }
      expect(ErrorUtils.getRetryDelay(networkError, 1)).toBe(1000)
      expect(ErrorUtils.getRetryDelay(networkError, 2)).toBe(2000)
      expect(ErrorUtils.getRetryDelay(networkError, 3)).toBe(4000)
    })

    it('should return 60 seconds for rate limited errors', () => {
      const rateLimitedError = { ...mockError, code: 'RATE_LIMITED' as const }
      expect(ErrorUtils.getRetryDelay(rateLimitedError)).toBe(60000)
    })

    it('should return linear backoff for server errors', () => {
      const serverError = { ...mockError, code: 'SERVER_ERROR' as const }
      expect(ErrorUtils.getRetryDelay(serverError, 1)).toBe(2000)
      expect(ErrorUtils.getRetryDelay(serverError, 2)).toBe(4000)
      expect(ErrorUtils.getRetryDelay(serverError, 3)).toBe(6000)
    })

    it('should cap network error delay at 10 seconds', () => {
      const networkError = { ...mockError, code: 'NETWORK_ERROR' as const }
      expect(ErrorUtils.getRetryDelay(networkError, 10)).toBe(10000)
    })

    it('should cap server error delay at 30 seconds', () => {
      const serverError = { ...mockError, code: 'SERVER_ERROR' as const }
      expect(ErrorUtils.getRetryDelay(serverError, 20)).toBe(30000)
    })
  })

  describe('formatForLogging', () => {
    it('should format error for logging with all required fields', () => {
      const context = { userId: '123', operation: 'login' }
      const result = ErrorUtils.formatForLogging(mockError, context)

      expect(result).toMatchObject({
        code: 'NETWORK_ERROR',
        message: 'Network error',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true,
        context
      })
      expect(result.timestamp).toBeDefined()
      expect(typeof result.timestamp).toBe('string')
    })

    it('should include details when present', () => {
      const errorWithDetails = { ...mockError, details: { endpoint: '/api/test' } }
      const result = ErrorUtils.formatForLogging(errorWithDetails)

      expect(result.details).toEqual({ endpoint: '/api/test' })
    })
  })
})