/**
 * Cross-Browser Authentication Compatibility Tests
 * 
 * Tests for authentication functionality across different browsers:
 * - Storage API compatibility
 * - Token handling across browsers
 * - Event handling differences
 * - Fallback mechanisms
 * - Browser-specific security features
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { tokenStorage } from '../tokenStorage'
import { AuthProvider } from '../auth'
import { render, screen, waitFor, act } from '@testing-library/react'
import React from 'react'

// Mock different browser environments
const mockBrowserEnvironments = {
  chrome: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: true,
    webCrypto: true,
  },
  firefox: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: true,
    webCrypto: true,
  },
  safari: {
    userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: true,
    webCrypto: true,
  },
  edge: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: true,
    webCrypto: true,
  },
  ie11: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: false, // IE11 has limited storage event support
    webCrypto: false,
  },
  oldMobile: {
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 10_0 like Mac OS X) AppleWebKit/602.1.38 (KHTML, like Gecko) Version/10.0 Mobile/14A300 Safari/602.1',
    localStorage: true,
    sessionStorage: true,
    cookies: true,
    storageEvents: false,
    webCrypto: false,
  },
  privateBrowsing: {
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    localStorage: false, // Private browsing may disable localStorage
    sessionStorage: true,
    cookies: false, // Private browsing may disable cookies
    storageEvents: false,
    webCrypto: true,
  },
}

describe('Cross-Browser Authentication Compatibility', () => {
  let originalUserAgent: string
  let originalLocalStorage: Storage
  let originalSessionStorage: Storage
  let originalDocument: Document

  beforeEach(() => {
    // Store original browser APIs
    originalUserAgent = navigator.userAgent
    originalLocalStorage = window.localStorage
    originalSessionStorage = window.sessionStorage
    originalDocument = document

    vi.clearAllMocks()
  })

  afterEach(() => {
    // Restore original browser APIs
    Object.defineProperty(navigator, 'userAgent', {
      value: originalUserAgent,
      writable: true,
    })
    Object.defineProperty(window, 'localStorage', {
      value: originalLocalStorage,
      writable: true,
    })
    Object.defineProperty(window, 'sessionStorage', {
      value: originalSessionStorage,
      writable: true,
    })
    Object.defineProperty(global, 'document', {
      value: originalDocument,
      writable: true,
    })
  })

  const mockBrowserEnvironment = (browserName: keyof typeof mockBrowserEnvironments) => {
    const env = mockBrowserEnvironments[browserName]

    // Mock user agent
    Object.defineProperty(navigator, 'userAgent', {
      value: env.userAgent,
      writable: true,
    })

    // Mock localStorage
    if (env.localStorage) {
      const mockStorage = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
        length: 0,
        key: vi.fn(),
      }
      Object.defineProperty(window, 'localStorage', {
        value: mockStorage,
        writable: true,
      })
    } else {
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      })
    }

    // Mock sessionStorage
    if (env.sessionStorage) {
      const mockSessionStorage = {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
        length: 0,
        key: vi.fn(),
      }
      Object.defineProperty(window, 'sessionStorage', {
        value: mockSessionStorage,
        writable: true,
      })
    } else {
      Object.defineProperty(window, 'sessionStorage', {
        value: undefined,
        writable: true,
      })
    }

    // Mock document.cookie
    if (env.cookies) {
      Object.defineProperty(document, 'cookie', {
        value: '',
        writable: true,
      })
    } else {
      Object.defineProperty(document, 'cookie', {
        get: () => {
          throw new Error('Cookies disabled')
        },
        set: () => {
          throw new Error('Cookies disabled')
        },
      })
    }

    // Mock storage events
    if (!env.storageEvents) {
      window.addEventListener = vi.fn()
      window.removeEventListener = vi.fn()
    }

    // Mock Web Crypto API
    if (env.webCrypto) {
      Object.defineProperty(window, 'crypto', {
        value: {
          getRandomValues: vi.fn((arr) => {
            for (let i = 0; i < arr.length; i++) {
              arr[i] = Math.floor(Math.random() * 256)
            }
            return arr
          }),
          subtle: {
            generateKey: vi.fn(),
            encrypt: vi.fn(),
            decrypt: vi.fn(),
          },
        },
        writable: true,
      })
    } else {
      Object.defineProperty(window, 'crypto', {
        value: undefined,
        writable: true,
      })
    }

    return env
  }

  describe('Storage API Compatibility', () => {
    it('should work with localStorage in modern browsers', () => {
      mockBrowserEnvironment('chrome')

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)

      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'auth_access_token',
        testTokens.accessToken
      )
      expect(window.localStorage.setItem).toHaveBeenCalledWith(
        'auth_refresh_token',
        testTokens.refreshToken
      )
    })

    it('should fallback to sessionStorage when localStorage is unavailable', () => {
      const env = mockBrowserEnvironment('privateBrowsing')

      // Make localStorage throw error
      const mockLocalStorage = {
        setItem: vi.fn(() => {
          throw new Error('localStorage is not available')
        }),
        getItem: vi.fn(() => {
          throw new Error('localStorage is not available')
        }),
        removeItem: vi.fn(() => {
          throw new Error('localStorage is not available')
        }),
        clear: vi.fn(),
        length: 0,
        key: vi.fn(),
      }

      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)

      // Should fallback to sessionStorage
      expect(window.sessionStorage.setItem).toHaveBeenCalledWith(
        'auth_access_token',
        testTokens.accessToken
      )
    })

    it('should handle storage quota exceeded errors', () => {
      mockBrowserEnvironment('safari')

      // Mock storage quota exceeded
      const mockLocalStorage = {
        setItem: vi.fn(() => {
          const error = new Error('QuotaExceededError')
          error.name = 'QuotaExceededError'
          throw error
        }),
        getItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
        length: 0,
        key: vi.fn(),
      }

      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      // Should not throw error, should handle gracefully
      expect(() => {
        tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)
      }).not.toThrow()

      // Should attempt to clear storage and retry
      expect(mockLocalStorage.clear).toHaveBeenCalled()
    })
  })

  describe('Cookie Handling Compatibility', () => {
    it('should handle cookies in browsers that support them', () => {
      mockBrowserEnvironment('chrome')

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)

      // Should set cookies as backup
      expect(document.cookie).toBeDefined()
    })

    it('should handle browsers with cookies disabled', () => {
      mockBrowserEnvironment('privateBrowsing')

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      // Should not throw error even if cookies are disabled
      expect(() => {
        tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)
      }).not.toThrow()
    })

    it('should handle SameSite cookie restrictions', () => {
      mockBrowserEnvironment('chrome')

      // Mock document.cookie to simulate SameSite restrictions
      let cookieValue = ''
      Object.defineProperty(document, 'cookie', {
        get: () => cookieValue,
        set: (value: string) => {
          // Simulate browser rejecting cookies without SameSite
          if (!value.includes('SameSite=')) {
            throw new Error('Cookie rejected due to SameSite policy')
          }
          cookieValue = value
        },
      })

      const testTokens = {
        accessToken: 'test-access-token',
        refreshToken: 'test-refresh-token',
      }

      // Should handle SameSite restrictions gracefully
      expect(() => {
        tokenStorage.setTokens(testTokens.accessToken, testTokens.refreshToken)
      }).not.toThrow()
    })
  })

  describe('Event Handling Compatibility', () => {
    it('should handle storage events in modern browsers', () => {
      mockBrowserEnvironment('chrome')

      const callback = vi.fn()
      const unsubscribe = tokenStorage.onTokensChanged(callback)

      // Simulate storage event
      const storageEvent = new StorageEvent('storage', {
        key: 'auth_access_token',
        newValue: 'new-token',
        oldValue: 'old-token',
        storageArea: localStorage,
      })

      window.dispatchEvent(storageEvent)

      expect(callback).toHaveBeenCalled()

      unsubscribe()
    })

    it('should fallback to polling when storage events are not supported', async () => {
      mockBrowserEnvironment('ie11')

      const callback = vi.fn()
      let pollInterval: NodeJS.Timeout

      // Mock setInterval for polling fallback
      const originalSetInterval = global.setInterval
      global.setInterval = vi.fn((fn, delay) => {
        pollInterval = originalSetInterval(fn, delay)
        return pollInterval
      })

      const unsubscribe = tokenStorage.onTokensChanged(callback)

      // Should use polling instead of events
      expect(global.setInterval).toHaveBeenCalled()

      // Simulate token change
      const mockLocalStorage = window.localStorage as any
      mockLocalStorage.getItem.mockReturnValue('new-token')

      // Wait for polling interval
      await new Promise(resolve => setTimeout(resolve, 1100))

      expect(callback).toHaveBeenCalled()

      unsubscribe()
      global.setInterval = originalSetInterval
    })

    it('should handle browsers with limited event support', () => {
      mockBrowserEnvironment('oldMobile')

      // Should not crash when addEventListener is not fully supported
      expect(() => {
        const callback = vi.fn()
        const unsubscribe = tokenStorage.onTokensChanged(callback)
        unsubscribe()
      }).not.toThrow()
    })
  })

  describe('Security Feature Compatibility', () => {
    it('should use Web Crypto API when available', () => {
      mockBrowserEnvironment('chrome')

      // Mock crypto.getRandomValues
      const mockCrypto = window.crypto as any
      mockCrypto.getRandomValues.mockImplementation((arr) => {
        for (let i = 0; i < arr.length; i++) {
          arr[i] = Math.floor(Math.random() * 256)
        }
        return arr
      })

      // Function that uses crypto for secure random generation
      const generateSecureToken = () => {
        if (window.crypto && window.crypto.getRandomValues) {
          const array = new Uint8Array(32)
          window.crypto.getRandomValues(array)
          return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
        }
        return Math.random().toString(36).substring(2)
      }

      const token = generateSecureToken()

      expect(window.crypto.getRandomValues).toHaveBeenCalled()
      expect(token).toHaveLength(64) // 32 bytes * 2 hex chars
    })

    it('should fallback to Math.random when Web Crypto is not available', () => {
      mockBrowserEnvironment('ie11')

      // Function that falls back to Math.random
      const generateToken = () => {
        if (window.crypto && window.crypto.getRandomValues) {
          const array = new Uint8Array(32)
          window.crypto.getRandomValues(array)
          return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
        }
        return Math.random().toString(36).substring(2)
      }

      const token = generateToken()

      expect(token).toBeDefined()
      expect(typeof token).toBe('string')
      expect(token.length).toBeGreaterThan(0)
    })

    it('should handle Content Security Policy restrictions', () => {
      mockBrowserEnvironment('chrome')

      // Mock CSP violation
      const originalEval = global.eval
      global.eval = () => {
        throw new Error('Content Security Policy violation')
      }

      // Should not use eval or similar unsafe practices
      expect(() => {
        tokenStorage.setTokens('test-token', 'test-refresh')
        tokenStorage.getAccessToken()
        tokenStorage.clearTokens()
      }).not.toThrow()

      global.eval = originalEval
    })
  })

  describe('Browser-Specific Workarounds', () => {
    it('should handle Safari private browsing localStorage issues', () => {
      mockBrowserEnvironment('safari')

      // Safari in private browsing throws on localStorage access
      const mockLocalStorage = {
        setItem: vi.fn(() => {
          throw new Error('localStorage is not available in private browsing')
        }),
        getItem: vi.fn(() => {
          throw new Error('localStorage is not available in private browsing')
        }),
        removeItem: vi.fn(() => {
          throw new Error('localStorage is not available in private browsing')
        }),
        clear: vi.fn(),
        length: 0,
        key: vi.fn(),
      }

      Object.defineProperty(window, 'localStorage', {
        value: mockLocalStorage,
        writable: true,
      })

      // Should handle gracefully and fallback
      expect(() => {
        tokenStorage.setTokens('test-token', 'test-refresh')
      }).not.toThrow()

      // Should fallback to sessionStorage
      expect(window.sessionStorage.setItem).toHaveBeenCalled()
    })

    it('should handle IE11 storage event limitations', () => {
      mockBrowserEnvironment('ie11')

      // IE11 has limited storage event support
      let eventCallback: ((event: StorageEvent) => void) | null = null

      window.addEventListener = vi.fn((type, callback) => {
        if (type === 'storage') {
          eventCallback = callback as (event: StorageEvent) => void
        }
      })

      const callback = vi.fn()
      tokenStorage.onTokensChanged(callback)

      // Manually trigger storage event (IE11 style)
      if (eventCallback) {
        const event = {
          key: 'auth_access_token',
          newValue: 'new-token',
          oldValue: 'old-token',
          storageArea: localStorage,
        } as StorageEvent

        eventCallback(event)
      }

      expect(callback).toHaveBeenCalled()
    })

    it('should handle mobile browser viewport changes', () => {
      mockBrowserEnvironment('oldMobile')

      // Mock mobile viewport change
      Object.defineProperty(window, 'innerHeight', {
        value: 600,
        writable: true,
      })

      // Simulate viewport change (mobile keyboard)
      Object.defineProperty(window, 'innerHeight', {
        value: 300,
        writable: true,
      })

      window.dispatchEvent(new Event('resize'))

      // Should not affect token storage
      expect(() => {
        tokenStorage.setTokens('test-token', 'test-refresh')
        const token = tokenStorage.getAccessToken()
        expect(token).toBeDefined()
      }).not.toThrow()
    })
  })

  describe('Performance Across Browsers', () => {
    it('should maintain acceptable performance in older browsers', async () => {
      mockBrowserEnvironment('ie11')

      const startTime = Date.now()

      // Perform multiple storage operations
      for (let i = 0; i < 100; i++) {
        tokenStorage.setTokens(`token-${i}`, `refresh-${i}`)
        tokenStorage.getAccessToken()
        tokenStorage.getRefreshToken()
      }

      const endTime = Date.now()
      const duration = endTime - startTime

      // Should complete within reasonable time even in older browsers
      expect(duration).toBeLessThan(1000) // Less than 1 second
    })

    it('should handle memory constraints in mobile browsers', () => {
      mockBrowserEnvironment('oldMobile')

      // Simulate memory pressure
      const largeData = 'x'.repeat(1024 * 1024) // 1MB string

      // Should handle large tokens gracefully
      expect(() => {
        tokenStorage.setTokens(largeData, largeData)
      }).not.toThrow()

      // Should be able to retrieve tokens
      const retrievedToken = tokenStorage.getAccessToken()
      expect(retrievedToken).toBeDefined()
    })
  })

  describe('Graceful Degradation', () => {
    it('should provide basic functionality when all storage is disabled', () => {
      // Mock environment with no storage
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      })
      Object.defineProperty(window, 'sessionStorage', {
        value: undefined,
        writable: true,
      })
      Object.defineProperty(document, 'cookie', {
        get: () => {
          throw new Error('Cookies disabled')
        },
        set: () => {
          throw new Error('Cookies disabled')
        },
      })

      // Should still function with in-memory storage
      expect(() => {
        tokenStorage.setTokens('test-token', 'test-refresh')
        const token = tokenStorage.getAccessToken()
        expect(token).toBe('test-token')
      }).not.toThrow()
    })

    it('should warn users about limited functionality', () => {
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

      // Mock environment with limited storage
      Object.defineProperty(window, 'localStorage', {
        value: undefined,
        writable: true,
      })

      tokenStorage.setTokens('test-token', 'test-refresh')

      // Should warn about fallback to less secure storage
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('localStorage not available')
      )

      consoleSpy.mockRestore()
    })
  })
});