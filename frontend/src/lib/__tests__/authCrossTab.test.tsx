/**
 * Cross-tab synchronization tests for AuthProvider
 */

import React from 'react'
import { render, screen, waitFor, act } from '@testing-library/react'
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { useRouter } from 'next/navigation'
import { AuthProvider, useAuth } from '../auth'
import { tokenStorage } from '../tokenStorage'
import { apiClient } from '../api'

// Mock dependencies
vi.mock('next/navigation')
vi.mock('../api')
vi.mock('../tokenStorage')
vi.mock('../errorRecovery')

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

const mockTokenStorage = tokenStorage as any
const mockApiClient = apiClient as any
const mockUseRouter = useRouter as any

// Test component to access auth context
function TestComponent() {
  const auth = useAuth()
  
  return (
    <div>
      <div data-testid="user">{auth.user ? auth.user.email : 'null'}</div>
      <div data-testid="church">{auth.church ? auth.church.name : 'null'}</div>
      <div data-testid="loading">{auth.loading.toString()}</div>
      <div data-testid="initialized">{auth.isInitialized.toString()}</div>
      <div data-testid="authenticated">{auth.isAuthenticated.toString()}</div>
      <button onClick={() => auth.logout()} data-testid="logout-btn">Logout</button>
    </div>
  )
}

describe('AuthProvider Cross-Tab Synchronization', () => {
  let tokenChangeCallback: ((tokens: any) => void) | null = null

  beforeEach(() => {
    vi.clearAllMocks()
    vi.useFakeTimers()
    
    mockUseRouter.mockReturnValue(mockRouter)
    
    // Mock tokenStorage methods
    mockTokenStorage.hasValidTokens.mockReturnValue(false)
    mockTokenStorage.getTokens.mockReturnValue(null)
    mockTokenStorage.getAccessToken.mockReturnValue(null)
    mockTokenStorage.getRefreshToken.mockReturnValue(null)
    mockTokenStorage.clearTokens.mockImplementation(() => {})
    mockTokenStorage.setTokens.mockImplementation(() => {})
    
    // Capture token change callback
    mockTokenStorage.onTokensChanged.mockImplementation((callback) => {
      tokenChangeCallback = callback
      return () => {} // unsubscribe function
    })

    // Mock API client
    mockApiClient.getProfile.mockResolvedValue({
      data: {
        user: {
          id: '1',
          email: '<EMAIL>',
          church: {
            id: '1',
            name: 'Test Church',
            slug: 'test-church',
          },
        },
      },
    })

    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/test-church/dashboard',
      },
      writable: true,
    })
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('Cross-Tab Logout Synchronization', () => {
    it('should logout user when tokens are cleared in another tab', async () => {
      // Set up authenticated state
      mockTokenStorage.hasValidTokens.mockReturnValue(true)
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'valid-token',
        refreshToken: 'valid-refresh',
        expiresAt: Date.now() + 900000,
        issuedAt: Date.now(),
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('initialized')).toHaveTextContent('true')
      })

      // Verify user is authenticated
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('true')
        expect(screen.getByTestId('user')).toHaveTextContent('<EMAIL>')
      })

      // Simulate token clearing in another tab
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(null)
        }
      })

      // Verify user is logged out
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
        expect(screen.getByTestId('user')).toHaveTextContent('null')
        expect(screen.getByTestId('church')).toHaveTextContent('null')
      })

      // Verify redirect to login
      expect(mockRouter.push).toHaveBeenCalledWith('/test-church/login?redirect=%2Ftest-church%2Fdashboard')
    })

    it('should redirect to general login for public routes', async () => {
      // Set up public route
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/some-public-page',
        },
        writable: true,
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate token clearing
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(null)
        }
      })

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/login?redirect=%2Fsome-public-page')
      })
    })
  })

  describe('Cross-Tab Token Update Synchronization', () => {
    it('should update auth state when tokens are refreshed in another tab', async () => {
      const newTokens = {
        accessToken: 'new-access-token',
        refreshToken: 'new-refresh-token',
        expiresAt: Date.now() + 900000,
        issuedAt: Date.now(),
      }

      // Set up initial state
      mockTokenStorage.hasValidTokens.mockReturnValue(true)
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'old-token',
        refreshToken: 'old-refresh',
        expiresAt: Date.now() + 900000,
        issuedAt: Date.now(),
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('initialized')).toHaveTextContent('true')
      })

      // Simulate token update in another tab
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(newTokens)
        }
      })

      // Verify checkAuthStatus is called to refresh user data
      await waitFor(() => {
        expect(mockApiClient.getProfile).toHaveBeenCalled()
      })
    })

    it('should logout if updated tokens are expired', async () => {
      const expiredTokens = {
        accessToken: 'expired-token',
        refreshToken: 'expired-refresh',
        expiresAt: Date.now() - 1000, // Expired
        issuedAt: Date.now() - 900000,
      }

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate expired token update
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(expiredTokens)
        }
      })

      // Verify user is logged out
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
      })
    })

    it('should handle auth check failure during token update', async () => {
      const newTokens = {
        accessToken: 'new-token',
        refreshToken: 'new-refresh',
        expiresAt: Date.now() + 900000,
        issuedAt: Date.now(),
      }

      // Mock API failure
      mockApiClient.getProfile.mockRejectedValue(new Error('API Error'))

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate token update
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(newTokens)
        }
      })

      // Verify user is logged out due to auth check failure
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
      })
    })
  })

  describe('Session Expiration Check', () => {
    it('should periodically check for session expiration', async () => {
      // Set up authenticated state
      mockTokenStorage.hasValidTokens.mockReturnValue(true)
      mockTokenStorage.getTokens.mockReturnValue({
        accessToken: 'valid-token',
        refreshToken: 'valid-refresh',
        expiresAt: Date.now() + 900000,
        issuedAt: Date.now(),
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('true')
      })

      // Mock token expiration
      mockTokenStorage.hasValidTokens.mockReturnValue(false)

      // Advance timer to trigger session check
      act(() => {
        vi.advanceTimersByTime(30000) // 30 seconds
      })

      // Verify user is logged out
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
      })
    })

    it('should not logout if user is already unauthenticated', async () => {
      // Start with unauthenticated state
      mockTokenStorage.hasValidTokens.mockReturnValue(false)

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Wait for initialization
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
      })

      // Advance timer
      act(() => {
        vi.advanceTimersByTime(30000)
      })

      // Verify no redirect is called (user already unauthenticated)
      expect(mockRouter.push).not.toHaveBeenCalled()
    })

    it('should clean up session check interval on unmount', () => {
      const { unmount } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Unmount component
      unmount()

      // Advance timer - should not cause any issues
      act(() => {
        vi.advanceTimersByTime(30000)
      })

      // No assertions needed - test passes if no errors are thrown
    })
  })

  describe('Component Lifecycle', () => {
    it('should handle rapid mount/unmount without memory leaks', () => {
      const { unmount } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate token change after unmount
      unmount()

      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(null)
        }
      })

      // No assertions needed - test passes if no errors are thrown
    })

    it('should unsubscribe from token changes on unmount', () => {
      const unsubscribeMock = vi.fn()
      mockTokenStorage.onTokensChanged.mockReturnValue(unsubscribeMock)

      const { unmount } = render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      unmount()

      expect(unsubscribeMock).toHaveBeenCalled()
    })
  })

  describe('Route Protection', () => {
    it('should redirect protected routes to church-specific login', async () => {
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/test-church/members',
        },
        writable: true,
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate logout
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(null)
        }
      })

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/login?redirect=%2Ftest-church%2Fmembers')
      })
    })

    it('should not redirect non-protected routes', async () => {
      Object.defineProperty(window, 'location', {
        value: {
          pathname: '/test-church/login',
        },
        writable: true,
      })

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Simulate logout
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback(null)
        }
      })

      // Should not redirect login page
      expect(mockRouter.push).not.toHaveBeenCalled()
    })
  })

  describe('Error Handling', () => {
    it('should handle token change callback errors gracefully', async () => {
      // Mock console.error to avoid test output noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

      render(
        <AuthProvider>
          <TestComponent />
        </AuthProvider>
      )

      // Mock API to throw error
      mockApiClient.getProfile.mockRejectedValue(new Error('Network error'))

      // Simulate token update that will cause error
      act(() => {
        if (tokenChangeCallback) {
          tokenChangeCallback({
            accessToken: 'new-token',
            refreshToken: 'new-refresh',
            expiresAt: Date.now() + 900000,
            issuedAt: Date.now(),
          })
        }
      })

      // Verify error is handled gracefully
      await waitFor(() => {
        expect(screen.getByTestId('authenticated')).toHaveTextContent('false')
      })

      consoleSpy.mockRestore()
    })
  })
})