/**
 * Tests for error recovery mechanisms
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { ErrorRecoveryManager, AuthRecoveryManager, RecoveryContext } from '../errorRecovery'
import { AuthError } from '../errors'

// Mock timers
vi.useFakeTimers({ shouldAdvanceTime: true })

describe('ErrorRecoveryManager', () => {
  let recoveryManager: ErrorRecoveryManager
  let mockOperation: ReturnType<typeof vi.fn>

  beforeEach(() => {
    recoveryManager = new ErrorRecoveryManager()
    mockOperation = vi.fn()
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.clearAllMocks()
  })

  describe('attemptRecovery', () => {
    const context: RecoveryContext = {
      operation: 'test',
      userId: 'user123',
      endpoint: '/api/test'
    }

    it('should succeed on first attempt when operation succeeds', async () => {
      const expectedResult = { success: true }
      mockOperation.mockResolvedValueOnce(expectedResult)

      const result = await recoveryManager.attemptRecovery(mockOperation, context)

      expect(result).toEqual(expectedResult)
      expect(mockOperation).toHaveBeenCalledTimes(1)
    })

    it('should retry retryable errors up to maxAttempts', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({ success: true })

      const onRetry = vi.fn()
      const promise = recoveryManager.attemptRecovery(mockOperation, context, {
        maxAttempts: 3,
        baseDelay: 10,
        onRetry
      })

      // Advance timers to handle delays
      await vi.advanceTimersByTimeAsync(50)

      const result = await promise
      expect(result).toEqual({ success: true })
      expect(mockOperation).toHaveBeenCalledTimes(3)
      expect(onRetry).toHaveBeenCalledTimes(2)
    }, 10000)

    it('should not retry non-retryable errors', async () => {
      const permissionError: AuthError = {
        code: 'PERMISSION_DENIED',
        message: 'Access denied',
        userMessage: 'Permission denied',
        retryable: false,
        recoverable: false,
        actionable: true
      }

      mockOperation.mockRejectedValueOnce(permissionError)

      await expect(
        recoveryManager.attemptRecovery(mockOperation, context)
      ).rejects.toMatchObject({
        code: 'PERMISSION_DENIED'
      })

      expect(mockOperation).toHaveBeenCalledTimes(1)
    })

    it('should call onMaxAttemptsReached when max attempts exceeded', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation.mockRejectedValue(networkError)
      const onMaxAttemptsReached = vi.fn()

      const promise = recoveryManager.attemptRecovery(mockOperation, context, {
        maxAttempts: 2,
        baseDelay: 10,
        onMaxAttemptsReached
      })

      await vi.advanceTimersByTimeAsync(50)

      await expect(promise).rejects.toMatchObject({
        code: 'NETWORK_ERROR'
      })

      expect(mockOperation).toHaveBeenCalledTimes(2)
      expect(onMaxAttemptsReached).toHaveBeenCalledTimes(1)
    }, 10000)

    it('should implement exponential backoff delay', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({ success: true })

      const promise = recoveryManager.attemptRecovery(mockOperation, context, {
        maxAttempts: 3,
        baseDelay: 100,
        backoffFactor: 2
      })

      // Fast-forward through the delays
      await vi.advanceTimersByTimeAsync(100) // First retry delay
      await vi.advanceTimersByTimeAsync(200) // Second retry delay

      const result = await promise
      expect(result).toEqual({ success: true })
    })

    it('should respect maxDelay cap', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({ success: true })

      const promise = recoveryManager.attemptRecovery(mockOperation, context, {
        maxAttempts: 2,
        baseDelay: 1000,
        backoffFactor: 10,
        maxDelay: 500 // Should cap the delay
      })

      // The delay should be capped at maxDelay (500ms) instead of baseDelay * backoffFactor (10000ms)
      await vi.advanceTimersByTimeAsync(500)

      const result = await promise
      expect(result).toEqual({ success: true })
    })
  })

  describe('getRecoveryStrategy', () => {
    it('should return recovery strategy for known error codes', () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      const strategy = recoveryManager.getRecoveryStrategy(networkError)

      expect(strategy).toBeDefined()
      expect(strategy?.canRecover).toBe(true)
      expect(strategy?.autoRetry).toBe(true)
      expect(strategy?.maxRetries).toBe(3)
    })

    it('should return null for unknown error codes', () => {
      const unknownError: AuthError = {
        code: 'UNKNOWN_ERROR',
        message: 'Unknown error',
        userMessage: 'An unknown error occurred',
        retryable: false,
        recoverable: false,
        actionable: false
      }

      const strategy = recoveryManager.getRecoveryStrategy(unknownError)
      expect(strategy).toBeNull()
    })
  })

  describe('registerRecoveryStrategy', () => {
    it('should allow registering custom recovery strategies', () => {
      const customStrategy = {
        canRecover: true,
        autoRetry: false,
        maxRetries: 1,
        userActions: ['Custom action'],
        recover: vi.fn().mockResolvedValue({ success: true })
      }

      recoveryManager.registerRecoveryStrategy('CUSTOM_ERROR', customStrategy)

      const customError: AuthError = {
        code: 'CUSTOM_ERROR' as any,
        message: 'Custom error',
        userMessage: 'A custom error occurred',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      const strategy = recoveryManager.getRecoveryStrategy(customError)
      expect(strategy).toEqual(customStrategy)
    })
  })

  describe('retry attempt tracking', () => {
    const context: RecoveryContext = {
      operation: 'test',
      userId: 'user123',
      endpoint: '/api/test'
    }

    it('should track retry attempts correctly', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce({ success: true })

      expect(recoveryManager.getRetryAttempts(context)).toBe(0)

      const promise = recoveryManager.attemptRecovery(mockOperation, context, {
        maxAttempts: 2,
        baseDelay: 10
      })

      await vi.advanceTimersByTimeAsync(10)
      await promise

      // Should be cleared after successful recovery
      expect(recoveryManager.getRetryAttempts(context)).toBe(0)
    })

    it('should clear retry attempts manually', async () => {
      const context: RecoveryContext = {
        operation: 'test',
        userId: 'user123',
        endpoint: '/api/test'
      }

      // Simulate some retry attempts
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockOperation.mockRejectedValue(networkError)

      try {
        await recoveryManager.attemptRecovery(mockOperation, context, {
          maxAttempts: 1,
          baseDelay: 10
        })
      } catch {
        // Expected to fail
      }

      recoveryManager.clearRetryAttempts(context)
      expect(recoveryManager.getRetryAttempts(context)).toBe(0)
    })
  })
})

describe('AuthRecoveryManager', () => {
  let authRecoveryManager: AuthRecoveryManager
  let mockLoginFn: ReturnType<typeof vi.fn>
  let mockRefreshFn: ReturnType<typeof vi.fn>

  beforeEach(() => {
    authRecoveryManager = new AuthRecoveryManager()
    mockLoginFn = vi.fn()
    mockRefreshFn = vi.fn()
  })

  afterEach(() => {
    vi.clearAllTimers()
    vi.clearAllMocks()
  })

  describe('recoverFromLoginError', () => {
    it('should attempt login recovery with limited retries', async () => {
      mockLoginFn.mockResolvedValueOnce(undefined)

      await authRecoveryManager.recoverFromLoginError(
        mockLoginFn,
        '<EMAIL>',
        'CHURCH1'
      )

      expect(mockLoginFn).toHaveBeenCalledTimes(1)
    })

    it('should not retry invalid credentials errors', async () => {
      const credentialsError: AuthError = {
        code: 'INVALID_CREDENTIALS',
        message: 'Invalid credentials',
        userMessage: 'Invalid email or password',
        retryable: false,
        recoverable: true,
        actionable: true
      }

      mockLoginFn.mockRejectedValueOnce(credentialsError)

      await expect(
        authRecoveryManager.recoverFromLoginError(
          mockLoginFn,
          '<EMAIL>',
          'CHURCH1'
        )
      ).rejects.toMatchObject({
        code: 'INVALID_CREDENTIALS'
      })

      expect(mockLoginFn).toHaveBeenCalledTimes(1)
    })

    it('should retry network errors during login', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockLoginFn
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce(undefined)

      const promise = authRecoveryManager.recoverFromLoginError(
        mockLoginFn,
        '<EMAIL>',
        'CHURCH1'
      )

      await vi.advanceTimersByTimeAsync(1000)
      await promise

      expect(mockLoginFn).toHaveBeenCalledTimes(2)
    })
  })

  describe('recoverFromTokenRefresh', () => {
    it('should attempt token refresh recovery', async () => {
      mockRefreshFn.mockResolvedValueOnce(undefined)

      await authRecoveryManager.recoverFromTokenRefresh(mockRefreshFn)

      expect(mockRefreshFn).toHaveBeenCalledTimes(1)
    })

    it('should retry token refresh on network errors', async () => {
      const networkError: AuthError = {
        code: 'NETWORK_ERROR',
        message: 'Network failed',
        userMessage: 'Network connection failed',
        retryable: true,
        recoverable: true,
        actionable: true
      }

      mockRefreshFn
        .mockRejectedValueOnce(networkError)
        .mockResolvedValueOnce(undefined)

      const promise = authRecoveryManager.recoverFromTokenRefresh(mockRefreshFn)

      await vi.advanceTimersByTimeAsync(500)
      await promise

      expect(mockRefreshFn).toHaveBeenCalledTimes(2)
    })

    it('should not retry token expired errors', async () => {
      const tokenExpiredError: AuthError = {
        code: 'TOKEN_EXPIRED',
        message: 'Token expired',
        userMessage: 'Your session has expired',
        retryable: false,
        recoverable: true,
        actionable: true
      }

      mockRefreshFn.mockRejectedValueOnce(tokenExpiredError)

      await expect(
        authRecoveryManager.recoverFromTokenRefresh(mockRefreshFn)
      ).rejects.toMatchObject({
        code: 'TOKEN_EXPIRED'
      })

      expect(mockRefreshFn).toHaveBeenCalledTimes(1)
    })
  })
})