/**
 * API Client Integration Tests
 * 
 * Tests for authenticated API requests including:
 * - Token refresh integration
 * - Error handling and propagation
 * - Retry logic for authentication failures
 * - Enhanced error classification
 */

import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest'
import { apiClient, ApiError } from '../api'
import { tokenRefreshInterceptor } from '../tokenRefreshInterceptor'
import { tokenStorage } from '../tokenStorage'

// Mock the dependencies
vi.mock('../tokenRefreshInterceptor', () => ({
  tokenRefreshInterceptor: {
    interceptRequest: vi.fn(),
    interceptResponse: vi.fn(),
    clearRefreshState: vi.fn(),
  },
}))

vi.mock('../tokenStorage', () => ({
  tokenStorage: {
    getAccessToken: vi.fn(),
    getRefreshToken: vi.fn(),
    setTokens: vi.fn(),
    clearTokens: vi.fn(),
    getTokens: vi.fn(),
  },
}))

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe('API Client Integration Tests', () => {
  const mockTokenRefreshInterceptor = tokenRefreshInterceptor as {
    interceptRequest: Mock
    interceptResponse: Mock
    clearRefreshState: Mock
  }

  const mockTokenStorage = tokenStorage as {
    getAccessToken: Mock
    getRefreshToken: Mock
    setTokens: Mock
    clearTokens: Mock
    getTokens: Mock
  }

  beforeEach(() => {
    vi.clearAllMocks()
    mockFetch.mockClear()
    
    // Set default environment
    process.env.NEXT_PUBLIC_API_URL = 'http://localhost:3000/api'
    
    // Default mock implementations
    mockTokenRefreshInterceptor.interceptRequest.mockImplementation(async (config) => config)
    mockTokenRefreshInterceptor.interceptResponse.mockImplementation(async (response) => response)
  })

  afterEach(() => {
    mockTokenRefreshInterceptor.clearRefreshState()
  })

  describe('Authentication Integration', () => {
    it('should automatically include authentication headers in requests', async () => {
      const accessToken = 'valid-access-token'
      mockTokenStorage.getAccessToken.mockReturnValue(accessToken)

      // Mock successful response
      const mockResponse = new Response(JSON.stringify({ data: { churches: [] } }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      })
      mockFetch.mockResolvedValueOnce(mockResponse)

      await apiClient.getChurches()

      expect(mockTokenRefreshInterceptor.interceptRequest).toHaveBeenCalledWith({
        url: 'http://localhost:3000/api/churches',
        options: {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      })
    })

    it('should handle token refresh during authenticated requests', async () => {
      const originalResponse = new Response('Unauthorized', { status: 401 })
      const refreshedResponse = new Response(JSON.stringify({ data: { members: [] } }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' },
      })

      // Mock interceptor to simulate token refresh
      mockTokenRefreshInterceptor.interceptResponse
        .mockResolvedValueOnce(refreshedResponse)

      mockFetch.mockResolvedValueOnce(originalResponse)

      const result = await apiClient.getMembers('test-church')

      expect(mockTokenRefreshInterceptor.interceptResponse).toHaveBeenCalledWith(
        originalResponse,
        expect.objectContaining({
          url: 'http://localhost:3000/api/churches/test-church/members',
        })
      )
      expect(result).toEqual({ data: { members: [] } })
    })

    it('should handle authentication failures with proper error classification', async () => {
      const errorResponse = new Response(
        JSON.stringify({ 
          message: 'Invalid credentials',
          code: 'INVALID_CREDENTIALS'
        }),
        { status: 401 }
      )

      // Mock interceptor to return the error response as-is
      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(errorResponse)
      mockFetch.mockResolvedValueOnce(errorResponse)

      try {
        await apiClient.login({
          email: '<EMAIL>',
          password: 'wrongpassword',
          churchCode: 'TEST'
        })
        expect.fail('Should have thrown an error')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).status).toBe(401)
        expect((error as ApiError).code).toBe('INVALID_CREDENTIALS')
        expect((error as ApiError).isType('INVALID_CREDENTIALS')).toBe(true)
      }
    })
  })

  describe('Error Handling and Classification', () => {
    it('should classify network errors correctly', async () => {
      mockFetch.mockRejectedValueOnce(new TypeError('Failed to fetch'))

      try {
        await apiClient.getChurches()
        expect.fail('Should have thrown an error')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).code).toBe('NETWORK_ERROR')
        expect((error as ApiError).retryable).toBe(true)
        expect((error as ApiError).getUserMessage()).toContain('internet connection')
      }
    })

    it('should classify HTTP errors correctly', async () => {
      const testCases = [
        { status: 400, expectedCode: 'BAD_REQUEST', retryable: false },
        { status: 403, expectedCode: 'FORBIDDEN', retryable: false },
        { status: 404, expectedCode: 'NOT_FOUND', retryable: false },
        { status: 422, expectedCode: 'VALIDATION_ERROR', retryable: false },
        { status: 429, expectedCode: 'RATE_LIMITED', retryable: true },
        { status: 500, expectedCode: 'INTERNAL_SERVER_ERROR', retryable: true },
        { status: 503, expectedCode: 'SERVICE_UNAVAILABLE', retryable: true },
      ]

      for (const testCase of testCases) {
        const errorResponse = new Response(
          JSON.stringify({ message: 'Test error' }),
          { status: testCase.status }
        )

        mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(errorResponse)
        mockFetch.mockResolvedValueOnce(errorResponse)

        try {
          await apiClient.getChurches()
          expect.fail('Should have thrown an error')
        } catch (error) {
          expect(error).toBeInstanceOf(ApiError)
          expect((error as ApiError).status).toBe(testCase.status)
          expect((error as ApiError).code).toBe(testCase.expectedCode)
          expect((error as ApiError).retryable).toBe(testCase.retryable)
        }

        vi.clearAllMocks()
        mockFetch.mockClear()
      }
    })

    it('should handle malformed JSON responses gracefully', async () => {
      const errorResponse = new Response('Invalid JSON', { status: 500 })

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(errorResponse)
      mockFetch.mockResolvedValueOnce(errorResponse)

      try {
        await apiClient.getChurches()
        expect.fail('Should have thrown an error')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).code).toBe('NETWORK_ERROR')
        expect((error as ApiError).message).toBe('Network error')
      }
    })

    it('should provide user-friendly error messages', async () => {
      const testCases = [
        { code: 'NETWORK_ERROR', expectedMessage: 'internet connection' },
        { code: 'UNAUTHORIZED', expectedMessage: 'session has expired' },
        { code: 'FORBIDDEN', expectedMessage: 'do not have permission' },
        { code: 'NOT_FOUND', expectedMessage: 'not found' },
        { code: 'VALIDATION_ERROR', expectedMessage: 'check your input' },
        { code: 'RATE_LIMITED', expectedMessage: 'Too many requests' },
        { code: 'INTERNAL_SERVER_ERROR', expectedMessage: 'server error' },
      ]

      for (const testCase of testCases) {
        const error = new ApiError('Test message', 500, testCase.code)
        expect(error.getUserMessage().toLowerCase()).toContain(testCase.expectedMessage.toLowerCase())
      }
    })
  })

  describe('Church-scoped API Endpoints', () => {
    beforeEach(() => {
      // Mock successful authentication
      mockTokenStorage.getAccessToken.mockReturnValue('valid-token')
      mockTokenRefreshInterceptor.interceptRequest.mockImplementation(async (config) => ({
        ...config,
        options: {
          ...config.options,
          headers: new Headers({
            ...config.options.headers,
            'Authorization': 'Bearer valid-token',
          }),
        },
      }))
    })

    it('should handle member management endpoints with authentication', async () => {
      const mockMembersResponse = new Response(
        JSON.stringify({ data: { members: [{ id: '1', name: 'John Doe' }] } }),
        { status: 200 }
      )
      const mockCreateResponse = new Response(
        JSON.stringify({ data: { id: '2', name: 'Jane Doe' } }),
        { status: 201 }
      )

      mockTokenRefreshInterceptor.interceptResponse
        .mockResolvedValueOnce(mockMembersResponse)
        .mockResolvedValueOnce(mockCreateResponse)

      mockFetch
        .mockResolvedValueOnce(mockMembersResponse)
        .mockResolvedValueOnce(mockCreateResponse)

      // Test GET members
      const membersResult = await apiClient.getMembers('test-church')
      expect(membersResult.data.members).toHaveLength(1)

      // Test CREATE member
      const createResult = await apiClient.createMember('test-church', {
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
      })
      expect(createResult.data.id).toBe('2')

      expect(mockFetch).toHaveBeenCalledTimes(2)
      expect(mockTokenRefreshInterceptor.interceptRequest).toHaveBeenCalledTimes(2)
    })

    it('should handle event management endpoints with authentication', async () => {
      const mockEventsResponse = new Response(
        JSON.stringify({ data: { events: [{ id: '1', title: 'Sunday Service' }] } }),
        { status: 200 }
      )

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(mockEventsResponse)
      mockFetch.mockResolvedValueOnce(mockEventsResponse)

      const result = await apiClient.getEvents('test-church', { limit: '10' })

      expect(result.data.events).toHaveLength(1)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/churches/test-church/events?limit=10',
        expect.objectContaining({
          headers: expect.any(Headers),
        })
      )
    })

    it('should handle branch management endpoints with authentication', async () => {
      const mockBranchesResponse = new Response(
        JSON.stringify({ data: { branches: [{ id: '1', name: 'Main Campus' }] } }),
        { status: 200 }
      )

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(mockBranchesResponse)
      mockFetch.mockResolvedValueOnce(mockBranchesResponse)

      const result = await apiClient.getBranches('test-church')

      expect(result.data.branches).toHaveLength(1)
      expect(mockFetch).toHaveBeenCalledWith(
        'http://localhost:3000/api/churches/test-church/branches',
        expect.objectContaining({
          headers: expect.any(Headers),
        })
      )
    })
  })

  describe('Retry Logic and Error Recovery', () => {
    it('should handle token refresh failures gracefully', async () => {
      const unauthorizedResponse = new Response('Unauthorized', { status: 401 })
      
      // Mock interceptor to simulate failed token refresh
      mockTokenRefreshInterceptor.interceptResponse.mockRejectedValueOnce(
        new ApiError('Token refresh failed', 401, 'UNAUTHORIZED')
      )

      mockFetch.mockResolvedValueOnce(unauthorizedResponse)

      try {
        await apiClient.getMembers('test-church')
        expect.fail('Should have thrown an error')
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).code).toBe('UNAUTHORIZED')
      }
    })

    it('should handle concurrent requests during token refresh', async () => {
      const unauthorizedResponse1 = new Response('Unauthorized', { status: 401 })
      const unauthorizedResponse2 = new Response('Unauthorized', { status: 401 })
      const successResponse1 = new Response(
        JSON.stringify({ data: { members: [] } }),
        { status: 200 }
      )
      const successResponse2 = new Response(
        JSON.stringify({ data: { events: [] } }),
        { status: 200 }
      )

      // Mock interceptor to handle both requests after refresh
      mockTokenRefreshInterceptor.interceptResponse
        .mockResolvedValueOnce(successResponse1)
        .mockResolvedValueOnce(successResponse2)

      mockFetch
        .mockResolvedValueOnce(unauthorizedResponse1)
        .mockResolvedValueOnce(unauthorizedResponse2)

      // Make concurrent requests
      const [result1, result2] = await Promise.all([
        apiClient.getMembers('test-church'),
        apiClient.getEvents('test-church'),
      ])

      expect(result1.data.members).toEqual([])
      expect(result2.data.events).toEqual([])
      expect(mockTokenRefreshInterceptor.interceptResponse).toHaveBeenCalledTimes(2)
    })
  })

  describe('API Error Class', () => {
    it('should create ApiError with all properties', () => {
      const error = new ApiError(
        'Test error message',
        404,
        'NOT_FOUND',
        { field: 'value' },
        '/test/endpoint',
        false
      )

      expect(error.message).toBe('Test error message')
      expect(error.status).toBe(404)
      expect(error.code).toBe('NOT_FOUND')
      expect(error.details).toEqual({ field: 'value' })
      expect(error.endpoint).toBe('/test/endpoint')
      expect(error.retryable).toBe(false)
      expect(error.name).toBe('ApiError')
    })

    it('should provide isType helper method', () => {
      const error = new ApiError('Test', 404, 'NOT_FOUND')

      expect(error.isType('NOT_FOUND')).toBe(true)
      expect(error.isType('NETWORK_ERROR')).toBe(false)
    })

    it('should serialize to JSON correctly', () => {
      const error = new ApiError(
        'Test error',
        500,
        'INTERNAL_SERVER_ERROR',
        { detail: 'test' },
        '/api/test',
        true
      )

      const json = error.toJSON()

      expect(json).toEqual({
        name: 'ApiError',
        message: 'Test error',
        status: 500,
        code: 'INTERNAL_SERVER_ERROR',
        details: { detail: 'test' },
        endpoint: '/api/test',
        retryable: true,
        stack: expect.any(String),
      })
    })
  })

  describe('Edge Cases and Error Scenarios', () => {
    it('should handle empty response bodies', async () => {
      const emptyResponse = new Response('{}', { status: 200 })

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(emptyResponse)
      mockFetch.mockResolvedValueOnce(emptyResponse)

      // For endpoints that might return empty responses
      const result = await apiClient.deleteMember('test-church', 'member-id')

      // Should handle empty response gracefully
      expect(result).toBeDefined()
    })

    it('should handle unexpected response formats', async () => {
      const textResponse = new Response('Plain text response', {
        status: 200,
        headers: { 'Content-Type': 'text/plain' },
      })

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(textResponse)
      mockFetch.mockResolvedValueOnce(textResponse)

      // Should attempt to parse as JSON and handle gracefully
      await expect(apiClient.getChurches()).rejects.toThrow()
    })

    it('should handle very large error responses', async () => {
      const largeErrorData = {
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        details: {
          errors: Array(1000).fill({ field: 'test', message: 'Invalid value' }),
        },
      }

      const errorResponse = new Response(
        JSON.stringify(largeErrorData),
        { status: 422 }
      )

      mockTokenRefreshInterceptor.interceptResponse.mockResolvedValueOnce(errorResponse)
      mockFetch.mockResolvedValueOnce(errorResponse)

      try {
        await apiClient.createMember('test-church', {
          firstName: '',
          lastName: '',
          email: 'invalid-email',
        })
      } catch (error) {
        expect(error).toBeInstanceOf(ApiError)
        expect((error as ApiError).details.errors).toHaveLength(1000)
      }
    })
  })
})