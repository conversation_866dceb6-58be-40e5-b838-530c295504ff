import { User, Church } from '@/types'
import { tokenRefreshInterceptor, RequestConfig } from './tokenRefreshInterceptor'

// API Base URL
export const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'

// Enhanced API Error class for better error handling and classification
export class ApiError extends Error {
  public readonly status: number
  public readonly code: string
  public readonly details: Record<string, unknown>
  public readonly endpoint: string
  public readonly retryable: boolean

  constructor(
    message: string,
    status: number,
    code: string,
    details: Record<string, unknown> = {},
    endpoint: string = '',
    retryable: boolean = false
  ) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.code = code
    this.details = details
    this.endpoint = endpoint
    this.retryable = retryable

    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, ApiError)
    }
  }

  // Helper method to check if error is of a specific type
  public isType(code: string): boolean {
    return this.code === code
  }

  // Helper method to get user-friendly error message
  public getUserMessage(): string {
    switch (this.code) {
      case 'NETWORK_ERROR':
        return 'Unable to connect to the server. Please check your internet connection and try again.'
      case 'UNAUTHORIZED':
        return 'Your session has expired. Please log in again.'
      case 'FORBIDDEN':
        return 'You do not have permission to perform this action.'
      case 'NOT_FOUND':
        return 'The requested resource was not found.'
      case 'VALIDATION_ERROR':
        return 'Please check your input and try again.'
      case 'RATE_LIMITED':
        return 'Too many requests. Please wait a moment and try again.'
      case 'INTERNAL_SERVER_ERROR':
        return 'A server error occurred. Please try again later.'
      case 'SERVICE_UNAVAILABLE':
        return 'The service is temporarily unavailable. Please try again later.'
      default:
        return this.message
    }
  }

  // Convert to JSON for logging/debugging
  public toJSON() {
    return {
      name: this.name,
      message: this.message,
      status: this.status,
      code: this.code,
      details: this.details,
      endpoint: this.endpoint,
      retryable: this.retryable,
      stack: this.stack,
    }
  }
}

// API Client with automatic token handling and refresh
class ApiClient {
  private baseURL: string

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL
  }

  public async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<{ data: T }> {
    const url = `${this.baseURL}${endpoint}`

    const config: RequestConfig = {
      url,
      options: {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      }
    }

    try {
      // Apply request interceptor to add authentication headers
      const interceptedConfig = await tokenRefreshInterceptor.interceptRequest(config)

      // Make the request
      let response = await fetch(interceptedConfig.url, interceptedConfig.options)

      // Apply response interceptor to handle token refresh
      response = await tokenRefreshInterceptor.interceptResponse(response, config)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({
          message: 'Network error',
          code: 'NETWORK_ERROR'
        }))

        // Handle specific error cases
        let errorMessage = errorData.message || `HTTP error! status: ${response.status}`
        let errorCode = errorData.code || this.getErrorCodeFromStatus(response.status)

        // Handle request entity too large error
        if (response.status === 413) {
          errorMessage = 'Request too large. Please try a smaller image or use an image URL instead.'
          errorCode = 'REQUEST_TOO_LARGE'
        }

        // Create enhanced error with proper classification
        const apiError = new ApiError(
          errorMessage,
          response.status,
          errorCode,
          errorData.details || {},
          endpoint,
          this.isRetryableError(response.status)
        )

        throw apiError
      }

      return response.json()
    } catch (error) {
      // Handle network errors and other exceptions
      if (error instanceof ApiError) {
        throw error
      }

      // Handle fetch errors (network issues, etc.)
      if (error instanceof TypeError && (error.message.includes('fetch') || error.message.includes('Failed to fetch'))) {
        throw new ApiError(
          'Network connection failed. Please check your internet connection.',
          0,
          'NETWORK_ERROR',
          { originalError: error.message },
          endpoint,
          true // Network errors are retryable
        )
      }

      // Handle other unexpected errors
      throw new ApiError(
        error instanceof Error ? error.message : 'An unexpected error occurred',
        0,
        'UNKNOWN_ERROR',
        { originalError: error },
        endpoint,
        false
      )
    }
  }

  private getErrorCodeFromStatus(status: number): string {
    switch (status) {
      case 400:
        return 'BAD_REQUEST'
      case 401:
        return 'UNAUTHORIZED'
      case 403:
        return 'FORBIDDEN'
      case 404:
        return 'NOT_FOUND'
      case 409:
        return 'CONFLICT'
      case 422:
        return 'VALIDATION_ERROR'
      case 429:
        return 'RATE_LIMITED'
      case 500:
        return 'INTERNAL_SERVER_ERROR'
      case 502:
        return 'BAD_GATEWAY'
      case 503:
        return 'SERVICE_UNAVAILABLE'
      case 504:
        return 'GATEWAY_TIMEOUT'
      default:
        return 'HTTP_ERROR'
    }
  }

  private isRetryableError(status: number): boolean {
    // Retryable errors: network issues, server errors, rate limiting
    return status === 0 || status === 429 || status >= 500
  }

  // Auth endpoints
  async login(data: { emailOrUserId: string; password: string }): Promise<{
    data: {
      user: User & {
        church: Church
        role: {
          id: string
          name: string
          permissions: string[]
        }
      }
      tokens: {
        accessToken: string
        refreshToken: string
        expiresIn: string
      }
    }
  }> {
    return this.request('/auth/login', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async register(data: {
    email: string
    password: string
    firstName: string
    lastName: string
    churchId?: string
    churchSlug?: string
  }): Promise<{ data: User }> {
    return this.request('/auth/register', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getProfile(): Promise<{
    data: {
      user: User
      church?: Church
    }
  }> {
    return this.request('/auth/profile')
  }

  async refreshToken() {
    return this.request('/auth/refresh', { method: 'POST' })
  }

  async forgotPassword(email: string) {
    return this.request('/auth/forgot-password', {
      method: 'POST',
      body: JSON.stringify({ email }),
    })
  }

  async resetPassword(token: string, newPassword: string) {
    return this.request('/auth/reset-password', {
      method: 'POST',
      body: JSON.stringify({ token, newPassword }),
    })
  }

  async resendVerificationEmail() {
    return this.request('/auth/resend-verification', {
      method: 'POST',
    })
  }

  // Church endpoints
  async getChurches(): Promise<{ data: { churches: Church[] } }> {
    return this.request('/churches')
  }

  async getChurchBySlug(slug: string): Promise<{
    data: {
      church: {
        id: string
        name: string
        slug: string
        churchCode: string
        description?: string
        address?: string
        phone?: string
        email?: string
        website?: string
        logo?: string
        createdAt: string
        updatedAt: string
      }
    }
  }> {
    return this.request(`/churches/slug/${slug}`)
  }

  async registerChurch(data: {
    name: string
    slug: string
    email: string
    phone?: string
    address?: string
    website?: string
    description?: string
    adminUser: {
      firstName: string
      lastName: string
      email: string
      password: string
    }
  }): Promise<{
    data: {
      churchUrl: string
      adminUrl: string
    }
  }> {
    // Transform frontend data structure to match backend API
    const requestData = {
      church: {
        name: data.name,
        slug: data.slug,
        // Only include optional fields if they have actual values (not empty strings)
        ...(data.email && { email: data.email }),
        ...(data.phone && { phone: data.phone }),
        ...(data.address && { address: data.address }),
        ...(data.website && { website: data.website }),
        ...(data.description && { description: data.description }),
      },
      admin: {
        firstName: data.adminUser.firstName,
        lastName: data.adminUser.lastName,
        email: data.adminUser.email,
        password: data.adminUser.password,
      }
    }

    return this.request('/churches/register', {
      method: 'POST',
      body: JSON.stringify(requestData),
    })
  }

  // Church-scoped endpoints
  async getChurchSettings(slug: string) {
    return this.request(`/churches/${slug}/settings`)
  }

  async updateChurch(slug: string, data: {
    name?: string
    description?: string
    address?: string
    phone?: string
    email?: string
    website?: string
  }) {
    return this.request(`/churches/${slug}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async updateChurchSettings(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/settings`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async updateChurchLogo(slug: string, logo: string) {
    return this.request(`/churches/${slug}/logo`, {
      method: 'PUT',
      body: JSON.stringify({ logo }),
    })
  }

  // Members
  async getMembers(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/members${query}`)
  }

  async createMember(slug: string, data: {
    firstName: string
    lastName: string
    email: string
    phone?: string
    dateOfBirth?: string
    gender?: 'male' | 'female' | 'other'
    address?: string
    branchId?: string
    roleId?: string
    status?: 'active' | 'inactive' | 'suspended'
  }) {
    return this.request(`/churches/${slug}/members`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateMember(slug: string, memberId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/members/${memberId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async getMember(slug: string, memberId: string) {
    return this.request(`/churches/${slug}/members/${memberId}`)
  }

  async deleteMember(slug: string, memberId: string) {
    return this.request(`/churches/${slug}/members/${memberId}`, {
      method: 'DELETE',
    })
  }

  async assignMemberRole(slug: string, memberId: string, roleId: string) {
    return this.request(`/churches/${slug}/members/${memberId}/role`, {
      method: 'POST',
      body: JSON.stringify({ roleId }),
    })
  }

  async removeMemberRole(slug: string, memberId: string) {
    return this.request(`/churches/${slug}/members/${memberId}/role`, {
      method: 'DELETE',
    })
  }

  async assignMemberBranch(slug: string, memberId: string, branchId: string) {
    return this.request(`/churches/${slug}/members/${memberId}/branch`, {
      method: 'POST',
      body: JSON.stringify({ branchId }),
    })
  }

  async removeMemberBranch(slug: string, memberId: string) {
    return this.request(`/churches/${slug}/members/${memberId}/branch`, {
      method: 'DELETE',
    })
  }

  // Roles
  async getRoles(slug: string) {
    return this.request(`/churches/${slug}/roles`)
  }

  async createRole(slug: string, data: { name: string; description?: string; permissions?: string[] }) {
    return this.request(`/churches/${slug}/roles`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getRole(slug: string, roleId: string) {
    return this.request(`/churches/${slug}/roles/${roleId}`)
  }

  async updateRole(slug: string, roleId: string, data: { name?: string; description?: string; permissions?: string[] }) {
    return this.request(`/churches/${slug}/roles/${roleId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteRole(slug: string, roleId: string) {
    return this.request(`/churches/${slug}/roles/${roleId}`, {
      method: 'DELETE',
    })
  }

  // Events
  async getEvents(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/events${query}`)
  }

  async createEvent(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/events`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getEvent(slug: string, eventId: string) {
    return this.request(`/churches/${slug}/events/${eventId}`)
  }

  async updateEvent(slug: string, eventId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/events/${eventId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteEvent(slug: string, eventId: string) {
    return this.request(`/churches/${slug}/events/${eventId}`, {
      method: 'DELETE',
    })
  }

  // Event RSVP
  async createEventRsvp(slug: string, eventId: string, data: {
    status: 'pending' | 'attending' | 'not_attending' | 'maybe'
    attendeeCount?: number
    notes?: string
  }) {
    return this.request(`/churches/${slug}/events/${eventId}/rsvp`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateEventRsvp(slug: string, eventId: string, data: {
    status?: 'pending' | 'attending' | 'not_attending' | 'maybe'
    attendeeCount?: number
    notes?: string
  }) {
    return this.request(`/churches/${slug}/events/${eventId}/rsvp`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteEventRsvp(slug: string, eventId: string) {
    return this.request(`/churches/${slug}/events/${eventId}/rsvp`, {
      method: 'DELETE',
    })
  }

  // Event Likes
  async toggleEventLike(slug: string, eventId: string) {
    return this.request(`/churches/${slug}/events/${eventId}/like`, {
      method: 'POST',
    })
  }

  async getEventLikes(slug: string, eventId: string) {
    return this.request(`/churches/${slug}/events/${eventId}/likes`)
  }

  // Event Comments
  async createEventComment(slug: string, eventId: string, data: {
    content: string
    parentCommentId?: string
  }) {
    return this.request(`/churches/${slug}/events/${eventId}/comments`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getEventComments(slug: string, eventId: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/events/${eventId}/comments${query}`)
  }

  async updateEventComment(slug: string, eventId: string, commentId: string, data: {
    content: string
  }) {
    return this.request(`/churches/${slug}/events/${eventId}/comments/${commentId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteEventComment(slug: string, eventId: string, commentId: string) {
    return this.request(`/churches/${slug}/events/${eventId}/comments/${commentId}`, {
      method: 'DELETE',
    })
  }

  // Finances
  async getFinances(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/finances${query}`)
  }

  async createFinance(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/finances`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getFinance(slug: string, financeId: string) {
    return this.request(`/churches/${slug}/finances/${financeId}`)
  }

  async updateFinance(slug: string, financeId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/finances/${financeId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteFinance(slug: string, financeId: string) {
    return this.request(`/churches/${slug}/finances/${financeId}`, {
      method: 'DELETE',
    })
  }

  // Payment Methods
  async getPaymentMethods(slug: string) {
    return this.request(`/churches/${slug}/payment-methods`)
  }

  async createPaymentMethod(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/payment-methods`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updatePaymentMethod(slug: string, methodId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/payment-methods/${methodId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deletePaymentMethod(slug: string, methodId: string) {
    return this.request(`/churches/${slug}/payment-methods/${methodId}`, {
      method: 'DELETE',
    })
  }

  async setDefaultPaymentMethod(slug: string, methodId: string) {
    return this.request(`/churches/${slug}/payment-methods/${methodId}/set-default`, {
      method: 'POST',
    })
  }

  // Finance Categories
  async getFinanceCategories(slug: string) {
    return this.request(`/churches/${slug}/finances/categories`)
  }

  async createFinanceCategory(slug: string, data: { name: string; description?: string }) {
    return this.request(`/churches/${slug}/finances/categories`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async updateFinanceCategory(slug: string, categoryId: string, data: { name?: string; description?: string }) {
    return this.request(`/churches/${slug}/finances/categories/${categoryId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteFinanceCategory(slug: string, categoryId: string) {
    return this.request(`/churches/${slug}/finances/categories/${categoryId}`, {
      method: 'DELETE',
    })
  }



  // Financial Settings
  async getFinancialSettings(slug: string) {
    return this.request(`/churches/${slug}/financial-settings`)
  }

  async updateFinancialSettings(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/financial-settings`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  // Currencies
  async getCurrencies() {
    return this.request('/currencies')
  }

  // Branches
  async getBranches(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/branches${query}`)
  }

  async createBranch(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/branches`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getBranch(slug: string, branchId: string) {
    return this.request(`/churches/${slug}/branches/${branchId}`)
  }

  async updateBranch(slug: string, branchId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/branches/${branchId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteBranch(slug: string, branchId: string) {
    return this.request(`/churches/${slug}/branches/${branchId}`, {
      method: 'DELETE',
    })
  }

  // Announcements
  async getAnnouncements(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    const endpoint = `/churches/${slug}/announcements${query}`
    return this.request(endpoint)
  }

  async createAnnouncement(slug: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/announcements`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getAnnouncement(slug: string, announcementId: string) {
    return this.request(`/churches/${slug}/announcements/${announcementId}`)
  }

  async updateAnnouncement(slug: string, announcementId: string, data: Record<string, unknown>) {
    return this.request(`/churches/${slug}/announcements/${announcementId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteAnnouncement(slug: string, announcementId: string) {
    return this.request(`/churches/${slug}/announcements/${announcementId}`, {
      method: 'DELETE',
    })
  }

  async markAnnouncementAsViewed(slug: string, announcementId: string, data?: { acknowledged?: boolean }) {
    return this.request(`/churches/${slug}/announcements/${announcementId}/view`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    })
  }

  async createAnnouncementComment(slug: string, announcementId: string, data: { content: string; parentCommentId?: string }) {
    return this.request(`/churches/${slug}/announcements/${announcementId}/comments`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getAnnouncementComments(slug: string, announcementId: string) {
    return this.request(`/churches/${slug}/announcements/${announcementId}/comments`)
  }

  async updateAnnouncementComment(slug: string, commentId: string, data: { content: string }) {
    return this.request(`/churches/${slug}/announcements/comments/${commentId}`, {
      method: 'PUT',
      body: JSON.stringify(data),
    })
  }

  async deleteAnnouncementComment(slug: string, commentId: string) {
    return this.request(`/churches/${slug}/announcements/comments/${commentId}`, {
      method: 'DELETE',
    })
  }

  async createAnnouncementReaction(slug: string, announcementId: string, data: { reactionType: string }) {
    return this.request(`/churches/${slug}/announcements/${announcementId}/reactions`, {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async deleteAnnouncementReaction(slug: string, announcementId: string) {
    return this.request(`/churches/${slug}/announcements/${announcementId}/reactions`, {
      method: 'DELETE',
    })
  }
  async getDashboardOverview(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/analytics/overview${query}`)
  }

  // Notifications
  async getNotifications(slug: string, params?: { page?: number; limit?: number; unreadOnly?: boolean }): Promise<{ data: { notifications: import('@/types').Notification[]; pagination: { page: number; limit: number; total: number; totalPages: number } } }> {
    const query = params ? '?' + new URLSearchParams(Object.entries(params).reduce((acc, [k, v]) => {
      if (v !== undefined && v !== null) acc[k] = String(v)
      return acc
    }, {} as Record<string, string>)).toString() : ''
    return this.request(`/churches/${slug}/notifications${query}`)
  }

  async getNotificationsUnreadCount(slug: string): Promise<{ data: { count: number } }> {
    return this.request(`/churches/${slug}/notifications/unread-count`)
  }

  async markNotificationsRead(slug: string, data?: { notificationIds?: string[] }): Promise<{ status: string; message: string }> {
    return this.request(`/churches/${slug}/notifications/mark-read`, {
      method: 'POST',
      body: JSON.stringify(data || {}),
    }) as unknown as Promise<{ status: string; message: string }>
  }

  async getMemberAnalytics(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/analytics/members${query}`)
  }

  async getEventAnalytics(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/analytics/events${query}`)
  }

  async getFinancialAnalytics(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/analytics/financial${query}`)
  }

  async getBranchAnalytics(slug: string, params?: Record<string, string | number>) {
    const query = params ? '?' + new URLSearchParams(params as Record<string, string>).toString() : ''
    return this.request(`/churches/${slug}/analytics/branches${query}`)
  }

  // Onboarding endpoints
  async checkAvailability(data: { churchSlug: string; adminEmail: string }) {
    return this.request('/onboarding/check-availability', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async initiateOnboarding(data: Record<string, unknown>) {
    return this.request('/onboarding/initiate', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async completeOnboarding(data: Record<string, unknown>) {
    return this.request('/onboarding/complete', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async verifyOnboarding(data: { verificationToken: string }) {
    return this.request('/onboarding/verify', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async resendVerification(data: { email: string; churchSlug: string }) {
    return this.request('/onboarding/resend-verification', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async setupInitialData(data: Record<string, unknown>) {
    return this.request('/onboarding/setup-initial-data', {
      method: 'POST',
      body: JSON.stringify(data),
    })
  }

  async getOnboardingProgress(token: string) {
    return this.request(`/onboarding/progress/${token}`)
  }
}

export const apiClient = new ApiClient()
export const api = apiClient