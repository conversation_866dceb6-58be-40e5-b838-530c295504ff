/**
 * Error recovery mechanisms for authentication and API errors
 * Implements requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { AuthError, ErrorUtils, ErrorClassifier } from './errors'

export interface RetryOptions {
  maxAttempts?: number
  baseDelay?: number
  maxDelay?: number
  backoffFactor?: number
  onRetry?: (attempt: number, error: AuthError) => void
  onMaxAttemptsReached?: (error: AuthError) => void
}

export interface RecoveryContext {
  operation: string
  userId?: string
  churchId?: string
  endpoint?: string
  metadata?: Record<string, unknown>
}

/**
 * Error recovery manager that handles automatic retry logic and recovery strategies
 */
export class ErrorRecoveryManager {
  private retryAttempts = new Map<string, number>()
  private recoveryStrategies = new Map<string, RecoveryStrategy>()

  constructor() {
    this.initializeDefaultStrategies()
  }

  /**
   * Attempt to recover from an error with automatic retry logic
   */
  async attemptRecovery<T>(
    operation: () => Promise<T>,
    context: RecoveryContext,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxAttempts = 3,
      baseDelay = 1000,
      maxDelay = 30000,
      backoffFactor = 2,
      onRetry,
      onMaxAttemptsReached
    } = options

    const operationKey = this.getOperationKey(context)
    let attempt = this.retryAttempts.get(operationKey) || 0

    while (attempt < maxAttempts) {
      try {
        const result = await operation()
        // Success - reset retry count
        this.retryAttempts.delete(operationKey)
        return result
      } catch (error) {
        attempt++
        this.retryAttempts.set(operationKey, attempt)

        const authError = ErrorClassifier.classifyError(error, context.operation)

        // Check if error is retryable
        if (!ErrorUtils.isRetryable(authError) || attempt >= maxAttempts) {
          this.retryAttempts.delete(operationKey)

          if (attempt >= maxAttempts) {
            onMaxAttemptsReached?.(authError)
          }

          throw authError
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          baseDelay * Math.pow(backoffFactor, attempt - 1),
          maxDelay
        )

        onRetry?.(attempt, authError)

        // Wait before retry
        await this.delay(delay)
      }
    }

    throw new Error('Max attempts reached') // Should never reach here
  }

  /**
   * Get recovery strategy for a specific error
   */
  getRecoveryStrategy(error: AuthError): RecoveryStrategy | null {
    return this.recoveryStrategies.get(error.code) || null
  }

  /**
   * Register a custom recovery strategy
   */
  registerRecoveryStrategy(errorCode: string, strategy: RecoveryStrategy): void {
    this.recoveryStrategies.set(errorCode, strategy)
  }

  /**
   * Clear retry attempts for an operation
   */
  clearRetryAttempts(context: RecoveryContext): void {
    const operationKey = this.getOperationKey(context)
    this.retryAttempts.delete(operationKey)
  }

  /**
   * Get current retry attempt count for an operation
   */
  getRetryAttempts(context: RecoveryContext): number {
    const operationKey = this.getOperationKey(context)
    return this.retryAttempts.get(operationKey) || 0
  }

  private getOperationKey(context: RecoveryContext): string {
    return `${context.operation}:${context.userId || 'anonymous'}:${context.endpoint || 'unknown'}`
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private initializeDefaultStrategies(): void {
    // Network error recovery
    this.recoveryStrategies.set('NETWORK_ERROR', {
      canRecover: true,
      autoRetry: true,
      maxRetries: 3,
      retryDelay: 2000,
      userActions: [
        'Check your internet connection',
        'Try refreshing the page',
        'Wait a moment and try again'
      ],
      recover: async (error, context) => {
        // Could implement network connectivity check here
        return { success: false, message: 'Please check your connection and try again' }
      }
    })

    // Token expired recovery
    this.recoveryStrategies.set('TOKEN_EXPIRED', {
      canRecover: true,
      autoRetry: false,
      maxRetries: 1,
      userActions: ['Log in again to continue'],
      recover: async (error, context) => {
        // Redirect to login would be handled by auth context
        return { success: false, message: 'Please log in again', requiresLogin: true }
      }
    })

    // Invalid credentials recovery
    this.recoveryStrategies.set('INVALID_CREDENTIALS', {
      canRecover: true,
      autoRetry: false,
      maxRetries: 0,
      userActions: [
        'Double-check your email or user ID',
        'Verify your password is correct',
        'Try resetting your password if needed'
      ],
      recover: async (error, context) => {
        return { success: false, message: 'Please check your credentials and try again' }
      }
    })

    // Church not found recovery
    this.recoveryStrategies.set('CHURCH_NOT_FOUND', {
      canRecover: true,
      autoRetry: false,
      maxRetries: 0,
      userActions: [
        'Verify the church code is correct',
        'Check the church URL for typos',
        'Contact the church administrator'
      ],
      recover: async (error, context) => {
        return { success: false, message: 'Please verify the church information' }
      }
    })

    // Rate limited recovery
    this.recoveryStrategies.set('RATE_LIMITED', {
      canRecover: true,
      autoRetry: true,
      maxRetries: 2,
      retryDelay: 60000, // 1 minute
      userActions: ['Wait a few minutes before trying again'],
      recover: async (error, context) => {
        return { success: false, message: 'Please wait before trying again' }
      }
    })

    // Server error recovery
    this.recoveryStrategies.set('SERVER_ERROR', {
      canRecover: true,
      autoRetry: true,
      maxRetries: 2,
      retryDelay: 5000,
      userActions: [
        'Wait a few minutes and try again',
        'Refresh the page'
      ],
      recover: async (error, context) => {
        return { success: false, message: 'Server temporarily unavailable' }
      }
    })

    // Account inactive recovery
    this.recoveryStrategies.set('ACCOUNT_INACTIVE', {
      canRecover: false,
      autoRetry: false,
      maxRetries: 0,
      userActions: ['Contact your church administrator to activate your account'],
      recover: async (error, context) => {
        return { success: false, message: 'Account activation required', requiresSupport: true }
      }
    })

    // Email not verified recovery
    this.recoveryStrategies.set('EMAIL_NOT_VERIFIED', {
      canRecover: true,
      autoRetry: false,
      maxRetries: 0,
      userActions: [
        'Check your email for a verification link',
        'Request a new verification email if needed'
      ],
      recover: async (error, context) => {
        return { success: false, message: 'Email verification required', requiresVerification: true }
      }
    })
  }
}

export interface RecoveryStrategy {
  canRecover: boolean
  autoRetry: boolean
  maxRetries: number
  retryDelay?: number
  userActions: string[]
  recover: (error: AuthError, context: RecoveryContext) => Promise<RecoveryResult>
}

export interface RecoveryResult {
  success: boolean
  message: string
  requiresLogin?: boolean
  requiresSupport?: boolean
  requiresVerification?: boolean
  data?: unknown
}

/**
 * Specialized recovery manager for authentication operations
 */
export class AuthRecoveryManager extends ErrorRecoveryManager {
  constructor() {
    super()
    this.initializeAuthStrategies()
  }

  /**
   * Attempt to recover from login errors
   */
  async recoverFromLoginError(
    loginFn: () => Promise<void>,
    emailOrUserId: string
  ): Promise<void> {
    const context: RecoveryContext = {
      operation: 'login',
      endpoint: '/auth/login',
      metadata: { emailOrUserId }
    }

    return this.attemptRecovery(loginFn, context, {
      maxAttempts: 2, // Limited retries for login
      baseDelay: 1000,
      onRetry: (attempt, error) => {
        console.log(`Login retry attempt ${attempt} for error: ${error.code}`)
      },
      onMaxAttemptsReached: (error) => {
        console.error('Max login attempts reached:', error)
      }
    })
  }

  /**
   * Attempt to recover from token refresh errors
   */
  async recoverFromTokenRefresh(
    refreshFn: () => Promise<void>
  ): Promise<void> {
    const context: RecoveryContext = {
      operation: 'token_refresh',
      endpoint: '/auth/refresh'
    }

    return this.attemptRecovery(refreshFn, context, {
      maxAttempts: 2,
      baseDelay: 500,
      onMaxAttemptsReached: (error) => {
        // Token refresh failed - user needs to log in again
        console.error('Token refresh failed, logout required:', error)
      }
    })
  }

  private initializeAuthStrategies(): void {
    // Override login-specific strategies
    this.registerRecoveryStrategy('INVALID_CREDENTIALS', {
      canRecover: true,
      autoRetry: false,
      maxRetries: 0,
      userActions: [
        'Double-check your email or user ID',
        'Verify your password is correct',
        'Try resetting your password if needed'
      ],
      recover: async (error, context) => {
        // Could implement credential validation hints here
        return {
          success: false,
          message: 'Please verify your login credentials',
          data: {
            emailOrUserId: context.metadata?.emailOrUserId
          }
        }
      }
    })
  }
}

// Global instances
export const errorRecoveryManager = new ErrorRecoveryManager()
export const authRecoveryManager = new AuthRecoveryManager()