interface Currency {
  code: string;
  name: string;
  symbol: string;
  isActive: boolean;
}

const currencies: Currency[] = [
  { code: 'USD', name: 'US Dollar', symbol: '$', isActive: true },
  { code: 'ZM<PERSON>', name: 'Zambian <PERSON>', symbol: 'ZK', isActive: true },
  { code: 'EUR', name: 'Euro', symbol: '€', isActive: true },
  { code: 'GBP', name: 'British Pound', symbol: '£', isActive: true },
  { code: 'ZAR', name: 'South African Rand', symbol: 'R', isActive: true },
  { code: 'KES', name: 'Kenyan Shilling', symbol: 'KSh', isActive: true },
  { code: 'UGX', name: 'Ugandan <PERSON>', symbol: 'USh', isActive: true },
  { code: 'TZS', name: 'Tanzanian <PERSON>', symbol: 'TSh', isActive: true },
];

export function getCurrencySymbol(currencyCode: string): string {
  const currency = currencies.find(c => c.code === currencyCode);
  return currency?.symbol || currencyCode;
}

export function formatCurrency(amount: number, currencyCode: string = 'ZMW'): string {
  const currency = currencies.find(c => c.code === currencyCode);
  
  if (!currency) {
    // Fallback to basic formatting if currency not found
    return `${currencyCode} ${amount.toLocaleString()}`;
  }

  // For most currencies, use Intl.NumberFormat
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
    }).format(amount);
  } catch (error) {
    // Fallback for currencies not supported by Intl.NumberFormat
    return `${currency.symbol}${amount.toLocaleString()}`;
  }
}

export function formatAmount(amount: number, currencyCode: string = 'ZMW'): string {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol}${amount.toLocaleString()}`;
}

// Split a currency into symbol and numeric parts for custom styling
export function formatCurrencyParts(amount: number, currencyCode: string = 'ZMW'): { symbol: string; number: string } {
  try {
    const parts = new Intl.NumberFormat('en-US', { style: 'currency', currency: currencyCode }).formatToParts(amount);
    const symbol = parts.find(p => p.type === 'currency')?.value ?? getCurrencySymbol(currencyCode);
    const number = parts.filter(p => p.type !== 'currency').map(p => p.value).join('');
    return { symbol, number };
  } catch {
    // Fallback if Intl throws for unknown currency
    return { symbol: getCurrencySymbol(currencyCode), number: amount.toLocaleString() };
  }
}

export { currencies };