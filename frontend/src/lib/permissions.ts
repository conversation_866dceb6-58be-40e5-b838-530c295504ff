import { User } from '@/types'

export interface Permission {
  name: string
  description: string
}

export const PERMISSIONS = {
  // Member Management
  VIEW_MEMBERS: { name: 'view_members', description: 'View church members' },
  MANAGE_MEMBERS: { name: 'manage_members', description: 'Create, edit, and manage church members' },
  
  // Event Management
  VIEW_EVENTS: { name: 'view_events', description: 'View church events' },
  MANAGE_EVENTS: { name: 'manage_events', description: 'Create and manage church events' },
  
  // Financial Management
  VIEW_DONATIONS: { name: 'view_donations', description: 'View donation records' },
  MANAGE_DONATIONS: { name: 'manage_donations', description: 'Record and manage donations' },
  VIEW_FINANCIAL_REPORTS: { name: 'view_financial_reports', description: 'Access financial reports' },
  
  // Branch Management
  VIEW_BRANCHES: { name: 'view_branches', description: 'View church branches' },
  MANAGE_BRANCHES: { name: 'manage_branches', description: 'Create and manage church branches' },
  
  // Church Administration
  MANAGE_CHURCH_SETTINGS: { name: 'manage_church_settings', description: 'Modify church settings' },
  VIEW_ROLES: { name: 'view_roles', description: 'View user roles and permissions' },
  MANAGE_ROLES: { name: 'manage_roles', description: 'Manage user roles and permissions' },
  VIEW_ANALYTICS: { name: 'view_analytics', description: 'Access church analytics' },
  
  // Communication
  MANAGE_ANNOUNCEMENTS: { name: 'manage_announcements', description: 'Create and manage announcements' },
  
  // Onboarding
  MANAGE_ONBOARDING: { name: 'manage_onboarding', description: 'Manage member onboarding process' }
} as const

// Role-based permission mapping
export const ROLE_PERMISSIONS = {
  'Super Admin': [
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.MANAGE_EVENTS,
    PERMISSIONS.VIEW_DONATIONS,
    PERMISSIONS.MANAGE_DONATIONS,
    PERMISSIONS.VIEW_FINANCIAL_REPORTS,
    PERMISSIONS.VIEW_BRANCHES,
    PERMISSIONS.MANAGE_BRANCHES,
    PERMISSIONS.MANAGE_CHURCH_SETTINGS,
    PERMISSIONS.VIEW_ROLES,
    PERMISSIONS.MANAGE_ROLES,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.MANAGE_ANNOUNCEMENTS,
    PERMISSIONS.MANAGE_ONBOARDING
  ],
  'Pastor': [
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.MANAGE_EVENTS,
    PERMISSIONS.VIEW_DONATIONS,
    PERMISSIONS.MANAGE_DONATIONS,
    PERMISSIONS.VIEW_FINANCIAL_REPORTS,
    PERMISSIONS.VIEW_BRANCHES,
    PERMISSIONS.MANAGE_BRANCHES,
    PERMISSIONS.MANAGE_CHURCH_SETTINGS,
    PERMISSIONS.VIEW_ROLES,
    PERMISSIONS.MANAGE_ROLES,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.MANAGE_ANNOUNCEMENTS,
    PERMISSIONS.MANAGE_ONBOARDING
  ],
  'Elder': [
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.MANAGE_EVENTS,
    PERMISSIONS.VIEW_DONATIONS,
    PERMISSIONS.MANAGE_DONATIONS,
    PERMISSIONS.VIEW_FINANCIAL_REPORTS,
    PERMISSIONS.VIEW_BRANCHES,
    PERMISSIONS.MANAGE_BRANCHES,
    PERMISSIONS.VIEW_ROLES,
    PERMISSIONS.VIEW_ANALYTICS,
    PERMISSIONS.MANAGE_ANNOUNCEMENTS
  ],
  'Deacon': [
    PERMISSIONS.VIEW_MEMBERS,
    PERMISSIONS.MANAGE_MEMBERS,
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.MANAGE_EVENTS,
    PERMISSIONS.VIEW_DONATIONS,
    PERMISSIONS.MANAGE_DONATIONS,
    PERMISSIONS.VIEW_BRANCHES,
    PERMISSIONS.MANAGE_ANNOUNCEMENTS
  ],
  'Member': [
    PERMISSIONS.VIEW_EVENTS,
    PERMISSIONS.VIEW_BRANCHES
  ],
  'Visitor': [
    PERMISSIONS.VIEW_EVENTS
  ]
} as const

export function hasPermission(user: User | null, permission: Permission): boolean {
  if (!user?.role?.name) return false
  
  const roleName = user.role.name as keyof typeof ROLE_PERMISSIONS
  const rolePermissions = ROLE_PERMISSIONS[roleName] || []
  
  return rolePermissions.some(p => p.name === permission.name)
}

export function hasAnyPermission(user: User | null, permissions: Permission[]): boolean {
  return permissions.some(permission => hasPermission(user, permission))
}

export function hasAllPermissions(user: User | null, permissions: Permission[]): boolean {
  return permissions.every(permission => hasPermission(user, permission))
}

export function getUserPermissions(user: User | null): Permission[] {
  if (!user?.role?.name) return []
  
  const roleName = user.role.name as keyof typeof ROLE_PERMISSIONS
  return [...(ROLE_PERMISSIONS[roleName] || [])]
}

export function canAccessPage(user: User | null, requiredPermissions: Permission[]): boolean {
  return hasAnyPermission(user, requiredPermissions)
}