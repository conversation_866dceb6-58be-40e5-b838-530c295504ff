/**
 * Token Refresh Interceptor
 * 
 * Provides automatic token refresh functionality for API requests:
 * - Intercepts 401 responses and attempts token refresh
 * - Queues concurrent requests during refresh process
 * - <PERSON>les refresh failures with proper cleanup
 * - Integrates with token storage service
 */

import { tokenStorage, TokenPair } from './tokenStorage'

export interface RequestConfig {
  url: string
  options: RequestInit
}

export interface QueuedRequest {
  config: RequestConfig
  resolve: (response: Response) => void
  reject: (error: Error) => void
}

export class TokenRefreshInterceptor {
  private isRefreshing = false
  private requestQueue: QueuedRequest[] = []
  private refreshPromise: Promise<TokenPair> | null = null

  /**
   * Intercept outgoing requests to add authentication headers
   */
  public interceptRequest = async (config: RequestConfig): Promise<RequestConfig> => {
    const headers = new Headers(config.options.headers)
    const accessToken = tokenStorage.getAccessToken()
    
    if (accessToken) {
      headers.set('Authorization', `Bearer ${accessToken}`)
    }
    
    return {
      ...config,
      options: {
        ...config.options,
        headers
      }
    }
  }

  /**
   * Intercept responses to handle token expiration
   */
  public interceptResponse = async (
    response: Response,
    originalConfig: RequestConfig
  ): Promise<Response> => {
    // If response is successful or not a 401, return as-is
    if (response.ok || response.status !== 401) {
      return response
    }

    // Don't attempt refresh for auth endpoints to avoid infinite loops
    if (this.isAuthEndpoint(originalConfig.url)) {
      return response
    }

    // If we're already refreshing, queue this request
    if (this.isRefreshing) {
      return this.queueRequest(originalConfig)
    }

    // Attempt token refresh
    try {
      const newTokens = await this.handleTokenRefresh()
      
      // Update the original request with new token
      const updatedConfig = await this.interceptRequest(originalConfig)
      
      // Retry the original request
      const retryResponse = await fetch(updatedConfig.url, updatedConfig.options)
      
      // Process queued requests with new tokens
      this.processQueue(null)
      
      return retryResponse
    } catch (error) {
      // Refresh failed, process queue with error and clear tokens
      this.processQueue(error as Error)
      tokenStorage.clearTokens()
      
      // Redirect to login if we're in a browser environment
      if (typeof window !== 'undefined') {
        // Get current path to redirect back after login
        const currentPath = window.location.pathname
        const pathSegments = currentPath.split('/').filter(Boolean)
        
        // Check if we're in a church-specific path (format: /church-slug/...)
        // Church paths typically don't contain common route patterns like 'general-page'
        const isChurchPath = pathSegments.length >= 1 && 
          !pathSegments[0].startsWith('(') && 
          !['general-page', 'login', 'register', 'forgot-password', 'reset-password'].includes(pathSegments[0])
        
        const loginPath = isChurchPath 
          ? `/${pathSegments[0]}/login`
          : '/login'
        
        window.location.href = loginPath
      }
      
      return response
    }
  }

  /**
   * Handle token refresh process
   */
  public handleTokenRefresh = async (): Promise<TokenPair> => {
    // If already refreshing, return the existing promise
    if (this.refreshPromise) {
      return this.refreshPromise
    }

    this.isRefreshing = true
    
    this.refreshPromise = this.performTokenRefresh()
    
    try {
      const tokens = await this.refreshPromise
      return tokens
    } finally {
      this.isRefreshing = false
      this.refreshPromise = null
    }
  }

  /**
   * Perform the actual token refresh API call
   */
  private performTokenRefresh = async (): Promise<TokenPair> => {
    const refreshToken = tokenStorage.getRefreshToken()
    
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    const response = await fetch(`${this.getApiBaseUrl()}/auth/refresh`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken }),
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ message: 'Token refresh failed' }))
      throw new Error(error.message || 'Token refresh failed')
    }

    const data = await response.json()
    const { accessToken, refreshToken: newRefreshToken } = data.data.tokens

    // Store new tokens
    tokenStorage.setTokens(accessToken, newRefreshToken)

    return tokenStorage.getTokens()!
  }

  /**
   * Queue a request during token refresh
   */
  private queueRequest = (config: RequestConfig): Promise<Response> => {
    return new Promise((resolve, reject) => {
      this.requestQueue.push({
        config,
        resolve,
        reject,
      })
    })
  }

  /**
   * Process queued requests after token refresh
   */
  private processQueue = (error: Error | null): void => {
    const queue = [...this.requestQueue]
    this.requestQueue = []

    queue.forEach(({ config, resolve, reject }) => {
      if (error) {
        reject(error)
      } else {
        // Retry the request with new tokens
        this.interceptRequest(config)
          .then(updatedConfig => fetch(updatedConfig.url, updatedConfig.options))
          .then(resolve)
          .catch(reject)
      }
    })
  }

  /**
   * Check if URL is an auth endpoint to avoid infinite refresh loops
   */
  private isAuthEndpoint = (url: string): boolean => {
    const authEndpoints = ['/auth/login', '/auth/refresh', '/auth/logout', '/auth/register']
    return authEndpoints.some(endpoint => url.includes(endpoint))
  }

  /**
   * Get API base URL
   */
  private getApiBaseUrl = (): string => {
    return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'
  }

  /**
   * Clear any pending refresh state (useful for testing)
   */
  public clearRefreshState = (): void => {
    this.isRefreshing = false
    this.refreshPromise = null
    this.requestQueue = []
  }

  /**
   * Get current refresh state (useful for testing)
   */
  public getRefreshState = () => {
    return {
      isRefreshing: this.isRefreshing,
      queueLength: this.requestQueue.length,
      hasRefreshPromise: !!this.refreshPromise,
    }
  }
}

// Export singleton instance
export const tokenRefreshInterceptor = new TokenRefreshInterceptor()