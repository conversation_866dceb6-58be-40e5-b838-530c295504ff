/**
 * Image compression and processing utilities
 */

export interface CompressOptions {
  maxWidth?: number
  maxHeight?: number
  quality?: number
  maxSizeKB?: number
}

/**
 * Compress an image file to reduce its size
 */
export function compressImage(
  file: File,
  options: CompressOptions = {}
): Promise<string> {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = 800,  // Reduced from 1200
      maxHeight = 600, // Reduced from 800
      quality = 0.6,   // Reduced from 0.8
      maxSizeKB = 200  // Reduced from 500KB to 200KB
    } = options

    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')
    const img = new Image()

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        let { width, height } = img
        
        // Always resize to fit within max dimensions
        const aspectRatio = width / height
        
        if (width > height) {
          width = Math.min(width, maxWidth)
          height = width / aspectRatio
        } else {
          height = Math.min(height, maxHeight)
          width = height * aspectRatio
        }

        // Set canvas dimensions
        canvas.width = width
        canvas.height = height

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height)
        
        // Start with lower quality and progressively reduce
        let currentQuality = quality
        let compressedDataUrl = canvas.toDataURL('image/jpeg', currentQuality)
        
        // Aggressive compression loop
        while (getBase64SizeKB(compressedDataUrl) > maxSizeKB && currentQuality > 0.1) {
          currentQuality -= 0.05 // Smaller steps for better control
          compressedDataUrl = canvas.toDataURL('image/jpeg', currentQuality)
        }

        // If still too large, progressively reduce dimensions
        let scaleFactor = 1.0
        while (getBase64SizeKB(compressedDataUrl) > maxSizeKB && scaleFactor > 0.3) {
          scaleFactor -= 0.1
          const newWidth = Math.floor(width * scaleFactor)
          const newHeight = Math.floor(height * scaleFactor)
          
          canvas.width = newWidth
          canvas.height = newHeight
          ctx?.drawImage(img, 0, 0, newWidth, newHeight)
          compressedDataUrl = canvas.toDataURL('image/jpeg', Math.max(currentQuality, 0.3))
        }

        // Final check - if still too large, use minimum settings
        if (getBase64SizeKB(compressedDataUrl) > maxSizeKB) {
          canvas.width = Math.min(400, width * 0.5)
          canvas.height = Math.min(300, height * 0.5)
          ctx?.drawImage(img, 0, 0, canvas.width, canvas.height)
          compressedDataUrl = canvas.toDataURL('image/jpeg', 0.3)
        }

        resolve(compressedDataUrl)
      } catch (error) {
        reject(new Error('Failed to compress image'))
      }
    }

    img.onerror = () => {
      reject(new Error('Failed to load image'))
    }

    // Create object URL for the image
    const objectUrl = URL.createObjectURL(file)
    img.src = objectUrl
  })
}

/**
 * Get the size of a base64 string in KB
 */
function getBase64SizeKB(base64String: string): number {
  // Remove data URL prefix if present
  const base64Data = base64String.split(',')[1] || base64String
  
  // Calculate size: base64 is ~4/3 the size of original binary data
  const sizeInBytes = (base64Data.length * 3) / 4
  return sizeInBytes / 1024
}

/**
 * Validate image file
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // Check file type
  if (!file.type.startsWith('image/')) {
    return { valid: false, error: 'Please select an image file' }
  }

  // Check file size (10MB limit for original file)
  const maxSizeBytes = 10 * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { valid: false, error: 'Image size must be less than 10MB' }
  }

  // Check for supported formats
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']
  if (!supportedTypes.includes(file.type)) {
    return { valid: false, error: 'Supported formats: JPG, PNG, GIF, WebP' }
  }

  return { valid: true }
}

/**
 * Create a thumbnail from an image file
 */
export function createThumbnail(
  file: File,
  size: number = 150
): Promise<string> {
  return compressImage(file, {
    maxWidth: size,
    maxHeight: size,
    quality: 0.8,
    maxSizeKB: 50 // Small thumbnail
  })
}