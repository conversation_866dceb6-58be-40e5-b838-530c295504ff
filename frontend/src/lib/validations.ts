import { z } from "zod"

// Auth Schemas
export const loginSchema = z.object({
  emailOrUserId: z.string().min(1, "Email or User ID is required"),
  password: z.string().min(6, "Password must be at least 6 characters"),
})

export const registerSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one lowercase letter, one uppercase letter, and one number"),
  confirmPassword: z.string(),
  churchSlug: z.string().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
})

// Church Registration Schema
export const churchRegistrationSchema = z.object({
  name: z.string().min(1, "Church name is required"),
  slug: z.string()
    .min(3, "Slug must be at least 3 characters")
    .regex(/^[a-z0-9-]+$/, "Slug can only contain lowercase letters, numbers, and hyphens"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  address: z.string().optional(),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
  description: z.string().optional(),
  adminUser: z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().email("Invalid email address"),
    password: z.string()
      .min(8, "Password must be at least 8 characters")
      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain at least one lowercase letter, one uppercase letter, and one number"),
  }),
})

// Event Schema
export const eventSchema = z.object({
  title: z.string().min(1, "Event title is required"),
  description: z.string().optional(),
  type: z.enum([
    "Sunday Service",
    "Bible Study", 
    "Prayer Meeting",
    "Youth Service",
    "Choir Practice",
    "Community Outreach",
    "Fundraiser",
    "Conference",
    "Retreat",
    "Wedding",
    "Funeral",
    "Baptism",
    "Communion",
    "Special Service",
    "Social Event",
    "Other"
  ]),
  startDate: z.date(),
  endDate: z.date(),
  location: z.string().optional(),
  virtualLink: z.string().url("Invalid URL").optional().or(z.literal("")),
  maxAttendees: z.number().positive().optional(),
  isPublic: z.boolean().default(true),
  allowRsvp: z.boolean().default(true),
  allowDonations: z.boolean().default(false),
  imageUrl: z.string()
    .refine((url) => {
      if (!url) return true; // Optional field
      // Allow regular URLs or data URLs
      return url.match(/^https?:\/\/.+/) || url.match(/^data:image\/(jpeg|jpg|png|gif|webp);base64,/);
    }, {
      message: "Must be a valid URL or image file"
    })
    .optional()
    .or(z.literal("")),
})

// Donation Schema
export const donationSchema = z.object({
  amount: z.number().positive("Amount must be greater than 0"),
  currency: z.string().min(1, "Currency is required"),
  type: z.string().min(1, "Donation type is required"),
  method: z.string().min(1, "Payment method is required"),
  isAnonymous: z.boolean().default(false),
  notes: z.string().optional(),
  eventId: z.string().optional(),
  memberId: z.string().optional(),
})

// Payment Method Schema
export const paymentMethodSchema = z.object({
  name: z.string().min(1, "Payment method name is required"),
  type: z.enum(['mobile_money', 'bank_card', 'bank_transfer', 'cash']),
  provider: z.string().optional(),
  accountNumber: z.string().optional(),
  accountName: z.string().optional(),
  bankName: z.string().optional(),
  phoneNumber: z.string().optional(),
  isDefault: z.boolean().default(false),
})

// Donation Category Schema
export const donationCategorySchema = z.object({
  name: z.string().min(1, "Category name is required"),
  description: z.string().optional(),
})

// Financial Settings Schema
export const financialSettingsSchema = z.object({
  defaultCurrency: z.string().min(1, "Default currency is required"),
  enableOnlineGiving: z.boolean().default(true),
  enableAnonymousDonations: z.boolean().default(true),
  requireReceiptGeneration: z.boolean().default(true),
  enableDonationCategories: z.boolean().default(true),
  enableEventDonations: z.boolean().default(true),
  enableRecurringDonations: z.boolean().default(false),
  minimumDonationAmount: z.number().min(0, "Minimum amount must be 0 or greater").default(0),
})

// Onboarding Schemas
export const checkAvailabilitySchema = z.object({
  churchSlug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
  adminEmail: z.string().min(1, 'Email is required').email('Invalid email format'),
})

export const initiateOnboardingSchema = z.object({
  church: z.object({
    name: z.string().min(1, 'Church name is required').max(100),
    slug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
    description: z.string().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    website: z.string().url().optional().or(z.literal("")),
    logo: z.string().url().optional().or(z.literal("")),
  }),
  admin: z.object({
    firstName: z.string().min(1, 'First name is required').max(50),
    lastName: z.string().min(1, 'Last name is required').max(50),
    email: z.string().min(1, 'Email is required').email('Invalid email format'),
    password: z.string().min(8, 'Password must be at least 8 characters'),
    phone: z.string().optional(),
    dateOfBirth: z.string().optional(),
    gender: z.enum(['male', 'female', 'other', 'prefer_not_to_say']).optional(),
    address: z.string().optional(),
  }),
  settings: z.object({
    allowSelfRegistration: z.boolean(),
    requireEmailVerification: z.boolean(),
    timezone: z.string(),
    locale: z.string(),
    theme: z.object({
      primaryColor: z.string(),
      secondaryColor: z.string(),
    }).optional(),
    features: z.object({
      events: z.boolean(),
      donations: z.boolean(),
      messaging: z.boolean(),
      calendar: z.boolean(),
      onlineGiving: z.boolean(),
      memberDirectory: z.boolean(),
      eventRsvp: z.boolean(),
      recurringEvents: z.boolean(),
    }).optional(),
  }).optional(),
})

export const completeOnboardingSchema = z.object({
  onboardingToken: z.string().min(1, 'Onboarding token is required'),
  confirmPassword: z.string().min(8, 'Password confirmation is required'),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
  subscribeToUpdates: z.boolean(),
})

export const setupInitialDataSchema = z.object({
  createSampleData: z.boolean(),
  setupBranches: z.array(z.object({
    name: z.string().min(1, 'Branch name is required').max(100),
    slug: z.string().min(2).max(50).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens'),
    description: z.string().optional(),
    address: z.string().optional(),
    phone: z.string().optional(),
    email: z.string().email().optional(),
    capacity: z.number().positive().optional(),
    coordinates: z.object({
      latitude: z.number(),
      longitude: z.number(),
    }).optional(),
    isMainBranch: z.boolean(),
  })),
  setupRoles: z.array(z.object({
    name: z.string().min(1, 'Role name is required').max(50),
    description: z.string().optional(),
    permissions: z.array(z.string()).default([]),
  })),
})

// Church Settings Schema
export const churchSettingsSchema = z.object({
  // Basic Settings
  allowSelfRegistration: z.boolean().optional().default(true),
  requireEmailVerification: z.boolean().optional().default(true),
  timezone: z.string().optional().default('UTC'),
  locale: z.string().optional().default('en'),
  
  // Theme Settings
  theme: z.object({
    primaryColor: z.string().default('#3B82F6'),
    secondaryColor: z.string().default('#64748B'),
    logo: z.string().url().optional().or(z.literal("")),
  }).optional(),
  
  // Feature Settings
  features: z.object({
    events: z.boolean().default(true),
    donations: z.boolean().default(false),
    messaging: z.boolean().default(true),
    calendar: z.boolean().default(true),
    onlineGiving: z.boolean().default(false),
    memberDirectory: z.boolean().default(true),
    eventRsvp: z.boolean().default(true),
    recurringEvents: z.boolean().default(true),
  }).optional(),
  
  // Event Settings
  eventSettings: z.object({
    defaultEventType: z.string().default('other'),
    requireApprovalForPublicEvents: z.boolean().default(false),
    allowMemberEventCreation: z.boolean().default(false),
    maxEventsPerMonth: z.number().int().positive().optional(),
    eventImageRequired: z.boolean().default(false),
  }).optional(),
  
  // Donation Settings
  donationSettings: z.object({
    defaultCurrency: z.string().length(3).default('USD'),
    enableAnonymousDonations: z.boolean().default(true),
    requireReceiptNumbers: z.boolean().default(true),
    taxDeductibleByDefault: z.boolean().default(true),
    enableEventDonations: z.boolean().default(true),
    minimumDonationAmount: z.number().positive().default(1),
    enableRecurringDonations: z.boolean().default(false),
  }).optional(),
})

// Church Profile Schema
export const churchProfileSchema = z.object({
  name: z.string().min(1, "Church name is required"),
  description: z.string().optional(),
  address: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Invalid email address").optional().or(z.literal("")),
  website: z.string().url("Invalid website URL").optional().or(z.literal("")),
})

// Type exports
export type LoginForm = z.infer<typeof loginSchema>
export type RegisterForm = z.infer<typeof registerSchema>
export type ChurchRegistrationForm = z.infer<typeof churchRegistrationSchema>
export type EventForm = z.infer<typeof eventSchema>
export type DonationForm = z.infer<typeof donationSchema>
export type PaymentMethodForm = z.infer<typeof paymentMethodSchema>
export type DonationCategoryForm = z.infer<typeof donationCategorySchema>
export type FinancialSettingsForm = z.infer<typeof financialSettingsSchema>
export type CheckAvailabilityForm = z.infer<typeof checkAvailabilitySchema>
export type InitiateOnboardingForm = z.infer<typeof initiateOnboardingSchema>
export type CompleteOnboardingForm = z.infer<typeof completeOnboardingSchema>
export type SetupInitialDataForm = z.infer<typeof setupInitialDataSchema>
export type ChurchSettingsForm = z.infer<typeof churchSettingsSchema>
export type ChurchProfileForm = z.infer<typeof churchProfileSchema>