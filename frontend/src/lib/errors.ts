/**
 * Comprehensive error classification system for authentication and API errors
 * Implements requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { ApiError } from './api'

// Enhanced error codes for better classification
export type AuthErrorCode = 
  | 'INVALID_CREDENTIALS'
  | 'ACCOUNT_INACTIVE' 
  | 'EMAIL_NOT_VERIFIED'
  | 'CHURCH_NOT_FOUND'
  | 'INVALID_CHURCH_CODE'
  | 'TOKEN_EXPIRED'
  | 'REFRESH_FAILED'
  | 'NETWORK_ERROR'
  | 'RATE_LIMITED'
  | 'SERVER_ERROR'
  | 'VALIDATION_ERROR'
  | 'PERMISSION_DENIED'
  | 'SESSION_EXPIRED'
  | 'UNKNOWN_ERROR'

export interface AuthError {
  code: AuthErrorCode
  message: string
  userMessage: string
  details?: Record<string, unknown>
  retryable: boolean
  recoverable: boolean
  actionable: boolean
  suggestedActions?: string[]
  contactSupport?: boolean
}

/**
 * Error classification utility that converts various error types into standardized AuthError
 */
export class ErrorClassifier {
  /**
   * Classify an error from API response or other sources
   */
  static classifyError(error: unknown, context?: string): AuthError {
    // Handle ApiError instances
    if (error instanceof ApiError) {
      return this.classifyApiError(error, context)
    }

    // Handle standard Error instances
    if (error instanceof Error) {
      return this.classifyStandardError(error, context)
    }

    // Handle response objects with error data
    if (this.isErrorResponse(error)) {
      return this.classifyErrorResponse(error, context)
    }

    // Handle string errors
    if (typeof error === 'string') {
      return this.classifyStringError(error, context)
    }

    // Fallback for unknown error types
    return this.createUnknownError(error, context)
  }

  /**
   * Classify ApiError instances
   */
  private static classifyApiError(error: ApiError, context?: string): AuthError {
    const baseError = {
      message: error.message,
      details: error.details,
    }

    // Check message content for specific error types regardless of status code
    const message = error.message.toLowerCase()

    // Email verification check
    if (message.includes('verify your email') || message.includes('email verification') || message.includes('email address before logging in')) {
      return {
        ...baseError,
        code: 'EMAIL_NOT_VERIFIED',
        userMessage: 'Please verify your email address before logging in.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Check your email for a verification link',
          'Look in your spam/junk folder if you don\'t see the email',
          'Contact your church administrator if you need a new verification email'
        ]
      }
    }

    // Account inactive check
    if (message.includes('account is inactive') || message.includes('account inactive')) {
      return {
        ...baseError,
        code: 'ACCOUNT_INACTIVE',
        userMessage: 'Your account is inactive. Please contact your church administrator.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact your church administrator to activate your account']
      }
    }

    // Church inactive check
    if (message.includes('church is inactive') || message.includes('church inactive')) {
      return {
        ...baseError,
        code: 'ACCOUNT_INACTIVE',
        userMessage: 'Your church account is inactive. Please contact support.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact support to reactivate your church account'],
        contactSupport: true
      }
    }

    switch (error.code) {
      case 'UNAUTHORIZED':
        if (context === 'login') {
          return {
            ...baseError,
            code: 'INVALID_CREDENTIALS',
            userMessage: 'Invalid email, password, or church code. Please check your credentials and try again.',
            retryable: true,
            recoverable: true,
            actionable: true,
            suggestedActions: [
              'Double-check your email address',
              'Verify your password is correct',
              'Confirm your church code is accurate',
              'Try resetting your password if needed'
            ]
          }
        }
        return {
          ...baseError,
          code: 'TOKEN_EXPIRED',
          userMessage: 'Your session has expired. Please log in again.',
          retryable: false,
          recoverable: true,
          actionable: true,
          suggestedActions: ['Log in again to continue']
        }

      case 'FORBIDDEN':
        return {
          ...baseError,
          code: 'PERMISSION_DENIED',
          userMessage: 'You do not have permission to perform this action.',
          retryable: false,
          recoverable: false,
          actionable: true,
          suggestedActions: ['Contact your church administrator for access']
        }

      case 'NOT_FOUND':
        if (context === 'church' || error.message.toLowerCase().includes('church')) {
          return {
            ...baseError,
            code: 'CHURCH_NOT_FOUND',
            userMessage: 'Church not found. Please check your church code or URL.',
            retryable: true,
            recoverable: true,
            actionable: true,
            suggestedActions: [
              'Verify the church code is correct',
              'Check the church URL for typos',
              'Contact the church administrator'
            ]
          }
        }
        return {
          ...baseError,
          code: 'UNKNOWN_ERROR',
          userMessage: 'The requested resource was not found.',
          retryable: false,
          recoverable: false,
          actionable: false
        }

      case 'VALIDATION_ERROR':
        return {
          ...baseError,
          code: 'VALIDATION_ERROR',
          userMessage: 'Please check your input and try again.',
          retryable: true,
          recoverable: true,
          actionable: true,
          suggestedActions: ['Review the form for any errors or missing information']
        }

      case 'RATE_LIMITED':
        return {
          ...baseError,
          code: 'RATE_LIMITED',
          userMessage: 'Too many attempts. Please wait a moment and try again.',
          retryable: true,
          recoverable: true,
          actionable: true,
          suggestedActions: ['Wait a few minutes before trying again']
        }

      case 'NETWORK_ERROR':
        return {
          ...baseError,
          code: 'NETWORK_ERROR',
          userMessage: 'Unable to connect to the server. Please check your internet connection.',
          retryable: true,
          recoverable: true,
          actionable: true,
          suggestedActions: [
            'Check your internet connection',
            'Try refreshing the page',
            'Wait a moment and try again'
          ]
        }

      case 'INTERNAL_SERVER_ERROR':
      case 'BAD_GATEWAY':
      case 'SERVICE_UNAVAILABLE':
      case 'GATEWAY_TIMEOUT':
        return {
          ...baseError,
          code: 'SERVER_ERROR',
          userMessage: 'A server error occurred. Please try again in a few moments.',
          retryable: true,
          recoverable: true,
          actionable: true,
          suggestedActions: [
            'Wait a few minutes and try again',
            'Refresh the page'
          ],
          contactSupport: true
        }

      default:
        return this.createUnknownError(error, context)
    }
  }

  /**
   * Classify standard Error instances
   */
  private static classifyStandardError(error: Error, context?: string): AuthError {
    const message = error.message.toLowerCase()

    // Email verification check
    if (message.includes('verify your email') || message.includes('email verification') || message.includes('email address before logging in')) {
      return {
        code: 'EMAIL_NOT_VERIFIED',
        message: error.message,
        userMessage: 'Please verify your email address before logging in.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Check your email for a verification link',
          'Look in your spam/junk folder if you don\'t see the email',
          'Contact your church administrator if you need a new verification email'
        ]
      }
    }

    // Account inactive check
    if (message.includes('account is inactive') || message.includes('account inactive')) {
      return {
        code: 'ACCOUNT_INACTIVE',
        message: error.message,
        userMessage: 'Your account is inactive. Please contact your church administrator.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact your church administrator to activate your account']
      }
    }

    // Network-related errors
    if (message.includes('network') || message.includes('fetch') || message.includes('connection')) {
      return {
        code: 'NETWORK_ERROR',
        message: error.message,
        userMessage: 'Network connection failed. Please check your internet connection and try again.',
        retryable: true,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Check your internet connection',
          'Try refreshing the page',
          'Wait a moment and try again'
        ]
      }
    }

    // Token-related errors
    if (message.includes('token') && (message.includes('expired') || message.includes('invalid'))) {
      return {
        code: 'TOKEN_EXPIRED',
        message: error.message,
        userMessage: 'Your session has expired. Please log in again.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Log in again to continue']
      }
    }

    // Church-related errors
    if (message.includes('church') && message.includes('not found')) {
      return {
        code: 'CHURCH_NOT_FOUND',
        message: error.message,
        userMessage: 'Church not found. Please check your church code.',
        retryable: true,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Verify the church code is correct',
          'Contact the church administrator'
        ]
      }
    }

    // Credential-related errors
    if (message.includes('credential') || message.includes('password') || message.includes('email')) {
      return {
        code: 'INVALID_CREDENTIALS',
        message: error.message,
        userMessage: 'Invalid credentials. Please check your email and password.',
        retryable: true,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Double-check your email address',
          'Verify your password is correct',
          'Try resetting your password if needed'
        ]
      }
    }

    return this.createUnknownError(error, context)
  }

  /**
   * Classify error response objects
   */
  private static classifyErrorResponse(error: any, context?: string): AuthError {
    const status = error.status || error.response?.status
    const message = error.message || error.response?.data?.message || 'An error occurred'
    const code = error.code || error.response?.data?.code

    // Check message content first for specific patterns
    const messageText = message.toLowerCase()

    // Email verification check
    if (messageText.includes('verify your email') || messageText.includes('email verification') || messageText.includes('email address before logging in')) {
      return {
        code: 'EMAIL_NOT_VERIFIED',
        message,
        userMessage: 'Please verify your email address before logging in.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Check your email for a verification link',
          'Look in your spam/junk folder if you don\'t see the email',
          'Contact your church administrator if you need a new verification email'
        ]
      }
    }

    // Account inactive check
    if (messageText.includes('account is inactive') || messageText.includes('account inactive')) {
      return {
        code: 'ACCOUNT_INACTIVE',
        message,
        userMessage: 'Your account is inactive. Please contact your church administrator.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact your church administrator to activate your account']
      }
    }

    // Church inactive check
    if (messageText.includes('church is inactive') || messageText.includes('church inactive')) {
      return {
        code: 'ACCOUNT_INACTIVE',
        message,
        userMessage: 'Your church account is inactive. Please contact support.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact support to reactivate your church account'],
        contactSupport: true
      }
    }

    // Handle specific error codes from backend
    if (code === 'ACCOUNT_INACTIVE') {
      return {
        code: 'ACCOUNT_INACTIVE',
        message,
        userMessage: 'Your account is inactive. Please contact your church administrator.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: ['Contact your church administrator to activate your account']
      }
    }

    if (code === 'EMAIL_NOT_VERIFIED') {
      return {
        code: 'EMAIL_NOT_VERIFIED',
        message,
        userMessage: 'Please verify your email address before logging in.',
        retryable: false,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Check your email for a verification link',
          'Request a new verification email if needed'
        ]
      }
    }

    if (code === 'INVALID_CHURCH_CODE') {
      return {
        code: 'INVALID_CHURCH_CODE',
        message,
        userMessage: 'Invalid church code. Please check the code and try again.',
        retryable: true,
        recoverable: true,
        actionable: true,
        suggestedActions: [
          'Verify the church code is correct',
          'Contact your church administrator for the correct code'
        ]
      }
    }

    // Handle by HTTP status code
    if (status) {
      switch (status) {
        case 401:
          return this.classifyApiError(new ApiError(message, status, 'UNAUTHORIZED'), context)
        case 403:
          return this.classifyApiError(new ApiError(message, status, 'FORBIDDEN'), context)
        case 404:
          return this.classifyApiError(new ApiError(message, status, 'NOT_FOUND'), context)
        case 422:
          return this.classifyApiError(new ApiError(message, status, 'VALIDATION_ERROR'), context)
        case 429:
          return this.classifyApiError(new ApiError(message, status, 'RATE_LIMITED'), context)
        case 500:
        case 502:
        case 503:
        case 504:
          return this.classifyApiError(new ApiError(message, status, 'INTERNAL_SERVER_ERROR'), context)
      }
    }

    return this.createUnknownError(error, context)
  }

  /**
   * Classify string errors
   */
  private static classifyStringError(error: string, context?: string): AuthError {
    return this.classifyStandardError(new Error(error), context)
  }

  /**
   * Create unknown error fallback
   */
  private static createUnknownError(error: unknown, context?: string): AuthError {
    const message = error instanceof Error ? error.message : 
                   typeof error === 'string' ? error : 
                   'An unexpected error occurred'

    return {
      code: 'UNKNOWN_ERROR',
      message,
      userMessage: 'An unexpected error occurred. Please try again.',
      retryable: true,
      recoverable: false,
      actionable: true,
      suggestedActions: [
        'Try refreshing the page',
        'Wait a moment and try again'
      ],
      contactSupport: true,
      details: { originalError: error, context }
    }
  }

  /**
   * Type guard to check if error is an error response object
   */
  private static isErrorResponse(error: unknown): error is { status?: number; message?: string; response?: any; code?: string } {
    return typeof error === 'object' && error !== null && 
           ('status' in error || 'response' in error || 'message' in error)
  }
}

/**
 * Utility functions for error handling
 */
export const ErrorUtils = {
  /**
   * Check if an error is retryable
   */
  isRetryable(error: AuthError): boolean {
    return error.retryable
  },

  /**
   * Check if an error is recoverable (user can take action)
   */
  isRecoverable(error: AuthError): boolean {
    return error.recoverable
  },

  /**
   * Check if an error requires user action
   */
  isActionable(error: AuthError): boolean {
    return error.actionable
  },

  /**
   * Check if user should contact support
   */
  shouldContactSupport(error: AuthError): boolean {
    return error.contactSupport === true
  },

  /**
   * Get retry delay based on error type (in milliseconds)
   */
  getRetryDelay(error: AuthError, attempt: number = 1): number {
    if (!error.retryable) return 0

    switch (error.code) {
      case 'NETWORK_ERROR':
        return Math.min(1000 * Math.pow(2, attempt - 1), 10000) // Exponential backoff, max 10s
      case 'RATE_LIMITED':
        return 60000 // 1 minute for rate limiting
      case 'SERVER_ERROR':
        return Math.min(2000 * attempt, 30000) // Linear backoff, max 30s
      default:
        return 1000 * attempt // 1s per attempt
    }
  },

  /**
   * Format error for logging
   */
  formatForLogging(error: AuthError, context?: Record<string, unknown>): Record<string, unknown> {
    return {
      code: error.code,
      message: error.message,
      userMessage: error.userMessage,
      retryable: error.retryable,
      recoverable: error.recoverable,
      actionable: error.actionable,
      details: error.details,
      context,
      timestamp: new Date().toISOString()
    }
  }
}