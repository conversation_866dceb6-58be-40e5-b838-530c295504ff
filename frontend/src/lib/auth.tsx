'use client'

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { apiClient } from '@/lib/api'
import { tokenStorage, TokenPair } from '@/lib/tokenStorage'
import { User, Church, AuthContextType } from '@/types'
import { AuthError, ErrorClassifier } from '@/lib/errors'
import { authRecoveryManager } from '@/lib/errorRecovery'

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [church, setChurch] = useState<Church | null>(null)
  const [loading, setLoading] = useState(true)
  const [isInitialized, setIsInitialized] = useState(false)
  const [error, setError] = useState<AuthError | null>(null)
  const router = useRouter()

  // Use refs to track mounted state and prevent memory leaks
  const mountedRef = useRef(true)
  const tokenChangeUnsubscribeRef = useRef<(() => void) | null>(null)
  const sessionCheckIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Initialize authentication on mount
  useEffect(() => {
    mountedRef.current = true

    const initializeAuth = async () => {
      if (!mountedRef.current) return
      await checkAuthStatus()
    }

    // Set up cross-tab synchronization
    const unsubscribe = tokenStorage.onTokensChanged((tokens) => {
      if (!mountedRef.current) return

      if (!tokens) {
        // Tokens were cleared (logout in another tab or session expired)
        console.debug('Cross-tab sync: Tokens cleared, logging out')
        handleCrossTabLogout()
      } else {
        // Tokens were updated (refresh in another tab)
        console.debug('Cross-tab sync: Tokens updated, refreshing auth state')
        handleCrossTabTokenUpdate(tokens)
      }
    })

    tokenChangeUnsubscribeRef.current = unsubscribe
    initializeAuth()

    return () => {
      mountedRef.current = false
      if (tokenChangeUnsubscribeRef.current) {
        tokenChangeUnsubscribeRef.current()
      }
    }
  }, [])

  // Separate effect for session expiration check
  useEffect(() => {
    if (!isInitialized) return

    // Check every 30 seconds for session expiration
    const interval = setInterval(() => {
      if (!mountedRef.current) return

      // Check if tokens are expired
      if (!tokenStorage.hasValidTokens() && user) {
        console.debug('Session expired, logging out all tabs')
        // Inline logout logic to avoid circular dependency
        setUser(null)
        setChurch(null)
        setError(null)

        // Redirect to login if on protected route
        const pathname = window.location.pathname
        if (isProtectedRoute(pathname)) {
          redirectToLogin(pathname)
        }
      }
    }, 30000) // 30 seconds

    sessionCheckIntervalRef.current = interval

    return () => {
      if (sessionCheckIntervalRef.current) {
        clearInterval(sessionCheckIntervalRef.current)
        sessionCheckIntervalRef.current = null
      }
    }
  }, [isInitialized, user, router])

  // Check authentication status
  const checkAuthStatus = useCallback(async () => {
    if (!mountedRef.current) return

    try {
      setLoading(true)
      setError(null)

      // Check if we have valid tokens
      if (!tokenStorage.hasValidTokens()) {
        if (mountedRef.current) {
          setUser(null)
          setChurch(null)
          setIsInitialized(true)
          setLoading(false)
        }
        return
      }

      // Fetch user profile
      const response = await apiClient.getProfile()

      if (!mountedRef.current) return

      setUser(response.data.user)

      // Extract church from user data
      if (response.data.user.church) {
        setChurch(response.data.user.church)
      }

    } catch (error: any) {
      if (!mountedRef.current) return

      console.error('Auth check failed:', error)

      // Handle different error types
      const authError = createAuthError(error)
      setError(authError)

      // Clear tokens and user state for auth failures
      tokenStorage.clearTokens()
      setUser(null)
      setChurch(null)

      // Redirect to login if on protected route
      const pathname = window.location.pathname
      if (isProtectedRoute(pathname)) {
        redirectToLogin(pathname)
      }
    } finally {
      if (mountedRef.current) {
        setLoading(false)
        setIsInitialized(true)
      }
    }
  }, [router])

  // Refresh tokens manually with error recovery
  const refreshTokens = useCallback(async () => {
    if (!mountedRef.current) return

    try {
      setError(null)

      // Use error recovery manager for token refresh
      await authRecoveryManager.recoverFromTokenRefresh(async () => {
        const refreshToken = tokenStorage.getRefreshToken()
        if (!refreshToken) {
          throw new Error('No refresh token available')
        }

        const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api'}/auth/refresh`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ refreshToken }),
        })

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Token refresh failed' }))
          throw new Error(errorData.message || 'Token refresh failed')
        }

        const data = await response.json()
        const { accessToken, refreshToken: newRefreshToken } = data.data.tokens

        // Store new tokens
        tokenStorage.setTokens(accessToken, newRefreshToken)

        // Re-check auth status to update user data
        await checkAuthStatus()
      })

    } catch (error: any) {
      if (!mountedRef.current) return

      console.error('Token refresh failed:', error)
      const authError = createAuthError(error, 'token_refresh')
      setError(authError)

      // Clear tokens and redirect to login
      tokenStorage.clearTokens()
      setUser(null)
      setChurch(null)

      const pathname = window.location.pathname
      if (isProtectedRoute(pathname)) {
        redirectToLogin(pathname)
      }
    }
  }, [checkAuthStatus])

  // Login function with error recovery
  const login = useCallback(async (emailOrUserId: string, password: string) => {
    if (!mountedRef.current) return

    setLoading(true)
    setError(null)

    try {
      // Use error recovery manager for login attempts
      await authRecoveryManager.recoverFromLoginError(
        async () => {
          const response = await apiClient.login({ emailOrUserId, password })

          if (!mountedRef.current) return

          if (response.data.tokens?.accessToken) {
            // Store tokens using token storage service
            tokenStorage.setTokens(
              response.data.tokens.accessToken,
              response.data.tokens.refreshToken
            )

            setUser(response.data.user)
            if (response.data.user.church) {
              setChurch(response.data.user.church)
            }

            // Automatically redirect to user's church dashboard
            const userChurch = response.data.user.church
            if (userChurch?.slug) {
              router.push(`/${userChurch.slug}/dashboard`)
            } else {
              // Fallback to general dashboard (shouldn't happen in multi-tenant setup)
              router.push('/dashboard')
            }
          }
        },
        emailOrUserId
      )
    } catch (error: any) {
      if (!mountedRef.current) return

      console.error('Login failed:', error)
      const authError = createAuthError(error, 'login')
      setError(authError)
      throw authError
    } finally {
      if (mountedRef.current) {
        setLoading(false)
      }
    }
  }, [router])

  // Logout function
  const logout = useCallback(() => {
    // Clear tokens using token storage service
    tokenStorage.clearTokens()
    setUser(null)
    setChurch(null)
    setError(null)

    // Always redirect to the main login page since users can login from anywhere
    router.push('/login?logout=true')
  }, [router])

  // Clear error function
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Handle cross-tab logout synchronization
  const handleCrossTabLogout = useCallback(() => {
    if (!mountedRef.current) return

    console.debug('Handling cross-tab logout')
    setUser(null)
    setChurch(null)
    setError(null)

    // Redirect to login if on protected route
    const pathname = window.location.pathname
    if (isProtectedRoute(pathname)) {
      redirectToLogin(pathname)
    }
  }, [router])

  // Handle cross-tab token update synchronization
  const handleCrossTabTokenUpdate = useCallback(async (tokens: TokenPair) => {
    if (!mountedRef.current) return

    console.debug('Handling cross-tab token update')
    
    // Check if tokens are still valid
    if (tokens.expiresAt <= Date.now()) {
      console.debug('Cross-tab tokens are expired, clearing auth state')
      handleCrossTabLogout()
      return
    }

    // Re-check auth status to get updated user data
    // This ensures UI is updated with any changes from other tabs
    try {
      await checkAuthStatus()
    } catch (error) {
      console.error('Failed to update auth state from cross-tab token update:', error)
      // If we can't verify the tokens, clear the auth state
      handleCrossTabLogout()
    }
  }, [checkAuthStatus, handleCrossTabLogout])



  // Helper function to create auth errors using the new classification system
  const createAuthError = (error: any, context?: string): AuthError => {
    return ErrorClassifier.classifyError(error, context)
  }

  // Helper function to check if route is protected
  const isProtectedRoute = (pathname: string): boolean => {
    return pathname.includes('/dashboard') ||
      /^\/[^/]+\/(members|events|donations|branches|analytics|announcements|roles|settings)/.test(pathname)
  }

  // Helper function to check if route segment is a public route
  const isPublicRoute = (segment: string): boolean => {
    const publicRoutes = ['login', 'register', 'forgot-password', 'reset-password', 'onboarding', 'register-church']
    return publicRoutes.includes(segment)
  }

  // Helper function to redirect to appropriate login page
  const redirectToLogin = (pathname: string) => {
    // Always redirect to the main login page since users can login from anywhere
    const loginUrl = '/login'
    const redirectParam = `?redirect=${encodeURIComponent(pathname)}`
    router.push(loginUrl + redirectParam)
  }

  const value: AuthContextType = {
    user,
    church,
    login,
    logout,
    refreshTokens,
    checkAuthStatus,
    loading,
    isInitialized,
    isAuthenticated: !!user,
    error,
    clearError,
  }

  // Show loading screen while checking authentication
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-sm text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}