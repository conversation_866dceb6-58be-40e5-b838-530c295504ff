import '@testing-library/jest-dom'
import { vi } from 'vitest'

// Extend global with vitest globals
declare global {
  const describe: typeof import('vitest').describe
  const it: typeof import('vitest').it
  const expect: typeof import('vitest').expect
  const beforeEach: typeof import('vitest').beforeEach
  const afterEach: typeof import('vitest').afterEach
}

// Global test setup
global.ResizeObserver = class ResizeObserver {
  constructor(_cb: ResizeObserverCallback) {
    // Constructor parameter is required by the interface but not used in tests
    void _cb // Explicitly mark as unused
  }
  observe() {}
  unobserve() {}
  disconnect() {}
}

// Mock localStorage
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: vi.fn(() => null),
    setItem: vi.fn(() => null),
    removeItem: vi.fn(() => null),
    clear: vi.fn(() => null),
  },
  writable: true,
})

// Mock next/navigation
vi.mock('next/navigation', () => ({
  useRouter() {
    return {
      push: vi.fn(),
      replace: vi.fn(),
      back: vi.fn(),
      forward: vi.fn(),
      refresh: vi.fn(),
      prefetch: vi.fn(),
    }
  },
  useSearchParams() {
    return new URLSearchParams()
  },
  usePathname() {
    return ''
  },
}))