// app/page.tsx
'use client'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Logo } from '@/components/ui/logo'
import {
  Users, Calendar, DollarSign, Lightbulb, Shield, Settings,
  ArrowRight, Star, Play, Menu, CheckCircle, BarChart3, Smartphone, MessageSquare
} from 'lucide-react'
import { motion, useScroll, useTransform } from 'framer-motion'

export default function HomePage() {
  const { scrollY } = useScroll()
  const headerBg = useTransform(
    scrollY,
    [0, 120],
    ['rgba(255,255,255,0)', 'rgba(255,255,255,0.75)']
  )

  /* ------------------------------------------------------------------ */
  /* Re-usable spring presets                                           */
  /* ------------------------------------------------------------------ */
  const fadeInUp = {
    hidden: { opacity: 0, y: 40 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.6, ease: 'easeOut' } }
  }

  return (
    <>
      {/* ---------------------------------------------------------------- */}
      {/* Transparent / Blur Header                                        */}
      {/* ---------------------------------------------------------------- */}
      <motion.header
        style={{ backgroundColor: headerBg }}
        initial={{ y: -100, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.6, ease: 'easeOut' }}
        className="fixed top-0 z-50 w-full backdrop-blur-lg"
      >
        <div className="mx-auto flex h-16 max-w-7xl items-center justify-between px-6">
          <Logo size="md" />
          <nav className="hidden md:flex items-center gap-8 text-sm font-medium text-gray-700">
            {['Features', 'Solutions', 'Pricing', 'Resources'].map((l) => (
              <Link key={l} href={`/${l.toLowerCase()}`} className="hover:text-blue-600">
                {l}
              </Link>
            ))}
          </nav>
          <div className="flex items-center gap-3">
            <Button variant="ghost" asChild className="hidden md:inline-flex">
              <Link href="/login">Sign In</Link>
            </Button>
            <Button asChild className="px-5">
              <Link href="/onboarding">Get Started</Link>
            </Button>
            <Button variant="ghost" size="sm" className="md:hidden">
              <Menu className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </motion.header>

      {/* ---------------------------------------------------------------- */}
      {/* Hero                                                             */}
      {/* ---------------------------------------------------------------- */}
      <section className="relative flex min-h-screen items-center justify-center overflow-hidden bg-gradient-to-br from-slate-50 via-white to-indigo-50">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.12, scale: 1 }}
          transition={{ duration: 1.2, ease: 'easeOut' }}
          className="absolute top-0 left-0 h-72 w-72 rounded-full bg-blue-500 blur-3xl"
        />
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 0.12, scale: 1 }}
          transition={{ duration: 1.2, delay: 0.2, ease: 'easeOut' }}
          className="absolute bottom-0 right-0 h-72 w-72 rounded-full bg-indigo-500 blur-3xl"
        />

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.3 }}
          className="relative mx-auto max-w-4xl px-6 text-center"
        >
          <p className="inline-flex items-center gap-2 rounded-full bg-blue-50 px-3 py-1 text-sm font-medium text-blue-700">
            <Star className="h-3 w-3" /> Trusted by 500+ churches worldwide
          </p>

          <h1 className="mt-6 text-4xl font-extrabold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            Modern Church Management <br />
            <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              Simplified
            </span>
          </h1>

          <p className="mx-auto mt-6 max-w-xl text-lg text-gray-600">
            One platform to grow your community, streamline operations and deepen
            connections across every location you serve.
          </p>

          <div className="mt-10 flex justify-center gap-4">
            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button size="lg" asChild className="gap-2">
                <Link href="/onboarding">
                  Start Free Trial <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </motion.div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button size="lg" variant="outline" asChild className="gap-2">
                <Link href="/demo">
                  <Play className="h-4 w-4" /> Watch Demo
                </Link>
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </section>

      {/* ---------------------------------------------------------------- */}
      {/* 1. Stats — glassmorphism counters                                */}
      {/* ---------------------------------------------------------------- */}
      <section className="relative -mt-20 z-10">
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto max-w-6xl px-6"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 rounded-3xl bg-white/70 p-8 shadow-xl backdrop-blur-md ring-1 ring-slate-200/50">
            {[
              { value: '500+', label: 'Churches' },
              { value: '50K+', label: 'Active Members' },
              { value: '99.9%', label: 'Uptime' },
              { value: '24/7', label: 'Support' }
            ].map((s, i) => (
              <motion.div
                key={s.label}
                initial={{ opacity: 0, scale: 0.7 }}
                whileInView={{ opacity: 1, scale: 1 }}
                viewport={{ once: true }}
                transition={{ delay: i * 0.1, type: 'spring', stiffness: 200 }}
                className="text-center"
              >
                <p className="text-4xl font-bold text-gray-900">{s.value}</p>
                <p className="mt-1 text-sm text-gray-600">{s.label}</p>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* ---------------------------------------------------------------- */}
      {/* 2. Features — floating glass cards                               */}
      {/* ---------------------------------------------------------------- */}
      <section className="py-24">
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-6"
        >
          <div className="text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Everything your church needs in one platform
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-lg text-gray-600">
              Powerful features designed to help your church thrive and grow your community.
            </p>
          </div>

          <div className="mt-16 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
            {[
              { icon: Users, title: 'Member Management', desc: 'Organize profiles, families and roles.' },
              { icon: Calendar, title: 'Event Planning', desc: 'Schedule events, registrations & reminders.' },
              { icon: DollarSign, title: 'Donation Tracking', desc: 'Track tithes, receipts & tax statements.' },
              { icon: Lightbulb, title: 'Multi-Branch Support', desc: 'Central oversight for every location.' },
              { icon: Shield, title: 'Role-Based Access', desc: 'Granular permissions for every role.' },
              { icon: Settings, title: 'Customizable Settings', desc: 'Tailor the platform to your needs.' }
            ].map((f, i) => (
              <motion.div
                key={f.title}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: i * 0.1, type: 'spring', stiffness: 200 }}
                whileHover={{ y: -8, transition: { type: 'spring', stiffness: 300 } }}
              >
                <Card className="group relative overflow-hidden rounded-3xl border-0 bg-white/50 shadow-lg backdrop-blur-sm transition-shadow hover:shadow-2xl">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-indigo-600/10" />
                  <CardHeader className="relative">
                    <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl bg-white/80 shadow">
                      <f.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <CardTitle className="text-lg">{f.title}</CardTitle>
                    <CardDescription className="text-gray-600">{f.desc}</CardDescription>
                  </CardHeader>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </section>

      {/* ---------------------------------------------------------------- */}
      {/* 3. Benefits — split hero testimonial                             */}
      {/* ---------------------------------------------------------------- */}
      <section className="bg-slate-50 py-24">
        <motion.div
          variants={fadeInUp}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-6 grid gap-12 lg:grid-cols-2 items-center"
        >
          <div>
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Why churches choose icengelo
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              We built every feature with your unique needs in mind so you can focus on ministry.
            </p>

            <ul className="mt-8 space-y-6">
              {[
                { icon: Smartphone, title: 'Mobile-First Design', desc: 'Full access on every device.' },
                { icon: BarChart3, title: 'Powerful Analytics', desc: 'Data-driven decisions made easy.' },
                { icon: MessageSquare, title: 'Seamless Communication', desc: 'Keep everyone in the loop.' }
              ].map((b, i) => (
                <motion.li
                  key={b.title}
                  initial={{ opacity: 0, x: -40 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: i * 0.15, type: 'spring', stiffness: 200 }}
                  className="flex items-start gap-4"
                >
                  <div className="flex h-12 w-12 shrink-0 items-center justify-center rounded-xl bg-blue-100 text-blue-600 shadow">
                    <b.icon className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{b.title}</h3>
                    <p className="text-sm text-gray-600">{b.desc}</p>
                  </div>
                </motion.li>
              ))}
            </ul>
          </div>

          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ type: 'spring', stiffness: 200 }}
            className="relative"
          >
            <div className="rounded-3xl bg-white p-8 shadow-xl">
              <div className="flex items-center gap-4">
                <div className="h-14 w-14 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600" />
                <div>
                  <p className="font-bold text-gray-900">Pastor John Davis</p>
                  <p className="text-sm text-gray-500">Grace Community Church</p>
                </div>
              </div>

              <blockquote className="mt-6 italic text-gray-700">
                “icengelo saved us countless admin hours—now we can focus on people, not paperwork.”
              </blockquote>

              <div className="mt-4 flex items-center gap-1">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                ))}
                <CheckCircle className="ml-3 h-4 w-4 text-green-500" />
                <span className="ml-1 text-sm text-gray-500">Verified Customer</span>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </section>

      {/* ---------------------------------------------------------------- */}
      {/* 4. CTA — floating glass                                          */}
      {/* ---------------------------------------------------------------- */}
      {/* <motion.section
        initial={{ opacity: 0, y: 60 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.7, type: 'spring', stiffness: 100 }}
        className="py-24"
      >
        <div className="mx-auto max-w-5xl px-6">
          <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 p-12 text-center text-white">
            <div className="absolute -top-8 -left-8 h-40 w-40 rounded-full bg-white/10 blur-2xl" />
            <div className="absolute -bottom-8 -right-8 h-40 w-40 rounded-full bg-white/10 blur-2xl" />

            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              Ready to transform your church management?
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-blue-100">
              Join hundreds of churches already using icengelo.
            </p>

            <div className="mt-10 flex justify-center gap-4">
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button size="lg" asChild className="bg-white text-blue-600 shadow-xl">
                  <Link href="/onboarding">Start Free Trial</Link>
                </Button>
              </motion.div>
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button size="lg" variant="outline" asChild className="border-white text-white hover:bg-white/10">
                  <Link href="/demo">Schedule Demo</Link>
                </Button>
              </motion.div>
            </div>
          </div>
        </div>
      </motion.section> */}

      {/* ---------------------------------------------------------------- */}
      {/* Footer — glass                                                   */}
      {/* ---------------------------------------------------------------- */}
      <footer className="bg-gray-900">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          className="mx-auto max-w-7xl px-6 py-16"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div>
              <Logo size="md" className="text-white" />
              <p className="mt-4 text-sm text-gray-400">
                Modern church management software designed to help your community thrive.
              </p>
            </div>

            {[
              { title: 'Product', links: ['Features', 'Pricing', 'Integrations', 'Roadmap'] },
              { title: 'Resources', links: ['Blog', 'Help Center', 'Webinars', 'Community'] },
              { title: 'Company', links: ['About Us', 'Careers', 'Contact', 'Privacy Policy'] }
            ].map((col) => (
              <div key={col.title}>
                <h3 className="font-semibold text-white">{col.title}</h3>
                <ul className="mt-4 space-y-2 text-sm">
                  {col.links.map((l) => (
                    <li key={l}>
                      <Link href={`/${l.toLowerCase().replace(' ', '-')}`} className="text-gray-400 hover:text-white">
                        {l}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>

          <div className="mt-12 border-t border-gray-800 pt-8 text-center text-sm text-gray-500">
            &copy; 2025 icengelo. All rights reserved.
          </div>
        </motion.div>
      </footer>
    </>
  )
}