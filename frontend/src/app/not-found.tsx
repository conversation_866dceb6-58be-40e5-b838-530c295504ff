'use client'

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Home, ArrowLeft, Search } from 'lucide-react'
import { useAuth } from '@/lib/auth'

export default function NotFound() {
  const router = useRouter()
  const { user, church, isAuthenticated } = useAuth()

  const handleGoBack = () => {
    router.back()
  }

  const getHomeUrl = () => {
    if (isAuthenticated && church?.slug) {
      return `/${church.slug}/dashboard`
    }
    return '/'
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
            <Search className="w-8 h-8 text-red-600" />
          </div>
          <CardTitle className="text-2xl font-bold text-gray-900">
            Page Not Found
          </CardTitle>
          <CardDescription className="text-gray-600">
            The page you&apos;re looking for doesn&apos;t exist or has been moved.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Button 
              onClick={handleGoBack}
              variant="outline"
              className="w-full"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Go Back
            </Button>
            
            <Button asChild className="w-full">
              <Link href={getHomeUrl()}>
                <Home className="w-4 h-4 mr-2" />
                {isAuthenticated && church ? 'Church Dashboard' : 'Home'}
              </Link>
            </Button>
          </div>
          
          {isAuthenticated && user && (
            <div className="pt-4 border-t">
              <p className="text-sm text-gray-500 mb-2">
                Logged in as {user.firstName} {user.lastName}
              </p>
              {church && (
                <p className="text-xs text-gray-400">
                  {church.name}
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}