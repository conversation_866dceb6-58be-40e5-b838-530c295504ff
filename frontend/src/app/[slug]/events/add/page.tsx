'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Calendar, UserX } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Branch } from '@/types'
import { CreateEventForm } from '@/components/forms/CreateEventForm'
import { toast } from 'sonner'

function AddEventContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)

  // Check permissions
  const canManageEvents = hasPermission(user, PERMISSIONS.MANAGE_EVENTS)

  const fetchBranches = async () => {
    if (!church?.slug) return

    try {
      setLoading(true)
      const response = await apiClient.getBranches(church.slug)
      if (response.data && (response.data as any).branches) {
        setBranches((response.data as any).branches)
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
      toast.error('Failed to load branches')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (canManageEvents && church?.slug) {
      fetchBranches()
    }
  }, [canManageEvents, church?.slug])

  const handleSuccess = () => {
    toast.success('Event created successfully!')
    router.push(`/${church?.slug}/events`)
  }

  const handleBack = () => {
    router.push(`/${church?.slug}/events`)
  }

  if (!canManageEvents) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="w-full max-w-md text-center">
          <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h1 className="text-xl font-semibold mb-2">Access Denied</h1>
          <p className="text-muted-foreground">
            You don&apos;t have permission to create church events. 
            Contact your church administrator if you need access.
          </p>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 p-6 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Create New Event</h1>
          <p className="text-muted-foreground">
            Schedule a new event or service for your church
          </p>
        </div>
      </div>

      {/* Form Section */}
      <div className="bg-background border rounded-lg p-6">
        <div className="mb-6">
          <h2 className="text-xl font-semibold flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Event Information
          </h2>
          <p className="text-muted-foreground mt-1">
            Fill in the details below to create a new event. Required fields are marked with an asterisk (*).
          </p>
        </div>
        
        <CreateEventForm 
          onSuccess={handleSuccess}
          branches={branches}
        />
      </div>
    </div>
  )
}

export default function AddEventPage() {
  return (
    <DashboardLayout>
      <AddEventContent />
    </DashboardLayout>
  )
}