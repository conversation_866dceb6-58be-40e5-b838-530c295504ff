'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, UserX, Calendar } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Event, Branch } from '@/types'
import { EditEventForm } from '@/components/forms/EditEventForm'
import { toast } from 'sonner'

function EditEventContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const eventId = params.id as string
  
  const [event, setEvent] = useState<Event | null>(null)
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)

  // Check permissions
  const canManageEvents = hasPermission(user, PERMISSIONS.MANAGE_EVENTS)

  const fetchData = async () => {
    if (!church?.slug || !eventId) return

    try {
      setLoading(true)
      
      // Fetch event and branches in parallel
      const [eventResponse, branchesResponse] = await Promise.all([
        apiClient.getEvent(church.slug, eventId),
        apiClient.getBranches(church.slug)
      ])

      if (eventResponse.data && (eventResponse.data as any).event) {
        setEvent((eventResponse.data as any).event as Event)
      }

      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load event details')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (canManageEvents && church?.slug && eventId) {
      fetchData()
    }
  }, [canManageEvents, church?.slug, eventId])

  const handleSuccess = () => {
    toast.success('Event updated successfully!')
    router.push(`/${church?.slug}/events/${eventId}`)
  }

  const handleBack = () => {
    router.push(`/${church?.slug}/events/${eventId}`)
  }

  if (!canManageEvents) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to edit church events. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading event details...</p>
        </div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Event Not Found</CardTitle>
            <CardDescription>
              The event you&apos;re trying to edit could not be found.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Event</h1>
          <p className="text-muted-foreground">
            Update {event.title}&apos;s information
          </p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="max-w-4xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Event Information
          </CardTitle>
          <CardDescription>
            Update the event&apos;s details below. Required fields are marked with an asterisk (*).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EditEventForm 
            event={event}
            branches={branches}
            onSuccess={handleSuccess}
          />
        </CardContent>
      </Card>
    </div>
  )
}

export default function EditEventPage() {
  return (
    <DashboardLayout>
      <EditEventContent />
    </DashboardLayout>
  )
}