'use client'

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { 
  Calendar, 
  MapPin, 
  Clock,
  Users,
  ArrowLeft,
  ExternalLink,
  Heart,
  MessageCircle,
  Edit
} from 'lucide-react'
import { api } from '@/lib/api'
import { Event } from '@/types'
import { toast } from 'sonner'
import { formatDate, formatTime } from '@/lib/utils'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import EventRsvpDialog from '@/components/events/EventRsvpDialog'
import EventCommentsDialog from '@/components/events/EventCommentsDialog'

function EventDetailContent() {
  const { user, church } = useAuth()
  const params = useParams()
  const router = useRouter()
  const eventId = params.id as string
  
  const [event, setEvent] = useState<Event | null>(null)
  const [loading, setLoading] = useState(true)
  const [isLiked, setIsLiked] = useState(false)
  const [likesCount, setLikesCount] = useState(0)
  const [isLiking, setIsLiking] = useState(false)
  const [showRsvpDialog, setShowRsvpDialog] = useState(false)
  const [showCommentsDialog, setShowCommentsDialog] = useState(false)

  const canManageEvents = hasPermission(user, PERMISSIONS.MANAGE_EVENTS)

  useEffect(() => {
    if (church?.slug && eventId) {
      fetchEvent()
    }
  }, [church?.slug, eventId])

  const fetchEvent = async () => {
    if (!church?.slug) return

    try {
      setLoading(true)
      const response = await api.getEvent(church.slug, eventId)
      const eventData = (response.data as any).event
      
      setEvent(eventData)
      setIsLiked(eventData.socialStats?.userLiked || false)
      setLikesCount(eventData.socialStats?.likesCount || 0)
    } catch (error) {
      console.error('Error fetching event:', error)
      toast.error('Failed to load event')
      router.push(`/${church.slug}/events`)
    } finally {
      setLoading(false)
    }
  }

  const handleLike = async () => {
    if (!church || !user || isLiking) return

    setIsLiking(true)
    try {
      const response = await api.toggleEventLike(church.slug, eventId)
      const { liked } = (response.data as any)
      
      setIsLiked(liked)
      setLikesCount(prev => liked ? prev + 1 : prev - 1)
      
      toast.success(liked ? 'Event liked!' : 'Event unliked!')
    } catch (error) {
      console.error('Error toggling like:', error)
      toast.error('Failed to update like')
    } finally {
      setIsLiking(false)
    }
  }

  const handleRsvpUpdate = (rsvp: any) => {
    if (event) {
      setEvent({
        ...event,
        socialStats: {
          likesCount: event.socialStats?.likesCount ?? 0,
          commentsCount: event.socialStats?.commentsCount ?? 0,
          userLiked: event.socialStats?.userLiked ?? false,
          userRsvp: rsvp || undefined,
        },
      })
    }
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading event...</div>
      </div>
    )
  }

  if (!event) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-2">Event not found</h2>
          <p className="text-muted-foreground mb-4">The event you're looking for doesn't exist.</p>
          <Button onClick={() => router.push(`/${church?.slug}/events`)}>
            Back to Events
          </Button>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="flex-1 space-y-6 p-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push(`/${church?.slug}/events`)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Events
          </Button>
          
          <div className="flex-1" />
          
          {canManageEvents && (
            <Button
              variant="outline"
              onClick={() => router.push(`/${church?.slug}/events/${eventId}/edit`)}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Event
            </Button>
          )}
        </div>

        {/* Event Details */}
        <div className="grid gap-6 lg:grid-cols-3">
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary">
                        {event.type.replace('_', ' ').toUpperCase()}
                      </Badge>
                      {event.requiresRsvp && (
                        <Badge variant="outline">RSVP Required</Badge>
                      )}
                      {!event.isPublic && (
                        <Badge variant="outline">Private</Badge>
                      )}
                    </div>
                    <CardTitle className="text-2xl">{event.title}</CardTitle>
                  </div>
                  {event.imageUrl && (
                    <img
                      src={event.imageUrl}
                      alt={event.title}
                      className="w-24 h-24 rounded-lg object-cover"
                    />
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {event.description && (
                  <p className="text-muted-foreground">{event.description}</p>
                )}

                <Separator />

                <div className="grid gap-4 sm:grid-cols-2">
                  <div className="flex items-center gap-3">
                    <Calendar className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">{formatDate(event.startDate)}</p>
                      <p className="text-sm text-muted-foreground">Date</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <Clock className="w-5 h-5 text-muted-foreground" />
                    <div>
                      <p className="font-medium">
                        {formatTime(event.startDate)}
                        {event.endDate && ` - ${formatTime(event.endDate)}`}
                      </p>
                      <p className="text-sm text-muted-foreground">Time</p>
                    </div>
                  </div>

                  {event.location && (
                    <div className="flex items-center gap-3">
                      <MapPin className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <p className="font-medium">{event.location}</p>
                        <p className="text-sm text-muted-foreground">Location</p>
                      </div>
                    </div>
                  )}

                  {event.virtualLink && (
                    <div className="flex items-center gap-3">
                      <ExternalLink className="w-5 h-5 text-muted-foreground" />
                      <div>
                        <a
                          href={event.virtualLink}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="font-medium text-blue-600 hover:underline"
                        >
                          Join Virtual Event
                        </a>
                        <p className="text-sm text-muted-foreground">Virtual Link</p>
                      </div>
                    </div>
                  )}
                </div>

                <Separator />

                {/* Social Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={handleLike}
                      disabled={isLiking}
                      className="flex items-center gap-2"
                    >
                      <Heart 
                        className={`w-4 h-4 ${
                          isLiked ? 'fill-red-500 text-red-500' : 'text-muted-foreground'
                        }`} 
                      />
                      <span>{likesCount}</span>
                    </Button>

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setShowCommentsDialog(true)}
                      className="flex items-center gap-2"
                    >
                      <MessageCircle className="w-4 h-4 text-muted-foreground" />
                      <span>{event.socialStats?.commentsCount || 0}</span>
                    </Button>
                  </div>

                  {event.requiresRsvp && (
                    <Button
                      onClick={() => setShowRsvpDialog(true)}
                      variant={event.socialStats?.userRsvp ? "outline" : "default"}
                    >
                      {event.socialStats?.userRsvp ? 'Update RSVP' : 'RSVP Now'}
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* RSVP Stats */}
            {event.requiresRsvp && event.rsvpStats && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Attendance
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Going</span>
                    <span className="font-medium text-green-600">
                      {event.rsvpStats.attending}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Maybe</span>
                    <span className="font-medium text-yellow-600">
                      {event.rsvpStats.maybe}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Can't go</span>
                    <span className="font-medium text-red-600">
                      {event.rsvpStats.notAttending}
                    </span>
                  </div>
                  {event.maxAttendees && (
                    <>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Capacity</span>
                        <span className="font-medium">
                          {event.rsvpStats.attending} / {event.maxAttendees}
                        </span>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Event Info */}
            <Card>
              <CardHeader>
                <CardTitle>Event Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Type</span>
                  <span className="font-medium">
                    {event.type.replace('_', ' ')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-muted-foreground">Visibility</span>
                  <span className="font-medium">
                    {event.isPublic ? 'Public' : 'Private'}
                  </span>
                </div>
                {event.allowDonations && (
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Donations</span>
                    <span className="font-medium text-green-600">Accepted</span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {showRsvpDialog && (
        <EventRsvpDialog
          event={event}
          currentRsvp={event.socialStats?.userRsvp}
          onClose={() => setShowRsvpDialog(false)}
          onRsvpUpdate={handleRsvpUpdate}
        />
      )}

      {showCommentsDialog && (
        <EventCommentsDialog
          event={event}
          onClose={() => setShowCommentsDialog(false)}
        />
      )}
    </>
  )
}

export default function EventDetailPage() {
  return (
    <DashboardLayout>
      <EventDetailContent />
    </DashboardLayout>
  )
}