'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  ArrowLeft,
  UserPlus,
  User,
  Phone,
  MapPin,
  Building,
  Settings,
  Eye,
  Save,
  UserX
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Branch, Role } from '@/types'
import { toast } from 'sonner'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

// Form validation schema
const createMemberSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'First name contains invalid characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'Last name contains invalid characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase(),
  phone: z.string()
    .optional()
    .refine((phone) => {
      if (!phone || phone.trim() === '') return true;
      // Remove common formatting characters and validate
      const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
      return /^[\+]?[1-9][\d]{6,15}$/.test(cleanPhone);
    }, 'Please enter a valid phone number'),
  dateOfBirth: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return !isNaN(birthDate.getTime()) && age >= 0 && age <= 120;
    }, 'Please enter a valid date of birth'),
  gender: z.enum(['male', 'female', 'other'], {
    message: 'Gender must be male, female, or other'
  }).optional(),
  address: z.string()
    .max(500, 'Address must be less than 500 characters')
    .optional(),
  branchId: z.string()
    .uuid('Invalid branch selection')
    .optional(),
  roleId: z.string()
    .uuid('Invalid role selection')
    .optional(),
  status: z.enum(['active', 'inactive', 'suspended'], {
    message: 'Status must be active, inactive, or suspended'
  }),
});

type CreateMemberFormData = z.infer<typeof createMemberSchema>

function AddMemberContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const churchSlug = params.slug as string

  const [branches, setBranches] = useState<Branch[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('personal')

  // Form setup with react-hook-form and zod validation
  const form = useForm<CreateMemberFormData>({
    resolver: zodResolver(createMemberSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      address: '',
      status: 'active',
    }
  })

  // Check permissions
  const canManageMembers = hasPermission(user, PERMISSIONS.MANAGE_MEMBERS)

  const fetchInitialData = useCallback(async () => {
    const slug = church?.slug || churchSlug
    if (!slug) return

    try {
      setInitialLoading(true)

      // Always fetch branches
      const branchesResponse = await apiClient.getBranches(slug, { limit: 100 })
      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }

      // Try to fetch roles, but don't fail if user doesn't have permission
      try {
        const rolesResponse = await apiClient.getRoles(slug)
        if (rolesResponse.data && (rolesResponse.data as any).roles) {
          setRoles((rolesResponse.data as any).roles)
        }
      } catch (roleError: any) {
        // If user doesn't have permission to view roles, that's okay
        if (roleError.status !== 403) {
          console.error('Error fetching roles:', roleError)
        }
      }
    } catch (error) {
      console.error('Error fetching initial data:', error)
      toast.error('Failed to load initial data')
    } finally {
      setInitialLoading(false)
    }
  }, [church?.slug, churchSlug])

  useEffect(() => {
    const slug = church?.slug || churchSlug
    if (canManageMembers && slug) {
      fetchInitialData()
    }
  }, [canManageMembers, church?.slug, churchSlug, fetchInitialData])

  const onSubmit = async (data: CreateMemberFormData) => {
    const slug = church?.slug || churchSlug
    if (!slug) return

    try {
      setLoading(true)

      // Prepare submission data - only include fields with actual values
      const submitData: any = {
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        email: data.email.trim(),
        status: data.status
      }

      // Add optional fields only if they have values
      if (data.phone && data.phone.trim()) {
        submitData.phone = data.phone.trim()
      }

      if (data.dateOfBirth && data.dateOfBirth.trim()) {
        submitData.dateOfBirth = data.dateOfBirth.trim()
      }

      if (data.gender) {
        submitData.gender = data.gender
      }

      if (data.address && data.address.trim()) {
        submitData.address = data.address.trim()
      }

      if (data.branchId && data.branchId.trim()) {
        submitData.branchId = data.branchId.trim()
      }

      if (data.roleId && data.roleId.trim()) {
        submitData.roleId = data.roleId.trim()
      }

      await apiClient.createMember(slug, submitData)

      toast.success('Member created successfully! They will receive an email with their login credentials.')
      router.push(`/${slug}/members`)
    } catch (error: any) {
      console.error('Error creating member:', error)
      toast.error(error.message || 'Failed to create member')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    const slug = church?.slug || churchSlug
    router.push(`/${slug}/members`)
  }

  if (!canManageMembers) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to add church members.
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (initialLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading form data...</div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-8 p-6">
      {/* Modern Header with Progress Indicator */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={handleCancel} className="shrink-0">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0 flex-1">
            <h1 className="text-3xl font-bold tracking-tight">Add New Member</h1>
            <p className="text-muted-foreground">
              Create a new member account for your church community
            </p>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Eye className="h-4 w-4" />
            <span>All fields validated</span>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center gap-2 text-sm">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${activeTab === 'personal' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
            <User className="h-3 w-3" />
            Personal Info
          </div>
          <div className="w-8 h-px bg-border" />
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${activeTab === 'contact' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
            <Phone className="h-3 w-3" />
            Contact Details
          </div>
          <div className="w-8 h-px bg-border" />
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${activeTab === 'church' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
            }`}>
            <Building className="h-3 w-3" />
            Church Info
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="personal" className="flex items-center gap-2">
                <User className="h-4 w-4" />
                Personal Info
              </TabsTrigger>
              <TabsTrigger value="contact" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Contact Details
              </TabsTrigger>
              <TabsTrigger value="church" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Church Info
              </TabsTrigger>
            </TabsList>

            {/* Personal Information Tab */}
            <TabsContent value="personal" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <UserPlus className="h-5 w-5" />
                    Personal Information
                  </CardTitle>
                  <CardDescription>
                    Basic personal details for the new member
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Name Fields */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-medium">First Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter first name"
                              className="text-lg"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Member's legal first name
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base font-medium">Last Name *</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter last name"
                              className="text-lg"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Member's legal last name
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Email */}
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base font-medium">Email Address *</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            className="text-lg"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Primary email for church communications and account access
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />



                  {/* Date of Birth and Gender */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth</FormLabel>
                          <FormControl>
                            <Input type="date" {...field} />
                          </FormControl>
                          <FormDescription>
                            Optional - helps with age-appropriate ministry assignments
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select gender" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Optional - for ministry and event planning
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Contact Details Tab */}
            <TabsContent value="contact" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Phone className="h-5 w-5" />
                    Contact Information
                  </CardTitle>
                  <CardDescription>
                    How to reach this member for church communications
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Phone */}
                  <FormField
                    control={form.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Phone Number</FormLabel>
                        <FormControl>
                          <Input
                            type="tel"
                            placeholder="+****************"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Primary phone number for urgent communications
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Address */}
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Home Address</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Street address, city, state, zip code"
                            className="min-h-[100px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Complete mailing address for church correspondence
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Church Information Tab */}
            <TabsContent value="church" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Church Membership Details
                  </CardTitle>
                  <CardDescription>
                    Church-specific information and assignments
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Role Selection */}
                  <FormField
                    control={form.control}
                    name="roleId"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Member Role</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select role (defaults to member)" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {roles.map((role) => (
                              <SelectItem key={role.id} value={role.id}>
                                <div className="flex items-center gap-2">
                                  <Settings className="h-4 w-4" />
                                  <div>
                                    <div className="font-medium">{role.name}</div>
                                    {role.description && (
                                      <div className="text-xs text-muted-foreground">{role.description}</div>
                                    )}
                                    {role.isSystem && (
                                      <div className="text-xs text-blue-600">System Role</div>
                                    )}
                                  </div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormDescription>
                          Determines the member's permissions and access level
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Branch and Status */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="branchId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Church Branch</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select branch (defaults to main)" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {branches.map((branch) => (
                                <SelectItem key={branch.id} value={branch.id}>
                                  <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    <div>
                                      <div className="font-medium">{branch.name}</div>
                                      {(branch as any).isMainBranch && (
                                        <div className="text-xs text-muted-foreground">Main Campus</div>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Primary branch for attendance and ministry involvement
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Membership Status</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="active">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                                  <div>
                                    <div className="font-medium">Active</div>
                                    <div className="text-xs text-muted-foreground">Full church privileges</div>
                                  </div>
                                </div>
                              </SelectItem>
                              <SelectItem value="inactive">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                                  <div>
                                    <div className="font-medium">Inactive</div>
                                    <div className="text-xs text-muted-foreground">Limited access</div>
                                  </div>
                                </div>
                              </SelectItem>
                              <SelectItem value="suspended">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                                  <div>
                                    <div className="font-medium">Suspended</div>
                                    <div className="text-xs text-muted-foreground">No access</div>
                                  </div>
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Current membership status and access level
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-6 border-t">
            <Button type="button" variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading} size="lg" className="min-w-[140px]">
              {loading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                  Creating...
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  Create Member
                </div>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

export default function AddMemberPage() {
  return (
    <DashboardLayout>
      <AddMemberContent />
    </DashboardLayout>
  )
}