'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Badge } from '@/components/ui/badge'
import { Ava<PERSON>, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  ArrowLeft, 
  User, 
  Phone, 
  MapPin, 
  Building, 
  Settings,
  Save,
  UserX,
  Edit,
  Shield,
  Calendar,
  Mail,
  Home,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Member, Branch, Role } from '@/types'
import { toast } from 'sonner'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

// Form validation schema
const updateMemberSchema = z.object({
  firstName: z.string()
    .min(1, 'First name is required')
    .max(50, 'First name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'First name contains invalid characters'),
  lastName: z.string()
    .min(1, 'Last name is required')
    .max(50, 'Last name must be less than 50 characters')
    .regex(/^[a-zA-ZÀ-ÿ\s'-]+$/, 'Last name contains invalid characters'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase(),
  phone: z.string()
    .optional()
    .refine((phone) => {
      if (!phone || phone.trim() === '') return true;
      const cleanPhone = phone.replace(/[\s\-\(\)]/g, '');
      return /^[\+]?[1-9][\d]{6,15}$/.test(cleanPhone);
    }, 'Please enter a valid phone number'),
  dateOfBirth: z.string()
    .optional()
    .refine((date) => {
      if (!date) return true;
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      return !isNaN(birthDate.getTime()) && age >= 0 && age <= 120;
    }, 'Please enter a valid date of birth'),
  gender: z.enum(['male', 'female', 'other'], {
    message: 'Gender must be male, female, or other'
  }).optional(),
  address: z.string()
    .max(500, 'Address must be less than 500 characters')
    .optional(),
  branchId: z.string()
    .uuid('Invalid branch selection')
    .optional(),
  status: z.enum(['active', 'inactive', 'suspended'], {
    message: 'Status must be active, inactive, or suspended'
  }),
});

type UpdateMemberFormData = z.infer<typeof updateMemberSchema>

function MemberProfileContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const churchSlug = params.slug as string
  const memberId = params.id as string
  
  const [member, setMember] = useState<Member | null>(null)
  const [branches, setBranches] = useState<Branch[]>([])
  const [roles, setRoles] = useState<Role[]>([])
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('profile')
  const [isEditing, setIsEditing] = useState(false)

  // Form setup with react-hook-form and zod validation
  const form = useForm<UpdateMemberFormData>({
    resolver: zodResolver(updateMemberSchema),
  })

  // Check permissions
  const canManageMembers = hasPermission(user, PERMISSIONS.MANAGE_MEMBERS)
  const canViewMembers = hasPermission(user, PERMISSIONS.VIEW_MEMBERS)

  const fetchMemberData = useCallback(async () => {
    const slug = church?.slug || churchSlug
    if (!slug || !memberId) return

    try {
      setInitialLoading(true)
      
      // Fetch member, branches, and roles in parallel
      const [memberResponse, branchesResponse, rolesResponse] = await Promise.all([
        apiClient.getMember(slug, memberId),
        apiClient.getBranches(slug, { limit: 100 }),
        apiClient.getRoles(slug)
      ])
      
      if (memberResponse.data && (memberResponse.data as any).member) {
        const memberData = (memberResponse.data as any).member
        setMember(memberData)
        
        // Set form values
        form.reset({
          firstName: memberData.firstName || '',
          lastName: memberData.lastName || '',
          email: memberData.email || '',
          phone: memberData.phone || '',
          dateOfBirth: memberData.dateOfBirth ? memberData.dateOfBirth.split('T')[0] : '',
          gender: memberData.gender || undefined,
          address: memberData.address || '',
          branchId: memberData.branchId || undefined,
          status: memberData.status || 'active',
        })
      }
      
      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }
      
      if (rolesResponse.data && (rolesResponse.data as any).roles) {
        setRoles((rolesResponse.data as any).roles)
      }
    } catch (error) {
      console.error('Error fetching member data:', error)
      toast.error('Failed to load member data')
    } finally {
      setInitialLoading(false)
    }
  }, [church?.slug, churchSlug, memberId, form])

  useEffect(() => {
    const slug = church?.slug || churchSlug
    if ((canManageMembers || canViewMembers) && slug && memberId) {
      fetchMemberData()
    }
  }, [canManageMembers, canViewMembers, church?.slug, churchSlug, memberId, fetchMemberData])

  const onSubmit = async (data: UpdateMemberFormData) => {
    const slug = church?.slug || churchSlug
    if (!slug || !memberId) return

    try {
      setLoading(true)

      // Prepare submission data - only include fields with actual values
      const submitData: any = {
        firstName: data.firstName.trim(),
        lastName: data.lastName.trim(),
        email: data.email.trim(),
        status: data.status
      }

      // Add optional fields only if they have values
      if (data.phone && data.phone.trim()) {
        submitData.phone = data.phone.trim()
      }
      
      if (data.dateOfBirth && data.dateOfBirth.trim()) {
        submitData.dateOfBirth = data.dateOfBirth.trim()
      }
      
      if (data.gender) {
        submitData.gender = data.gender
      }
      
      if (data.address && data.address.trim()) {
        submitData.address = data.address.trim()
      }
      
      if (data.branchId && data.branchId.trim()) {
        submitData.branchId = data.branchId.trim()
      }

      await apiClient.updateMember(slug, memberId, submitData)
      
      toast.success('Member updated successfully!')
      setIsEditing(false)
      
      // Refresh member data
      await fetchMemberData()
    } catch (error: any) {
      console.error('Error updating member:', error)
      toast.error(error.message || 'Failed to update member')
    } finally {
      setLoading(false)
    }
  }

  const handleRoleChange = async (roleId: string) => {
    const slug = church?.slug || churchSlug
    if (!slug || !memberId) return

    try {
      setLoading(true)
      
      if (member?.role?.id === roleId) {
        // Remove role if it's the same
        await apiClient.removeMemberRole(slug, memberId)
        toast.success('Role removed successfully!')
      } else {
        // Assign new role
        await apiClient.assignMemberRole(slug, memberId, roleId)
        toast.success('Role assigned successfully!')
      }
      
      // Refresh member data
      await fetchMemberData()
    } catch (error: any) {
      console.error('Error updating member role:', error)
      toast.error(error.message || 'Failed to update member role')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    const slug = church?.slug || churchSlug
    router.push(`/${slug}/members`)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'suspended':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'inactive':
        return <AlertCircle className="h-4 w-4" />
      case 'suspended':
        return <XCircle className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  if (!canManageMembers && !canViewMembers) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to view member profiles. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (initialLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading member profile...</div>
      </div>
    )
  }

  if (!member) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Member Not Found</CardTitle>
            <CardDescription>
              The requested member could not be found or you don't have permission to view them.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-8 p-6">
      {/* Header */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={handleCancel} className="shrink-0">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0 flex-1">
            <div className="flex items-center gap-4">
              <Avatar className="h-16 w-16">
                <AvatarImage src={member.profileImage} />
                <AvatarFallback className="text-lg">
                  {member.firstName?.[0]}{member.lastName?.[0]}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold tracking-tight">
                  {member.firstName} {member.lastName}
                </h1>
                <p className="text-sm text-muted-foreground mb-2">
                  Member ID: {member.userId}
                </p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge className={getStatusColor(member.status || 'active')}>
                    {getStatusIcon(member.status || 'active')}
                    {member.status || 'active'}
                  </Badge>
                  {member.role && (
                    <Badge variant="outline" className="flex items-center gap-1">
                      <Shield className="h-3 w-3" />
                      {member.role.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          </div>
          {canManageMembers && (
            <Button 
              onClick={() => setIsEditing(!isEditing)} 
              variant={isEditing ? "outline" : "default"}
            >
              <Edit className="h-4 w-4 mr-2" />
              {isEditing ? 'Cancel Edit' : 'Edit Profile'}
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles & Permissions
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Activity
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Personal Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <User className="h-5 w-5" />
                    Personal Information
                  </CardTitle>
                  <CardDescription>
                    Basic personal details and contact information
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Name Fields */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>First Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter first name"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Last Name</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter last name"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Email and Phone */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Mail className="h-4 w-4" />
                            Email Address
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center gap-2">
                            <Phone className="h-4 w-4" />
                            Phone Number
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="+****************"
                              disabled={!isEditing}
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Date of Birth and Gender */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="dateOfBirth"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Date of Birth</FormLabel>
                          <FormControl>
                            <Input 
                              type="date" 
                              disabled={!isEditing}
                              {...field} 
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="gender"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Gender</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            disabled={!isEditing}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select gender" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="male">Male</SelectItem>
                              <SelectItem value="female">Female</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Address */}
                  <FormField
                    control={form.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="flex items-center gap-2">
                          <Home className="h-4 w-4" />
                          Home Address
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Street address, city, state, zip code"
                            className="min-h-[100px]"
                            disabled={!isEditing}
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>

              {/* Church Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Church Information
                  </CardTitle>
                  <CardDescription>
                    Church membership details and assignments
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="branchId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Church Branch</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            disabled={!isEditing}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select branch" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {branches.map((branch) => (
                                <SelectItem key={branch.id} value={branch.id}>
                                  <div className="flex items-center gap-2">
                                    <MapPin className="h-4 w-4" />
                                    <div>
                                      <div className="font-medium">{branch.name}</div>
                                      {(branch as any).isMainBranch && (
                                        <div className="text-xs text-muted-foreground">Main Campus</div>
                                      )}
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="status"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Membership Status</FormLabel>
                          <Select 
                            onValueChange={field.onChange} 
                            defaultValue={field.value}
                            disabled={!isEditing}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="active">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                                  Active
                                </div>
                              </SelectItem>
                              <SelectItem value="inactive">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-yellow-500 rounded-full" />
                                  Inactive
                                </div>
                              </SelectItem>
                              <SelectItem value="suspended">
                                <div className="flex items-center gap-2">
                                  <div className="w-2 h-2 bg-red-500 rounded-full" />
                                  Suspended
                                </div>
                              </SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Save Button */}
              {isEditing && canManageMembers && (
                <div className="flex justify-end pt-6 border-t">
                  <Button type="submit" disabled={loading} size="lg" className="min-w-[140px]">
                    {loading ? (
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                        Saving...
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <Save className="h-4 w-4" />
                        Save Changes
                      </div>
                    )}
                  </Button>
                </div>
              )}
            </form>
          </Form>
        </TabsContent>

        {/* Roles & Permissions Tab */}
        <TabsContent value="roles" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Role Management
              </CardTitle>
              <CardDescription>
                Manage member roles and permissions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Current Role */}
              <div>
                <h3 className="text-lg font-medium mb-4">Current Role</h3>
                {member.role ? (
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5 text-blue-600" />
                      <div>
                        <div className="font-medium">{member.role.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {member.role.permissions?.length || 0} permissions
                        </div>
                      </div>
                    </div>
                    {canManageMembers && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleRoleChange(member.role!.id)}
                        disabled={loading}
                      >
                        Remove Role
                      </Button>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>No role assigned</p>
                  </div>
                )}
              </div>

              {/* Available Roles */}
              {canManageMembers && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Available Roles</h3>
                  <div className="grid gap-3">
                    {roles
                      .filter(role => role.id !== member.role?.id)
                      .map((role) => (
                        <div key={role.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex items-center gap-3">
                            <Settings className="h-5 w-5 text-gray-600" />
                            <div>
                              <div className="font-medium">{role.name}</div>
                              {role.description && (
                                <div className="text-sm text-muted-foreground">{role.description}</div>
                              )}
                              <div className="text-sm text-muted-foreground">
                                {role.permissions?.length || 0} permissions
                                {role.isSystem && (
                                  <Badge variant="outline" className="ml-2 text-xs">
                                    System Role
                                  </Badge>
                                )}
                              </div>
                            </div>
                          </div>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRoleChange(role.id)}
                            disabled={loading}
                          >
                            Assign Role
                          </Button>
                        </div>
                      ))}
                  </div>
                </div>
              )}

              {/* Permissions Display */}
              {member.role && member.role.permissions && member.role.permissions.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium mb-4">Current Permissions</h3>
                  <div className="grid gap-2 md:grid-cols-2">
                    {member.role.permissions.map((permission) => (
                      <div key={permission} className="flex items-center gap-2 p-2 bg-muted rounded">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">{permission.replace(/_/g, ' ').toLowerCase()}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Member Activity
              </CardTitle>
              <CardDescription>
                Recent activity and membership history
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-2 h-2 bg-green-500 rounded-full" />
                  <div className="flex-1">
                    <div className="font-medium">Member joined</div>
                    <div className="text-sm text-muted-foreground">
                      {member.createdAt ? new Date(member.createdAt).toLocaleDateString() : 'Unknown'}
                    </div>
                  </div>
                </div>
                
                {member.lastLoginAt && (
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <div className="w-2 h-2 bg-blue-500 rounded-full" />
                    <div className="flex-1">
                      <div className="font-medium">Last login</div>
                      <div className="text-sm text-muted-foreground">
                        {new Date(member.lastLoginAt).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                )}

                <div className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="w-2 h-2 bg-gray-500 rounded-full" />
                  <div className="flex-1">
                    <div className="font-medium">Email verification</div>
                    <div className="text-sm text-muted-foreground">
                      {member.isEmailVerified ? 'Verified' : 'Pending verification'}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function MemberProfilePage() {
  return (
    <DashboardLayout>
      <MemberProfileContent />
    </DashboardLayout>
  )
}