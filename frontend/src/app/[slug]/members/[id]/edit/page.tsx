'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Edit, UserX } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Member, Branch } from '@/types'
import { EditMemberDialog } from '@/components/forms/EditMemberDialog'
import { toast } from 'sonner'

function EditMemberContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const memberId = params.id as string
  
  const [member, setMember] = useState<Member | null>(null)
  const [branches, setBranches] = useState<Branch[]>([])
  const [loading, setLoading] = useState(true)

  // Check permissions
  const canManageMembers = hasPermission(user, PERMISSIONS.MANAGE_MEMBERS)

  const fetchData = async () => {
    if (!church?.slug || !memberId) return

    try {
      setLoading(true)
      
      // Fetch member and branches in parallel
      const [memberResponse, branchesResponse] = await Promise.all([
        apiClient.getMember(church.slug, memberId),
        apiClient.getBranches(church.slug)
      ])

      if (memberResponse.data && (memberResponse.data as any).member) {
        setMember((memberResponse.data as any).member as Member)
      }

      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast.error('Failed to load member details')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (canManageMembers && church?.slug && memberId) {
      fetchData()
    }
  }, [canManageMembers, church?.slug, memberId])

  const handleSuccess = () => {
    toast.success('Member updated successfully!')
    router.push(`/${church?.slug}/members/${memberId}`)
  }

  const handleBack = () => {
    router.push(`/${church?.slug}/members/${memberId}`)
  }

  if (!canManageMembers) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to edit church members. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading member details...</p>
        </div>
      </div>
    )
  }

  if (!member) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Edit className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Member Not Found</CardTitle>
            <CardDescription>
              The member you&apos;re trying to edit could not be found.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Member</h1>
          <p className="text-muted-foreground">
            Update {member.firstName} {member.lastName}&apos;s information
          </p>
        </div>
      </div>

      {/* Form Card */}
      <Card className="max-w-4xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5" />
            Member Information
          </CardTitle>
          <CardDescription>
            Update the member&apos;s details below. Required fields are marked with an asterisk (*).
          </CardDescription>
        </CardHeader>
        <CardContent>
          <EditMemberDialog 
            member={member}
            branches={branches}
            open={true}
            onOpenChange={() => {}} // Not used in page mode
            onSuccess={handleSuccess}
            isPageMode={true}
          />
        </CardContent>
      </Card>
    </div>
  )
}

export default function EditMemberPage() {
  return (
    <DashboardLayout>
      <EditMemberContent />
    </DashboardLayout>
  )
}