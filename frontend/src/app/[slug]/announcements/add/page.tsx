'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Checkbox } from '@/components/ui/checkbox'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { 
  ArrowLeft, 
  MessageSquare, 
  Save, 
  UserX, 
  Users, 
  Bell, 
  Pin, 
  X, 
  Settings, 
  Calendar,
  Target,
  FileText,
  Plus,
  AlertCircle,
  Clock,
  Eye,
  Send
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Branch, Event } from '@/types'
import { toast } from 'sonner'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'

// Form validation schema
const announcementFormSchema = z.object({
  title: z.string().min(1, 'Title is required').max(255, 'Title must be less than 255 characters'),
  summary: z.string().max(500, 'Summary must be less than 500 characters').optional(),
  content: z.string().min(1, 'Content is required'),
  type: z.string(),
  priority: z.string(),
  targetAudience: z.string(),
  targetBranches: z.array(z.string()).optional(),
  targetRoles: z.array(z.string()).optional(),
  eventId: z.string().optional(),
  imageUrl: z.string().url('Please enter a valid URL').or(z.literal('')).optional(),
  tags: z.array(z.string()),
  scheduledFor: z.string().optional(),
  expiresAt: z.string().optional(),
  isPinned: z.boolean(),
  allowComments: z.boolean(),
  requiresAcknowledgment: z.boolean(),
  sendNotification: z.boolean(),
  notificationChannels: z.object({
    email: z.boolean(),
    sms: z.boolean(),
    push: z.boolean(),
    inApp: z.boolean(),
  }),
}).refine((data) => {
  if (data.targetAudience === 'specific_branches' && (!data.targetBranches || data.targetBranches.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Please select at least one branch",
  path: ['targetBranches']
}).refine((data) => {
  if (data.targetAudience === 'specific_roles' && (!data.targetRoles || data.targetRoles.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Please select at least one role",
  path: ['targetRoles']
});

type AnnouncementFormData = z.infer<typeof announcementFormSchema>

const ANNOUNCEMENT_TYPES = [
  { value: 'general', label: 'General' },
  { value: 'urgent', label: 'Urgent' },
  { value: 'event_related', label: 'Event Related' },
  { value: 'service_update', label: 'Service Update' },
  { value: 'prayer_request', label: 'Prayer Request' },
  { value: 'community_news', label: 'Community News' },
  { value: 'financial', label: 'Financial' },
  { value: 'special_event', label: 'Special Event' },
  { value: 'ministry_update', label: 'Ministry Update' },
  { value: 'volunteer_opportunity', label: 'Volunteer Opportunity' },
  { value: 'other', label: 'Other' }
]

const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low', description: 'For general information and updates' },
  { value: 'normal', label: 'Normal', description: 'For regular church communications' },
  { value: 'high', label: 'High', description: 'For important information that needs attention' },
  { value: 'urgent', label: 'Urgent', description: 'For critical information requiring immediate attention' }
]

const TARGET_AUDIENCES = [
  { value: 'all_members', label: 'All Members', description: 'Send to all church members' },
  { value: 'specific_branches', label: 'Specific Branches', description: 'Send to selected branches only' },
  { value: 'specific_roles', label: 'Specific Roles', description: 'Send to members with specific roles' },
  { value: 'custom_group', label: 'Custom Group', description: 'Send to a custom group of members' }
]

const AVAILABLE_ROLES = [
  'Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member', 'Visitor'
]

function AddAnnouncementContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const churchSlug = params.slug as string
  
  const [branches, setBranches] = useState<Branch[]>([])
  const [events, setEvents] = useState<Event[]>([])
  const [newTag, setNewTag] = useState('')
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('content')

  // Form setup with react-hook-form and zod validation
  const form = useForm<AnnouncementFormData>({
    resolver: zodResolver(announcementFormSchema),
    defaultValues: {
      title: '',
      summary: '',
      content: '',
      type: 'general',
      priority: 'normal',
      targetAudience: 'all_members',
      targetBranches: [],
      targetRoles: [],
      eventId: '',
      imageUrl: '',
      tags: [],
      scheduledFor: '',
      expiresAt: '',
      isPinned: false,
      allowComments: true,
      requiresAcknowledgment: false,
      sendNotification: true,
      notificationChannels: {
        email: true,
        sms: false,
        push: true,
        inApp: true
      }
    }
  })

  // Check permissions
  const canManageAnnouncements = hasPermission(user, PERMISSIONS.MANAGE_ANNOUNCEMENTS)

  const fetchInitialData = useCallback(async () => {
    const slug = church?.slug || churchSlug
    if (!slug) return

    try {
      setInitialLoading(true)
      
      // Fetch branches and events in parallel
      const [branchesResponse, eventsResponse] = await Promise.all([
        apiClient.getBranches(slug, { limit: 100 }),
        apiClient.getEvents(slug, { limit: 100, status: 'published' })
      ])

      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }

      if (eventsResponse.data && (eventsResponse.data as any).events) {
        setEvents((eventsResponse.data as any).events)
      }
    } catch (error) {
      console.error('Error fetching initial data:', error)
      toast.error('Failed to load initial data')
    } finally {
      setInitialLoading(false)
    }
  }, [church?.slug, churchSlug])

  useEffect(() => {
    const slug = church?.slug || churchSlug
    if (canManageAnnouncements && slug) {
      fetchInitialData()
    }
  }, [canManageAnnouncements, church?.slug, churchSlug, fetchInitialData])

  const addTag = () => {
    const currentTags = form.getValues('tags')
    if (newTag.trim() && !currentTags.includes(newTag.trim()) && currentTags.length < 10) {
      form.setValue('tags', [...currentTags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    const currentTags = form.getValues('tags')
    form.setValue('tags', currentTags.filter(tag => tag !== tagToRemove))
  }

  const onSubmit = async (data: AnnouncementFormData) => {
    const slug = church?.slug || churchSlug
    if (!slug) return

    try {
      setLoading(true)

      // Prepare submission data - only include fields with actual values
      const submitData: any = {
        title: data.title.trim(),
        content: data.content.trim(),
        type: data.type,
        priority: data.priority,
        targetAudience: data.targetAudience,
        isPinned: data.isPinned,
        allowComments: data.allowComments,
        requiresAcknowledgment: data.requiresAcknowledgment,
        sendNotification: data.sendNotification,
        notificationChannels: data.notificationChannels
      }

      // Add optional fields only if they have values
      if (data.summary && data.summary.trim()) {
        submitData.summary = data.summary.trim()
      }
      
      if (data.targetBranches && data.targetBranches.length > 0) {
        submitData.targetBranches = data.targetBranches
      }
      
      if (data.targetRoles && data.targetRoles.length > 0) {
        submitData.targetRoles = data.targetRoles
      }
      
      if (data.eventId && data.eventId.trim()) {
        submitData.eventId = data.eventId.trim()
      }
      
      if (data.imageUrl && data.imageUrl.trim()) {
        submitData.imageUrl = data.imageUrl.trim()
      }
      
      if (data.tags.length > 0) {
        submitData.tags = data.tags
      }
      
      if (data.scheduledFor && data.scheduledFor.trim()) {
        const scheduledDate = new Date(data.scheduledFor)
        if (!isNaN(scheduledDate.getTime()) && scheduledDate > new Date()) {
          submitData.scheduledFor = scheduledDate.toISOString()
        }
      }
      
      if (data.expiresAt && data.expiresAt.trim()) {
        const expiryDate = new Date(data.expiresAt)
        const referenceDate = submitData.scheduledFor ? new Date(submitData.scheduledFor) : new Date()
        if (!isNaN(expiryDate.getTime()) && expiryDate > referenceDate) {
          submitData.expiresAt = expiryDate.toISOString()
        }
      }

      await apiClient.createAnnouncement(slug, submitData)
      
      toast.success('Announcement created successfully')
      router.push(`/${slug}/announcements`)
    } catch (error: any) {
      console.error('Error creating announcement:', error)
      toast.error(error.message || 'Failed to create announcement')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    const slug = church?.slug || churchSlug
    router.push(`/${slug}/announcements`)
  }

  if (!canManageAnnouncements) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to create announcements. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (initialLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading form data...</div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-8 p-6">
      {/* Modern Header with Progress Indicator */}
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={handleCancel} className="shrink-0">
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div className="min-w-0 flex-1">
            <h1 className="text-3xl font-bold tracking-tight">Create Announcement</h1>
            <p className="text-muted-foreground">
              Share important information with your church community
            </p>
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Eye className="h-4 w-4" />
            <span>Preview available after content</span>
          </div>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center gap-2 text-sm">
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${
            activeTab === 'content' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            <FileText className="h-3 w-3" />
            Content
          </div>
          <div className="w-8 h-px bg-border" />
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${
            activeTab === 'targeting' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            <Target className="h-3 w-3" />
            Targeting
          </div>
          <div className="w-8 h-px bg-border" />
          <div className={`flex items-center gap-2 px-3 py-1 rounded-full transition-colors ${
            activeTab === 'settings' ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
          }`}>
            <Settings className="h-3 w-3" />
            Settings
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content" className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Content
              </TabsTrigger>
              <TabsTrigger value="targeting" className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Targeting
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            {/* Content Tab */}
            <TabsContent value="content" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5" />
                    Announcement Content
                  </CardTitle>
                  <CardDescription>
                    Create compelling content that will engage your church community
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base font-medium">Title *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter a compelling announcement title"
                            className="text-lg"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Make it clear and attention-grabbing (max 255 characters)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Type and Priority Row */}
                  <div className="grid gap-6 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="type"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Announcement Type</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {ANNOUNCEMENT_TYPES.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Helps categorize your announcement
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="priority"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Priority Level</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select priority" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              {PRIORITY_LEVELS.map((priority) => (
                                <SelectItem key={priority.value} value={priority.value}>
                                  <div className="flex items-center gap-2">
                                    {priority.value === 'urgent' && <AlertCircle className="h-4 w-4 text-red-500" />}
                                    {priority.value === 'high' && <AlertCircle className="h-4 w-4 text-orange-500" />}
                                    <div>
                                      <div className="font-medium">{priority.label}</div>
                                      <div className="text-xs text-muted-foreground">{priority.description}</div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Summary */}
                  <FormField
                    control={form.control}
                    name="summary"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Summary (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Brief one-line summary for announcement lists"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Appears in announcement previews and notifications (max 500 characters)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Content */}
                  <FormField
                    control={form.control}
                    name="content"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base font-medium">Full Content *</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Write your full announcement content here. Be clear, informative, and engaging..."
                            className="min-h-[200px]"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          The main content that members will read. Use clear language and include all important details.
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Image URL */}
                  <FormField
                    control={form.control}
                    name="imageUrl"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Featured Image (Optional)</FormLabel>
                        <FormControl>
                          <Input
                            type="url"
                            placeholder="https://example.com/image.jpg"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          Add a visual element to make your announcement more engaging
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Event Association */}
                  {events.length > 0 && (
                    <FormField
                      control={form.control}
                      name="eventId"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Related Event (Optional)</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Link to an event" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="">No related event</SelectItem>
                              {events.map((event) => (
                                <SelectItem key={event.id} value={event.id}>
                                  <div className="flex items-center gap-2">
                                    <Calendar className="h-4 w-4" />
                                    <div>
                                      <div className="font-medium">{event.title}</div>
                                      <div className="text-xs text-muted-foreground">
                                        {new Date(event.startDate).toLocaleDateString()}
                                      </div>
                                    </div>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            Associate this announcement with an upcoming event
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Tags */}
                  <FormField
                    control={form.control}
                    name="tags"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Tags (Optional)</FormLabel>
                        <FormControl>
                          <div className="space-y-3">
                            <div className="flex gap-2">
                              <Input
                                value={newTag}
                                onChange={(e) => setNewTag(e.target.value)}
                                placeholder="Add a tag"
                                onKeyPress={(e) => {
                                  if (e.key === 'Enter') {
                                    e.preventDefault()
                                    addTag()
                                  }
                                }}
                              />
                              <Button 
                                type="button" 
                                onClick={addTag} 
                                disabled={!newTag.trim() || field.value.length >= 10}
                                variant="outline"
                              >
                                <Plus className="h-4 w-4" />
                              </Button>
                            </div>
                            {field.value.length > 0 && (
                              <div className="flex flex-wrap gap-2">
                                {field.value.map((tag) => (
                                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                                    {tag}
                                    <X
                                      className="h-3 w-3 cursor-pointer hover:text-destructive"
                                      onClick={() => removeTag(tag)}
                                    />
                                  </Badge>
                                ))}
                              </div>
                            )}
                          </div>
                        </FormControl>
                        <FormDescription>
                          Add up to 10 tags to help organize and filter announcements
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            {/* Targeting Tab */}
            <TabsContent value="targeting" className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Target Audience
                  </CardTitle>
                  <CardDescription>
                    Choose who should receive this announcement and control its distribution
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="targetAudience"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base font-medium">Audience Type</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select audience" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {TARGET_AUDIENCES.map((audience) => (
                              <SelectItem key={audience.value} value={audience.value}>
                                <div className="py-2">
                                  <div className="font-medium">{audience.label}</div>
                                  <div className="text-xs text-muted-foreground">{audience.description}</div>
                                </div>
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Branch Selection */}
                  {form.watch('targetAudience') === 'specific_branches' && (
                    <FormField
                      control={form.control}
                      name="targetBranches"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Branches</FormLabel>
                          <FormControl>
                            <div className="grid gap-3 mt-3">
                              {branches.map((branch) => (
                                <div key={branch.id} className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                                  <Checkbox
                                    id={`branch-${branch.id}`}
                                    checked={field.value?.includes(branch.id) || false}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || []
                                      if (checked) {
                                        field.onChange([...currentValue, branch.id])
                                      } else {
                                        field.onChange(currentValue.filter(id => id !== branch.id))
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`branch-${branch.id}`} className="flex-1 cursor-pointer">
                                    <div className="flex items-center justify-between">
                                      <span className="font-medium">{branch.name}</span>
                                      {branch.isMainBranch && (
                                        <Badge variant="outline" className="ml-2">Main Campus</Badge>
                                      )}
                                    </div>
                                    {branch.address && (
                                      <div className="text-sm text-muted-foreground">{branch.address}</div>
                                    )}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </FormControl>
                          <FormDescription>
                            Select which church branches should receive this announcement
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}

                  {/* Role Selection */}
                  {form.watch('targetAudience') === 'specific_roles' && (
                    <FormField
                      control={form.control}
                      name="targetRoles"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Select Roles</FormLabel>
                          <FormControl>
                            <div className="grid gap-3 mt-3 md:grid-cols-2">
                              {AVAILABLE_ROLES.map((role) => (
                                <div key={role} className="flex items-center space-x-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors">
                                  <Checkbox
                                    id={`role-${role}`}
                                    checked={field.value?.includes(role) || false}
                                    onCheckedChange={(checked) => {
                                      const currentValue = field.value || []
                                      if (checked) {
                                        field.onChange([...currentValue, role])
                                      } else {
                                        field.onChange(currentValue.filter(r => r !== role))
                                      }
                                    }}
                                  />
                                  <Label htmlFor={`role-${role}`} className="flex-1 cursor-pointer font-medium">
                                    {role}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </FormControl>
                          <FormDescription>
                            Select which member roles should receive this announcement
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              <div className="grid gap-6 lg:grid-cols-2">
                {/* Scheduling Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Clock className="h-5 w-5" />
                      Schedule & Timing
                    </CardTitle>
                    <CardDescription>
                      Control when your announcement is published and expires
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <FormField
                      control={form.control}
                      name="scheduledFor"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Schedule For (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="datetime-local"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Leave empty to publish immediately, or schedule for later
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="expiresAt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Expires At (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="datetime-local"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription>
                            Automatically archive this announcement after the specified date
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>

                {/* Announcement Options Card */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Announcement Options
                    </CardTitle>
                    <CardDescription>
                      Configure how members can interact with this announcement
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <FormField
                      control={form.control}
                      name="isPinned"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base flex items-center gap-2">
                              <Pin className="h-4 w-4" />
                              Pin Announcement
                            </FormLabel>
                            <FormDescription>
                              Pinned announcements appear at the top of the list
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="allowComments"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Allow Comments</FormLabel>
                            <FormDescription>
                              Let members comment and discuss this announcement
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="requiresAcknowledgment"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                          <div className="space-y-0.5">
                            <FormLabel className="text-base">Require Acknowledgment</FormLabel>
                            <FormDescription>
                              Members must acknowledge they&apos;ve read this announcement
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Notifications Card */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Bell className="h-5 w-5" />
                    Notification Settings
                  </CardTitle>
                  <CardDescription>
                    Control how members are notified about this announcement
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <FormField
                    control={form.control}
                    name="sendNotification"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base flex items-center gap-2">
                            <Send className="h-4 w-4" />
                            Send Notifications
                          </FormLabel>
                          <FormDescription>
                            Notify members when this announcement is published
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  {form.watch('sendNotification') && (
                    <div className="space-y-4 pl-6 border-l-2 border-muted">
                      <h4 className="font-medium text-sm">Notification Channels</h4>
                      <div className="grid gap-4 md:grid-cols-2">
                        <FormField
                          control={form.control}
                          name="notificationChannels.email"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <FormLabel className="text-sm font-medium">Email</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="notificationChannels.sms"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <FormLabel className="text-sm font-medium">SMS</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="notificationChannels.push"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <FormLabel className="text-sm font-medium">Push</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="notificationChannels.inApp"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                              <FormLabel className="text-sm font-medium">In-App</FormLabel>
                              <FormControl>
                                <Switch
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                            </FormItem>
                          )}
                        />
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {/* Enhanced Form Actions */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <Button type="submit" disabled={loading} className="sm:order-2 flex-1 sm:flex-none">
                  <Save className="h-4 w-4 mr-2" />
                  {loading ? 'Creating...' : 'Create Announcement'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleCancel}
                  className="sm:order-1"
                >
                  Cancel
                </Button>
                {/* Quick Actions */}
                <div className="sm:order-3 flex gap-2 sm:ml-auto">
                  <Button 
                    type="button" 
                    variant="ghost" 
                    size="sm"
                    onClick={() => form.reset()}
                    disabled={loading}
                  >
                    Reset Form
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>
    </div>
  )
}

export default function AddAnnouncementPage() {
  return (
    <DashboardLayout>
      <AddAnnouncementContent />
    </DashboardLayout>
  )
}