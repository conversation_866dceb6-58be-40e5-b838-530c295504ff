'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { 
  ArrowLeft, 
  MessageSquare, 
  Edit, 
  Calendar,
  User,
  UserX,
  Eye,
  Heart,
  ThumbsUp,
  Pin,
  Bell,
  Users,
  Send,
  Trash2,
  MoreHorizontal,
  AlertTriangle
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Announcement, AnnouncementComment } from '@/types'
import { toast } from 'sonner'

function AnnouncementDetailsContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const announcementId = params.id as string
  
  const [announcement, setAnnouncement] = useState<Announcement | null>(null)
  const [comments, setComments] = useState<AnnouncementComment[]>([])
  const [newComment, setNewComment] = useState('')
  const [loading, setLoading] = useState(true)
  const [commentLoading, setCommentLoading] = useState(false)

  // Check permissions
  const canManageAnnouncements = hasPermission(user, PERMISSIONS.MANAGE_ANNOUNCEMENTS)
  const canViewAnnouncements = true // All authenticated users can view announcements

  const fetchAnnouncement = async () => {
    if (!church?.slug || !announcementId) return

    try {
      setLoading(true)
      const response = await apiClient.getAnnouncement(church.slug, announcementId)
      
      if (response.data && (response.data as any).announcement) {
        const announcementData = (response.data as any).announcement
        setAnnouncement(announcementData)
        
        // Set comments from the announcement response if they exist
        if (announcementData.comments) {
          setComments(announcementData.comments)
        }
        
        // Mark as viewed - with explicit empty object
        try {
          await apiClient.markAnnouncementAsViewed(church.slug, announcementId, {})
        } catch (viewError) {
          console.warn('Failed to mark as viewed:', viewError)
          // Don't fail the whole operation if view tracking fails
        }
        
        // Load comments separately if they weren't included or if we need fresh data
        if (announcementData.allowComments && !announcementData.comments) {
          await fetchComments()
        }
      }
    } catch (error) {
      console.error('Error fetching announcement:', error)
      toast.error('Failed to load announcement details')
      router.push(`/${church.slug}/announcements`)
    } finally {
      setLoading(false)
    }
  }

  const fetchComments = async () => {
    if (!church?.slug || !announcementId) return

    try {
      const response = await apiClient.getAnnouncementComments(church.slug, announcementId)
      
      if (response.data && (response.data as any).comments) {
        setComments((response.data as any).comments)
      }
    } catch (error) {
      console.error('Error fetching comments:', error)
    }
  }

  useEffect(() => {
    if (canViewAnnouncements && church?.slug && announcementId) {
      fetchAnnouncement()
    }
  }, [canViewAnnouncements, church?.slug, announcementId])

  const handleReaction = async (reactionType: string) => {
    if (!church?.slug || !announcement) return

    try {
      await apiClient.createAnnouncementReaction(church.slug, announcement.id, { reactionType })
      toast.success('Reaction added')
      fetchAnnouncement() // Refresh to get updated stats
    } catch (error: any) {
      console.error('Error adding reaction:', error)
      toast.error(error.message || 'Failed to add reaction')
    }
  }

  const handleRemoveReaction = async () => {
    if (!church?.slug || !announcement) return

    try {
      await apiClient.deleteAnnouncementReaction(church.slug, announcement.id)
      toast.success('Reaction removed')
      fetchAnnouncement() // Refresh to get updated stats
    } catch (error: any) {
      console.error('Error removing reaction:', error)
      toast.error(error.message || 'Failed to remove reaction')
    }
  }

  const handleAddComment = async () => {
    if (!church?.slug || !announcement || !newComment.trim()) return

    try {
      setCommentLoading(true)
      await apiClient.createAnnouncementComment(church.slug, announcement.id, {
        content: newComment.trim()
      })
      
      setNewComment('')
      toast.success('Comment added')
      fetchComments()
      fetchAnnouncement() // Refresh to get updated comment count
    } catch (error: any) {
      console.error('Error adding comment:', error)
      toast.error(error.message || 'Failed to add comment')
    } finally {
      setCommentLoading(false)
    }
  }

  const handleAcknowledge = async () => {
    if (!church?.slug || !announcement) return

    try {
      await apiClient.markAnnouncementAsViewed(church.slug, announcement.id, { acknowledged: true })
      toast.success('Announcement acknowledged')
      fetchAnnouncement() // Refresh to get updated acknowledgment status
    } catch (error: any) {
      console.error('Error acknowledging announcement:', error)
      toast.error(error.message || 'Failed to acknowledge announcement')
    }
  }

  const handleEdit = () => {
    router.push(`/${church?.slug}/announcements/${announcementId}/edit`)
  }

  const handleBack = () => {
    router.push(`/${church?.slug}/announcements`)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'normal': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'archived': return 'bg-yellow-100 text-yellow-800'
      case 'expired': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!canViewAnnouncements) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to view announcements. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading announcement...</div>
      </div>
    )
  }

  if (!announcement) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Announcement Not Found</CardTitle>
            <CardDescription>
              The announcement you&apos;re looking for doesn&apos;t exist or has been removed.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {announcement.isPinned && <Pin className="h-6 w-6 text-blue-600" />}
              {announcement.type === 'urgent' && <AlertTriangle className="h-6 w-6 text-red-600" />}
              {announcement.title}
            </h1>
            <p className="text-muted-foreground">Announcement Details</p>
          </div>
        </div>
        {canManageAnnouncements && (
          <Button onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
        )}
      </div>

      <div className="grid gap-6 lg:grid-cols-3">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Announcement Content */}
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Badge className={getPriorityColor(announcement.priority)}>
                      {announcement.priority}
                    </Badge>
                    <Badge className={getStatusColor(announcement.status)}>
                      {announcement.status}
                    </Badge>
                    <Badge variant="outline">
                      {announcement.type.replace('_', ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center gap-2">
                      {announcement.author ? (
                        <>
                          <div className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs">
                            {announcement.author.firstName[0]}{announcement.author.lastName[0]}
                          </div>
                          <span>{announcement.author.firstName} {announcement.author.lastName}</span>
                        </>
                      ) : (
                        <>
                          <User className="h-4 w-4" />
                          <span>Unknown Author</span>
                        </>
                      )}
                    </div>
                    <span>•</span>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {announcement.publishedAt ? formatDate(announcement.publishedAt) : 
                       announcement.scheduledFor ? `Scheduled for ${formatDate(announcement.scheduledFor)}` : 
                       'Draft'}
                    </div>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {announcement.summary && (
                <div className="bg-muted/50 p-4 rounded-lg">
                  <p className="text-muted-foreground">{announcement.summary}</p>
                </div>
              )}

              <div className="prose max-w-none">
                <p className="whitespace-pre-wrap">{announcement.content}</p>
              </div>

              {announcement.imageUrl && (
                <div className="rounded-lg overflow-hidden">
                  <img 
                    src={announcement.imageUrl} 
                    alt="Announcement" 
                    className="w-full h-auto"
                  />
                </div>
              )}

              {announcement.tags && announcement.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {announcement.tags.map((tag) => (
                    <Badge key={tag} variant="secondary">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* External Links */}
              {announcement.externalLinks && announcement.externalLinks.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Related Links</h4>
                  {announcement.externalLinks.map((link, index) => (
                    <div key={index} className="border rounded-lg p-3">
                      <a 
                        href={link.url} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:underline font-medium"
                      >
                        {link.title}
                      </a>
                      {link.description && (
                        <p className="text-sm text-muted-foreground mt-1">{link.description}</p>
                      )}
                    </div>
                  ))}
                </div>
              )}

              <Separator />

              {/* Stats */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {announcement.stats?.viewCount || 0} views
                  </div>
                  {announcement.allowComments && (
                    <div className="flex items-center gap-1">
                      <MessageSquare className="h-4 w-4" />
                      {announcement.stats?.commentCount || 0} comments
                    </div>
                  )}
                  <div className="flex items-center gap-1">
                    <Heart className="h-4 w-4" />
                    {announcement.stats?.reactionCount || 0} reactions
                  </div>
                  {announcement.requiresAcknowledgment && (
                    <div className="flex items-center gap-1">
                      <Users className="h-4 w-4" />
                      {announcement.stats?.acknowledgmentCount || 0} acknowledged
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2">
                  {announcement.stats?.userReaction ? (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleRemoveReaction}
                      className="bg-blue-50 border-blue-200"
                    >
                      <Heart className="h-4 w-4 mr-1 text-blue-600" />
                      {announcement.stats.userReaction}
                    </Button>
                  ) : (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReaction('like')}
                    >
                      <Heart className="h-4 w-4 mr-1" />
                      Like
                    </Button>
                  )}

                  {announcement.requiresAcknowledgment && !announcement.stats?.isAcknowledged && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleAcknowledge}
                    >
                      <ThumbsUp className="h-4 w-4 mr-1" />
                      Acknowledge
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Comments Section */}
          {announcement.allowComments && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Comments ({comments.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add Comment */}
                <div className="space-y-2">
                  <Textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Add a comment..."
                    rows={3}
                  />
                  <Button 
                    onClick={handleAddComment} 
                    disabled={!newComment.trim() || commentLoading}
                    size="sm"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    {commentLoading ? 'Posting...' : 'Post Comment'}
                  </Button>
                </div>

                <Separator />

                {/* Comments List */}
                <div className="space-y-4">
                  {comments.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      No comments yet. Be the first to comment!
                    </div>
                  ) : (
                    comments.map((comment) => (
                      <div key={comment.id} className="space-y-2">
                        <div className="flex items-start gap-3">
                          <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm">
                            {comment.author.firstName[0]}{comment.author.lastName[0]}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium">{comment.author.firstName} {comment.author.lastName}</span>
                              <span className="text-xs text-muted-foreground">
                                {formatDate(comment.createdAt)}
                              </span>
                            </div>
                            <p className="text-sm whitespace-pre-wrap">{comment.content}</p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Details Card */}
          <Card>
            <CardHeader>
              <CardTitle>Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-muted-foreground">Target Audience</label>
                <div className="flex items-center gap-2 mt-1">
                  <Users className="h-4 w-4" />
                  <span className="capitalize">
                    {announcement.targetAudience.replace('_', ' ')}
                  </span>
                </div>
              </div>

              {announcement.event && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Related Event</label>
                  <div className="mt-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/${church?.slug}/events/${announcement.event?.id}`)}
                    >
                      {announcement.event.title}
                    </Button>
                  </div>
                </div>
              )}

              {announcement.scheduledFor && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Scheduled For</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(announcement.scheduledFor)}</span>
                  </div>
                </div>
              )}

              {announcement.expiresAt && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Expires At</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(announcement.expiresAt)}</span>
                  </div>
                </div>
              )}

              <div>
                <label className="text-sm font-medium text-muted-foreground">Created</label>
                <div className="flex items-center gap-2 mt-1">
                  <Calendar className="h-4 w-4" />
                  <span>{formatDate(announcement.createdAt)}</span>
                </div>
              </div>

              {announcement.updatedAt !== announcement.createdAt && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Last Updated</label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4" />
                    <span>{formatDate(announcement.updatedAt)}</span>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Settings Card */}
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Pinned</span>
                <Badge variant={announcement.isPinned ? "default" : "secondary"}>
                  {announcement.isPinned ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Comments</span>
                <Badge variant={announcement.allowComments ? "default" : "secondary"}>
                  {announcement.allowComments ? "Enabled" : "Disabled"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Acknowledgment</span>
                <Badge variant={announcement.requiresAcknowledgment ? "default" : "secondary"}>
                  {announcement.requiresAcknowledgment ? "Required" : "Optional"}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default function AnnouncementDetailsPage() {
  return (
    <DashboardLayout>
      <AnnouncementDetailsContent />
    </DashboardLayout>
  )
}