'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, MessageSquare, Save, UserX, Calendar, Users, Bell, Pin, X, Trash2 } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Announcement, Branch, Event } from '@/types'
import { toast } from 'sonner'

interface AnnouncementFormData {
  title: string
  summary: string
  content: string
  type: string
  priority: string
  status: string
  targetAudience: string
  targetBranches: string[]
  targetRoles: string[]
  eventId: string
  imageUrl: string
  tags: string[]
  scheduledFor: string
  expiresAt: string
  isPinned: boolean
  allowComments: boolean
  requiresAcknowledgment: boolean
  sendNotification: boolean
  notificationChannels: {
    email: boolean
    sms: boolean
    push: boolean
    inApp: boolean
  }
}

const ANNOUNCEMENT_TYPES = [
  { value: 'general', label: 'General' },
  { value: 'urgent', label: 'Urgent' },
  { value: 'event_related', label: 'Event Related' },
  { value: 'service_update', label: 'Service Update' },
  { value: 'prayer_request', label: 'Prayer Request' },
  { value: 'community_news', label: 'Community News' },
  { value: 'financial', label: 'Financial' },
  { value: 'special_event', label: 'Special Event' },
  { value: 'ministry_update', label: 'Ministry Update' },
  { value: 'volunteer_opportunity', label: 'Volunteer Opportunity' },
  { value: 'other', label: 'Other' }
]

const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low', description: 'For general information and updates' },
  { value: 'normal', label: 'Normal', description: 'For regular church communications' },
  { value: 'high', label: 'High', description: 'For important information that needs attention' },
  { value: 'urgent', label: 'Urgent', description: 'For critical information requiring immediate attention' }
]

const STATUS_OPTIONS = [
  { value: 'draft', label: 'Draft', description: 'Save as draft (not visible to members)' },
  { value: 'published', label: 'Published', description: 'Publish immediately (visible to members)' },
  { value: 'scheduled', label: 'Scheduled', description: 'Schedule for later publication' },
  { value: 'archived', label: 'Archived', description: 'Archive this announcement' }
]

const TARGET_AUDIENCES = [
  { value: 'all_members', label: 'All Members', description: 'Send to all church members' },
  { value: 'specific_branches', label: 'Specific Branches', description: 'Send to selected branches only' },
  { value: 'specific_roles', label: 'Specific Roles', description: 'Send to members with specific roles' },
  { value: 'custom_group', label: 'Custom Group', description: 'Send to a custom group of members' }
]

const AVAILABLE_ROLES = [
  'Super Admin', 'Pastor', 'Elder', 'Deacon', 'Member', 'Visitor'
]

function EditAnnouncementContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const announcementId = params.id as string
  
  const [originalAnnouncement, setOriginalAnnouncement] = useState<Announcement | null>(null)
  const [formData, setFormData] = useState<AnnouncementFormData>({
    title: '',
    summary: '',
    content: '',
    type: 'general',
    priority: 'normal',
    status: 'draft',
    targetAudience: 'all_members',
    targetBranches: [],
    targetRoles: [],
    eventId: '',
    imageUrl: '',
    tags: [],
    scheduledFor: '',
    expiresAt: '',
    isPinned: false,
    allowComments: true,
    requiresAcknowledgment: false,
    sendNotification: true,
    notificationChannels: {
      email: true,
      sms: false,
      push: true,
      inApp: true
    }
  })
  const [branches, setBranches] = useState<Branch[]>([])
  const [events, setEvents] = useState<Event[]>([])
  const [newTag, setNewTag] = useState('')
  const [loading, setLoading] = useState(false)
  const [initialLoading, setInitialLoading] = useState(true)

  // Check permissions
  const canManageAnnouncements = hasPermission(user, PERMISSIONS.MANAGE_ANNOUNCEMENTS)

  const fetchAnnouncement = async () => {
    if (!church?.slug || !announcementId) return

    try {
      const response = await apiClient.getAnnouncement(church.slug, announcementId)
      
      if (response.data && (response.data as any).announcement) {
        const announcement = (response.data as any).announcement
        setOriginalAnnouncement(announcement)
        
        // Convert dates to datetime-local format
        const scheduledFor = announcement.scheduledFor ? 
          new Date(announcement.scheduledFor).toISOString().slice(0, 16) : ''
        const expiresAt = announcement.expiresAt ? 
          new Date(announcement.expiresAt).toISOString().slice(0, 16) : ''
        
        // Populate form with existing data
        setFormData({
          title: announcement.title || '',
          summary: announcement.summary || '',
          content: announcement.content || '',
          type: announcement.type || 'general',
          priority: announcement.priority || 'normal',
          status: announcement.status || 'draft',
          targetAudience: announcement.targetAudience || 'all_members',
          targetBranches: announcement.targetBranches || [],
          targetRoles: announcement.targetRoles || [],
          eventId: announcement.eventId || '',
          imageUrl: announcement.imageUrl || '',
          tags: announcement.tags || [],
          scheduledFor,
          expiresAt,
          isPinned: announcement.isPinned || false,
          allowComments: announcement.allowComments !== false,
          requiresAcknowledgment: announcement.requiresAcknowledgment || false,
          sendNotification: announcement.sendNotification !== false,
          notificationChannels: {
            email: announcement.notificationChannels?.email !== false,
            sms: announcement.notificationChannels?.sms || false,
            push: announcement.notificationChannels?.push !== false,
            inApp: announcement.notificationChannels?.inApp !== false
          }
        })
      }
    } catch (error) {
      console.error('Error fetching announcement:', error)
      toast.error('Failed to load announcement details')
      router.push(`/${church.slug}/announcements`)
    } finally {
      setInitialLoading(false)
    }
  }

  const fetchInitialData = async () => {
    if (!church?.slug) return

    try {
      // Fetch branches and events in parallel
      const [branchesResponse, eventsResponse] = await Promise.all([
        apiClient.getBranches(church.slug, { limit: 100 }),
        apiClient.getEvents(church.slug, { limit: 100, status: 'published' })
      ])

      if (branchesResponse.data && (branchesResponse.data as any).branches) {
        setBranches((branchesResponse.data as any).branches)
      }

      if (eventsResponse.data && (eventsResponse.data as any).events) {
        setEvents((eventsResponse.data as any).events)
      }
    } catch (error) {
      console.error('Error fetching initial data:', error)
      toast.error('Failed to load initial data')
    }
  }

  useEffect(() => {
    if (canManageAnnouncements && church?.slug && announcementId) {
      fetchAnnouncement()
      fetchInitialData()
    }
  }, [canManageAnnouncements, church?.slug, announcementId])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleNestedInputChange = (parent: string, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [parent]: {
        ...(prev as any)[parent],
        [field]: value
      }
    }))
  }

  const handleArrayToggle = (field: 'targetBranches' | 'targetRoles', value: string) => {
    setFormData(prev => {
      const currentArray = prev[field]
      const newArray = currentArray.includes(value)
        ? currentArray.filter(item => item !== value)
        : [...currentArray, value]
      
      return { ...prev, [field]: newArray }
    })
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim()) && formData.tags.length < 10) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!church?.slug || !announcementId) return

    try {
      setLoading(true)

      // Validate required fields
      if (!formData.title.trim()) {
        toast.error('Title is required')
        return
      }

      if (!formData.content.trim()) {
        toast.error('Content is required')
        return
      }

      // Validate target audience requirements
      if (formData.targetAudience === 'specific_branches' && formData.targetBranches.length === 0) {
        toast.error('Please select at least one branch for specific branch targeting')
        return
      }

      if (formData.targetAudience === 'specific_roles' && formData.targetRoles.length === 0) {
        toast.error('Please select at least one role for specific role targeting')
        return
      }

      // Prepare submission data
      const submitData: any = {
        title: formData.title.trim(),
        content: formData.content.trim(),
        summary: formData.summary.trim() || undefined,
        type: formData.type,
        priority: formData.priority,
        status: formData.status,
        targetAudience: formData.targetAudience,
        isPinned: formData.isPinned,
        allowComments: formData.allowComments,
        requiresAcknowledgment: formData.requiresAcknowledgment,
        sendNotification: formData.sendNotification,
        notificationChannels: formData.notificationChannels
      }

      // Add optional fields
      if (formData.targetBranches.length > 0) {
        submitData.targetBranches = formData.targetBranches
      }
      if (formData.targetRoles.length > 0) {
        submitData.targetRoles = formData.targetRoles
      }
      if (formData.eventId) {
        submitData.eventId = formData.eventId
      }
      if (formData.imageUrl.trim()) {
        submitData.imageUrl = formData.imageUrl.trim()
      }
      if (formData.tags.length > 0) {
        submitData.tags = formData.tags
      }
      if (formData.scheduledFor) {
        submitData.scheduledFor = new Date(formData.scheduledFor).toISOString()
      }
      if (formData.expiresAt) {
        submitData.expiresAt = new Date(formData.expiresAt).toISOString()
      }

      await apiClient.updateAnnouncement(church.slug, announcementId, submitData)
      
      toast.success('Announcement updated successfully')
      router.push(`/${church.slug}/announcements/${announcementId}`)
    } catch (error: any) {
      console.error('Error updating announcement:', error)
      toast.error(error.message || 'Failed to update announcement')
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!church?.slug || !announcementId || !originalAnnouncement) return

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${originalAnnouncement.title}"? This action cannot be undone.`
    )

    if (!confirmDelete) return

    try {
      setLoading(true)
      await apiClient.deleteAnnouncement(church.slug, announcementId)
      
      toast.success('Announcement deleted successfully')
      router.push(`/${church.slug}/announcements`)
    } catch (error: any) {
      console.error('Error deleting announcement:', error)
      toast.error(error.message || 'Failed to delete announcement')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    router.push(`/${church?.slug}/announcements/${announcementId}`)
  }

  if (!canManageAnnouncements) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to edit announcements. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (initialLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading announcement...</div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Announcement</h1>
          <p className="text-muted-foreground">
            Update announcement details for {originalAnnouncement?.title}
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Update the main details for your announcement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="title">Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter announcement title"
                required
              />
            </div>

            <div>
              <Label htmlFor="summary">Summary (Optional)</Label>
              <Input
                id="summary"
                value={formData.summary}
                onChange={(e) => handleInputChange('summary', e.target.value)}
                placeholder="Brief summary that appears in listings"
                maxLength={500}
              />
              <p className="text-xs text-muted-foreground mt-1">
                Optional short description shown in announcement lists
              </p>
            </div>

            <div>
              <Label htmlFor="content">Content *</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => handleInputChange('content', e.target.value)}
                placeholder="Enter the full announcement content"
                rows={6}
                required
              />
            </div>

            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <Label htmlFor="type">Type</Label>
                <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {ANNOUNCEMENT_TYPES.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="priority">Priority</Label>
                <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {PRIORITY_LEVELS.map((priority) => (
                      <SelectItem key={priority.value} value={priority.value}>
                        <div>
                          <div className="font-medium">{priority.label}</div>
                          <div className="text-xs text-muted-foreground">{priority.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {STATUS_OPTIONS.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        <div>
                          <div className="font-medium">{status.label}</div>
                          <div className="text-xs text-muted-foreground">{status.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <Label htmlFor="imageUrl">Image URL (Optional)</Label>
              <Input
                id="imageUrl"
                type="url"
                value={formData.imageUrl}
                onChange={(e) => handleInputChange('imageUrl', e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </CardContent>
        </Card>

        {/* Targeting */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Target Audience
            </CardTitle>
            <CardDescription>
              Choose who should receive this announcement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="targetAudience">Audience Type</Label>
              <Select value={formData.targetAudience} onValueChange={(value) => handleInputChange('targetAudience', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {TARGET_AUDIENCES.map((audience) => (
                    <SelectItem key={audience.value} value={audience.value}>
                      <div>
                        <div className="font-medium">{audience.label}</div>
                        <div className="text-xs text-muted-foreground">{audience.description}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {formData.targetAudience === 'specific_branches' && (
              <div>
                <Label>Select Branches</Label>
                <div className="grid gap-2 mt-2">
                  {branches.map((branch) => (
                    <div key={branch.id} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`branch-${branch.id}`}
                        checked={formData.targetBranches.includes(branch.id)}
                        onChange={() => handleArrayToggle('targetBranches', branch.id)}
                        className="rounded"
                      />
                      <Label htmlFor={`branch-${branch.id}`} className="flex-1">
                        {branch.name} {branch.isMainBranch && <Badge variant="secondary" className="ml-2">Main</Badge>}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {formData.targetAudience === 'specific_roles' && (
              <div>
                <Label>Select Roles</Label>
                <div className="grid gap-2 mt-2">
                  {AVAILABLE_ROLES.map((role) => (
                    <div key={role} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id={`role-${role}`}
                        checked={formData.targetRoles.includes(role)}
                        onChange={() => handleArrayToggle('targetRoles', role)}
                        className="rounded"
                      />
                      <Label htmlFor={`role-${role}`} className="flex-1">
                        {role}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Additional Options */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Options</CardTitle>
            <CardDescription>
              Configure additional settings for your announcement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Event Association */}
            {events.length > 0 && (
              <div>
                <Label htmlFor="eventId">Related Event (Optional)</Label>
                <Select value={formData.eventId} onValueChange={(value) => handleInputChange('eventId', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a related event" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No related event</SelectItem>
                    {events.map((event) => (
                      <SelectItem key={event.id} value={event.id}>
                        {event.title} - {new Date(event.startDate).toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {/* Tags */}
            <div>
              <Label>Tags (Optional)</Label>
              <div className="space-y-2">
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder="Add a tag"
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault()
                        addTag()
                      }
                    }}
                  />
                  <Button type="button" onClick={addTag} disabled={!newTag.trim() || formData.tags.length >= 10}>
                    Add
                  </Button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <X
                          className="h-3 w-3 cursor-pointer"
                          onClick={() => removeTag(tag)}
                        />
                      </Badge>
                    ))}
                  </div>
                )}
                <p className="text-xs text-muted-foreground">
                  Add up to 10 tags to help categorize your announcement
                </p>
              </div>
            </div>

            {/* Scheduling */}
            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <Label htmlFor="scheduledFor">Schedule For (Optional)</Label>
                <Input
                  id="scheduledFor"
                  type="datetime-local"
                  value={formData.scheduledFor}
                  onChange={(e) => handleInputChange('scheduledFor', e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Leave empty to publish immediately
                </p>
              </div>

              <div>
                <Label htmlFor="expiresAt">Expires At (Optional)</Label>
                <Input
                  id="expiresAt"
                  type="datetime-local"
                  value={formData.expiresAt}
                  onChange={(e) => handleInputChange('expiresAt', e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Auto-archive after this date
                </p>
              </div>
            </div>

            <Separator />

            {/* Settings */}
            <div className="space-y-4">
              <h4 className="font-medium">Announcement Settings</h4>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="isPinned" className="flex items-center gap-2">
                      <Pin className="h-4 w-4" />
                      Pin Announcement
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Pinned announcements appear at the top of the list
                    </p>
                  </div>
                  <Switch
                    id="isPinned"
                    checked={formData.isPinned}
                    onCheckedChange={(checked) => handleInputChange('isPinned', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="allowComments">Allow Comments</Label>
                    <p className="text-sm text-muted-foreground">
                      Let members comment on this announcement
                    </p>
                  </div>
                  <Switch
                    id="allowComments"
                    checked={formData.allowComments}
                    onCheckedChange={(checked) => handleInputChange('allowComments', checked)}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="requiresAcknowledgment">Require Acknowledgment</Label>
                    <p className="text-sm text-muted-foreground">
                      Members must acknowledge they&apos;ve read this announcement
                    </p>
                  </div>
                  <Switch
                    id="requiresAcknowledgment"
                    checked={formData.requiresAcknowledgment}
                    onCheckedChange={(checked) => handleInputChange('requiresAcknowledgment', checked)}
                  />
                </div>
              </div>
            </div>

            <Separator />

            {/* Notification Settings */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="sendNotification" className="flex items-center gap-2">
                    <Bell className="h-4 w-4" />
                    Send Notifications
                  </Label>
                  <p className="text-sm text-muted-foreground">
                    Notify members when this announcement is published
                  </p>
                </div>
                <Switch
                  id="sendNotification"
                  checked={formData.sendNotification}
                  onCheckedChange={(checked) => handleInputChange('sendNotification', checked)}
                />
              </div>

              {formData.sendNotification && (
                <div className="ml-6 space-y-3">
                  <h5 className="font-medium text-sm">Notification Channels</h5>
                  <div className="space-y-2">
                    {Object.entries(formData.notificationChannels).map(([channel, enabled]) => (
                      <div key={channel} className="flex items-center justify-between">
                        <Label htmlFor={`notify-${channel}`} className="capitalize">
                          {channel === 'inApp' ? 'In-App' : channel}
                        </Label>
                        <Switch
                          id={`notify-${channel}`}
                          checked={enabled}
                          onCheckedChange={(checked) => handleNestedInputChange('notificationChannels', channel, checked)}
                        />
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Form Actions */}
        <div className="flex gap-4 pt-6 border-t">
          <Button type="submit" disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Saving...' : 'Save Changes'}
          </Button>
          <Button type="button" variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            type="button" 
            variant="destructive"
            onClick={handleDelete}
            disabled={loading}
            className="ml-auto"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete Announcement
          </Button>
        </div>
      </form>
    </div>
  )
}

export default function EditAnnouncementPage() {
  return (
    <DashboardLayout>
      <EditAnnouncementContent />
    </DashboardLayout>
  )
}