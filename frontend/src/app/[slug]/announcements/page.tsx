'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  MessageSquare, 
  Search, 
  Plus, 
  Eye, 
  Heart, 
  Pin, 
  Edit,
  Trash2,
  Bell,
  Clock,
  User,
  UserX,
  ChevronLeft,
  ChevronRight,
  Calendar,
  AlertTriangle
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Announcement } from '@/types'
import { toast } from 'sonner'

interface AnnouncementStats {
  totalAnnouncements: number
  publishedAnnouncements: number
  pinnedAnnouncements: number
  draftAnnouncements: number
}

const ANNOUNCEMENT_TYPES = [
  { value: 'general', label: 'General' },
  { value: 'urgent', label: 'Urgent' },
  { value: 'event_related', label: 'Event Related' },
  { value: 'service_update', label: 'Service Update' },
  { value: 'prayer_request', label: 'Prayer Request' },
  { value: 'community_news', label: 'Community News' },
  { value: 'financial', label: 'Financial' },
  { value: 'special_event', label: 'Special Event' },
  { value: 'ministry_update', label: 'Ministry Update' },
  { value: 'volunteer_opportunity', label: 'Volunteer Opportunity' },
  { value: 'other', label: 'Other' }
]

const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low' },
  { value: 'normal', label: 'Normal' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
]

const STATUS_OPTIONS = [
  { value: 'published', label: 'Published' },
  { value: 'draft', label: 'Draft' },
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'archived', label: 'Archived' },
  { value: 'expired', label: 'Expired' }
]

function AnnouncementsContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const churchSlug = params.slug as string
  const [announcements, setAnnouncements] = useState<Announcement[]>([])
  const [stats, setStats] = useState<AnnouncementStats>({
    totalAnnouncements: 0,
    publishedAnnouncements: 0,
    pinnedAnnouncements: 0,
    draftAnnouncements: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState<string>('')
  const [selectedStatus, setSelectedStatus] = useState<string>('')
  const [selectedPriority, setSelectedPriority] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Check permissions
  const canManageAnnouncements = hasPermission(user, PERMISSIONS.MANAGE_ANNOUNCEMENTS)
  const canViewAnnouncements = true // All authenticated users can view announcements

  const fetchAnnouncements = async () => {
    if (!churchSlug || !canViewAnnouncements) return

    try {
      setLoading(true)
      const requestParams: Record<string, string | number> = {
        page: currentPage,
        limit: 10,
        ...(searchTerm && { search: searchTerm }),
        ...(selectedType && { type: selectedType }),
        ...(selectedStatus && { status: selectedStatus }),
        ...(selectedPriority && { priority: selectedPriority })
      }

      const response = await apiClient.getAnnouncements(churchSlug, requestParams)
      
      if (response.data && (response.data as Record<string, unknown>).announcements) {
        const announcementsData = (response.data as Record<string, unknown>).announcements as Announcement[]
        const paginationData = (response.data as Record<string, unknown>).pagination as any
        
        setAnnouncements(announcementsData)
        if (paginationData) {
          setTotalPages(paginationData.pages || 1)
        }

        // Calculate stats
        const publishedCount = announcementsData.filter(a => a.status === 'published').length
        const pinnedCount = announcementsData.filter(a => a.isPinned).length
        const draftCount = announcementsData.filter(a => a.status === 'draft').length

        setStats({
          totalAnnouncements: announcementsData.length,
          publishedAnnouncements: publishedCount,
          pinnedAnnouncements: pinnedCount,
          draftAnnouncements: draftCount
        })
      }
    } catch (error) {
      console.error('Error fetching announcements:', error)
      toast.error('Failed to load announcements')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnnouncements()
  }, [canViewAnnouncements, churchSlug, currentPage, searchTerm, selectedType, selectedStatus, selectedPriority])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleFilterChange = (type: string, value: string) => {
    setCurrentPage(1)
    switch (type) {
      case 'type':
        setSelectedType(value === 'all' ? '' : value)
        break
      case 'status':
        setSelectedStatus(value === 'all' ? '' : value)
        break
      case 'priority':
        setSelectedPriority(value === 'all' ? '' : value)
        break
    }
  }

  const handleDeleteAnnouncement = async (announcement: Announcement) => {
    if (!churchSlug) return

    const confirmDelete = window.confirm(
      `Are you sure you want to delete "${announcement.title}"? This action cannot be undone.`
    )

    if (!confirmDelete) return

    try {
      await apiClient.deleteAnnouncement(churchSlug, announcement.id)
      toast.success('Announcement deleted successfully')
      fetchAnnouncements()
    } catch (error: any) {
      console.error('Error deleting announcement:', error)
      toast.error(error.message || 'Failed to delete announcement')
    }
  }

  const handleEditAnnouncement = (announcement: Announcement) => {
    router.push(`/${churchSlug}/announcements/${announcement.id}/edit`)
  }

  const handleViewAnnouncement = (announcement: Announcement) => {
    router.push(`/${churchSlug}/announcements/${announcement.id}`)
  }

  const handleAddAnnouncement = () => {
    router.push(`/${churchSlug}/announcements/add`)
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'normal': return 'bg-blue-100 text-blue-800'
      case 'low': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      case 'scheduled': return 'bg-blue-100 text-blue-800'
      case 'archived': return 'bg-yellow-100 text-yellow-800'
      case 'expired': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (!canViewAnnouncements) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to view announcements. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Announcements</h1>
          <p className="text-muted-foreground">
            View and manage church announcements
          </p>
        </div>
        {canManageAnnouncements && (
          <Button onClick={handleAddAnnouncement}>
            <Plus className="h-4 w-4 mr-2" />
            New Announcement
          </Button>
        )}
      </div>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Announcements</CardTitle>
            <MessageSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAnnouncements}</div>
            <p className="text-xs text-muted-foreground">All announcements</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Published</CardTitle>
            <Bell className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.publishedAnnouncements}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalAnnouncements > 0 ? Math.round((stats.publishedAnnouncements / stats.totalAnnouncements) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pinned</CardTitle>
            <Pin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pinnedAnnouncements}</div>
            <p className="text-xs text-muted-foreground">Important notices</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Drafts</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.draftAnnouncements}</div>
            <p className="text-xs text-muted-foreground">Unpublished</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Announcements</CardTitle>
          <CardDescription>
            Search and filter church announcements
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 mb-6">
            {/* Search Bar */}
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input 
                    placeholder="Search announcements by title or content..." 
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <Select value={selectedType || 'all'} onValueChange={(value) => handleFilterChange('type', value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Types" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {ANNOUNCEMENT_TYPES.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedStatus || 'all'} onValueChange={(value) => handleFilterChange('status', value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  {STATUS_OPTIONS.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={selectedPriority || 'all'} onValueChange={(value) => handleFilterChange('priority', value)}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priority</SelectItem>
                  {PRIORITY_LEVELS.map((priority) => (
                    <SelectItem key={priority.value} value={priority.value}>
                      {priority.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Announcements Table */}
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="text-muted-foreground">Loading announcements...</div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Announcement</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Priority</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Author</TableHead>
                    <TableHead>Published</TableHead>
                    <TableHead>Stats</TableHead>
                    {canManageAnnouncements && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {announcements.map((announcement) => (
                    <TableRow key={announcement.id}>
                      <TableCell>
                        <div className="flex items-start gap-3">
                          <div className="flex-1">
                            <div className="font-medium flex items-center gap-2">
                              {announcement.isPinned && <Pin className="h-3 w-3 text-blue-600" />}
                              {announcement.type === 'urgent' && <AlertTriangle className="h-3 w-3 text-red-600" />}
                              {announcement.title}
                            </div>
                            {announcement.summary && (
                              <div className="text-sm text-muted-foreground mt-1 line-clamp-2">
                                {announcement.summary}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {ANNOUNCEMENT_TYPES.find(t => t.value === announcement.type)?.label || announcement.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getPriorityColor(announcement.priority)}>
                          {announcement.priority}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusColor(announcement.status)}>
                          {announcement.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="h-6 w-6 rounded-full bg-secondary text-secondary-foreground flex items-center justify-center text-xs">
                            {announcement.author ? `${announcement.author.firstName[0]}${announcement.author.lastName[0]}` : <User className="h-3 w-3" />}
                          </div>
                          <span className="text-sm">
                            {announcement.author ? `${announcement.author.firstName} ${announcement.author.lastName}` : 'Unknown'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <Calendar className="h-3 w-3" />
                          {announcement.publishedAt ? formatDate(announcement.publishedAt) : 
                           announcement.scheduledFor ? `Scheduled for ${formatDate(announcement.scheduledFor)}` : 
                           'Draft'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-xs text-muted-foreground">
                          <div className="flex items-center gap-1">
                            <Eye className="h-3 w-3" />
                            {announcement.stats?.viewCount || 0} views
                          </div>
                          {announcement.allowComments && (
                            <div className="flex items-center gap-1">
                              <MessageSquare className="h-3 w-3" />
                              {announcement.stats?.commentCount || 0} comments
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Heart className="h-3 w-3" />
                            {announcement.stats?.reactionCount || 0} reactions
                          </div>
                        </div>
                      </TableCell>
                      {canManageAnnouncements && (
                        <TableCell>
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => handleViewAnnouncement(announcement)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => handleEditAnnouncement(announcement)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => handleDeleteAnnouncement(announcement)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              {announcements.length === 0 && (
                <div className="text-center py-8">
                  <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No announcements found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchTerm || selectedType || selectedStatus || selectedPriority
                      ? 'Try adjusting your filters to see more announcements.'
                      : 'Create your first announcement to get started.'
                    }
                  </p>
                  {!searchTerm && !selectedType && !selectedStatus && !selectedPriority && canManageAnnouncements && (
                    <Button onClick={handleAddAnnouncement}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Announcement
                    </Button>
                  )}
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function AnnouncementsPage() {
  return (
    <DashboardLayout>
      <AnnouncementsContent />
    </DashboardLayout>
  )
}