'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/lib/auth';
import { formatCurrency } from '@/lib/currency';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Tabs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Users, 
  Calendar, 
  DollarSign, 
  Building2,
  Activity,
  Coins
} from 'lucide-react';

interface DashboardOverview {
  totalMembers: number;
  totalEvents: number;
  totalDonations: number;
  totalRevenue: number;
  memberGrowthRate: number;
  eventAttendanceRate: number;
  donationGrowthRate: number;
  recentActivity: Array<{
    type: string;
    title: string;
    description: string;
    timestamp: string;
  }>;
}

interface MemberAnalytics {
  totalMembers: number;
  membersByStatus: Array<{
    status: string;
    count: number;
    percentage: number;
  }>;
  membersByGender: Array<{
    gender: string;
    count: number;
    percentage: number;
  }>;
  membersByBranch: Array<{
    branchId: string | null;
    branchName: string;
    count: number;
    percentage: number;
  }>;
}

interface EventAnalytics {
  totalEvents: number;
  eventsByType: Array<{
    type: string;
    count: number;
    percentage: number;
    averageAttendance: number;
  }>;
  upcomingEvents: Array<{
    id: string;
    title: string;
    type: string;
    startDate: string;
    rsvpCount: number;
  }>;
}

interface FinancialAnalytics {
  totalRevenue: number;
  donationsByType: Array<{
    type: string;
    count: number;
    totalAmount: number;
    percentage: number;
  }>;
  topDonors: Array<{
    donorId: string | null;
    donorName: string;
    totalDonations: number;
    donationCount: number;
    isAnonymous: boolean;
  }>;
}

interface BranchAnalytics {
  totalBranches: number;
  branchComparison: Array<{
    branchId: string;
    branchName: string;
    memberCount: number;
    eventCount: number;
    totalDonations: number;
  }>;
}

export default function AnalyticsPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { church } = useAuth();
  
  const [period, setPeriod] = useState('30d');
  const [loading, setLoading] = useState(true);
  const [overview, setOverview] = useState<DashboardOverview | null>(null);
  const [memberAnalytics, setMemberAnalytics] = useState<MemberAnalytics | null>(null);
  const [eventAnalytics, setEventAnalytics] = useState<EventAnalytics | null>(null);
  const [financialAnalytics, setFinancialAnalytics] = useState<FinancialAnalytics | null>(null);
  const [branchAnalytics, setBranchAnalytics] = useState<BranchAnalytics | null>(null);

  useEffect(() => {
    fetchAnalytics();
  }, [period, slug]); // eslint-disable-line react-hooks/exhaustive-deps

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      
      const [overviewRes, memberRes, eventRes, financialRes, branchRes] = await Promise.all([
        fetch(`/api/churches/${slug}/analytics/dashboard?period=${period}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch(`/api/churches/${slug}/analytics/members?period=${period}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch(`/api/churches/${slug}/analytics/events?period=${period}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch(`/api/churches/${slug}/analytics/financial?period=${period}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        }),
        fetch(`/api/churches/${slug}/analytics/branches?period=${period}`, {
          headers: { 'Authorization': `Bearer ${token}` }
        })
      ]);

      if (overviewRes.ok) {
        const data = await overviewRes.json();
        setOverview(data.data.overview);
      }

      if (memberRes.ok) {
        const data = await memberRes.json();
        setMemberAnalytics(data.data.analytics);
      }

      if (eventRes.ok) {
        const data = await eventRes.json();
        setEventAnalytics(data.data.analytics);
      }

      if (financialRes.ok) {
        const data = await financialRes.json();
        setFinancialAnalytics(data.data.analytics);
      }

      if (branchRes.ok) {
        const data = await branchRes.json();
        setBranchAnalytics(data.data.analytics);
      }
    } catch (error) {
      console.error('Failed to fetch analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrencyForChurch = (amount: number) => {
    return formatCurrency(amount, church?.defaultCurrency || 'USD');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h1 className="text-3xl font-bold">Analytics</h1>
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid gap-4 grid-cols-1 [grid-template-columns:repeat(auto-fit,minmax(220px,1fr))]">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-32" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Analytics</h1>
        <Select value={period} onValueChange={setPeriod}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
            <SelectItem value="1y">Last year</SelectItem>
            <SelectItem value="all">All time</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Overview Cards */}
      {overview && (
        <div className="grid gap-4 grid-cols-1 [grid-template-columns:repeat(auto-fit,minmax(220px,1fr))]">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalMembers}</div>
              <p className="text-xs text-muted-foreground">
                +{overview.memberGrowthRate}% from last period
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Events</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalEvents}</div>
              <p className="text-xs text-muted-foreground">
                {overview.eventAttendanceRate}% attendance rate
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Donations</CardTitle>
              <Coins className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{overview.totalDonations}</div>
              <p className="text-xs text-muted-foreground">
                Donation transactions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrencyForChurch(overview.totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                +{overview.donationGrowthRate}% from last period
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Tabs defaultValue="members" className="space-y-4">
        <TabsList>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
          <TabsTrigger value="branches">Branches</TabsTrigger>
        </TabsList>

        {/* Members Analytics */}
        <TabsContent value="members" className="space-y-4">
          {memberAnalytics && (
            <div className="grid gap-4 grid-cols-1 [grid-template-columns:repeat(auto-fit,minmax(260px,1fr))]">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Members by Status</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {memberAnalytics.membersByStatus.map((item) => (
                    <div key={item.status} className="flex justify-between items-center">
                      <span className="capitalize">{item.status}</span>
                      <div className="flex items-center gap-2">
                        <span>{item.count}</span>
                        <Badge variant="secondary">{item.percentage}%</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Members by Gender</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {memberAnalytics.membersByGender.map((item) => (
                    <div key={item.gender} className="flex justify-between items-center">
                      <span className="capitalize">{item.gender.replace('_', ' ')}</span>
                      <div className="flex items-center gap-2">
                        <span>{item.count}</span>
                        <Badge variant="secondary">{item.percentage}%</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Members by Branch</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {memberAnalytics.membersByBranch.map((item, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span>{item.branchName}</span>
                      <div className="flex items-center gap-2">
                        <span>{item.count}</span>
                        <Badge variant="secondary">{item.percentage}%</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Events Analytics */}
        <TabsContent value="events" className="space-y-4">
          {eventAnalytics && (
            <div className="grid gap-4 grid-cols-1 [grid-template-columns:repeat(auto-fit,minmax(300px,1fr))]">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Events by Type</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {eventAnalytics.eventsByType.map((item) => (
                    <div key={item.type} className="flex justify-between items-center">
                      <span className="capitalize">{item.type.replace('_', ' ')}</span>
                      <div className="flex items-center gap-2">
                        <span>{item.count}</span>
                        <Badge variant="secondary">{item.percentage}%</Badge>
                        <Badge variant="outline">Avg: {item.averageAttendance}</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Upcoming Events</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {eventAnalytics.upcomingEvents.map((event) => (
                    <div key={event.id} className="p-2 border rounded">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-muted-foreground capitalize">
                            {event.type.replace('_', ' ')} • {formatDate(event.startDate)}
                          </p>
                        </div>
                        <Badge>{event.rsvpCount} RSVPs</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Financial Analytics */}
        <TabsContent value="financial" className="space-y-4">
          {financialAnalytics && (
            <div className="grid gap-4 grid-cols-1 [grid-template-columns:repeat(auto-fit,minmax(300px,1fr))]">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Donations by Type</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {financialAnalytics.donationsByType.map((item) => (
                    <div key={item.type} className="flex justify-between items-center">
                      <span className="capitalize">{item.type.replace('_', ' ')}</span>
                      <div className="flex items-center gap-2">
                        <span>{formatCurrencyForChurch(item.totalAmount)}</span>
                        <Badge variant="secondary">{item.percentage}%</Badge>
                        <Badge variant="outline">{item.count} donations</Badge>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Top Donors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {financialAnalytics.topDonors.map((donor, index) => (
                    <div key={index} className="p-2 border rounded">
                      <div className="flex justify-between items-center">
                        <div>
                          <h4 className="font-medium">{donor.donorName}</h4>
                          <p className="text-sm text-muted-foreground">
                            {donor.donationCount} donations
                          </p>
                        </div>
                        <span className="font-medium">{formatCurrencyForChurch(donor.totalDonations)}</span>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        {/* Branch Analytics */}
        <TabsContent value="branches" className="space-y-4">
          {branchAnalytics && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Branch Comparison</CardTitle>
                <CardDescription>
                  Overview of {branchAnalytics.totalBranches} branches
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {branchAnalytics.branchComparison.map((branch) => (
                    <div key={branch.branchId} className="p-4 border rounded">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{branch.branchName}</h4>
                          <div className="flex gap-4 mt-2 text-sm text-muted-foreground">
                            <span>{branch.memberCount} members</span>
                            <span>{branch.eventCount} events</span>
                            <span>{formatCurrencyForChurch(branch.totalDonations)} donations</span>
                          </div>
                        </div>
                        <Building2 className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>

      {/* Recent Activity */}
      {overview?.recentActivity && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {overview.recentActivity.map((activity, index) => (
                <div key={index} className="flex items-center gap-3 p-2 rounded hover:bg-muted/50">
                  <div className="flex-1">
                    <p className="font-medium">{activity.title}</p>
                    <p className="text-sm text-muted-foreground">{activity.description}</p>
                  </div>
                  <span className="text-sm text-muted-foreground">
                    {formatDate(activity.timestamp)}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}