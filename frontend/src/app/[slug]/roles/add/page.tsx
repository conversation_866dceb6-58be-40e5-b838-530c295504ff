'use client'

import { useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { apiClient } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage
} from '@/components/ui/form'
import { ArrowLeft, Shield, Save, UserX } from 'lucide-react'
import { toast } from 'sonner'

const createRoleSchema = z.object({
    name: z.string().min(1, 'Role name is required'),
    description: z.string().optional(),
    permissions: z.array(z.string()),
})

type CreateRoleForm = z.infer<typeof createRoleSchema>

// Available permissions grouped by category
const PERMISSION_GROUPS = [
    {
        name: 'Church Management',
        permissions: [
            { id: 'manage_church', label: 'Manage Church', description: 'Full church management access' },
            { id: 'view_church', label: 'View Church', description: 'View church information' },
        ]
    },
    {
        name: 'Member Management',
        permissions: [
            { id: 'manage_members', label: 'Manage Members', description: 'Create, edit, and delete member accounts' },
            { id: 'view_members', label: 'View Members', description: 'View member information and profiles' },
            { id: 'invite_members', label: 'Invite Members', description: 'Send invitations to new members' },
            { id: 'remove_members', label: 'Remove Members', description: 'Remove members from the church' },
        ]
    },
    {
        name: 'Role Management',
        permissions: [
            { id: 'manage_roles', label: 'Manage Roles', description: 'Create and manage user roles and permissions' },
            { id: 'view_roles', label: 'View Roles', description: 'View existing roles' },
            { id: 'assign_roles', label: 'Assign Roles', description: 'Assign roles to members' },
        ]
    },
    {
        name: 'Events Management',
        permissions: [
            { id: 'manage_events', label: 'Manage Events', description: 'Create, edit, and delete church events' },
            { id: 'view_events', label: 'View Events', description: 'View church events and details' },
            { id: 'create_events', label: 'Create Events', description: 'Create new events' },
        ]
    },
    {
        name: 'Financial Management',
        permissions: [
            { id: 'manage_finances', label: 'Manage Finances', description: 'Full financial management access' },
            { id: 'view_finances', label: 'View Finances', description: 'View financial information' },
        ]
    },
    {
        name: 'Communication',
        permissions: [
            { id: 'send_announcements', label: 'Send Announcements', description: 'Send announcements to members' },
            { id: 'manage_communications', label: 'Manage Communications', description: 'Manage all communications' },
        ]
    },
    {
        name: 'Reports & Analytics',
        permissions: [
            { id: 'view_reports', label: 'View Reports', description: 'Access church analytics and reports' },
            { id: 'generate_reports', label: 'Generate Reports', description: 'Generate detailed reports' },
        ]
    },
    {
        name: 'Settings',
        permissions: [
            { id: 'manage_settings', label: 'Manage Settings', description: 'Modify church settings and configuration' },
        ]
    },
]

function AddRoleContent() {
    const { user } = useAuth()
    const params = useParams()
    const router = useRouter()
    const slug = params.slug as string
    const [loading, setLoading] = useState(false)

    // Check permissions
    const canManageRoles = hasPermission(user, PERMISSIONS.MANAGE_ROLES)

    const form = useForm<CreateRoleForm>({
        resolver: zodResolver(createRoleSchema),
        defaultValues: {
            name: '',
            description: '',
            permissions: [],
        },
    })

    const onSubmit = async (data: CreateRoleForm) => {
        if (!canManageRoles) return

        try {
            setLoading(true)
            await apiClient.createRole(slug, data)
            toast.success('Role created successfully')
            router.push(`/${slug}/roles`)
        } catch (error) {
            console.error('Error creating role:', error)
            toast.error('Failed to create role')
        } finally {
            setLoading(false)
        }
    }

    const handlePermissionChange = (permissionId: string, checked: boolean) => {
        const currentPermissions = form.getValues('permissions')
        if (checked) {
            form.setValue('permissions', [...currentPermissions, permissionId])
        } else {
            form.setValue('permissions', currentPermissions.filter(p => p !== permissionId))
        }
    }

    if (!canManageRoles) {
        return (
            <div className="flex-1 flex items-center justify-center">
                <Card className="w-full max-w-md">
                    <CardHeader className="text-center">
                        <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
                        <CardTitle>Access Denied</CardTitle>
                        <CardDescription>
                            You don&apos;t have permission to create roles.
                            Contact your church administrator if you need access.
                        </CardDescription>
                    </CardHeader>
                </Card>
            </div>
        )
    }

    return (
        <div className="flex-1 space-y-6 p-6">
            <div className="flex items-center gap-4 mb-6">
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/${slug}/roles`)}
                >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Roles
                </Button>
                <div>
                    <h1 className="text-3xl font-bold">Add New Role</h1>
                    <p className="text-muted-foreground">Create a new role with specific permissions</p>
                </div>
            </div>

            <div className="max-w-2xl">
                <Card>
                    <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                            <Shield className="h-5 w-5" />
                            Role Details
                        </CardTitle>
                        <CardDescription>
                            Define the role name, description, and permissions
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Form {...form}>
                            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                                <FormField
                                    control={form.control}
                                    name="name"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Role Name</FormLabel>
                                            <FormControl>
                                                <Input placeholder="e.g., Youth Leader, Treasurer" {...field} />
                                            </FormControl>
                                            <FormDescription>
                                                Choose a descriptive name for this role
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <FormField
                                    control={form.control}
                                    name="description"
                                    render={({ field }) => (
                                        <FormItem>
                                            <FormLabel>Description (Optional)</FormLabel>
                                            <FormControl>
                                                <Textarea
                                                    placeholder="Describe the responsibilities and purpose of this role"
                                                    className="min-h-[80px]"
                                                    {...field}
                                                />
                                            </FormControl>
                                            <FormDescription>
                                                Provide additional context about this role&apos;s responsibilities
                                            </FormDescription>
                                            <FormMessage />
                                        </FormItem>
                                    )}
                                />

                                <div className="space-y-4">
                                    <div>
                                        <h3 className="text-lg font-medium">Permissions</h3>
                                        <p className="text-sm text-muted-foreground">
                                            Select the permissions this role should have
                                        </p>
                                    </div>

                                    <div className="space-y-6">
                                        {PERMISSION_GROUPS.map((group) => (
                                            <div key={group.name} className="space-y-3">
                                                <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                                                    {group.name}
                                                </h4>
                                                <div className="grid gap-3">
                                                    {group.permissions.map((permission) => (
                                                        <div key={permission.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                                                            <Checkbox
                                                                id={permission.id}
                                                                checked={form.watch('permissions').includes(permission.id)}
                                                                onCheckedChange={(checked) =>
                                                                    handlePermissionChange(permission.id, checked as boolean)
                                                                }
                                                            />
                                                            <div className="grid gap-1.5 leading-none">
                                                                <label
                                                                    htmlFor={permission.id}
                                                                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                                                >
                                                                    {permission.label}
                                                                </label>
                                                                <p className="text-xs text-muted-foreground">
                                                                    {permission.description}
                                                                </p>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>

                                <div className="flex gap-4 pt-4">
                                    <Button type="submit" disabled={loading}>
                                        {loading ? (
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        ) : (
                                            <Save className="h-4 w-4 mr-2" />
                                        )}
                                        Create Role
                                    </Button>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => router.push(`/${slug}/roles`)}
                                    >
                                        Cancel
                                    </Button>
                                </div>
                            </form>
                        </Form>
                    </CardContent>
                </Card>
            </div>
        </div>
    )
}

export default function AddRolePage() {
    return (
        <DashboardLayout>
            <AddRoleContent />
        </DashboardLayout>
    )
}