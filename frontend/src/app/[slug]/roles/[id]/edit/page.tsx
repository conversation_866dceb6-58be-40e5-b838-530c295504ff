'use client'

import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { apiClient } from '@/lib/api'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form'
import { ArrowLeft, Shield, Save, AlertTriangle } from 'lucide-react'
import { toast } from 'sonner'

const updateRoleSchema = z.object({
  name: z.string().min(1, 'Role name is required'),
  description: z.string().optional(),
  permissions: z.array(z.string()),
})

type UpdateRoleForm = z.infer<typeof updateRoleSchema>

interface Role {
  id: string
  name: string
  description?: string
  permissions: string[]
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

// Available permissions grouped by category
const PERMISSION_GROUPS = [
  {
    name: 'Church Management',
    permissions: [
      { id: 'manage_church', label: 'Manage Church', description: 'Full church management access' },
      { id: 'view_church', label: 'View Church', description: 'View church information' },
    ]
  },
  {
    name: 'Member Management',
    permissions: [
      { id: 'manage_members', label: 'Manage Members', description: 'Create, edit, and delete member accounts' },
      { id: 'view_members', label: 'View Members', description: 'View member information and profiles' },
      { id: 'invite_members', label: 'Invite Members', description: 'Send invitations to new members' },
      { id: 'remove_members', label: 'Remove Members', description: 'Remove members from the church' },
    ]
  },
  {
    name: 'Role Management',
    permissions: [
      { id: 'manage_roles', label: 'Manage Roles', description: 'Create and manage user roles and permissions' },
      { id: 'view_roles', label: 'View Roles', description: 'View existing roles' },
      { id: 'assign_roles', label: 'Assign Roles', description: 'Assign roles to members' },
    ]
  },
  {
    name: 'Events Management',
    permissions: [
      { id: 'manage_events', label: 'Manage Events', description: 'Create, edit, and delete church events' },
      { id: 'view_events', label: 'View Events', description: 'View church events and details' },
      { id: 'create_events', label: 'Create Events', description: 'Create new events' },
    ]
  },
  {
    name: 'Financial Management',
    permissions: [
      { id: 'manage_finances', label: 'Manage Finances', description: 'Full financial management access' },
      { id: 'view_finances', label: 'View Finances', description: 'View financial information' },
    ]
  },
  {
    name: 'Communication',
    permissions: [
      { id: 'send_announcements', label: 'Send Announcements', description: 'Send announcements to members' },
      { id: 'manage_communications', label: 'Manage Communications', description: 'Manage all communications' },
    ]
  },
  {
    name: 'Reports & Analytics',
    permissions: [
      { id: 'view_reports', label: 'View Reports', description: 'Access church analytics and reports' },
      { id: 'generate_reports', label: 'Generate Reports', description: 'Generate detailed reports' },
    ]
  },
  {
    name: 'Settings',
    permissions: [
      { id: 'manage_settings', label: 'Manage Settings', description: 'Modify church settings and configuration' },
    ]
  },
]

export default function EditRolePage() {
  const params = useParams()
  const router = useRouter()
  const slug = params.slug as string
  const roleId = params.id as string
  
  const [role, setRole] = useState<Role | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const form = useForm<UpdateRoleForm>({
    resolver: zodResolver(updateRoleSchema),
    defaultValues: {
      name: '',
      description: '',
      permissions: [],
    },
  })

  const fetchRole = useCallback(async () => {
    try {
      setLoading(true)
      const response = await apiClient.getRole(slug, roleId)
      const roleData = (response.data as any).role
      setRole(roleData)
      
      // Update form with role data
      form.reset({
        name: roleData.name,
        description: roleData.description || '',
        permissions: roleData.permissions || [],
      })
    } catch (error) {
      console.error('Error fetching role:', error)
      toast.error('Failed to load role')
      router.push(`/${slug}/roles`)
    } finally {
      setLoading(false)
    }
  }, [slug, roleId, form, router])

  useEffect(() => {
    // trigger fetch when route params change
    fetchRole()
  }, [slug, roleId])

  const onSubmit = async (data: UpdateRoleForm) => {
    try {
      setSaving(true)
      await apiClient.updateRole(slug, roleId, data)
      toast.success('Role updated successfully')
      router.push(`/${slug}/roles`)
    } catch (error) {
      console.error('Error updating role:', error)
      toast.error('Failed to update role')
    } finally {
      setSaving(false)
    }
  }

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    const currentPermissions = form.getValues('permissions')
    if (checked) {
      form.setValue('permissions', [...currentPermissions, permissionId])
    } else {
      form.setValue('permissions', currentPermissions.filter(p => p !== permissionId))
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!role) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Role not found</h1>
          <Button onClick={() => router.push(`/${slug}/roles`)}>
            Back to Roles
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push(`/${slug}/roles`)}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Roles
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Edit Role</h1>
          <p className="text-muted-foreground">Modify role details and permissions</p>
        </div>
      </div>

      <div className="max-w-2xl">
        {role.isSystem && (
          <Card className="mb-6 border-amber-200 bg-amber-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-amber-800">
                <AlertTriangle className="h-5 w-5" />
                <div>
                  <p className="font-medium">System Role</p>
                  <p className="text-sm">
                    This is a system role. Changes may affect core functionality.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Role Details
              {role.isSystem && (
                <Badge variant="default" className="ml-2">System</Badge>
              )}
            </CardTitle>
            <CardDescription>
              Modify the role name, description, and permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Role Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Youth Leader, Treasurer" {...field} />
                      </FormControl>
                      <FormDescription>
                        Choose a descriptive name for this role
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description (Optional)</FormLabel>
                      <FormControl>
                        <Textarea 
                          placeholder="Describe the responsibilities and purpose of this role"
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                        <FormDescription>
                          Provide additional context about this role&apos;s responsibilities
                        </FormDescription>                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-4">
                  <div>
                    <h3 className="text-lg font-medium">Permissions</h3>
                    <p className="text-sm text-muted-foreground">
                      Select the permissions this role should have
                    </p>
                  </div>
                  
                  <div className="space-y-6">
                    {PERMISSION_GROUPS.map((group) => (
                      <div key={group.name} className="space-y-3">
                        <h4 className="font-medium text-sm text-muted-foreground uppercase tracking-wide">
                          {group.name}
                        </h4>
                        <div className="grid gap-3">
                          {group.permissions.map((permission) => (
                            <div key={permission.id} className="flex items-start space-x-3 p-3 border rounded-lg">
                              <Checkbox
                                id={permission.id}
                                checked={form.watch('permissions').includes(permission.id)}
                                onCheckedChange={(checked) => 
                                  handlePermissionChange(permission.id, checked as boolean)
                                }
                              />
                              <div className="grid gap-1.5 leading-none">
                                <label
                                  htmlFor={permission.id}
                                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                                >
                                  {permission.label}
                                </label>
                                <p className="text-xs text-muted-foreground">
                                  {permission.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex gap-4 pt-4">
                  <Button type="submit" disabled={saving}>
                    {saving ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    Update Role
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => router.push(`/${slug}/roles`)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}