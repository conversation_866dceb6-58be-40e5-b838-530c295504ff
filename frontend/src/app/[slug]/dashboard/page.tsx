'use client'

import { useAuth } from '@/lib/auth'
import { useDashboardData } from '@/hooks/useDashboardData'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AuthGuard } from '@/components/auth/AuthGuard'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { formatCurrency } from '@/lib/currency'
import {
  Calendar,
  Users,
  DollarSign,
  MapPin,
  TrendingUp,
  Activity,
  AlertCircle,
  RefreshCw,
  BarChart3,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from 'lucide-react'
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XA<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import { useState } from 'react'

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8']

function DashboardContent() {
  const { user, church } = useAuth()
  const [period, setPeriod] = useState('30d')
  const {
    overview,
    events,
    finances,
    branches,
    memberAnalytics,
    eventAnalytics,
    financialAnalytics,
    chartData,
    loading,
    error,
    refetch
  } = useDashboardData(period)

  // The DashboardLayout already handles auth loading and missing user/church
  // So we can assume here that user and church exist
  if (!user || !church) {
    // This shouldn't happen since DashboardLayout handles it, but just in case
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <p className="text-sm text-muted-foreground">Loading user data...</p>
        </div>
      </div>
    )
  }

  const userRole = user.role?.name || 'Member'
  const isAdmin = ['Super Admin', 'Pastor', 'Elder'].includes(userRole)
  const canManageMembers = ['Super Admin', 'Pastor', 'Elder', 'Deacon'].includes(userRole)
  const canViewFinancials = ['Super Admin', 'Pastor', 'Elder', 'Deacon'].includes(userRole)

  if (error) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data: {error}
            <button
              onClick={refetch}
              className="ml-2 underline hover:no-underline"
            >
              Try again
            </button>
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  // Chart data from analytics endpoints
  const memberGrowthData = chartData.memberGrowth
  const eventTypeData = chartData.eventTypes
  const financeData = chartData.financeTrends
  const activityData = chartData.weeklyActivity

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Analytics Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user.firstName}! Here&apos;s your church analytics overview.
          </p>
        </div>
        <div className="flex items-center gap-4">
          {loading && (
            <RefreshCw className="h-4 w-4 animate-spin text-muted-foreground" />
          )}
          <Select value={period} onValueChange={setPeriod}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Stats - Role-based visibility */}
      {isAdmin && (
        <div className="grid gap-4 grid-cols-4 [grid-template-columns:repeat(auto-fit,minmax(220px,1fr))]">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Members</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <>
                  <div className="text-2xl font-bold">{overview?.totalMembers || 0}</div>
                  <p className="text-xs text-muted-foreground flex items-center">
                    {overview?.totalMembers ? (
                      <>
                        <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                        {overview?.memberGrowthRate ? `+${overview.memberGrowthRate}%` : 'No growth data'} from last month
                      </>
                    ) : (
                      <span className="text-muted-foreground">No members registered yet</span>
                    )}
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">This Month&apos;s Events</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-16" />
              ) : (
                <>
                  <div className="text-2xl font-bold">{overview?.totalEvents || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    {overview?.totalEvents ? (
                      `${events?.length || 0} upcoming this week`
                    ) : (
                      'No events scheduled'
                    )}
                  </p>
                </>
              )}
            </CardContent>
          </Card>

          {canViewFinancials && (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Offerings</CardTitle>
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <Skeleton className="h-8 w-20" />
                  ) : (
                    <>
                      <div className="text-2xl font-bold">
                        {formatCurrency(
                          (finances?.filter(f => f.type === 'offering' || f.type === 'tithe')
                            .reduce((sum: number, f: any) => sum + (typeof f.amount === 'number' ? f.amount : parseFloat(f.amount ?? '0')), 0)) || 0,
                          church.defaultCurrency
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground">
                        {finances?.filter(f => f.type === 'offering' || f.type === 'tithe').length || 0} offerings this period
                      </p>
                    </>
                  )}
                </CardContent>
              </Card>
            </>
          )}

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Branches</CardTitle>
              <MapPin className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              {loading ? (
                <Skeleton className="h-8 w-12" />
              ) : (
                <>
                  <div className="text-2xl font-bold">{branches?.length || 0}</div>
                  <p className="text-xs text-muted-foreground">
                    {branches?.length ? (
                      `${branches?.filter(b => (b as any).isMain || (b as any).isMainBranch).length || 0} main, ${branches?.filter(b => !((b as any).isMain || (b as any).isMainBranch)).length || 0} satellite`
                    ) : (
                      'No branches configured'
                    )}
                  </p>
                </>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="financial">Financial</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            {/* Member Growth Chart */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <LineChart className="h-5 w-5" />
                  Member Growth
                </CardTitle>
                <CardDescription>Monthly member registration trends</CardDescription>
              </CardHeader>
              <CardContent>
                {memberGrowthData?.some(item => (item?.members || 0) > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={memberGrowthData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Area type="monotone" dataKey="members" stroke="#8884d8" fill="#8884d8" fillOpacity={0.6} />
                    </AreaChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <LineChart className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">No Member Growth Data</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Add members to track growth trends
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Finance Trends */}
            {canViewFinancials && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Finance Trends
                  </CardTitle>
                  <CardDescription>Monthly financial activity</CardDescription>
                </CardHeader>
                <CardContent>
                  {financeData?.some(item => (item?.amount || 0) > 0) ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={financeData}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip formatter={(value) => formatCurrency(Number(value), church.defaultCurrency)} />
                        <Bar dataKey="amount" fill="#0088FE" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex flex-col items-center justify-center">
                      <BarChart3 className="h-12 w-12 text-muted-foreground mb-3" />
                      <p className="text-sm font-medium text-muted-foreground">No Finance Data</p>
                      <p className="text-xs text-muted-foreground text-center mt-1">
                        Record finances to see trends
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Weekly Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  Weekly Activity
                </CardTitle>
                <CardDescription>Events and attendance by day</CardDescription>
              </CardHeader>
              <CardContent>
                {activityData?.some(item => (item?.events || 0) > 0 || (item?.attendance || 0) > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={activityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Bar dataKey="events" fill="#00C49F" name="Events" />
                      <Bar dataKey="attendance" fill="#FFBB28" name="Attendance" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-[300px] flex flex-col items-center justify-center">
                    <Activity className="h-12 w-12 text-muted-foreground mb-3" />
                    <p className="text-sm font-medium text-muted-foreground">No Activity Data</p>
                    <p className="text-xs text-muted-foreground text-center mt-1">
                      Create events and track attendance<br />to see weekly activity patterns
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Event Types Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Event Distribution
                </CardTitle>
                <CardDescription>Events by type this month</CardDescription>
              </CardHeader>
              <CardContent>
                {eventTypeData?.some(item => (item?.count || 0) > 0) ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <RechartsPieChart>
                      <Pie
                        data={eventTypeData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${((percent || 0) * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {eventTypeData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </RechartsPieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="relative">
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          data={[{ name: 'No data', value: 100 }]}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#e5e7eb"
                          dataKey="value"
                          stroke="none"
                        >
                          <Cell fill="#e5e7eb" />
                        </Pie>
                      </RechartsPieChart>
                    </ResponsiveContainer>
                    <div className="absolute inset-0 flex flex-col items-center justify-center">
                      <PieChart className="h-8 w-8 text-muted-foreground mb-2" />
                      <p className="text-sm font-medium text-muted-foreground">No Event Data</p>
                      <p className="text-xs text-muted-foreground text-center mt-1">
                        Create and categorize events<br />to see distribution insights
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Members Tab */}
        <TabsContent value="members" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Member Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="flex justify-between items-center">
                    <span>Loading...</span>
                    <Skeleton className="h-5 w-12" />
                  </div>
                ) : memberAnalytics?.membersByStatus?.length ? (
                  memberAnalytics.membersByStatus.map((item) => (
                    <div key={item.status} className="flex justify-between items-center">
                      <span className="capitalize">{item.status.replace('_', ' ')}</span>
                      <Badge variant={item.status === 'active' ? 'default' : item.status === 'inactive' ? 'secondary' : 'outline'}>
                        {item.count}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No member data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Gender Distribution</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {loading ? (
                  <div className="flex justify-between items-center">
                    <span>Loading...</span>
                    <Skeleton className="h-5 w-12" />
                  </div>
                ) : memberAnalytics?.membersByGender?.length ? (
                  memberAnalytics.membersByGender.map((item) => (
                    <div key={item.gender} className="flex justify-between items-center">
                      <span className="capitalize">{item.gender.replace('_', ' ')}</span>
                      <Badge variant="default">{item.count}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-4">
                    <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No gender data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Member Growth</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="text-2xl font-bold">{overview?.totalMembers || 0}</div>
                <p className="text-xs text-muted-foreground flex items-center">
                  {overview?.totalMembers ? (
                    <>
                      <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
                      {overview?.memberGrowthRate ? `+${overview.memberGrowthRate}%` : 'No growth data'} from last period
                    </>
                  ) : (
                    <span className="text-muted-foreground">Start by adding your first members</span>
                  )}
                </p>
                <div className="text-sm">
                  <p className="text-muted-foreground">
                    Growth rate based on {period === '7d' ? 'last week' : period === '30d' ? 'last month' : period === '90d' ? 'last quarter' : 'last year'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Upcoming Events</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading ? (
                  <div className="space-y-3">
                    {[1, 2, 3].map(i => (
                      <div key={i} className="flex justify-between items-center p-2 border rounded">
                        <div>
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                        <Skeleton className="h-5 w-16" />
                      </div>
                    ))}
                  </div>
                ) : events?.length ? (
                  events.slice(0, 5).map((event) => (
                    <div key={event.id} className="flex justify-between items-center p-2 border rounded">
                      <div>
                        <p className="font-medium">{event.title}</p>
                        <p className="text-sm text-muted-foreground">{new Date(event.startDate).toLocaleDateString()}</p>
                      </div>
                      <Badge variant="outline">{event.type}</Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-6">
                    <Calendar className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">No upcoming events</p>
                    <p className="text-xs text-muted-foreground mt-1">Create your first event to get started</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Event Performance</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between items-center">
                  <span>Average Attendance</span>
                  <Badge variant="default">
                    {eventAnalytics?.attendanceMetrics.averageAttendanceRate
                      ? `${eventAnalytics.attendanceMetrics.averageAttendanceRate}%`
                      : 'No data'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Events This Period</span>
                  <Badge variant="default">{overview?.totalEvents || 0}</Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Most Popular</span>
                  <Badge variant="outline">
                    {eventAnalytics?.attendanceMetrics.mostPopularEventType || 'No data'}
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span>Peak Day</span>
                  <Badge variant="outline">
                    {eventAnalytics?.attendanceMetrics.peakAttendanceDay || 'No data'}
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Financial Tab */}
        {canViewFinancials && (
          <TabsContent value="financial" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle>Donation Types</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {loading ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map(i => (
                        <div key={i} className="flex justify-between items-center">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-5 w-16" />
                        </div>
                      ))}
                    </div>
                  ) : financialAnalytics?.donationsByType?.length ? (
                    financialAnalytics.donationsByType.map((item) => (
                      <div key={item.type} className="flex justify-between items-center">
                        <span className="capitalize">{item.type.replace('_', ' ')}</span>
                        <Badge variant="default">{formatCurrency(item.totalAmount, church.defaultCurrency)}</Badge>
                      </div>
                    ))
                  ) : finances?.length ? (
                    // Fallback to calculate from raw finance data
                    (() => {
                      const typeGroups = finances.reduce((acc: Record<string, number>, finance: any) => {
                        const type = finance.type
                        if (!acc[type]) acc[type] = 0
                        acc[type] += (typeof finance.amount === 'number' ? finance.amount : parseFloat(finance.amount ?? '0'))
                        return acc
                      }, {} as Record<string, number>)

                      return Object.entries(typeGroups).map(([type, amount]) => (
                        <div key={type} className="flex justify-between items-center">
                          <span className="capitalize">{type.replace('_', ' ')}</span>
                          <Badge variant="default">{formatCurrency(amount, church.defaultCurrency)}</Badge>
                        </div>
                      ))
                    })()
                  ) : (
                    <div className="text-center py-4">
                      <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No finance data available</p>
                      <p className="text-xs text-muted-foreground mt-1">Record finances to see insights</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Payment Methods</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {loading ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map(i => (
                        <div key={i} className="flex justify-between items-center">
                          <Skeleton className="h-4 w-24" />
                          <Skeleton className="h-5 w-12" />
                        </div>
                      ))}
                    </div>
                  ) : financialAnalytics?.donationsByMethod?.length ? (
                    financialAnalytics.donationsByMethod.map((item) => (
                      <div key={item.method} className="flex justify-between items-center">
                        <span className="capitalize">{item.method.replace('_', ' ')}</span>
                        <Badge variant="outline">{item.percentage}%</Badge>
                      </div>
                    ))
                  ) : finances?.length ? (
                    // Fallback to calculate from raw finance data
                    (() => {
                      const methodGroups = finances.reduce((acc: Record<string, number>, finance: any) => {
                        const method = finance.method
                        if (!acc[method]) acc[method] = 0
                        acc[method] += 1
                        return acc
                      }, {} as Record<string, number>)

                      const total = finances.length
                      return Object.entries(methodGroups).map(([method, count]) => (
                        <div key={method} className="flex justify-between items-center">
                          <span className="capitalize">{method.replace('_', ' ')}</span>
                          <Badge variant="outline">{Math.round((count / total) * 100)}%</Badge>
                        </div>
                      ))
                    })()
                  ) : (
                    <div className="text-center py-4">
                      <DollarSign className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No payment method data</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Top Contributors</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {loading ? (
                    <div className="space-y-2">
                      {[1, 2, 3].map(i => (
                        <div key={i} className="text-sm">
                          <Skeleton className="h-4 w-32 mb-1" />
                          <Skeleton className="h-4 w-20" />
                        </div>
                      ))}
                    </div>
                  ) : financialAnalytics?.topDonors?.length ? (
                    financialAnalytics.topDonors.slice(0, 3).map((donor) => (
                      <div key={donor.donorId || 'anonymous'} className="text-sm">
                        <p className="font-medium">{donor.donorName}</p>
                        <p className="text-muted-foreground">{formatCurrency(donor.totalDonations, church.defaultCurrency)}</p>
                      </div>
                    ))
                  ) : finances?.length ? (
                    // Fallback to show recent contributors from raw finance data
                    (() => {
                      const contributors = finances
                        .filter((f: any) => f.member)
                        .reduce((acc: Record<string, { name: string; total: number; count: number }>, finance: any) => {
                          const key = finance.member!.id
                          const name = `${finance.member!.firstName} ${finance.member!.lastName}`
                          if (!acc[key]) {
                            acc[key] = { name, total: 0, count: 0 }
                          }
                          acc[key].total += (typeof finance.amount === 'number' ? finance.amount : parseFloat(finance.amount ?? '0'))
                          acc[key].count += 1
                          return acc
                        }, {} as Record<string, { name: string; total: number; count: number }>)

                      return Object.values(contributors)
                        .sort((a: any, b: any) => b.total - a.total)
                        .slice(0, 3)
                        .map((contributor: any, index: number) => (
                          <div key={index} className="text-sm">
                            <p className="font-medium">{contributor.name}</p>
                            <p className="text-muted-foreground">{formatCurrency(contributor.total, church.defaultCurrency)}</p>
                          </div>
                        ))
                    })()
                  ) : (
                    <div className="text-center py-4">
                      <Users className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                      <p className="text-sm text-muted-foreground">No contributor data available</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Recent Financial Activity */}
            {finances?.length && (
              <Card>
                <CardHeader>
                  <CardTitle>Recent Financial Activity</CardTitle>
                  <CardDescription>Latest finance records and transactions</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {finances.slice(0, 5).map((finance: any) => (
                      <div key={finance.id} className="flex items-center justify-between border-b pb-2">
                        <div className="flex items-center space-x-3">
                          <div className="flex-shrink-0">
                            <DollarSign className="h-5 w-5 text-green-600" />
                          </div>
                          <div>
                            <p className="text-sm font-medium">
                              {finance.type.replace('_', ' ').toUpperCase()}
                            </p>
                            <p className="text-xs text-muted-foreground">
                              {finance.member ? `${finance.member.firstName} ${finance.member.lastName}` : 'Anonymous'}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-sm font-medium">
                            {formatCurrency(finance.amount, finance.currency)}
                          </p>
                          <p className="text-xs text-muted-foreground">
                            {new Date(finance.recordedAt || finance.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        )}
      </Tabs>

      {/* Recent Activity */}
      {(canManageMembers || isAdmin) && overview?.recentActivity && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Recent Activity
            </CardTitle>
            <CardDescription>Latest updates and actions in your church</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map(i => (
                  <div key={i} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    <Skeleton className="h-4 w-4" />
                    <div className="flex-1">
                      <Skeleton className="h-4 w-32 mb-1" />
                      <Skeleton className="h-3 w-48" />
                    </div>
                  </div>
                ))}
              </div>
            ) : overview.recentActivity.length > 0 ? (
              <div className="space-y-4">
                {overview.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                    {activity.type === 'member_joined' && <Users className="h-4 w-4 text-blue-500" />}
                    {activity.type === 'event_created' && <Calendar className="h-4 w-4 text-green-500" />}
                    {activity.type === 'donation_received' && <DollarSign className="h-4 w-4 text-yellow-500" />}
                    <div>
                      <p className="text-sm font-medium">{activity.title}</p>
                      <p className="text-xs text-muted-foreground">
                        {activity.description} - {new Date(activity.timestamp).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Activity className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground">No recent activity found</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default function DashboardPage() {
  return (
    <AuthGuard requireAuth={true}>
      <DashboardLayout>
        <DashboardContent />
      </DashboardLayout>
    </AuthGuard>
  )
}