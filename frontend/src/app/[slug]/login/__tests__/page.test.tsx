/**
 * Tests for enhanced church-specific login page with improved error handling and loading states
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 4.1, 4.2, 4.3, 4.4
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useParams, useRouter, useSearchParams } from 'next/navigation'
import ChurchLoginPage from '../page'
import { useAuth } from '@/lib/auth'
import { useFormSubmission } from '@/hooks/useFormSubmission'
import { apiClient } from '@/lib/api'
import { AuthError } from '@/lib/errors'
import { Church } from '@/types'
import { vi } from 'vitest'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useParams: vi.fn(),
  useRouter: vi.fn(),
  useSearchParams: vi.fn(),
}))

vi.mock('@/lib/auth', () => ({
  useAuth: vi.fn(),
}))

vi.mock('@/hooks/useFormSubmission', () => ({
  useFormSubmission: vi.fn(),
}))

vi.mock('@/lib/api', () => ({
  apiClient: {
    getChurchBySlug: vi.fn(),
  },
}))

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

const mockParams = {
  slug: 'test-church',
}

const mockSearchParams = {
  get: vi.fn(),
}

const mockLogin = vi.fn()
const mockHandleSubmit = vi.fn()
const mockClearError = vi.fn()

const mockChurch: Church = {
  id: 'church-1',
  name: 'Test Church',
  slug: 'test-church',
  churchCode: 'TEST123',
  email: '<EMAIL>',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  settings: {
    allowSelfRegistration: false,
    requireEmailVerification: true,
    timezone: 'UTC',
    theme: {},
    features: {
      events: true,
      donations: true,
      messaging: true,
      calendar: true,
      onlineGiving: true,
      memberDirectory: true,
      eventRsvp: true,
      recurringEvents: true,
    },
    eventSettings: {},
    donationSettings: {},
  },
}

const mockInvalidCredentialsError: AuthError = {
  code: 'INVALID_CREDENTIALS',
  message: 'Invalid credentials',
  userMessage: 'Invalid email, password, or church code. Please check your credentials and try again.',
  retryable: true,
  recoverable: true,
  actionable: true,
  suggestedActions: [
    'Double-check your email address',
    'Verify your password is correct',
    'Confirm your church code is accurate',
    'Try resetting your password if needed'
  ]
}

const mockNetworkError: AuthError = {
  code: 'NETWORK_ERROR',
  message: 'Network connection failed',
  userMessage: 'Unable to connect to the server. Please check your internet connection.',
  retryable: true,
  recoverable: true,
  actionable: true,
  suggestedActions: [
    'Check your internet connection',
    'Try refreshing the page',
    'Wait a moment and try again'
  ]
}

describe('ChurchLoginPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useParams as any).mockReturnValue(mockParams)
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useSearchParams as any).mockReturnValue(mockSearchParams)
    ;(mockSearchParams.get as any).mockReturnValue(null)
    
    ;(useAuth as any).mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      loading: false,
      error: null,
    })

    ;(useFormSubmission as any).mockReturnValue({
      isLoading: false,
      authError: null,
      handleSubmit: mockHandleSubmit,
      clearError: mockClearError,
    })

    ;(apiClient.getChurchBySlug as any).mockResolvedValue({
      data: { church: mockChurch }
    })
  })

  describe('Church Loading', () => {
    it('should show loading skeleton while fetching church data', () => {
      // Mock a pending promise to keep loading state
      ;(apiClient.getChurchBySlug as any).mockImplementation(() => new Promise(() => {}))

      render(<ChurchLoginPage />)

      expect(screen.getByTestId(/skeleton/i)).toBeInTheDocument()
    })

    it('should display church information after loading', async () => {
      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
        expect(screen.getByText('/test-church')).toBeInTheDocument()
        expect(screen.getByText('Code: TEST123')).toBeInTheDocument()
      })

      expect(screen.getByText('Enter your credentials to access the Test Church dashboard')).toBeInTheDocument()
    })

    it('should show church not found error for 404', async () => {
      ;(apiClient.getChurchBySlug as any).mockRejectedValue({
        status: 404,
        code: 'NOT_FOUND'
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('The church "test-church" could not be found.')).toBeInTheDocument()
      })

      expect(screen.getByText('Try General Login')).toBeInTheDocument()
      expect(screen.getByText('Register New Church')).toBeInTheDocument()
    })

    it('should show network error for church loading failures', async () => {
      ;(apiClient.getChurchBySlug as any).mockRejectedValue(new Error('Network error'))

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Unable to Load Church')).toBeInTheDocument()
        expect(screen.getByText('We couldn\'t load the information for "test-church"')).toBeInTheDocument()
      })

      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })

    it('should handle church loading retry', async () => {
      ;(apiClient.getChurchBySlug as any)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ data: { church: mockChurch } })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Try Again')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Try Again'))

      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should limit church loading retry attempts', async () => {
      ;(apiClient.getChurchBySlug as any).mockRejectedValue(new Error('Network error'))

      render(<ChurchLoginPage />)

      // Simulate multiple retry attempts
      for (let i = 0; i < 3; i++) {
        await waitFor(() => {
          const retryButton = screen.queryByText('Try Again')
          if (retryButton) {
            fireEvent.click(retryButton)
          }
        })
      }

      await waitFor(() => {
        expect(screen.getByText('Maximum retry attempts reached. Please check the church URL or try again later.')).toBeInTheDocument()
        expect(screen.getByText('Start Over')).toBeInTheDocument()
      })
    })
  })

  describe('Form Interaction', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should render login form with church-specific fields', () => {
      expect(screen.getByLabelText('Email address')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      
      // Should not have church code field (auto-populated from church data)
      expect(screen.queryByLabelText('Church Code')).not.toBeInTheDocument()
    })

    it('should toggle password visibility', () => {
      const passwordInput = screen.getByLabelText('Password')
      const toggleButton = screen.getByRole('button', { name: '' }) // Eye icon button

      expect(passwordInput).toHaveAttribute('type', 'password')

      fireEvent.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'text')

      fireEvent.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'password')
    })

    it('should disable form fields when loading', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      expect(screen.getByLabelText('Email address')).toBeDisabled()
      expect(screen.getByLabelText('Password')).toBeDisabled()
      expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled()
    })

    it('should submit form with church code from loaded church data', async () => {
      const mockSubmitFn = vi.fn()
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: (fn: any) => mockSubmitFn.mockImplementation(fn),
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      // Fill form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockSubmitFn).toHaveBeenCalled()
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123', 'TEST123')
      })
    })

    it('should handle form validation errors', async () => {
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Invalid email address')).toBeInTheDocument()
        expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument()
      })
    })
  })

  describe('Error Handling', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should display authentication errors', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockInvalidCredentialsError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      expect(screen.getByText('Invalid email, password, or church code. Please check your credentials and try again.')).toBeInTheDocument()
      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })

    it('should show retry count information', () => {
      // Mock component state to simulate retry attempts
      const TestWrapper = () => {
        const [retryCount, setRetryCount] = React.useState(1)
        
        React.useEffect(() => {
          ;(useFormSubmission as any).mockReturnValue({
            isLoading: false,
            authError: mockNetworkError,
            handleSubmit: mockHandleSubmit,
            clearError: mockClearError,
          })
        }, [])

        return <ChurchLoginPage />
      }

      render(<TestWrapper />)

      expect(screen.getByText(/Retry attempt \d+ of 3/)).toBeInTheDocument()
    })

    it('should show max retry warning after 3 attempts', () => {
      // Mock component state to simulate 3 retry attempts
      const TestWrapper = () => {
        const [retryCount, setRetryCount] = React.useState(3)
        
        return <ChurchLoginPage />
      }

      render(<TestWrapper />)

      expect(screen.getByText('Maximum retry attempts reached. Please check your credentials or try again later.')).toBeInTheDocument()
      expect(screen.getByText('Start Over')).toBeInTheDocument()
    })

    it('should handle retry button click', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockNetworkError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      fireEvent.click(screen.getByText('Try Again'))
      expect(mockClearError).toHaveBeenCalled()
    })

    it('should handle error dismissal', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockInvalidCredentialsError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      fireEvent.click(screen.getByText('Dismiss'))
      expect(mockClearError).toHaveBeenCalled()
    })
  })

  describe('Redirect Handling', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should redirect to church dashboard after successful login', async () => {
      const mockSubmitFn = vi.fn().mockImplementation(async (fn) => {
        await fn()
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockSubmitFn,
        clearError: mockClearError,
      })

      ;(mockLogin as any).mockResolvedValue(undefined)

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      // Fill and submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/dashboard')
      })
    })

    it('should redirect to specified URL for same church', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'redirect') return '/test-church/members'
        return null
      })

      const mockSubmitFn = vi.fn().mockImplementation(async (fn) => {
        await fn()
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockSubmitFn,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      // Fill and submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/members')
      })
    })

    it('should redirect to church dashboard for different church redirect', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'redirect') return '/other-church/members'
        return null
      })

      const mockSubmitFn = vi.fn().mockImplementation(async (fn) => {
        await fn()
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockSubmitFn,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      // Fill and submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/dashboard')
      })
    })

    it('should redirect authenticated users to dashboard', async () => {
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isAuthenticated: true,
        loading: false,
        error: null,
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/dashboard')
      })
    })
  })

  describe('Loading States', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should show loading button during submission', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      expect(screen.getByText('Signing in...')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled()
    })

    it('should show auth loading state', () => {
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isAuthenticated: false,
        loading: true,
        error: null,
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      expect(screen.getByText('Loading...')).toBeInTheDocument()
    })
  })

  describe('Logout Success Message', () => {
    it('should show logout success message when coming from logout', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'logout') return 'true'
        return null
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('You have been successfully logged out.')).toBeInTheDocument()
      })
    })

    it('should dismiss logout success message', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'logout') return 'true'
        return null
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Dismiss')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Dismiss'))
      expect(mockRouter.replace).toHaveBeenCalled()
    })
  })

  describe('Navigation Links', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should have proper navigation links', () => {
      expect(screen.getByText('Forgot your password?')).toHaveAttribute('href', '/forgot-password')
      expect(screen.getByText('General login')).toHaveAttribute('href', '/login')
      expect(screen.getByText('Register new church')).toHaveAttribute('href', '/register-church')
    })

    it('should show proper help text', () => {
      expect(screen.getByText("Don't have an account? Contact your church administrator")).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    beforeEach(async () => {
      render(<ChurchLoginPage />)
      
      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })
    })

    it('should have proper form labels and ARIA attributes', () => {
      expect(screen.getByLabelText('Email address')).toHaveAttribute('type', 'email')
      expect(screen.getByLabelText('Password')).toHaveAttribute('type', 'password')
    })

    it('should have proper button states and labels', () => {
      const submitButton = screen.getByRole('button', { name: /sign in/i })
      expect(submitButton).toHaveAttribute('type', 'submit')
    })

    it('should show proper loading indicators', () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      const { rerender } = render(<ChurchLoginPage />)
      rerender(<ChurchLoginPage />)

      expect(screen.getByText('Signing in...')).toBeInTheDocument()
      
      // Should have loading spinner
      const loadingSpinner = document.querySelector('.animate-spin')
      expect(loadingSpinner).toBeInTheDocument()
    })
  })

  describe('Edge Cases', () => {
    it('should handle missing church slug', async () => {
      ;(useParams as any).mockReturnValue({ slug: undefined })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('The church "" could not be found.')).toBeInTheDocument()
      })
    })

    it('should handle empty church response', async () => {
      ;(apiClient.getChurchBySlug as any).mockResolvedValue({
        data: { church: null }
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('The church "test-church" could not be found.')).toBeInTheDocument()
      })
    })

    it('should handle missing church code in loaded church', async () => {
      const churchWithoutCode = { ...mockChurch, churchCode: undefined }
      ;(apiClient.getChurchBySlug as any).mockResolvedValue({
        data: { church: churchWithoutCode }
      })

      render(<ChurchLoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Test Church')).toBeInTheDocument()
      })

      // Try to submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      // Should show error about missing church code
      await waitFor(() => {
        expect(screen.getByText('Church code not available. Please try again.')).toBeInTheDocument()
      })
    })
  })
})