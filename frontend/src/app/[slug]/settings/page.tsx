'use client'

import { useState, useEffect, useCallback } from 'react'
import { useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { apiClient } from '@/lib/api'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Settings, Building2, Palette, Zap, Calendar, DollarSign, UserX } from 'lucide-react'
import { toast } from 'sonner'
import { ChurchProfileForm } from '@/components/forms/church-profile-form'
import { ChurchLogoUpload } from '@/components/forms/church-logo-upload'
import { ChurchSettingsForm } from '@/components/forms/church-settings-form'
import type { Church, ChurchSettings } from '@/types'

interface ChurchData {
  id: string
  name: string
  slug: string
  churchCode: string
  description?: string
  address?: string
  phone?: string
  email?: string
  website?: string
  logo?: string
  settings: ChurchSettings
  createdAt: string
  updatedAt: string
}

function SettingsContent() {
  const { user } = useAuth()
  const params = useParams()
  const slug = params.slug as string

  const [church, setChurch] = useState<ChurchData | null>(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('profile')

  // Check permissions
  const canManageSettings = user ? hasPermission(user, PERMISSIONS.MANAGE_CHURCH_SETTINGS) : false

  const fetchChurchData = useCallback(async () => {
    if (!user) return
    
    try {
      setLoading(true)
      
      // Get church basic info
      const churchResponse = await apiClient.getChurchBySlug(slug)
      const churchData = churchResponse.data.church
      
      // Get church settings
      const settingsResponse = await apiClient.getChurchSettings(slug)
      const settings = (settingsResponse.data as any).church.settings
      
      setChurch({
        ...churchData,
        settings: typeof settings === 'string' ? JSON.parse(settings) : settings
      })
    } catch (error) {
      console.error('Error fetching church data:', error)
      toast.error('Failed to load church settings')
    } finally {
      setLoading(false)
    }
  }, [slug, user])

  useEffect(() => {
    fetchChurchData()
  }, [fetchChurchData])

  const handleLogoUpdate = async (logo: string) => {
    // Update the church state with new logo
    if (church) {
      setChurch({ ...church, logo })
    }
  }

  const handleProfileUpdate = async (data: any) => {
    try {
      await apiClient.updateChurch(slug, data)
      toast.success('Church profile updated successfully')
      fetchChurchData()
    } catch (error) {
      console.error('Error updating church profile:', error)
      toast.error('Failed to update church profile')
      throw error
    }
  }

  const handleSettingsUpdate = async (data: any) => {
    try {
      await apiClient.updateChurchSettings(slug, data)
      toast.success('Church settings updated successfully')
      fetchChurchData()
    } catch (error) {
      console.error('Error updating church settings:', error)
      toast.error('Failed to update church settings')
      throw error
    }
  }

  // Show loading state if user hasn't loaded yet
  if (!user) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Settings</h1>
            <p className="text-muted-foreground">Loading...</p>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!canManageSettings) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to manage church settings. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 space-y-6 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Settings</h1>
            <p className="text-muted-foreground">Manage your church profile and settings</p>
          </div>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  if (!church) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Church Not Found</CardTitle>
            <CardDescription>
              Unable to load church settings. Please try again later.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Settings</h1>
          <p className="text-muted-foreground">Manage your church profile and settings</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="general" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            General
          </TabsTrigger>
          <TabsTrigger value="theme" className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Theme
          </TabsTrigger>
          <TabsTrigger value="features" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Features
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Events
          </TabsTrigger>
          <TabsTrigger value="donations" className="flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            Donations
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-6">
          <ChurchLogoUpload
            currentLogo={church.logo}
            churchName={church.name}
            onLogoUpdate={handleLogoUpdate}
          />
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Church Profile
              </CardTitle>
              <CardDescription>
                Update your church&apos;s basic information and contact details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChurchProfileForm
                church={church}
                onSubmit={handleProfileUpdate}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="general" className="space-y-6">
          <ChurchSettingsForm
            church={church}
            onSubmit={handleSettingsUpdate}
            section="general"
          />
        </TabsContent>

        <TabsContent value="theme" className="space-y-6">
          <ChurchSettingsForm
            church={church}
            onSubmit={handleSettingsUpdate}
            section="theme"
          />
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <ChurchSettingsForm
            church={church}
            onSubmit={handleSettingsUpdate}
            section="features"
          />
        </TabsContent>

        <TabsContent value="events" className="space-y-6">
          <ChurchSettingsForm
            church={church}
            onSubmit={handleSettingsUpdate}
            section="events"
          />
        </TabsContent>

        <TabsContent value="donations" className="space-y-6">
          <ChurchSettingsForm
            church={church}
            onSubmit={handleSettingsUpdate}
            section="donations"
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default function SettingsPage() {
  return (
    <DashboardLayout>
      <SettingsContent />
    </DashboardLayout>
  )
}