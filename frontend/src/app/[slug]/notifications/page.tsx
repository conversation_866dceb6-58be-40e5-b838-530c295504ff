'use client'

import { useEffect, useMemo, useState } from 'react'
import { useParams } from 'next/navigation'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { api } from '@/lib/api'
import { useAuth } from '@/lib/auth'
import { Notification } from '@/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { cn } from '@/lib/utils'
import { Bell, CheckCheck, ChevronLeft, ChevronRight, ExternalLink } from 'lucide-react'

function NotificationsContent() {
  const params = useParams()
  const slug = params.slug as string
  const { user } = useAuth()
  const [items, setItems] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [page, setPage] = useState(1)
  const [limit] = useState(10)
  const [totalPages, setTotalPages] = useState(1)
  const [loading, setLoading] = useState(false)

  const refresh = async () => {
    if (!slug) return
    try {
      setLoading(true)
      const [list, count] = await Promise.all([
        api.getNotifications(slug, { page, limit, unreadOnly: false }),
        api.getNotificationsUnreadCount(slug)
      ])
      setItems(list.data.notifications)
      setUnreadCount(count.data.count)
      const total = list.data.pagination.total
      setTotalPages(Math.max(1, Math.ceil(total / limit)))
    } catch {
      setItems([])
      setUnreadCount(0)
      setTotalPages(1)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!user) return
    refresh()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [slug, page, user])

  const handleMarkAllRead = async () => {
    if (!slug) return
    await api.markNotificationsRead(slug).catch(() => {})
    await refresh()
  }

  const handleClickItem = async (n: Notification) => {
    if (!slug) return
    if (!n.isRead) {
      await api.markNotificationsRead(slug, { notificationIds: [n.id] }).catch(() => {})
      await refresh()
    }
    if (n.link) {
      window.location.href = `/${slug}${n.link.startsWith('/') ? '' : '/'}${n.link}`
    }
  }

  const formattedItems = useMemo(() => items, [items])

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Notifications</h1>
          <p className="text-muted-foreground">Your in-app updates and alerts</p>
        </div>
        {unreadCount > 0 && (
          <Button variant="outline" onClick={handleMarkAllRead} className="gap-2">
            <CheckCheck className="h-4 w-4" /> Mark all read
          </Button>
        )}
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Notifications</CardTitle>
          <CardDescription>Click an item to open its link and mark it as read</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8 text-muted-foreground">Loading...</div>
          ) : (
            <>
              <ScrollArea className="h-[560px]">
                {formattedItems.length === 0 ? (
                  <div className="p-4 text-sm text-muted-foreground flex flex-col items-center">
                    <Bell className="h-10 w-10 mb-2" />
                    No notifications
                  </div>
                ) : (
                  <div className="p-1 space-y-1">
                    {formattedItems.map((n) => (
                      <button key={n.id} onClick={() => handleClickItem(n)} className={cn('w-full text-left p-3 rounded-md hover:bg-accent', !n.isRead && 'bg-accent/30')}>
                        <div className="flex items-start gap-3">
                          <div className={cn('mt-1 h-2 w-2 rounded-full', n.isRead ? 'bg-muted' : 'bg-primary')} />
                          <div className="flex-1">
                            <div className="text-sm font-medium line-clamp-1 flex items-center gap-2">
                              {n.title}
                              {n.link && <ExternalLink className="h-3 w-3 text-muted-foreground" />}
                            </div>
                            {n.message && <div className="text-xs text-muted-foreground line-clamp-2">{n.message}</div>}
                            <div className="mt-1 text-[10px] text-muted-foreground">{new Date(n.createdAt).toLocaleString()}</div>
                          </div>
                          {!n.isRead && <Badge variant="secondary" className="text-[10px]">New</Badge>}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </ScrollArea>

              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">Page {page} of {totalPages}</div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" onClick={() => setPage(p => Math.max(1, p - 1))} disabled={page === 1}>
                      <ChevronLeft className="h-4 w-4" /> Previous
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => setPage(p => Math.min(totalPages, p + 1))} disabled={page === totalPages}>
                      Next <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function NotificationsPage() {
  return (
    <DashboardLayout>
      <NotificationsContent />
    </DashboardLayout>
  )
}
