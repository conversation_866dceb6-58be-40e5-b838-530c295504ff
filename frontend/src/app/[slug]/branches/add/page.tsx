'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { ArrowLeft, MapPin, Save, UserX } from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Member } from '@/types'
import { toast } from 'sonner'

interface BranchFormData {
  name: string
  slug: string
  description: string
  address: string
  phone: string
  email: string
  capacity: string
  leaderId: string
  isMain: boolean
  isActive: boolean
}

function AddBranchContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const [formData, setFormData] = useState<BranchFormData>({
    name: '',
    slug: '',
    description: '',
    address: '',
    phone: '',
    email: '',
    capacity: '',
    leaderId: '',
    isMain: false,
    isActive: true
  })
  const [members, setMembers] = useState<Member[]>([])
  const [loading, setLoading] = useState(false)
  const [membersLoading, setMembersLoading] = useState(true)

  // Check permissions
  const canManageBranches = hasPermission(user, PERMISSIONS.MANAGE_BRANCHES)

  const fetchMembers = async () => {
    if (!church?.slug) return

    try {
      setMembersLoading(true)
      const response = await apiClient.getMembers(church.slug, { limit: 100 })
      
      if (response.data && (response.data as any).members) {
        setMembers((response.data as any).members)
      }
    } catch (error) {
      console.error('Error fetching members:', error)
      toast.error('Failed to load members')
    } finally {
      setMembersLoading(false)
    }
  }

  useEffect(() => {
    if (canManageBranches && church?.slug) {
      fetchMembers()
    }
  }, [canManageBranches, church?.slug])

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
  }

  const handleInputChange = (field: string, value: string | boolean) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value }
      
      // Auto-generate slug when name changes
      if (field === 'name' && typeof value === 'string') {
        updated.slug = generateSlug(value)
      }
      
      return updated
    })
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!church?.slug) return

    try {
      setLoading(true)

      // Validate required fields
      if (!formData.name.trim()) {
        toast.error('Branch name is required')
        return
      }

      if (!formData.slug.trim()) {
        toast.error('Branch slug is required')
        return
      }

      const submitData: any = {
        name: formData.name.trim(),
        slug: formData.slug.trim(),
        description: formData.description.trim() || undefined,
        address: formData.address.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        email: formData.email.trim() || undefined,
        capacity: formData.capacity.trim() || undefined,
        branchLeader: formData.leaderId || undefined,
        isMainBranch: formData.isMain,
        isActive: formData.isActive
      }

      await apiClient.createBranch(church.slug, submitData)
      
      toast.success('Branch created successfully')
      router.push(`/${church.slug}/branches`)
    } catch (error: any) {
      console.error('Error creating branch:', error)
      toast.error(error.message || 'Failed to create branch')
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    router.push(`/${church?.slug}/branches`)
  }

  if (!canManageBranches) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to manage church branches. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="icon" onClick={handleCancel}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold">Add New Branch</h1>
          <p className="text-muted-foreground">
            Create a new church location or branch
          </p>
        </div>
      </div>

      {/* Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Branch Information
          </CardTitle>
          <CardDescription>
            Enter the details for the new church branch
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Basic Information */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="name">Branch Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Main Campus, Downtown Branch"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="slug">URL Slug *</Label>
                  <Input
                    id="slug"
                    value={formData.slug}
                    onChange={(e) => handleInputChange('slug', e.target.value)}
                    placeholder="e.g., main-campus, downtown-branch"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Used in URLs. Only lowercase letters, numbers, and hyphens.
                  </p>
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Brief description of this branch..."
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    placeholder="Full address of the branch..."
                    rows={2}
                  />
                </div>
              </div>

              {/* Contact & Settings */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="+****************"
                  />
                </div>

                <div>
                  <Label htmlFor="capacity">Capacity</Label>
                  <Input
                    id="capacity"
                    type="number"
                    value={formData.capacity}
                    onChange={(e) => handleInputChange('capacity', e.target.value)}
                    placeholder="Maximum seating capacity"
                    min="1"
                  />
                </div>

                <div>
                  <Label htmlFor="leaderId">Branch Leader</Label>
                  <Select 
                    value={formData.leaderId || "none"} 
                    onValueChange={(value) => handleInputChange('leaderId', value === "none" ? "" : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={membersLoading ? "Loading members..." : "Select a leader"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">No leader assigned</SelectItem>
                      {members.map((member) => (
                        <SelectItem key={member.id} value={member.id}>
                          {member.firstName} {member.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Settings */}
                <div className="space-y-4 pt-4 border-t">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isMain">Main Branch</Label>
                      <p className="text-sm text-muted-foreground">
                        Designate this as the primary church location
                      </p>
                    </div>
                    <Switch
                      id="isMain"
                      checked={formData.isMain}
                      onCheckedChange={(checked) => handleInputChange('isMain', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isActive">Active Status</Label>
                      <p className="text-sm text-muted-foreground">
                        Enable this branch for operations
                      </p>
                    </div>
                    <Switch
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-6 border-t">
              <Button type="submit" disabled={loading}>
                <Save className="h-4 w-4 mr-2" />
                {loading ? 'Creating...' : 'Create Branch'}
              </Button>
              <Button type="button" variant="outline" onClick={handleCancel}>
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}

export default function AddBranchPage() {
  return (
    <DashboardLayout>
      <AddBranchContent />
    </DashboardLayout>
  )
}