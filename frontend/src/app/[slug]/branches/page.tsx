'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { 
  MapPin, 
  Search, 
  Plus, 
  Mail, 
  Phone, 
  Users,
  Building,
  Crown,
  Edit,
  Eye,
  ChevronLeft,
  ChevronRight,
  UserX
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Branch } from '@/types'
import { toast } from 'sonner'

interface BranchStats {
  totalBranches: number
  activeBranches: number
  mainBranch: number
  totalCapacity: number
}

function BranchesContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const [branches, setBranches] = useState<Branch[]>([])
  const [stats, setStats] = useState<BranchStats>({
    totalBranches: 0,
    activeBranches: 0,
    mainBranch: 0,
    totalCapacity: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  // Check permissions
  const canManageBranches = hasPermission(user, PERMISSIONS.MANAGE_BRANCHES)
  const canViewBranches = hasPermission(user, PERMISSIONS.VIEW_BRANCHES)

  const fetchBranches = async () => {
    if (!church?.slug || !canViewBranches) return

    try {
      setLoading(true)
      const params: Record<string, string | number> = {
        page: currentPage,
        limit: 10,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { isActive: statusFilter === 'active' ? 'true' : 'false' })
      }

      const response = await apiClient.getBranches(church.slug, params)
      
      if (response.data && (response.data as Record<string, unknown>).branches) {
        const branchesData = (response.data as Record<string, unknown>).branches as Branch[]
        const paginationData = (response.data as Record<string, unknown>).pagination as any
        
        setBranches(branchesData)
        if (paginationData) {
          setTotalPages(paginationData.pages || 1)
        }

        // Calculate stats
        const totalCapacity = branchesData.reduce((sum: number, branch: Branch) => {
          const capacity = branch.capacity ? parseInt(branch.capacity) || 0 : 0
          return sum + capacity
        }, 0)
        const activeBranches = branchesData.filter((b: Branch) => b.isActive).length
        const mainBranches = branchesData.filter((b: Branch) => b.isMainBranch).length

        setStats({
          totalBranches: branchesData.length,
          activeBranches,
          mainBranch: mainBranches,
          totalCapacity
        })
      }
    } catch (error) {
      console.error('Error fetching branches:', error)
      toast.error('Failed to load branches')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchBranches()
  }, [canViewBranches, church?.slug, currentPage, searchTerm, statusFilter])

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    setCurrentPage(1)
  }

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value === 'all' ? '' : value)
    setCurrentPage(1)
  }

  const handleEditBranch = (branch: Branch) => {
    router.push(`/${church?.slug}/branches/${branch.id}/edit`)
  }

  const handleViewBranch = (branch: Branch) => {
    router.push(`/${church?.slug}/branches/${branch.id}`)
  }

  const handleAddBranch = () => {
    router.push(`/${church?.slug}/branches/add`)
  }

  if (!canViewBranches) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to view church branches. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Branches</h1>
          <p className="text-muted-foreground">
            Manage church locations and branch information
          </p>
        </div>
        {canManageBranches && (
          <Button onClick={handleAddBranch}>
            <Plus className="h-4 w-4 mr-2" />
            Add Branch
          </Button>
        )}
      </div>

      {/* Statistics */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Branches</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBranches}</div>
            <p className="text-xs text-muted-foreground">Church locations</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Branches</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeBranches}</div>
            <p className="text-xs text-muted-foreground">
              {stats.totalBranches > 0 ? Math.round((stats.activeBranches / stats.totalBranches) * 100) : 0}% operational
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Main Branch</CardTitle>
            <Crown className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.mainBranch}</div>
            <p className="text-xs text-muted-foreground">Primary location</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Capacity</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCapacity}</div>
            <p className="text-xs text-muted-foreground">Combined seating</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Branch Directory</CardTitle>
          <CardDescription>
            Search and filter church branches
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4 mb-6">
            {/* Search Bar */}
            <div className="flex gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input 
                    placeholder="Search branches by name, address, or description..." 
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
              </div>
            </div>

            {/* Filters */}
            <div className="flex gap-4">
              <Select value={statusFilter || 'all'} onValueChange={handleStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Branches Table */}
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="text-muted-foreground">Loading branches...</div>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Branch</TableHead>
                    <TableHead>Contact</TableHead>
                    <TableHead>Leader</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Type</TableHead>
                    {canManageBranches && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {branches.map((branch) => (
                    <TableRow key={branch.id}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                            <MapPin className="h-4 w-4" />
                          </div>
                          <div>
                            <div className="font-medium flex items-center gap-2">
                              {branch.name}
                              {branch.isMainBranch && <Crown className="h-3 w-3 text-yellow-600" />}
                            </div>
                            <div className="text-sm text-muted-foreground">{branch.address}</div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {branch.email && (
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3" />
                              {branch.email}
                            </div>
                          )}
                          {branch.phone && (
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Phone className="h-3 w-3" />
                              {branch.phone}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {branch.leader ? (
                          <div className="flex items-center gap-2">
                            <div className="h-6 w-6 rounded-full bg-secondary text-secondary-foreground flex items-center justify-center text-xs">
                              {branch.leader.firstName[0]}{branch.leader.lastName[0]}
                            </div>
                            <span className="text-sm">{branch.leader.firstName} {branch.leader.lastName}</span>
                          </div>
                        ) : (
                          <span className="text-sm text-muted-foreground">No leader assigned</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {branch.capacity || 'N/A'}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={branch.isActive ? 'default' : 'secondary'}>
                          {branch.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {branch.isMainBranch ? (
                          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
                            <Crown className="h-3 w-3 mr-1" />
                            Main
                          </Badge>
                        ) : (
                          <Badge variant="outline">Branch</Badge>
                        )}
                      </TableCell>
                      {canManageBranches && (
                        <TableCell>
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => handleViewBranch(branch)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button 
                              variant="ghost" 
                              size="icon"
                              onClick={() => handleEditBranch(branch)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function BranchesPage() {
  return (
    <DashboardLayout>
      <BranchesContent />
    </DashboardLayout>
  )
}