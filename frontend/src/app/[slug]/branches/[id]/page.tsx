'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  MapPin, 
  Mail, 
  Phone, 
  Users, 
  Crown, 
  Edit, 
  Calendar,
  Building,
  UserX,
  User
} from 'lucide-react'
import { apiClient } from '@/lib/api'
import { Branch } from '@/types'
import { toast } from 'sonner'

function BranchDetailsContent() {
  const { user, church } = useAuth()
  const router = useRouter()
  const params = useParams()
  const branchId = params.id as string
  
  const [branch, setBranch] = useState<Branch | null>(null)
  const [loading, setLoading] = useState(true)

  // Check permissions
  const canManageBranches = hasPermission(user, PERMISSIONS.MANAGE_BRANCHES)
  const canViewBranches = hasPermission(user, PERMISSIONS.VIEW_BRANCHES)

  const fetchBranch = async () => {
    if (!church?.slug || !branchId) return

    try {
      setLoading(true)
      const response = await apiClient.getBranch(church.slug, branchId)
      
      if (response.data && (response.data as any).branch) {
        setBranch((response.data as any).branch)
      }
    } catch (error) {
      console.error('Error fetching branch:', error)
      toast.error('Failed to load branch details')
      router.push(`/${church.slug}/branches`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (canViewBranches && church?.slug && branchId) {
      fetchBranch()
    }
  }, [canViewBranches, church?.slug, branchId])

  const handleEdit = () => {
    router.push(`/${church?.slug}/branches/${branchId}/edit`)
  }

  const handleBack = () => {
    router.push(`/${church?.slug}/branches`)
  }

  if (!canViewBranches) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Access Denied</CardTitle>
            <CardDescription>
              You don&apos;t have permission to view church branches. 
              Contact your church administrator if you need access.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-muted-foreground">Loading branch details...</div>
      </div>
    )
  }

  if (!branch) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <Building className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
            <CardTitle>Branch Not Found</CardTitle>
            <CardDescription>
              The branch you&apos;re looking for doesn&apos;t exist or has been removed.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="icon" onClick={handleBack}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              {branch.name}
              {branch.isMainBranch && <Crown className="h-6 w-6 text-yellow-600" />}
            </h1>
            <p className="text-muted-foreground">Branch Details</p>
          </div>
        </div>
        {canManageBranches && (
          <Button onClick={handleEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Branch
          </Button>
        )}
      </div>

      {/* Status and Type */}
      <div className="flex gap-2">
        <Badge variant={branch.isActive ? 'default' : 'secondary'}>
          {branch.isActive ? 'Active' : 'Inactive'}
        </Badge>
        {branch.isMainBranch && (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Crown className="h-3 w-3 mr-1" />
            Main Branch
          </Badge>
        )}
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Branch Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium text-muted-foreground">Name</label>
              <div className="font-medium">{branch.name}</div>
            </div>

            <div>
              <label className="text-sm font-medium text-muted-foreground">Slug</label>
              <div className="font-mono text-sm">{branch.slug}</div>
            </div>

            {branch.description && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Description</label>
                <div>{branch.description}</div>
              </div>
            )}

            {branch.address && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Address</label>
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 mt-0.5 text-muted-foreground" />
                  <div className="whitespace-pre-line">{branch.address}</div>
                </div>
              </div>
            )}

            {branch.capacity && (
              <div>
                <label className="text-sm font-medium text-muted-foreground">Capacity</label>
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-muted-foreground" />
                  <span>{branch.capacity} people</span>
                </div>
              </div>
            )}

            <div>
              <label className="text-sm font-medium text-muted-foreground">Created</label>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4 text-muted-foreground" />
                <span>{new Date(branch.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact & Leadership */}
        <Card>
          <CardHeader>
            <CardTitle>Contact & Leadership</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Contact Information */}
            <div className="space-y-3">
              <h4 className="font-medium">Contact Information</h4>
              
              {branch.email ? (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-muted-foreground" />
                  <a 
                    href={`mailto:${branch.email}`}
                    className="text-blue-600 hover:underline"
                  >
                    {branch.email}
                  </a>
                </div>
              ) : (
                <div className="text-muted-foreground text-sm">No email provided</div>
              )}

              {branch.phone ? (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  <a 
                    href={`tel:${branch.phone}`}
                    className="text-blue-600 hover:underline"
                  >
                    {branch.phone}
                  </a>
                </div>
              ) : (
                <div className="text-muted-foreground text-sm">No phone provided</div>
              )}
            </div>

            {/* Leadership */}
            <div className="space-y-3 pt-4 border-t">
              <h4 className="font-medium">Branch Leadership</h4>
              
              {branch.leader ? (
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                    {branch.leader.firstName[0]}{branch.leader.lastName[0]}
                  </div>
                  <div>
                    <div className="font-medium">
                      {branch.leader.firstName} {branch.leader.lastName}
                    </div>
                    <div className="text-sm text-muted-foreground">Branch Leader</div>
                    {branch.leader.email && (
                      <div className="text-sm text-blue-600">
                        <a href={`mailto:${branch.leader.email}`} className="hover:underline">
                          {branch.leader.email}
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 rounded-full bg-secondary flex items-center justify-center">
                    <User className="h-5 w-5 text-muted-foreground" />
                  </div>
                  <div>
                    <div className="font-medium text-muted-foreground">No leader assigned</div>
                    <div className="text-sm text-muted-foreground">Contact an administrator to assign a leader</div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Additional Information or Statistics could go here */}
      {branch.coordinates && (
        <Card>
          <CardHeader>
            <CardTitle>Location</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-sm text-muted-foreground">
              Coordinates: {branch.coordinates.lat}, {branch.coordinates.lng}
            </div>
            {/* You could add a map component here */}
          </CardContent>
        </Card>
      )}
    </div>
  )
}

export default function BranchDetailsPage() {
  return (
    <DashboardLayout>
      <BranchDetailsContent />
    </DashboardLayout>
  )
}