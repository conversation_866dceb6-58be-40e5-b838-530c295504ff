'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { formatCurrency } from '@/lib/currency'
import { api, apiClient } from '@/lib/api'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  DollarSign, 
  TrendingUp, 
  Users, 
  Calendar,
  AlertCircle
} from 'lucide-react'

interface FinanceStats {
  totalAmount: number
  totalFinances: number
  averageFinance: number
  topFinanceType: string
  monthlyGrowth: number
  currency: string
}

function FinancesDashboardContent() {
  const params = useParams()
  const { church } = useAuth()
  const slug = params.slug as string

  const [stats, setStats] = useState<FinanceStats | null>(null)
  const [recentFinances, setRecentFinances] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadDashboardData()
  }, [slug])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const [financesRes, financialAnalyticsRes] = await Promise.all([
        api.getFinances(slug, { limit: 10, sort: 'createdAt', order: 'desc' }),
        apiClient.getFinancialAnalytics(slug, { period: '30d' })
      ])

      const finances = (financesRes.data as any)?.finances || []
      const analytics = (financialAnalyticsRes.data as any)?.analytics
      setRecentFinances(finances)

      // Use real analytics data
      if (analytics) {
        const topDonationType = analytics.donationsByType.length > 0 
          ? analytics.donationsByType.reduce((prev: any, current: any) => 
              (prev.totalAmount > current.totalAmount) ? prev : current
            ).type
          : 'tithe'
        
        setStats({
          totalAmount: analytics.totalRevenue,
          totalFinances: analytics.donationsByType.reduce((sum: number, item: any) => sum + item.count, 0),
          averageFinance: analytics.totalRevenue / Math.max(1, analytics.donationsByType.reduce((sum: number, item: any) => sum + item.count, 0)),
          topFinanceType: topDonationType.charAt(0).toUpperCase() + topDonationType.slice(1).replace('_', ' '),
          monthlyGrowth: 0, // TODO: Calculate from trends when available
          currency: church?.defaultCurrency || 'ZMW'
        })
      } else if (finances.length > 0) {
        // Fallback to recent finances if analytics fails
        const totalAmount = finances.reduce((sum: number, d: any) => sum + d.amount, 0)
        const currency = finances[0]?.currency || church?.defaultCurrency || 'ZMW'
        
        setStats({
          totalAmount,
          totalFinances: finances.length,
          averageFinance: totalAmount / finances.length,
          topFinanceType: 'Recent finances only',
          monthlyGrowth: 0,
          currency
        })
      }
    } catch (err) {
      setError('Failed to load dashboard data')
      console.error('Error loading dashboard:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <div>
        <h1 className="text-3xl font-bold">Finances Dashboard</h1>
        <p className="text-muted-foreground">
          Overview of your church's financial activity and trends
        </p>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {stats && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Finances</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats.totalAmount, church?.defaultCurrency || stats.currency)}
              </div>
              <p className="text-xs text-muted-foreground">
                +{stats.monthlyGrowth}% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Number of Records</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.totalFinances}</div>
              <p className="text-xs text-muted-foreground">
                Total recorded finances
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Finance</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(stats.averageFinance, church?.defaultCurrency || stats.currency)}
              </div>
              <p className="text-xs text-muted-foreground">
                Per finance average
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Top Category</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.topFinanceType}</div>
              <p className="text-xs text-muted-foreground">
                Most common finance type
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Recent Finances</CardTitle>
          <CardDescription>
            Latest financial activity in your church
          </CardDescription>
        </CardHeader>
        <CardContent>
          {recentFinances.length > 0 ? (
            <div className="space-y-4">
              {recentFinances.map((finance) => (
                <div key={finance.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                      <DollarSign className="h-5 w-5 text-primary" />
                    </div>
                    <div>
                      <p className="font-medium">
                        {formatCurrency(typeof finance.amount === 'string' ? parseFloat(finance.amount) : finance.amount, church?.defaultCurrency || finance.currency)}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {finance.type} • {finance.method}
                      </p>
                      {finance.member && (
                        <p className="text-xs text-muted-foreground">
                          From: {finance.member.firstName} {finance.member.lastName}
                        </p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <Badge 
                      variant={finance.status === 'completed' ? 'default' : 'secondary'}
                    >
                      {finance.status}
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(finance.createdAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <DollarSign className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No finances yet</h3>
              <p className="text-muted-foreground">
                Start recording finances to see them here
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default function FinancesDashboard() {
  return (
    <DashboardLayout>
      <FinancesDashboardContent />
    </DashboardLayout>
  )
}