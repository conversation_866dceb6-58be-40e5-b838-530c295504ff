'use client'

import React, { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { useAuth } from '@/lib/auth'
import { api } from '@/lib/api'
import { formatCurrency } from '@/lib/currency'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import {
  Plus,
  DollarSign,
  BarChart3,
  AlertCircle,
  TrendingUp,
  ArrowUpDown,
  Filter,
  Search
} from 'lucide-react'
import { Finance, Currency } from '@/types'
import { FinanceForm } from '@/components/forms/FinanceForm'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

function FinancesContent() {
  const params = useParams()
  const { church } = useAuth()
  const slug = params.slug as string

  const [finances, setFinances] = useState<Finance[]>([])
  const [filteredFinances, setFilteredFinances] = useState<Finance[]>([])
  const [currencies, setCurrencies] = useState<Currency[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('finances')
  const [financialSummary, setFinancialSummary] = useState({
    totalIncome: 0,
    totalExpenses: 0,
    netIncome: 0,
    monthlyGrowth: 0
  })

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [typeFilter, setTypeFilter] = useState('all')
  const [sortField, setSortField] = useState<keyof Finance>('createdAt')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')

  // Modal states
  const [showFinanceForm, setShowFinanceForm] = useState(false)

  useEffect(() => {
    loadData()
  }, [slug])

  const loadData = async () => {
    try {
      setLoading(true)
      setError(null)

      // Load finances and currencies
      let financesData = []
      let currenciesData = []

      try {
        const financesRes = await api.getFinances(slug)
        financesData = (financesRes.data as any)?.finances || []
      } catch (financesErr) {
        console.warn('Failed to load finances:', financesErr)
        financesData = []
      }

      try {
        const currenciesRes = await api.getCurrencies()
        currenciesData = (currenciesRes.data as any)?.currencies || []
      } catch (currenciesErr) {
        console.warn('Failed to load currencies:', currenciesErr)
        // Fallback currencies
        currenciesData = [
          { code: 'USD', name: 'US Dollar', symbol: '$', isActive: true },
          { code: 'ZMW', name: 'Zambian Kwacha', symbol: 'ZK', isActive: true },
          { code: 'EUR', name: 'Euro', symbol: '€', isActive: true },
          { code: 'GBP', name: 'British Pound', symbol: '£', isActive: true }
        ]
      }

      setFinances(financesData)
      setFilteredFinances(financesData)
      setCurrencies(currenciesData)

      // Calculate financial summary
      calculateFinancialSummary(financesData)
    } catch (err) {
      setError('Failed to load finance data. Please check if the server is running.')
      console.error('Error loading finance data:', err)
    } finally {
      setLoading(false)
    }
  }

  const calculateFinancialSummary = (financeData: Finance[]) => {
    const income = financeData
      .filter(f => ['tithe', 'donation', 'offering', 'event_donation'].includes(f.type))
      .reduce((sum, f) => sum + (typeof f.amount === 'string' ? parseFloat(f.amount) : f.amount), 0)

    const expenses = financeData
      .filter(f => ['expense', 'other'].includes(f.type))
      .reduce((sum, f) => sum + (typeof f.amount === 'string' ? parseFloat(f.amount) : f.amount), 0)

    setFinancialSummary({
      totalIncome: income,
      totalExpenses: expenses,
      netIncome: income - expenses,
      monthlyGrowth: 12.5 // This would be calculated based on historical data
    })
  }

  // Filter and sort finances
  useEffect(() => {
    let filtered = [...finances]

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(finance => 
        finance.receiptNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        finance.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        finance.method.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (finance.member && `${finance.member.firstName} ${finance.member.lastName}`.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (finance.event && finance.event.title.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (finance.notes && finance.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      )
    }

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(finance => finance.status === statusFilter)
    }

    // Apply type filter
    if (typeFilter !== 'all') {
      filtered = filtered.filter(finance => finance.type === typeFilter)
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortField]
      let bValue = b[sortField]

      // Handle nested properties
      if (sortField === 'member') {
        aValue = a.member ? `${a.member.firstName} ${a.member.lastName}` : ''
        bValue = b.member ? `${b.member.firstName} ${b.member.lastName}` : ''
      }

      // Convert to string for comparison
      const aStr = String(aValue || '').toLowerCase()
      const bStr = String(bValue || '').toLowerCase()

      if (sortDirection === 'asc') {
        return aStr.localeCompare(bStr)
      } else {
        return bStr.localeCompare(aStr)
      }
    })

    setFilteredFinances(filtered)
  }, [finances, searchTerm, statusFilter, typeFilter, sortField, sortDirection])

  const handleSort = (field: keyof Finance) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleCreateFinance = async (data: any) => {
    try {
      await api.createFinance(slug, data)
      await loadData()
      setShowFinanceForm(false)
    } catch (err) {
      console.error('Error creating finance record:', err)
      setError('Failed to create finance record. Please try again.')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="flex-1 space-y-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Finances</h1>
          <p className="text-muted-foreground">
            Manage church finances, tithe, donations, and financial settings
          </p>
        </div>
        <Button onClick={() => setShowFinanceForm(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Record Finance
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="finances">Finances</TabsTrigger>
          <TabsTrigger value="summary">Summary & Statistics</TabsTrigger>
        </TabsList>

        <TabsContent value="finances" className="space-y-4">
          {/* Filters and Search */}
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-2 flex-1">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search finances..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="tithe">Tithe</SelectItem>
                  <SelectItem value="offering">Offering</SelectItem>
                  <SelectItem value="donation">Donation</SelectItem>
                  <SelectItem value="event_donation">Event Donation</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Data Table */}
          <Card>
            <CardContent className="p-0">
              {filteredFinances.length > 0 ? (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('amount')}
                      >
                        <div className="flex items-center gap-1">
                          Amount
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('type')}
                      >
                        <div className="flex items-center gap-1">
                          Type
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('method')}
                      >
                        <div className="flex items-center gap-1">
                          Method
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center gap-1">
                          Status
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead>From</TableHead>
                      <TableHead>Event</TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('receiptNumber')}
                      >
                        <div className="flex items-center gap-1">
                          Receipt
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="cursor-pointer hover:bg-muted/50"
                        onClick={() => handleSort('createdAt')}
                      >
                        <div className="flex items-center gap-1">
                          Date
                          <ArrowUpDown className="h-4 w-4" />
                        </div>
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredFinances.map((finance) => (
                      <TableRow key={finance.id}>
                        <TableCell className="font-medium">
                          {formatCurrency(
                            typeof finance.amount === 'string' ? parseFloat(finance.amount) : finance.amount, 
                            church?.defaultCurrency || finance.currency
                          )}
                        </TableCell>
                        <TableCell>
                          <span className="capitalize">
                            {finance.type.replace('_', ' ')}
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className="capitalize">
                            {finance.method.replace('_', ' ')}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              finance.status === 'completed' ? 'default' : 
                              finance.status === 'pending' ? 'secondary' : 
                              finance.status === 'failed' ? 'destructive' : 
                              'outline'
                            }
                          >
                            {finance.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {finance.member ? (
                            <span>{finance.member.firstName} {finance.member.lastName}</span>
                          ) : (
                            <span className="text-muted-foreground">Anonymous</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {finance.event ? (
                            <span>{finance.event.title}</span>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {finance.receiptNumber}
                        </TableCell>
                        <TableCell>
                          {new Date(finance.createdAt).toLocaleDateString()}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              ) : (
                <div className="flex flex-col items-center justify-center py-12">
                  <DollarSign className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">
                    {finances.length === 0 ? 'No finance records' : 'No matching records'}
                  </h3>
                  <p className="text-muted-foreground text-center mb-4">
                    {finances.length === 0 
                      ? 'Start recording finances to track your church\'s financial activities'
                      : 'Try adjusting your search or filter criteria'
                    }
                  </p>
                  {finances.length === 0 && (
                    <Button onClick={() => setShowFinanceForm(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Record First Finance
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Summary Stats */}
          {filteredFinances.length > 0 && (
            <div className="text-sm text-muted-foreground">
              Showing {filteredFinances.length} of {finances.length} records
            </div>
          )}
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          {/* Financial Overview Cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Income</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalIncome, church?.defaultCurrency || 'ZMW')}
                </div>
                <p className="text-xs text-muted-foreground">
                  +{financialSummary.monthlyGrowth}% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Expenses</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.totalExpenses, church?.defaultCurrency || 'ZMW')}
                </div>
                <p className="text-xs text-muted-foreground">
                  Church operational costs
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Net Income</CardTitle>
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatCurrency(financialSummary.netIncome, church?.defaultCurrency || 'ZMW')}
                </div>
                <p className="text-xs text-muted-foreground">
                  Income minus expenses
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Activity */}
          <Card>
            <CardHeader>
              <CardTitle>Recent Financial Activity</CardTitle>
              <CardDescription>Latest finance records and transactions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {finances.slice(0, 5).map((finance) => (
                  <div key={finance.id} className="flex items-center justify-between border-b pb-2">
                    <div className="flex items-center space-x-3">
                      <div className="flex-shrink-0">
                        <DollarSign className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">
                          {finance.type.replace('_', ' ').toUpperCase()}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {finance.member ? `${finance.member.firstName} ${finance.member.lastName}` : 'Anonymous'}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {formatCurrency(typeof finance.amount === 'string' ? parseFloat(finance.amount) : finance.amount, finance.currency)}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        {new Date(finance.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                ))}
                {finances.length === 0 && (
                  <p className="text-center text-muted-foreground py-8">
                    No recent financial activity
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      {showFinanceForm && (
        <FinanceForm
          onSubmit={handleCreateFinance}
          onCancel={() => setShowFinanceForm(false)}
          projects={[]}
          currencies={currencies}
        />
      )}
    </div>
  )
}

export default function FinancesPage() {
  return (
    <DashboardLayout>
      <FinancesContent />
    </DashboardLayout>
  )
}