'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Logo } from '@/components/ui/logo'
import { CheckCircle, ArrowRight, Building, Users, Calendar, DollarSign, BarChart3, Settings, Copy, Check } from 'lucide-react'
import { apiClient } from '@/lib/api'

type OnboardingStep = 'welcome' | 'features' | 'getting-started' | 'completion'

interface ChurchData {
  name: string
  slug: string
  churchCode: string
}

export default function OnboardingPage() {
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('welcome')
  const [churchData, setChurchData] = useState<ChurchData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [copiedCode, setCopiedCode] = useState(false)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const churchSlug = searchParams.get('church')
  const adminEmail = searchParams.get('admin')

  useEffect(() => {
    const loadChurchData = async () => {
      if (!churchSlug) {
        setError('Church information not found. Please try registering again.')
        setLoading(false)
        return
      }

      try {
        const response = await apiClient.getChurchBySlug(churchSlug)
        if (response.data?.church) {
          setChurchData({
            name: response.data.church.name,
            slug: response.data.church.slug,
            churchCode: response.data.church.churchCode
          })
        } else {
          setError('Church not found. Please try registering again.')
        }
      } catch (err: any) {
        console.error('Failed to load church data:', err)
        setError('Failed to load church information. Please try again.')
      } finally {
        setLoading(false)
      }
    }

    loadChurchData()
  }, [churchSlug])

  const handleNext = () => {
    const steps: OnboardingStep[] = ['welcome', 'features', 'getting-started', 'completion']
    const currentIndex = steps.indexOf(currentStep)
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1])
    }
  }

  const handlePrevious = () => {
    const steps: OnboardingStep[] = ['welcome', 'features', 'getting-started', 'completion']
    const currentIndex = steps.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1])
    }
  }

  const copyChurchCode = async () => {
    if (churchData?.churchCode) {
      try {
        await navigator.clipboard.writeText(churchData.churchCode)
        setCopiedCode(true)
        setTimeout(() => setCopiedCode(false), 2000)
      } catch (err) {
        console.error('Failed to copy church code:', err)
      }
    }
  }

  const goToLogin = () => {
    // Always redirect to the main login page since users can login from anywhere
    router.push('/login')
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-2xl">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
              <p>Loading your church information...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-2xl">
          <CardHeader>
            <CardTitle className="text-center text-red-600">Setup Error</CardTitle>
          </CardHeader>
          <CardContent>
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </CardContent>
          <CardFooter>
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => router.push('/register-church')}
            >
              Return to Registration
            </Button>
          </CardFooter>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-4xl">
        {/* Progress Indicator */}
        <div className="px-6 pt-6">
          <div className="flex items-center justify-between mb-8">
            {['welcome', 'features', 'getting-started', 'completion'].map((step, index) => {
              const isActive = step === currentStep
              const isCompleted = ['welcome', 'features', 'getting-started', 'completion'].indexOf(currentStep) > index
              
              return (
                <div key={step} className="flex items-center">
                  <div className={`
                    flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium
                    ${isActive ? 'border-primary bg-primary text-primary-foreground' : 
                      isCompleted ? 'border-green-500 bg-green-500 text-white' : 
                      'border-gray-300 bg-gray-100 text-gray-400'}
                  `}>
                    {isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
                  </div>
                  {index < 3 && (
                    <div className={`w-16 h-0.5 mx-2 ${isCompleted ? 'bg-green-500' : 'bg-gray-300'}`} />
                  )}
                </div>
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        {currentStep === 'welcome' && (
          <>
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <Logo size="lg" />
              </div>
              <CardTitle className="text-3xl">Welcome to chenglo!</CardTitle>
              <CardDescription className="text-lg">
                Congratulations! Your church <strong>{churchData?.name}</strong> has been successfully registered.
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="bg-amber-50 p-6 rounded-lg border border-amber-200">
                <h3 className="font-semibold text-amber-900 mb-3">What is chenglo?</h3>
                <p className="text-amber-800">
                  chenglo enhances church communities with comprehensive management tools designed to help you organize, 
                  connect, and grow your congregation. From member management to event planning, 
                  we enhance every aspect of church administration.
                </p>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="font-semibold text-green-900 mb-3">Your Church Setup</h3>
                <div className="space-y-2 text-green-800">
                  <p><strong>Church Name:</strong> {churchData?.name}</p>
                  <p><strong>Church URL:</strong> /{churchData?.slug}</p>
                  <p><strong>Admin Email:</strong> {adminEmail}</p>
                </div>
              </div>
            </CardContent>

            <CardFooter>
              <Button onClick={handleNext} className="w-full">
                Let's Get Started
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardFooter>
          </>
        )}

        {currentStep === 'features' && (
          <>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Powerful Features at Your Fingertips</CardTitle>
              <CardDescription>
                Discover what you can do with chenglo
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <Users className="w-8 h-8 text-blue-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Member Management</h3>
                    <p className="text-sm text-gray-600">
                      Easily manage your church members, track attendance, and organize contact information.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <Calendar className="w-8 h-8 text-green-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Event Planning</h3>
                    <p className="text-sm text-gray-600">
                      Create and manage church events, track RSVPs, and send automated reminders.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <DollarSign className="w-8 h-8 text-purple-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Donation Tracking</h3>
                    <p className="text-sm text-gray-600">
                      Monitor donations, generate reports, and manage your church's financial health.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <BarChart3 className="w-8 h-8 text-orange-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Analytics & Reports</h3>
                    <p className="text-sm text-gray-600">
                      Get insights into your church's growth and engagement with detailed analytics.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <Building className="w-8 h-8 text-red-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Multi-Branch Support</h3>
                    <p className="text-sm text-gray-600">
                      Manage multiple church locations and campuses from one central dashboard.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
                  <Settings className="w-8 h-8 text-gray-600 mt-1" />
                  <div>
                    <h3 className="font-semibold">Role-Based Access</h3>
                    <p className="text-sm text-gray-600">
                      Control who can access what with flexible permission and role management.
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex gap-3">
              <Button variant="outline" onClick={handlePrevious} className="flex-1">
                Previous
              </Button>
              <Button onClick={handleNext} className="flex-1">
                Continue
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardFooter>
          </>
        )}

        {currentStep === 'getting-started' && (
          <>
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">Getting Started</CardTitle>
              <CardDescription>
                Here's what you can do next to set up your church
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    1
                  </div>
                  <div>
                    <h3 className="font-semibold">Sign In to Your Dashboard</h3>
                    <p className="text-sm text-gray-600">
                      Use your admin credentials to access your church's management dashboard.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    2
                  </div>
                  <div>
                    <h3 className="font-semibold">Add Your First Members</h3>
                    <p className="text-sm text-gray-600">
                      Start by adding key church members and staff to your system.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    3
                  </div>
                  <div>
                    <h3 className="font-semibold">Create Your First Event</h3>
                    <p className="text-sm text-gray-600">
                      Set up your next church service or event to start engaging your community.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-4 border rounded-lg">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">
                    4
                  </div>
                  <div>
                    <h3 className="font-semibold">Customize Your Settings</h3>
                    <p className="text-sm text-gray-600">
                      Configure your church's preferences, roles, and permissions.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 p-4 rounded-lg">
                <h3 className="font-semibold text-amber-900 mb-2">💡 Pro Tip</h3>
                <p className="text-sm text-amber-800">
                  Take your time exploring the dashboard. You can always come back to these setup steps later 
                  from your admin panel.
                </p>
              </div>
            </CardContent>

            <CardFooter className="flex gap-3">
              <Button variant="outline" onClick={handlePrevious} className="flex-1">
                Previous
              </Button>
              <Button onClick={handleNext} className="flex-1">
                Almost Done!
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </CardFooter>
          </>
        )}

        {currentStep === 'completion' && (
          <>
            <CardHeader className="text-center">
              <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <CardTitle className="text-2xl text-green-600">Setup Complete!</CardTitle>
              <CardDescription>
                Your church is ready to go. Here's your important information:
              </CardDescription>
            </CardHeader>
            
            <CardContent className="space-y-6">
              <div className="bg-amber-50 p-6 rounded-lg border border-amber-200">
                <h3 className="font-semibold text-amber-900 mb-4">Your Church Code</h3>
                <div className="flex items-center justify-between bg-white p-4 rounded border">
                  <div>
                    <p className="text-2xl font-mono font-bold text-blue-700">
                      {churchData?.churchCode}
                    </p>
                    <p className="text-sm text-gray-600">
                      Share this code with your members for login
                    </p>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={copyChurchCode}
                    className="ml-4"
                  >
                    {copiedCode ? (
                      <>
                        <Check className="w-4 h-4 mr-2" />
                        Copied!
                      </>
                    ) : (
                      <>
                        <Copy className="w-4 h-4 mr-2" />
                        Copy
                      </>
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-green-50 p-6 rounded-lg">
                <h3 className="font-semibold text-green-900 mb-3">Important Notes</h3>
                <ul className="text-sm text-green-800 space-y-2">
                  <li>• Your admin account has been created with full privileges</li>
                  <li>• Members will use the church code to join your church</li>
                  <li>• You can find your church code anytime in the admin settings</li>
                  <li>• Keep your admin credentials secure</li>
                </ul>
              </div>

              <div className="bg-gray-50 p-6 rounded-lg">
                <h3 className="font-semibold text-gray-900 mb-3">Quick Access</h3>
                <div className="space-y-2 text-sm text-gray-700">
                  <p><strong>Church URL:</strong> /{churchData?.slug}</p>
                  <p><strong>Admin Email:</strong> {adminEmail}</p>
                  <p><strong>Dashboard:</strong> /{churchData?.slug}/dashboard</p>
                </div>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-3">
              <Button onClick={goToLogin} className="w-full">
                Sign In to Your Dashboard
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full"
                onClick={() => router.push('/')}
              >
                Return to Home
              </Button>
            </CardFooter>
          </>
        )}
      </Card>
    </div>
  )
}