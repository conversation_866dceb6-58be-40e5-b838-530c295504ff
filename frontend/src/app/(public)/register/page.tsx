'use client'

import Link from 'next/link'
import { UserPlus, Building2, Users } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'

export default function RegisterPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl text-center">Join a Church Community</CardTitle>
          <CardDescription className="text-center">
            Get connected with your church's digital community
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <Alert>
            <Users className="h-4 w-4" />
            <AlertDescription>
              Individual registration is not available. Please choose one of the options below.
            </AlertDescription>
          </Alert>

          <div className="space-y-4">
            <div className="text-center">
              <h3 className="font-semibold mb-2">Are you a church member?</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Contact your church administrator to create an account for you.
              </p>
              <Button asChild variant="outline" className="w-full">
                <Link href="/login">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Already Have an Account? Sign In
                </Link>
              </Button>
            </div>

            <div className="border-t pt-4">
              <div className="text-center">
                <h3 className="font-semibold mb-2">Are you a church administrator?</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Register your church and create accounts for your congregation.
                </p>
                <Button asChild className="w-full">
                  <Link href="/register-church">
                    <Building2 className="mr-2 h-4 w-4" />
                    Register Your Church
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}