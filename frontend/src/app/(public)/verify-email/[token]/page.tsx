'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { CheckCircle, XCircle, Loader2, Mail, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface VerificationState {
  status: 'loading' | 'success' | 'error';
  message: string;
}

export default function VerifyEmailPage() {
  const params = useParams();
  const router = useRouter();
  const token = params.token as string;
  
  const [verification, setVerification] = useState<VerificationState>({
    status: 'loading',
    message: 'Verifying your email address...'
  });

  useEffect(() => {
    let isCancelled = false; // Prevent double execution
    
    const verifyEmail = async () => {
      if (isCancelled) return;

      if (!token) {
        setVerification({
          status: 'error',
          message: 'Invalid verification link. No token provided.'
        });
        return;
      }

      // Handle case where token might be an array (Next.js dynamic routes can return arrays)
      const tokenString = Array.isArray(token) ? token[0] : token;

      try {
        const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api';
        const response = await fetch(`${apiUrl}/auth/verify-email/${tokenString}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        const data = await response.json();

        if (response.ok && data.status === 'success') {
          setVerification({
            status: 'success',
            message: data.message || 'Your email has been verified successfully!'
          });
        } else {
          setVerification({
            status: 'error',
            message: data.message || 'Failed to verify email. The link may be invalid or expired.'
          });
        }
      } catch (error) {
        console.error('Email verification error:', error);
        setVerification({
          status: 'error',
          message: 'An error occurred while verifying your email. Please try again later.'
        });
      }
    };

    verifyEmail();
    
    // Cleanup function to prevent double execution
    return () => {
      isCancelled = true;
    };
  }, [token]);

  const handleGoToLogin = () => {
    router.push('/login');
  };

  const handleResendVerification = () => {
    router.push('/login?message=Please contact your administrator to resend the verification email');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header with logo/branding */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
            <Mail className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900">Church Management</h1>
          <p className="text-gray-600">Email Verification</p>
        </div>

        <Card className="shadow-lg border-0">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto mb-4">
              {verification.status === 'loading' && (
                <div className="relative">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <Loader2 className="h-8 w-8 text-blue-600 animate-spin" />
                  </div>
                </div>
              )}
              {verification.status === 'success' && (
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                </div>
              )}
              {verification.status === 'error' && (
                <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                  <XCircle className="h-8 w-8 text-red-600" />
                </div>
              )}
            </div>
            
            <CardTitle className="text-xl font-semibold">
              {verification.status === 'loading' && 'Verifying Your Email'}
              {verification.status === 'success' && 'Email Verified Successfully!'}
              {verification.status === 'error' && 'Verification Failed'}
            </CardTitle>
            
            <CardDescription className="text-sm">
              {verification.status === 'loading' && 'Please wait while we verify your email address...'}
              {verification.status === 'success' && 'Your email has been successfully verified. You can now access your account.'}
              {verification.status === 'error' && 'We encountered an issue verifying your email address.'}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            <Alert className={`border ${
              verification.status === 'success' ? 'border-green-200 bg-green-50' :
              verification.status === 'error' ? 'border-red-200 bg-red-50' :
              'border-blue-200 bg-blue-50'
            }`}>
              <AlertDescription className={`text-sm ${
                verification.status === 'success' ? 'text-green-800' :
                verification.status === 'error' ? 'text-red-800' :
                'text-blue-800'
              }`}>
                {verification.message}
              </AlertDescription>
            </Alert>

            <div className="space-y-3 pt-2">
              {verification.status === 'success' && (
                <>
                  <Button 
                    onClick={handleGoToLogin}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                    size="lg"
                  >
                    Continue to Login
                  </Button>
                  <div className="text-center text-xs text-gray-500">
                    <p>You can now log in using your email and temporary password.</p>
                    <p className="mt-1">Remember to change your password after logging in.</p>
                  </div>
                </>
              )}

              {verification.status === 'error' && (
                <>
                  <Button 
                    onClick={handleResendVerification}
                    variant="outline"
                    className="w-full"
                    size="lg"
                  >
                    Contact Administrator
                  </Button>
                  <Button 
                    onClick={handleGoToLogin}
                    className="w-full"
                    size="lg"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back to Login
                  </Button>
                  <div className="text-center text-xs text-gray-500">
                    <p>If you continue to have issues, please contact your church administrator for assistance.</p>
                  </div>
                </>
              )}

              {verification.status === 'loading' && (
                <div className="text-center text-sm text-gray-500">
                  <p>This may take a few moments...</p>
                  <div className="mt-2 flex justify-center">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center mt-6 text-xs text-gray-500">
          <p>Secure email verification powered by Church Management System</p>
        </div>
      </div>
    </div>
  );
}