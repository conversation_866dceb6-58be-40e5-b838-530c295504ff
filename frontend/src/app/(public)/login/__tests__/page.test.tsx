/**
 * Tests for enhanced public login page with improved error handling and loading states
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 4.1, 4.2, 4.3, 4.4
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { useRouter, useSearchParams } from 'next/navigation'
import LoginPage from '../page'
import { useAuth } from '@/lib/auth'
import { useFormSubmission } from '@/hooks/useFormSubmission'
import { AuthError } from '@/lib/errors'
import { vi } from 'vitest'

// Mock dependencies
vi.mock('next/navigation', () => ({
  useRouter: vi.fn(),
  useSearchParams: vi.fn(),
}))

vi.mock('@/lib/auth', () => ({
  useAuth: vi.fn(),
}))

vi.mock('@/hooks/useFormSubmission', () => ({
  useFormSubmission: vi.fn(),
}))

const mockRouter = {
  push: vi.fn(),
  replace: vi.fn(),
  back: vi.fn(),
  forward: vi.fn(),
  refresh: vi.fn(),
  prefetch: vi.fn(),
}

const mockSearchParams = {
  get: vi.fn(),
}

const mockLogin = vi.fn()
const mockHandleSubmit = vi.fn()
const mockClearError = vi.fn()

const mockInvalidCredentialsError: AuthError = {
  code: 'INVALID_CREDENTIALS',
  message: 'Invalid credentials',
  userMessage: 'Invalid email, password, or church code. Please check your credentials and try again.',
  retryable: true,
  recoverable: true,
  actionable: true,
  suggestedActions: [
    'Double-check your email address',
    'Verify your password is correct',
    'Confirm your church code is accurate',
    'Try resetting your password if needed'
  ]
}

const mockNetworkError: AuthError = {
  code: 'NETWORK_ERROR',
  message: 'Network connection failed',
  userMessage: 'Unable to connect to the server. Please check your internet connection.',
  retryable: true,
  recoverable: true,
  actionable: true,
  suggestedActions: [
    'Check your internet connection',
    'Try refreshing the page',
    'Wait a moment and try again'
  ]
}

describe('LoginPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    ;(useRouter as any).mockReturnValue(mockRouter)
    ;(useSearchParams as any).mockReturnValue(mockSearchParams)
    ;(mockSearchParams.get as any).mockReturnValue(null)
    
    ;(useAuth as any).mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      loading: false,
      error: null,
    })

    ;(useFormSubmission as any).mockReturnValue({
      isLoading: false,
      authError: null,
      handleSubmit: mockHandleSubmit,
      clearError: mockClearError,
    })
  })

  describe('Initial Render', () => {
    it('should render login form with all fields', async () => {
      render(<LoginPage />)

      // Wait for loading to complete
      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      expect(screen.getByLabelText('Email address')).toBeInTheDocument()
      expect(screen.getByLabelText('Church Code')).toBeInTheDocument()
      expect(screen.getByLabelText('Password')).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    })

    it('should show loading skeleton initially', () => {
      render(<LoginPage />)

      // Should show skeleton elements
      expect(screen.getAllByTestId(/skeleton/i).length).toBeGreaterThan(0)
    })

    it('should show logout success message when coming from logout', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'logout') return 'true'
        return null
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('You have been successfully logged out.')).toBeInTheDocument()
      })
    })

    it('should have proper form validation', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Invalid email address')).toBeInTheDocument()
        expect(screen.getByText('Password must be at least 6 characters')).toBeInTheDocument()
      })
    })
  })

  describe('Form Interaction', () => {
    it('should toggle password visibility', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      const passwordInput = screen.getByLabelText('Password')
      const toggleButton = screen.getByRole('button', { name: '' }) // Eye icon button

      expect(passwordInput).toHaveAttribute('type', 'password')

      fireEvent.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'text')

      fireEvent.click(toggleButton)
      expect(passwordInput).toHaveAttribute('type', 'password')
    })

    it('should disable form fields when loading', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      expect(screen.getByLabelText('Email address')).toBeDisabled()
      expect(screen.getByLabelText('Church Code')).toBeDisabled()
      expect(screen.getByLabelText('Password')).toBeDisabled()
      expect(screen.getByRole('button', { name: /signing in/i })).toBeDisabled()
    })

    it('should submit form with correct data', async () => {
      const mockSubmitFn = vi.fn()
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: (fn: any) => mockSubmitFn.mockImplementation(fn),
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      // Fill form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Church Code'), {
        target: { value: 'TEST123' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockSubmitFn).toHaveBeenCalled()
      })
    })
  })

  describe('Error Handling', () => {
    it('should display authentication errors', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockInvalidCredentialsError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Invalid email, password, or church code. Please check your credentials and try again.')).toBeInTheDocument()
      })

      expect(screen.getByText('Try Again')).toBeInTheDocument()
    })

    it('should show retry count information', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockNetworkError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
        onError: (error: AuthError) => {
          // Simulate retry count increment
        }
      })

      const { rerender } = render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      // Simulate retry attempt by triggering error callback
      const onErrorCallback = (useFormSubmission as any).mock.calls[0][0].onError
      onErrorCallback(mockNetworkError)

      rerender(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText(/Retry attempt \d+ of 3/)).toBeInTheDocument()
      })
    })

    it('should show max retry warning after 3 attempts', async () => {
      // Mock component state to simulate 3 retry attempts
      const TestWrapper = () => {
        const [retryCount, setRetryCount] = React.useState(3)
        
        React.useEffect(() => {
          ;(useFormSubmission as any).mockReturnValue({
            isLoading: false,
            authError: mockNetworkError,
            handleSubmit: mockHandleSubmit,
            clearError: mockClearError,
          })
        }, [])

        return <LoginPage />
      }

      render(<TestWrapper />)

      await waitFor(() => {
        expect(screen.getByText('Maximum retry attempts reached. Please check your credentials or try again later.')).toBeInTheDocument()
      })

      expect(screen.getByText('Start Over')).toBeInTheDocument()
    })

    it('should handle retry button click', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockNetworkError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Try Again')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Try Again'))
      expect(mockClearError).toHaveBeenCalled()
    })

    it('should dismiss error messages', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: mockInvalidCredentialsError,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Dismiss')).toBeInTheDocument()
      })

      fireEvent.click(screen.getByText('Dismiss'))
      expect(mockClearError).toHaveBeenCalled()
    })
  })

  describe('Redirect Handling', () => {
    it('should redirect to specified URL after successful login', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'redirect') return '/test-church/members'
        return null
      })

      ;(useAuth as any).mockReturnValue({
        login: mockLogin.mockResolvedValue(undefined),
        isAuthenticated: false,
        loading: false,
        error: null,
      })

      const mockSubmitFn = vi.fn().mockImplementation(async (fn) => {
        await fn()
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockSubmitFn,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      // Fill and submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Church Code'), {
        target: { value: 'TEST123' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalledWith('/test-church/members')
      })
    })

    it('should redirect authenticated users', async () => {
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isAuthenticated: true,
        loading: false,
        error: null,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(mockRouter.push).toHaveBeenCalled()
      })
    })

    it('should validate redirect URLs for security', async () => {
      ;(mockSearchParams.get as any).mockImplementation((param: string) => {
        if (param === 'redirect') return 'https://malicious-site.com/steal-data'
        return null
      })

      const mockSubmitFn = vi.fn().mockImplementation(async (fn) => {
        await fn()
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockSubmitFn,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      // Fill and submit form
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })
      fireEvent.change(screen.getByLabelText('Church Code'), {
        target: { value: 'TEST123' }
      })
      fireEvent.change(screen.getByLabelText('Password'), {
        target: { value: 'password123' }
      })

      fireEvent.click(screen.getByRole('button', { name: /sign in/i }))

      // Should not redirect to external URL
      await waitFor(() => {
        expect(mockRouter.push).not.toHaveBeenCalledWith('https://malicious-site.com/steal-data')
      })
    })
  })

  describe('Loading States', () => {
    it('should show loading button during submission', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Signing in...')).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /signing in/i })
      expect(submitButton).toBeDisabled()
    })

    it('should show auth loading state', async () => {
      ;(useAuth as any).mockReturnValue({
        login: mockLogin,
        isAuthenticated: false,
        loading: true,
        error: null,
      })

      ;(useFormSubmission as any).mockReturnValue({
        isLoading: false,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Loading...')).toBeInTheDocument()
      })
    })
  })

  describe('Form Reset', () => {
    it('should reset form when start over is clicked', async () => {
      // Mock component to simulate max retry state
      const TestWrapper = () => {
        const [retryCount, setRetryCount] = React.useState(3)
        
        return <LoginPage />
      }

      render(<TestWrapper />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      // Fill form first
      fireEvent.change(screen.getByLabelText('Email address'), {
        target: { value: '<EMAIL>' }
      })

      // Simulate start over click
      const startOverButton = screen.queryByText('Start Over')
      if (startOverButton) {
        fireEvent.click(startOverButton)
        
        await waitFor(() => {
          expect(screen.getByLabelText('Email address')).toHaveValue('')
        })
      }
    })
  })

  describe('Accessibility', () => {
    it('should have proper form labels and ARIA attributes', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      expect(screen.getByLabelText('Email address')).toHaveAttribute('type', 'email')
      expect(screen.getByLabelText('Church Code')).toHaveAttribute('maxLength', '6')
      expect(screen.getByLabelText('Password')).toHaveAttribute('type', 'password')
    })

    it('should have proper button states and labels', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
      })

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      expect(submitButton).toHaveAttribute('type', 'submit')
    })

    it('should show proper loading indicators', async () => {
      ;(useFormSubmission as any).mockReturnValue({
        isLoading: true,
        authError: null,
        handleSubmit: mockHandleSubmit,
        clearError: mockClearError,
      })

      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Signing in...')).toBeInTheDocument()
      })

      // Should have loading spinner
      const loadingSpinner = document.querySelector('.animate-spin')
      expect(loadingSpinner).toBeInTheDocument()
    })
  })

  describe('Links and Navigation', () => {
    it('should have proper navigation links', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      })

      expect(screen.getByText('Forgot your password?')).toHaveAttribute('href', '/forgot-password')
      expect(screen.getByText('Register Church')).toHaveAttribute('href', '/register-church')
    })

    it('should show proper help text', async () => {
      render(<LoginPage />)

      await waitFor(() => {
        expect(screen.getByText("Don't have an account? Contact your church administrator")).toBeInTheDocument()
        expect(screen.getByText('Want to register a new church?')).toBeInTheDocument()
      })
    })
  })
})