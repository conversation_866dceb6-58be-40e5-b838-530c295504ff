'use client'

import { useState, useEffect, useCallback } from 'react'
import Link from 'next/link'
import { useSearchParams, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Eye, EyeOff, LogIn, RefreshCw, AlertCircle } from 'lucide-react'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { AuthErrorDisplay, InfoDisplay } from '@/components/ui/error-display'
import { Skeleton } from '@/components/ui/skeleton'
import { Logo } from '@/components/ui/logo'

import { useAuth } from '@/lib/auth'
import { loginSchema, type LoginForm } from '@/lib/validations'
import { useFormSubmission } from '@/hooks/useFormSubmission'
import { AuthError } from '@/lib/errors'

export default function LoginPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isPageLoading, setIsPageLoading] = useState(true)
  const [retryCount, setRetryCount] = useState(0)
  
  const { login, isAuthenticated, loading: authLoading, error: authError } = useAuth()
  const searchParams = useSearchParams()
  const router = useRouter()
  const redirectTo = searchParams.get('redirect')
  const fromLogout = searchParams.get('logout') === 'true'
  
  const form = useForm<LoginForm>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      emailOrUserId: '',
      password: '',
    },
  })

  const { isLoading: isSubmitting, authError: submitError, handleSubmit, clearError } = useFormSubmission({ 
    context: 'login',
    onSuccess: () => {
      setRetryCount(0)
    },
    onError: (error: AuthError) => {
      if (error.retryable) {
        setRetryCount(prev => prev + 1)
      }
    }
  })

  // Handle page initialization
  useEffect(() => {
    // Simulate brief loading to show loading state
    const timer = setTimeout(() => {
      setIsPageLoading(false)
    }, 300)

    return () => clearTimeout(timer)
  }, [])

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated && !authLoading) {
      if (redirectTo) {
        // Validate redirect URL
        try {
          const redirectUrl = new URL(redirectTo, window.location.origin)
          if (redirectUrl.origin === window.location.origin) {
            router.push(redirectTo)
            return
          }
        } catch (error) {
          console.warn('Invalid redirect URL:', redirectTo)
        }
      }
      // Default redirect will be handled by auth context
    }
  }, [isAuthenticated, authLoading, redirectTo, router])

  // Handle form submission with enhanced error handling
  const onSubmit = useCallback(async (data: LoginForm) => {
    clearError()
    
    await handleSubmit(async () => {
      await login(data.emailOrUserId, data.password)
      
      // Handle redirect after successful login
      if (redirectTo) {
        // Validate that the redirect URL is safe and appropriate
        try {
          const redirectUrl = new URL(redirectTo, window.location.origin)
          // Only allow redirects to the same origin
          if (redirectUrl.origin === window.location.origin) {
            router.push(redirectTo)
            return
          }
        } catch (error) {
          console.warn('Invalid redirect URL:', redirectTo)
        }
      }
      // The login function already handles the default redirect to dashboard
    })
  }, [handleSubmit, login, redirectTo, router, clearError])

  // Handle retry with form resubmission
  const handleRetry = useCallback(() => {
    clearError()
    form.handleSubmit(onSubmit)()
  }, [clearError, form, onSubmit])

  // Show loading skeleton during page initialization
  if (isPageLoading) {
    return <LoginPageSkeleton />
  }

  // Get the current error to display (prioritize submit error over auth error)
  const currentError = submitError || authError
  const canRetry = currentError?.retryable && retryCount < 3
  const isLoading = isSubmitting || authLoading

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <Logo size="lg" />
          </div>
          <CardTitle className="text-2xl text-center">Sign in to your account</CardTitle>
          <CardDescription className="text-center">
            Enter your email or user ID and password to access your church dashboard
          </CardDescription>
        </CardHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CardContent className="space-y-4">
              {/* Show logout success message */}
              {fromLogout && !currentError && (
                <InfoDisplay 
                  message="You have been successfully logged out."
                  onDismiss={() => {
                    const url = new URL(window.location.href)
                    url.searchParams.delete('logout')
                    router.replace(url.pathname + url.search)
                  }}
                />
              )}

              {/* Show authentication errors */}
              {currentError && (
                <AuthErrorDisplay 
                  error={currentError}
                  onRetry={canRetry ? handleRetry : undefined}
                  onDismiss={clearError}
                />
              )}

              {/* Show retry information */}
              {retryCount > 0 && retryCount < 3 && (
                <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-md border border-blue-200">
                  <div className="flex items-center">
                    <RefreshCw className="h-4 w-4 mr-2 text-blue-600" />
                    Retry attempt {retryCount} of 3
                  </div>
                </div>
              )}

              {/* Show max retry warning */}
              {retryCount >= 3 && (
                <div className="text-sm text-red-600 bg-red-50 p-3 rounded-md border border-red-200">
                  <div className="flex items-center">
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Maximum retry attempts reached. Please check your credentials or try again later.
                  </div>
                </div>
              )}

              <FormField
                control={form.control}
                name="emailOrUserId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email or User ID</FormLabel>
                    <FormControl>
                      <Input
                        type="text"
                        placeholder="Enter your email or 10-digit user ID"
                        disabled={isLoading}
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? 'text' : 'password'}
                          placeholder="Enter your password"
                          disabled={isLoading}
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                          onClick={() => setShowPassword(!showPassword)}
                          disabled={isLoading}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading || retryCount >= 3}
              >
                {isLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent" />
                    {isSubmitting ? 'Signing in...' : 'Loading...'}
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </>
                )}
              </Button>

              {/* Show retry button if max attempts reached */}
              {retryCount >= 3 && (
                <Button 
                  type="button"
                  variant="outline" 
                  className="w-full"
                  onClick={() => {
                    setRetryCount(0)
                    clearError()
                    form.reset()
                  }}
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Start Over
                </Button>
              )}

              <div className="text-sm text-center space-y-2">
                <Link 
                  href="/forgot-password" 
                  className="text-primary hover:underline"
                >
                  Forgot your password?
                </Link>
                
                <div className="text-muted-foreground">
                  Don&apos;t have an account?{' '}
                  <span className="text-primary">Contact your church administrator</span>
                </div>
                
                <div className="text-muted-foreground">
                  Want to register a new church?{' '}
                  <Link href="/register-church" className="text-primary hover:underline">
                    Register Church
                  </Link>
                </div>
              </div>
            </CardFooter>
          </form>
        </Form>
      </Card>
    </div>
  )
}

/**
 * Loading skeleton for the login page
 */
function LoginPageSkeleton() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3 mx-auto" />
        </CardHeader>
        
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/3" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-4 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-4">
          <Skeleton className="h-10 w-full" />
          <div className="space-y-2 w-full">
            <Skeleton className="h-4 w-1/2 mx-auto" />
            <Skeleton className="h-4 w-3/4 mx-auto" />
            <Skeleton className="h-4 w-2/3 mx-auto" />
          </div>
        </CardFooter>
      </Card>
    </div>
  )
}