import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  // Get token from cookies (more reliable than localStorage in middleware)
  const token = request.cookies.get('token')?.value

  // Define public routes that don't require authentication
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/register-church', 
    '/forgot-password',
    '/reset-password',
    '/onboarding'
  ]

  // Check if current path is a public route
  const isPublicRoute = publicRoutes.includes(request.nextUrl.pathname)

  // Extract church slug and route from URL
  const churchSlugMatch = request.nextUrl.pathname.match(/^\/([^/]+)\/(.*)$/)
  const isChurchScopedRoute = !!churchSlugMatch
  
  let churchSlug: string | null = null
  let churchRoute: string | null = null
  
  if (isChurchScopedRoute) {
    churchSlug = churchSlugMatch[1]
    churchRoute = churchSlugMatch[2]
    
    // Check if the church slug is actually a public route
    if (publicRoutes.some(route => route.startsWith(`/${churchSlug}`))) {
      // This is not a church-scoped route, treat as public
      return NextResponse.next()
    }
  }

  // Define protected church routes
  const protectedChurchRoutes = [
    'dashboard',
    'members',
    'events', 
    'donations',
    'branches',
    'analytics',
    'announcements',
    'roles',
    'settings'
  ]

  // Check if this is a protected route
  const isProtectedRoute = isChurchScopedRoute && 
    protectedChurchRoutes.some(route => churchRoute?.startsWith(route))

  // If accessing protected routes without token, redirect to main login
  if (isProtectedRoute && !token) {
    const loginUrl = new URL('/login', request.url)
    loginUrl.searchParams.set('redirect', request.nextUrl.pathname)
    return NextResponse.redirect(loginUrl)
  }

  // If accessing login routes with token, redirect to home
  if (token && isPublicRoute && request.nextUrl.pathname === '/login') {
    return NextResponse.redirect(new URL('/', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)  
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
}