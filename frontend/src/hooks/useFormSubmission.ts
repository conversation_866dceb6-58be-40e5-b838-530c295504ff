'use client'

import { useState } from 'react'
import { AuthError, ErrorClassifier } from '@/lib/errors'

interface UseFormSubmissionOptions {
  onSuccess?: () => void
  onError?: (error: AuthError) => void
  context?: string
}

export function useFormSubmission(options: UseFormSubmissionOptions = {}) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<AuthError | null>(null)
  const [rawError, setRawError] = useState<string | null>(null)

  const handleSubmit = async (submitFn: () => Promise<void>) => {
    setIsLoading(true)
    setError(null)
    setRawError(null)

    try {
      await submitFn()
      options.onSuccess?.()
    } catch (err) {
      // Classify the error using the new error classification system
      const authError = ErrorClassifier.classifyError(err, options.context)
      setError(authError)

      // Keep raw error message for backward compatibility
      const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred'
      setRawError(errorMessage)

      options.onError?.(authError)
    } finally {
      setIsLoading(false)
    }
  }

  const clearError = () => {
    setError(null)
    setRawError(null)
  }

  return {
    isLoading,
    error,
    rawError, // For backward compatibility
    authError: error, // Explicit access to classified error
    setError: clearError,
    clearError,
    handleSubmit,
  }
}