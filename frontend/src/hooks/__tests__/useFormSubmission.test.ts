/**
 * Tests for enhanced useFormSubmission hook with error classification
 * Tests requirements 3.1, 3.2, 3.3, 3.4, 3.5
 */

import { describe, it, expect, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useFormSubmission } from '../useFormSubmission'
import { AuthError } from '@/lib/errors'

describe('useFormSubmission', () => {
  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useFormSubmission())

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeNull()
    expect(result.current.rawError).toBeNull()
    expect(result.current.authError).toBeNull()
  })

  it('should handle successful form submission', async () => {
    const onSuccess = vi.fn()
    const { result } = renderHook(() => useFormSubmission({ onSuccess }))

    const mockSubmitFn = vi.fn().mockResolvedValue(undefined)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeNull()
    expect(result.current.rawError).toBeNull()
    expect(onSuccess).toHaveBeenCalledTimes(1)
    expect(mockSubmitFn).toHaveBeenCalledTimes(1)
  })

  it('should set loading state during submission', async () => {
    const { result } = renderHook(() => useFormSubmission())

    let resolvePromise: () => void
    const mockSubmitFn = vi.fn().mockImplementation(() => {
      return new Promise<void>((resolve) => {
        resolvePromise = resolve
      })
    })

    act(() => {
      result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(true)

    await act(async () => {
      resolvePromise()
    })

    expect(result.current.isLoading).toBe(false)
  })

  it('should classify and handle API errors correctly', async () => {
    const onError = vi.fn()
    const { result } = renderHook(() => 
      useFormSubmission({ onError, context: 'login' })
    )

    const apiError = {
      status: 401,
      code: 'UNAUTHORIZED',
      message: 'Invalid credentials'
    }

    const mockSubmitFn = vi.fn().mockRejectedValue(apiError)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeDefined()
    expect(result.current.error?.code).toBe('INVALID_CREDENTIALS')
    expect(result.current.error?.userMessage).toBe('Invalid email, password, or church code. Please check your credentials and try again.')
    expect(result.current.rawError).toBe('An unexpected error occurred')
    expect(onError).toHaveBeenCalledTimes(1)
    expect(onError).toHaveBeenCalledWith(expect.objectContaining({
      code: 'INVALID_CREDENTIALS'
    }))
  })

  it('should handle standard Error instances', async () => {
    const onError = vi.fn()
    const { result } = renderHook(() => useFormSubmission({ onError }))

    const standardError = new Error('Network connection failed')
    const mockSubmitFn = vi.fn().mockRejectedValue(standardError)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeDefined()
    expect(result.current.error?.code).toBe('NETWORK_ERROR')
    expect(result.current.error?.userMessage).toBe('Network connection failed. Please check your internet connection and try again.')
    expect(result.current.rawError).toBe('Network connection failed')
    expect(onError).toHaveBeenCalledTimes(1)
  })

  it('should handle string errors', async () => {
    const onError = vi.fn()
    const { result } = renderHook(() => useFormSubmission({ onError }))

    const stringError = 'Something went wrong'
    const mockSubmitFn = vi.fn().mockRejectedValue(stringError)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeDefined()
    expect(result.current.rawError).toBe('An unexpected error occurred')
    expect(onError).toHaveBeenCalledTimes(1)
  })

  it('should handle unknown error types', async () => {
    const onError = vi.fn()
    const { result } = renderHook(() => useFormSubmission({ onError }))

    const unknownError = { weird: 'error object' }
    const mockSubmitFn = vi.fn().mockRejectedValue(unknownError)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.isLoading).toBe(false)
    expect(result.current.error).toBeDefined()
    expect(result.current.error?.code).toBe('UNKNOWN_ERROR')
    expect(result.current.error?.userMessage).toBe('An unexpected error occurred. Please try again.')
    expect(result.current.rawError).toBe('An unexpected error occurred')
    expect(onError).toHaveBeenCalledTimes(1)
  })

  it('should use context for error classification', async () => {
    const { result } = renderHook(() => 
      useFormSubmission({ context: 'church' })
    )

    const notFoundError = {
      status: 404,
      message: 'Church not found'
    }

    const mockSubmitFn = vi.fn().mockRejectedValue(notFoundError)

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.error?.code).toBe('CHURCH_NOT_FOUND')
    expect(result.current.error?.userMessage).toBe('Church not found. Please check your church code or URL.')
  })

  it('should clear errors correctly', async () => {
    const { result } = renderHook(() => useFormSubmission())

    const mockSubmitFn = vi.fn().mockRejectedValue(new Error('Test error'))

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.error).toBeDefined()
    expect(result.current.rawError).toBeDefined()

    act(() => {
      result.current.clearError()
    })

    expect(result.current.error).toBeNull()
    expect(result.current.rawError).toBeNull()
  })

  it('should provide both setError and clearError for backward compatibility', () => {
    const { result } = renderHook(() => useFormSubmission())

    expect(typeof result.current.setError).toBe('function')
    expect(typeof result.current.clearError).toBe('function')
    expect(result.current.setError).toBe(result.current.clearError)
  })

  it('should provide authError as alias for error', async () => {
    const { result } = renderHook(() => useFormSubmission())

    const mockSubmitFn = vi.fn().mockRejectedValue(new Error('Test error'))

    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn)
    })

    expect(result.current.authError).toBe(result.current.error)
    expect(result.current.authError).toBeDefined()
  })

  it('should clear errors before new submission', async () => {
    const { result } = renderHook(() => useFormSubmission())

    // First submission with error
    const mockSubmitFn1 = vi.fn().mockRejectedValue(new Error('First error'))
    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn1)
    })

    expect(result.current.error).toBeDefined()

    // Second submission should clear previous error
    const mockSubmitFn2 = vi.fn().mockResolvedValue(undefined)
    await act(async () => {
      await result.current.handleSubmit(mockSubmitFn2)
    })

    expect(result.current.error).toBeNull()
    expect(result.current.rawError).toBeNull()
  })

  it('should handle context-specific error classification for different scenarios', async () => {
    // Test login context
    const { result: loginResult } = renderHook(() => 
      useFormSubmission({ context: 'login' })
    )

    const unauthorizedError = { status: 401, code: 'UNAUTHORIZED' }
    const mockLoginFn = vi.fn().mockRejectedValue(unauthorizedError)

    await act(async () => {
      await loginResult.current.handleSubmit(mockLoginFn)
    })

    expect(loginResult.current.error?.code).toBe('INVALID_CREDENTIALS')

    // Test token_refresh context
    const { result: refreshResult } = renderHook(() => 
      useFormSubmission({ context: 'token_refresh' })
    )

    const mockRefreshFn = vi.fn().mockRejectedValue(unauthorizedError)

    await act(async () => {
      await refreshResult.current.handleSubmit(mockRefreshFn)
    })

    expect(refreshResult.current.error?.code).toBe('TOKEN_EXPIRED')
  })
})