import { useState, useEffect, useCallback } from 'react'
import { apiClient } from '@/lib/api'
import { useAuth } from '@/lib/auth'
import { hasPermission, PERMISSIONS } from '@/lib/permissions'
import type { Announcement } from '@/types'

interface DashboardOverview {
  totalMembers: number
  totalEvents: number
  totalFinances: number
  totalRevenue: number
  memberGrowthRate: number
  eventAttendanceRate: number
  financeGrowthRate: number
  recentActivity: Array<{
    type: string
    title: string
    description: string
    timestamp: Date
    metadata?: Record<string, unknown>
  }>
}

interface Member {
  id: string
  firstName: string
  lastName: string
  email: string
  status: string
}

interface Event {
  id: string
  title: string
  type: string
  startDate: string
  status: string
  requiresRsvp?: boolean
  rsvpStats?: { attending?: number; maybe?: number; not_attending?: number }
}

interface Finance {
  id: string
  amount: number | string
  type: string
  recordedAt: string
}

interface Branch {
  id: string
  name: string
  isMain?: boolean
  isMainBranch?: boolean
  memberCount?: number
  status: string
}

interface MemberAnalytics {
  totalMembers: number
  membersByStatus: Array<{ status: string; count: number; percentage: number }>
  membersByGender: Array<{ gender: string; count: number; percentage: number }>
  membersByAgeGroup: Array<{ ageGroup: string; count: number; percentage: number }>
  membersByBranch: Array<{ branchId: string | null; branchName: string; count: number; percentage: number }>
  memberGrowthTrend: Array<{ period: string; count: number; date: Date }>
  topEngagedMembers: Array<{ id: string; name: string; engagementScore: number }>
}

interface EventAnalytics {
  totalEvents: number
  eventsByType: Array<{ type: string; count: number; percentage: number; averageAttendance: number }>
  eventsByStatus: Array<{ status: string; count: number; percentage: number }>
  attendanceMetrics: {
    totalRsvps: number
    averageAttendanceRate: number
    mostPopularEventType: string
    peakAttendanceDay: string
  }
  upcomingEvents: Array<{ 
    id: string; 
    title: string; 
    type: string; 
    startDate: Date; 
    rsvpCount: number; 
    attendanceRate?: number 
  }>
  eventTrends: Array<{ period: string; count: number; attendance: number; date: Date }>
}

interface FinancialAnalytics {
  totalRevenue: number
  donationsByType: Array<{ type: string; count: number; totalAmount: number; percentage: number }>
  donationsByMethod: Array<{ method: string; count: number; totalAmount: number; percentage: number }>
  donationTrends: Array<{ period: string; amount: number; count: number; date: Date }>
  topDonors: Array<{ 
    donorId: string | null; 
    donorName: string; 
    totalDonations: number; 
    donationCount: number; 
    isAnonymous: boolean 
  }>
  fundraisingGoals: Array<{ goal: string; target: number; current: number; percentage: number }>
}

interface ChartDataPoint {
  name: string
  value?: number
  count?: number
  date?: Date
  members?: number
  amount?: number
  events?: number
  attendance?: number
  [key: string]: any
}

interface DashboardData {
  overview: DashboardOverview | null
  members: Member[] | null
  events: Event[] | null
  finances: Finance[] | null
  branches: Branch[] | null
  announcements: Announcement[] | null
  memberAnalytics: MemberAnalytics | null
  eventAnalytics: EventAnalytics | null
  financialAnalytics: FinancialAnalytics | null
  chartData: {
    memberGrowth: ChartDataPoint[]
    eventTypes: ChartDataPoint[]
    financeTrends: ChartDataPoint[]
    weeklyActivity: ChartDataPoint[]
  }
  loading: boolean
  error: string | null
}

export function useDashboardData(period: string = '30d') {
  const { church, user } = useAuth()
  const [data, setData] = useState<DashboardData>({
    overview: null,
    members: null,
    events: null,
    finances: null,
    branches: null,
    announcements: null,
    memberAnalytics: null,
    eventAnalytics: null,
    financialAnalytics: null,
    chartData: {
      memberGrowth: [],
      eventTypes: [],
      financeTrends: [],
      weeklyActivity: []
    },
    loading: true,
    error: null,
  })

  // Helper function to generate time series data based on period
  const generateTimeSeriesLabels = (period: string): string[] => {
    const now = new Date()
    const labels: string[] = []
    
    switch (period) {
      case '7d':
        for (let i = 6; i >= 0; i--) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
          labels.push(date.toLocaleDateString('en-US', { weekday: 'short' }))
        }
        break
      case '30d':
      case '90d':
        const days = period === '30d' ? 30 : 90
        const interval = period === '30d' ? 5 : 15 // Show every 5 days for 30d, every 15 days for 90d
        for (let i = days; i >= 0; i -= interval) {
          const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000)
          labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }))
        }
        break
      case '1y':
        for (let i = 11; i >= 0; i--) {
          const date = new Date(now.getFullYear(), now.getMonth() - i, 1)
          labels.push(date.toLocaleDateString('en-US', { month: 'short' }))
        }
        break
      default:
        return ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
    }
    
    return labels
  }

  // Helper function to transform analytics data to chart format
  const transformToChartData = (
    overview: DashboardOverview | null, 
    memberAnalytics: MemberAnalytics | null,
    eventAnalytics: EventAnalytics | null,
    financialAnalytics: FinancialAnalytics | null
  ): { memberGrowth: ChartDataPoint[]; eventTypes: ChartDataPoint[]; financeTrends: ChartDataPoint[]; weeklyActivity: ChartDataPoint[] } => {
    const timeLabels = generateTimeSeriesLabels(period)

    // Member Growth Chart Data
    const memberGrowth: ChartDataPoint[] = memberAnalytics?.memberGrowthTrend.length 
      ? memberAnalytics.memberGrowthTrend.map(trend => ({
          name: trend.period,
          members: trend.totalMembers,
          newMembers: trend.newMembers,
          growthRate: trend.growthRate
        }))
      : []

    // Event Types Chart Data  
    const eventTypes: ChartDataPoint[] = eventAnalytics?.eventsByType.length
      ? eventAnalytics.eventsByType.map(event => ({
          name: event.type,
          value: event.percentage,
          count: event.count
        }))
      : []

    // Finance Trends Chart Data
    const financeTrends: ChartDataPoint[] = financialAnalytics?.donationTrends.length
      ? financialAnalytics.donationTrends.map(trend => ({
          name: trend.period,
          amount: trend.amount,
          count: trend.count,
          date: trend.date
        }))
      : []

    // Weekly Activity Chart Data (events and estimated attendance)
    const weeklyActivity: ChartDataPoint[] = eventAnalytics?.eventTrends.length
      ? eventAnalytics.eventTrends.slice(-7).map(trend => ({
          name: trend.period,
          events: trend.eventCount,
          attendance: trend.attendanceCount,
          averageAttendance: trend.averageAttendance
        }))
      : []

    return {
      memberGrowth,
      eventTypes,
      financeTrends,
      weeklyActivity
    }
  }

  useEffect(() => {
    // no body; replaced by fetcher below
  }, [])

  const fetchDashboardData = useCallback(async (): Promise<void> => {
    if (!church?.slug) return
    try {
      setData(prev => ({ ...prev, loading: true, error: null }))

      const canViewFinancial = hasPermission(user, PERMISSIONS.VIEW_FINANCIAL_REPORTS)
      const roleName = user?.role?.name || 'Member'
      const isMemberOrVisitor = roleName === 'Member' || roleName === 'Visitor'

      // Build promises with conditional inclusion for restricted endpoints and role needs
      const promises: Array<Promise<any> | null> = [
        // Overview is safe for all roles (aggregated)
        apiClient.getDashboardOverview(church.slug, { period }),
        // Members list only for staff roles
        isMemberOrVisitor ? null : apiClient.getMembers(church.slug, { limit: '5', status: 'active' }),
        // Events for everyone
        apiClient.getEvents(church.slug, { limit: '5', upcoming: 'true' }),
        // Finances list only for staff roles
        isMemberOrVisitor ? null : apiClient.getFinances(church.slug, { limit: '5' }),
        // Branches for everyone (basic info)
        apiClient.getBranches(church.slug, { status: 'active' }),
        // Member analytics only for staff roles
        isMemberOrVisitor ? null : apiClient.getMemberAnalytics(church.slug, { period }),
        // Event analytics only for staff roles
        isMemberOrVisitor ? null : apiClient.getEventAnalytics(church.slug, { period }),
        // Financial analytics only if permission
        canViewFinancial && !isMemberOrVisitor ? apiClient.getFinancialAnalytics(church.slug, { period }) : null,
        // Announcements for everyone
        apiClient.getAnnouncements(church.slug, { limit: '10', status: 'published' })
      ]

      const results = await Promise.all(promises.map(p => p?.catch((e: unknown) => e)))

      const [
        overviewResponse,
        membersResponseOrError,
        eventsResponse,
        financesResponseOrError,
        branchesResponse,
        memberAnalyticsResponseOrError,
        eventAnalyticsResponseOrError,
        financialAnalyticsResponseOrError,
        announcementsResponse
      ] = results

      const overview = overviewResponse?.data?.overview || null
      const memberAnalytics = !isMemberOrVisitor && !(memberAnalyticsResponseOrError instanceof Error)
        ? memberAnalyticsResponseOrError?.data?.analytics || null
        : null
      const eventAnalytics = !isMemberOrVisitor && !(eventAnalyticsResponseOrError instanceof Error)
        ? eventAnalyticsResponseOrError?.data?.analytics || null
        : null

      let financialAnalytics = null as FinancialAnalytics | null
      if (canViewFinancial && !isMemberOrVisitor && financialAnalyticsResponseOrError && !(financialAnalyticsResponseOrError instanceof Error)) {
        financialAnalytics = financialAnalyticsResponseOrError.data?.analytics || null
      } else if (
        financialAnalyticsResponseOrError &&
        (financialAnalyticsResponseOrError as { status?: number }).status === 403
      ) {
        financialAnalytics = null
      }

      const announcements = announcementsResponse?.data?.announcements || []

      const chartData = transformToChartData(overview, memberAnalytics, eventAnalytics, financialAnalytics)

      setData(prev => ({
        ...prev,
        overview,
        members: !isMemberOrVisitor && !(membersResponseOrError instanceof Error) ? (membersResponseOrError?.data?.members || []) : [],
        events: eventsResponse?.data?.events || [],
        finances: !isMemberOrVisitor && !(financesResponseOrError instanceof Error) ? (financesResponseOrError?.data?.finances || []) : [],
        branches: branchesResponse?.data?.branches || [],
        announcements,
        memberAnalytics,
        eventAnalytics,
        financialAnalytics,
        chartData,
        loading: false,
      }))
    } catch (err) {
      console.error('Error fetching dashboard data:', err)
      const message = err instanceof Error ? err.message : 'Failed to fetch dashboard data'
      setData(prev => ({
        ...prev,
        error: message,
        loading: false,
      }))
    }
  }, [apiClient, church?.slug, period, user])

  useEffect(() => {
    void fetchDashboardData()
  }, [fetchDashboardData])

  const refetch = () => {
    void fetchDashboardData()
  }

  return { ...data, refetch }
}