import { useEffect, useState } from 'react'
import { useAuth } from '@/lib/auth'
import { api } from '@/lib/api'

export function useNotificationCount() {
  const { church, user } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!church?.slug || !user) return

    let cancelled = false

    const fetchUnread = async () => {
      try {
        setLoading(true)
        const res = await api.getNotificationsUnreadCount(church.slug)
        if (!cancelled) setUnreadCount(res.data.count)
      } catch {
        if (!cancelled) setUnreadCount(0)
      } finally {
        if (!cancelled) setLoading(false)
      }
    }

    fetchUnread()
    const interval = setInterval(fetchUnread, 120000)
    return () => { cancelled = true; clearInterval(interval) }
  }, [church?.slug, user])

  return { unreadCount, loading }
}
