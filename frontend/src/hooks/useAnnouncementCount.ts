import { useState, useEffect } from 'react'
import { apiClient } from '@/lib/api'
import { useAuth } from '@/lib/auth'

export function useAnnouncementCount() {
  const { church, user } = useAuth()
  const [unreadCount, setUnreadCount] = useState(0)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (!church?.slug || !user) return

    const fetchUnreadCount = async () => {
      try {
        setLoading(true)
        
        // Fetch recent announcements (last 30 days) that are unread
        const response = await apiClient.getAnnouncements(church.slug, {
          limit: '50',
          status: 'published',
          unread: 'true' // This would be a backend filter for unread announcements
        })

        const announcements = (response.data as any).announcements || []
        
        // Count unread announcements (those published in the last 7 days that user hasn't viewed)
        const now = new Date()
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        
        const unreadAnnouncements = announcements.filter((announcement: any) => {
          const publishedAt = new Date(announcement.publishedAt || announcement.createdAt)
          const isRecent = publishedAt >= sevenDaysAgo
          const isUnread = !announcement.stats?.isViewed
          return isRecent && isUnread
        })

        setUnreadCount(unreadAnnouncements.length)
      } catch (error) {
        console.warn('Failed to fetch announcement count:', error)
        // Don't show error to user, just fail silently for badge counts
        setUnreadCount(0)
      } finally {
        setLoading(false)
      }
    }

    fetchUnreadCount()

    // Refresh count every 5 minutes
    const interval = setInterval(fetchUnreadCount, 5 * 60 * 1000)

    return () => clearInterval(interval)
  }, [church?.slug, user])

  return { unreadCount, loading }
}