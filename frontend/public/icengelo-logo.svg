<svg width="200" height="80" viewBox="0 0 200 80" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="lightGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFD700;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FFA500;stop-opacity:0.8" />
      <stop offset="100%" style="stop-color:#FF6B35;stop-opacity:0.3" />
    </radialGradient>
    <linearGradient id="textGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#2563EB;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Light rays background -->
  <g opacity="0.6">
    <path d="M 25 40 L 5 20 M 25 40 L 5 40 M 25 40 L 5 60 M 25 40 L 15 15 M 25 40 L 15 65" 
          stroke="#FFD700" stroke-width="1" opacity="0.4"/>
    <path d="M 25 40 L 45 20 M 25 40 L 45 40 M 25 40 L 45 60 M 25 40 L 35 15 M 25 40 L 35 65" 
          stroke="#FFD700" stroke-width="1" opacity="0.4"/>
  </g>
  
  <!-- Central light orb -->
  <circle cx="25" cy="40" r="12" fill="url(#lightGradient)" opacity="0.9"/>
  <circle cx="25" cy="40" r="8" fill="#FFE55C" opacity="0.7"/>
  <circle cx="25" cy="40" r="4" fill="#FFFFFF" opacity="1"/>
  
  <!-- Light beams -->
  <g opacity="0.8">
    <ellipse cx="25" cy="40" rx="20" ry="3" fill="#FFD700" opacity="0.3"/>
    <ellipse cx="25" cy="40" rx="3" ry="20" fill="#FFD700" opacity="0.3"/>
    <ellipse cx="25" cy="40" rx="14" ry="2" fill="#FFE55C" opacity="0.5" transform="rotate(45 25 40)"/>
    <ellipse cx="25" cy="40" rx="14" ry="2" fill="#FFE55C" opacity="0.5" transform="rotate(-45 25 40)"/>
  </g>
  
  <!-- Text -->
  <text x="60" y="48" font-family="'Segoe UI', Arial, sans-serif" font-size="24" font-weight="600" 
        fill="url(#textGradient)" letter-spacing="1px">icengelo</text>
  
  <!-- Tagline -->
  <text x="62" y="62" font-family="'Segoe UI', Arial, sans-serif" font-size="10" 
        fill="#6B7280" letter-spacing="0.5px">bringing light to communities</text>
</svg>