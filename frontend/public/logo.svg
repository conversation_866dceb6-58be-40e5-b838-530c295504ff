<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer glow circle -->
  <circle cx="100" cy="100" r="95" fill="none" stroke="url(#glow-gradient)" stroke-width="2" opacity="0.7"/>
  
  <!-- Main light rays -->
  <g stroke="url(#ray-gradient)" stroke-width="3" stroke-linecap="round">
    <line x1="100" y1="30" x2="100" y2="10" />
    <line x1="135" y1="35" x2="145" y2="15" />
    <line x1="165" y1="65" x2="185" y2="55" />
    <line x1="170" y1="100" x2="190" y2="100" />
    <line x1="165" y1="135" x2="185" y2="145" />
    <line x1="135" y1="165" x2="145" y2="185" />
    <line x1="100" y1="170" x2="100" y2="190" />
    <line x1="65" y1="165" x2="55" y2="185" />
    <line x1="35" y1="135" x2="15" y2="145" />
    <line x1="30" y1="100" x2="10" y2="100" />
    <line x1="35" y1="65" x2="15" y2="55" />
    <line x1="65" y1="35" x2="55" y2="15" />
  </g>
  
  <!-- Central flame/candle -->
  <g>
    <!-- Candle body -->
    <path d="M100 60 C110 60, 115 80, 110 120 C105 140, 95 140, 90 120 C85 80, 90 60, 100 60Z" fill="url(#candle-gradient)" />
    
    <!-- Wick -->
    <line x1="100" y1="55" x2="100" y2="45" stroke="#333" stroke-width="2" stroke-linecap="round" />
    
    <!-- Flame -->
    <path d="M100 45 Q105 35, 100 25 Q95 35, 100 45" fill="url(#flame-gradient)" />
    <path d="M100 40 Q103 32, 100 27 Q97 32, 100 40" fill="#FFD700" opacity="0.8" />
  </g>
  
  <!-- Text -->
  <text x="100" y="185" text-anchor="middle" font-family="serif" font-size="24" fill="url(#text-gradient)" font-weight="bold">icengelo</text>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="ray-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FBBF24" />
      <stop offset="50%" stop-color="#F59E0B" />
      <stop offset="100%" stop-color="#D97706" />
    </linearGradient>
    
    <linearGradient id="flame-gradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" stop-color="#FDE68A" />
      <stop offset="50%" stop-color="#F59E0B" />
      <stop offset="100%" stop-color="#D97706" />
    </linearGradient>
    
    <linearGradient id="candle-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FEF3C7" />
      <stop offset="30%" stop-color="#FDE68A" />
      <stop offset="70%" stop-color="#FBBF24" />
      <stop offset="100%" stop-color="#F59E0B" />
    </linearGradient>
    
    <linearGradient id="text-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#D97706" />
      <stop offset="50%" stop-color="#F59E0B" />
      <stop offset="100%" stop-color="#FBBF24" />
    </linearGradient>
    
    <linearGradient id="glow-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FDE68A" />
      <stop offset="50%" stop-color="#F59E0B" />
      <stop offset="100%" stop-color="#D97706" />
    </linearGradient>
    
    <!-- Glow filter -->
    <filter id="glow" x="-50%" y="-50%" width="200%" height="200%">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" result="blur" />
      <feFlood flood-color="#FBBF24" flood-opacity="0.5" result="glowColor" />
      <feComposite in="glowColor" in2="blur" operator="in" result="glow" />
      <feMerge>
        <feMergeNode in="glow" />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
  </defs>
</svg>