# Church Code - Agent Guidelines

## Technology Stack
**Backend**: Bun + TypeScript + Express + Drizzle ORM + PostgreSQL  
**Frontend**: Next.js 15 App Router + TypeScript + Tailwind CSS + shadcn/ui  
**Testing**: Vitest + Supertest (backend), Vitest + Testing Library (frontend)  
**Auth**: JWT with refresh tokens, multi-tenant church isolation

## Commands
**Backend**: `cd backend && bun run {dev|test|test:watch|lint|lint:fix|db:generate|db:migrate|db:push|db:studio}`  
**Frontend**: `cd frontend && bun run {dev|build|test|test:watch|lint|lint:fix|type-check}`  
**Single Test**: `cd backend && bunx vitest run path/to/test.test.ts` or `cd frontend && bunx vitest run path/to/test.test.ts`  
**API Docs**: http://localhost:3000/api-docs (Swagger UI when backend dev server running)

## Multi-Tenant Architecture (CRITICAL)
**Church Isolation**: Every data query MUST filter by `churchId` - never expose cross-tenant data  
**Endpoint Pattern**: Church-scoped routes use `/api/churches/{slug}/...`  
**Middleware Stack**: `authenticate → validateTenant → requireTenantAccess → checkChurchAccess(['roles'])`  
**JWT Context**: Tokens include `churchId` claim for tenant validation

## Code Style & Requirements

### Backend Guidelines
- **TypeScript strict mode**, camelCase variables/functions, PascalCase types/interfaces
- **Do not use the `any` type**: The use of TypeScript's `any` type is strictly prohibited. Always use specific types or generics for type safety.
- **Import order**: External libraries first, then relative imports  
- **Validation**: Use Zod schemas in separate type files, validate all inputs
- **Error handling**: Use catchAsync wrapper + throw AppError for expected errors
- **Database**: Always filter by `churchId` for tenant isolation, use soft deletes (`isActive: false`)
- **Response format**: `{ status: 'success', data: {...} }`

### Frontend Guidelines (MANDATORY)
- **Do not use the `any` type**: The use of TypeScript's `any` type is strictly prohibited. Always use specific types or generics for type safety.
- **MUST USE shadcn/ui components exclusively** - Install via `bunx shadcn-ui@latest add <component>`
- **MUST USE Tailwind CSS** for styling (no custom CSS files)
- **MUST USE Next.js App Router** (app directory, no pages router)
- **Icons**: Lucide React (`import { IconName } from "lucide-react"`)
- **Forms**: shadcn/ui Form + react-hook-form + Zod validation

### Development Workflow
1. **Before committing**: Run `bun run lint && bun run test` in both backend and frontend
2. **Multi-tenant testing**: Always test with multiple churches to verify data isolation
3. **Church-scoped endpoints**: Use tenant middleware stack for all church routes
4. **Documentation**: Update Swagger comments for new endpoints