# Authentication Fixes - Design Document

## Overview

This design addresses critical authentication issues in the Church Code application by implementing robust token management, consistent storage mechanisms, proper error handling, and improved user experience. The solution focuses on creating a reliable authentication system that works seamlessly across the multi-tenant architecture.

## Architecture

### Token Management Flow

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant AuthContext
    participant APIClient
    participant Backend
    participant TokenStorage

    User->>Frontend: Login Request
    Frontend->>Backend: POST /auth/login
    Backend->>Frontend: Access + Refresh Tokens
    Frontend->>TokenStorage: Store tokens (localStorage + cookies)
    Frontend->>AuthContext: Update auth state
    
    Note over Frontend,Backend: Normal API Request
    Frontend->>APIClient: API Request
    APIClient->>Backend: Request with Bearer token
    Backend->>APIClient: 401 Token Expired
    APIClient->>Backend: POST /auth/refresh
    Backend->>APIClient: New tokens
    APIClient->>TokenStorage: Update stored tokens
    APIClient->>Backend: Retry original request
    Backend->>APIClient: Success response
```

### Authentication State Management

```mermaid
stateDiagram-v2
    [*] --> Initializing
    Initializing --> Authenticated: Valid token found
    Initializing --> Unauthenticated: No valid token
    
    Authenticated --> TokenRefreshing: Token expired
    TokenRefreshing --> Authenticated: Refresh successful
    TokenRefreshing --> Unauthenticated: Refresh failed
    
    Unauthenticated --> Authenticating: Login attempt
    Authenticating --> Authenticated: Login successful
    Authenticating --> Unauthenticated: Login failed
    
    Authenticated --> Unauthenticated: Logout
    Authenticated --> Unauthenticated: Session expired
```

## Components and Interfaces

### 1. Enhanced Token Storage Service

**Purpose**: Provide consistent token storage and retrieval across localStorage and cookies.

**Interface**:
```typescript
interface TokenStorageService {
  setTokens(accessToken: string, refreshToken: string): void
  getAccessToken(): string | null
  getRefreshToken(): string | null
  clearTokens(): void
  onTokensChanged(callback: (tokens: TokenPair | null) => void): () => void
}
```

**Implementation Details**:
- Dual storage in localStorage and httpOnly cookies
- Automatic synchronization between storage mechanisms
- Event-based notifications for token changes
- Fallback mechanisms when one storage type fails

### 2. Automatic Token Refresh Interceptor

**Purpose**: Automatically handle token refresh for expired access tokens.

**Interface**:
```typescript
interface TokenRefreshInterceptor {
  interceptRequest(config: RequestConfig): Promise<RequestConfig>
  interceptResponse(response: Response): Promise<Response>
  handleTokenRefresh(): Promise<TokenPair>
}
```

**Implementation Details**:
- Intercepts 401 responses from API calls
- Automatically attempts token refresh using refresh token
- Queues concurrent requests during refresh process
- Updates stored tokens after successful refresh
- Triggers logout if refresh fails

### 3. Enhanced Authentication Context

**Purpose**: Provide centralized authentication state management with improved reliability.

**Interface**:
```typescript
interface AuthContextType {
  user: User | null
  church: Church | null
  isAuthenticated: boolean
  isLoading: boolean
  isInitialized: boolean
  login(email: string, password: string, churchCode: string): Promise<void>
  logout(): void
  refreshTokens(): Promise<void>
  checkAuthStatus(): Promise<void>
}
```

**Implementation Details**:
- Automatic token refresh handling
- Cross-tab synchronization using storage events
- Proper loading states during authentication checks
- Error boundary integration for auth failures
- Automatic redirect handling for protected routes

### 4. Improved API Client

**Purpose**: Handle authenticated requests with automatic token management.

**Implementation Details**:
- Automatic token inclusion in request headers
- Built-in retry logic for token refresh scenarios
- Proper error handling and propagation
- Request queuing during token refresh
- Configurable timeout and retry policies

### 5. Enhanced Authentication Middleware (Backend)

**Purpose**: Provide robust server-side authentication verification.

**Implementation Details**:
- Improved token verification with better error messages
- Church-scoped access validation
- Rate limiting for authentication attempts
- Audit logging for security events
- Graceful handling of malformed tokens

## Data Models

### Token Storage Schema

```typescript
interface TokenPair {
  accessToken: string
  refreshToken: string
  expiresAt: number
  issuedAt: number
}

interface StoredAuthData {
  tokens: TokenPair
  user: User
  church: Church
  lastRefresh: number
}
```

### Authentication State Schema

```typescript
interface AuthState {
  status: 'initializing' | 'authenticated' | 'unauthenticated' | 'refreshing' | 'error'
  user: User | null
  church: Church | null
  tokens: TokenPair | null
  error: AuthError | null
  lastActivity: number
}
```

### Error Handling Schema

```typescript
interface AuthError {
  code: 'INVALID_CREDENTIALS' | 'TOKEN_EXPIRED' | 'REFRESH_FAILED' | 'NETWORK_ERROR' | 'CHURCH_NOT_FOUND' | 'ACCOUNT_INACTIVE'
  message: string
  details?: Record<string, unknown>
  retryable: boolean
}
```

## Error Handling

### Error Classification

1. **Recoverable Errors**: Token expiration, network timeouts
   - Automatic retry with exponential backoff
   - Silent recovery when possible
   - User notification only if recovery fails

2. **User Action Required**: Invalid credentials, inactive account
   - Clear error messages with actionable guidance
   - Appropriate redirect to resolution flow
   - No automatic retry

3. **System Errors**: Server errors, malformed responses
   - Fallback to cached data when available
   - User notification with retry option
   - Error reporting for debugging

### Error Recovery Strategies

```mermaid
flowchart TD
    A[API Request Fails] --> B{Error Type?}
    B -->|401 Unauthorized| C[Attempt Token Refresh]
    B -->|Network Error| D[Retry with Backoff]
    B -->|403 Forbidden| E[Show Permission Error]
    B -->|Other 4xx| F[Show User Error]
    B -->|5xx Server Error| G[Show System Error]
    
    C --> H{Refresh Success?}
    H -->|Yes| I[Retry Original Request]
    H -->|No| J[Logout User]
    
    D --> K{Max Retries?}
    K -->|No| L[Wait and Retry]
    K -->|Yes| M[Show Network Error]
    
    I --> N[Return Response]
    L --> A
```

### Frontend Error Boundaries

- Authentication-specific error boundary to catch auth-related crashes
- Automatic fallback to login page for unrecoverable auth errors
- Error reporting integration for debugging
- User-friendly error messages with recovery options

## Testing Strategy

### Unit Tests

1. **Token Storage Service**
   - Test localStorage and cookie synchronization
   - Verify fallback mechanisms
   - Test event emission for token changes

2. **Token Refresh Logic**
   - Test automatic refresh on 401 responses
   - Verify request queuing during refresh
   - Test failure scenarios and cleanup

3. **Authentication Context**
   - Test state transitions
   - Verify cross-tab synchronization
   - Test error handling and recovery

### Integration Tests

1. **Authentication Flow**
   - End-to-end login/logout scenarios
   - Token refresh during active sessions
   - Multi-tab behavior verification

2. **API Client Integration**
   - Authenticated request handling
   - Automatic retry mechanisms
   - Error propagation and handling

3. **Route Protection**
   - Middleware authentication verification
   - Church-scoped access control
   - Redirect behavior for unauthenticated users

### Security Tests

1. **Token Security**
   - Verify httpOnly cookie implementation
   - Test token expiration handling
   - Validate secure transmission

2. **Session Management**
   - Test session invalidation on logout
   - Verify concurrent session handling
   - Test token revocation scenarios

### Performance Tests

1. **Authentication Performance**
   - Login/logout response times
   - Token refresh latency
   - Memory usage during long sessions

2. **Scalability**
   - Concurrent user authentication
   - Token refresh under load
   - Storage performance with large user bases

## Implementation Considerations

### Security Best Practices

- Use httpOnly cookies for refresh tokens when possible
- Implement proper CSRF protection
- Add rate limiting for authentication endpoints
- Use secure token generation and validation
- Implement proper session timeout handling

### Performance Optimizations

- Implement token refresh queuing to avoid duplicate requests
- Use efficient storage mechanisms with minimal overhead
- Optimize authentication state checks
- Implement proper caching for user/church data
- Use lazy loading for non-critical authentication features

### Browser Compatibility

- Fallback mechanisms for older browsers
- Polyfills for missing storage APIs
- Graceful degradation for unsupported features
- Testing across different browser environments

### Monitoring and Observability

- Authentication success/failure metrics
- Token refresh frequency monitoring
- Error rate tracking by error type
- Performance metrics for auth operations
- Security event logging and alerting