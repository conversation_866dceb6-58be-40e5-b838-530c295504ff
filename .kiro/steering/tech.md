# Church Code - Technology Stack

## Backend Stack
- **Runtime**: Bun (JavaScript runtime and package manager)
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: JWT with refresh tokens + bcrypt password hashing
- **Validation**: Zod schemas for request/response validation
- **Testing**: Vitest with Supertest for API testing
- **Documentation**: Swagger/OpenAPI 3.0 for interactive API testing

## Frontend Stack
- **Framework**: Next.js 15+ with App Router
- **Language**: TypeScript (strict mode)
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui (MANDATORY - no other UI libraries)
- **Icons**: Lucide React (included with shadcn/ui)
- **Forms**: react-hook-form with Zod validation
- **Testing**: Vitest + Testing Library
- **State Management**: React Context API

## Development Tools
- **Package Manager**: Bun (for both frontend and backend)
- **Linting**: ESLint with TypeScript rules
- **Database Tools**: Drizzle Studio for database GUI
- **API Testing**: Swagger UI at http://localhost:3000/api-docs

## Common Commands

### Backend Development
```bash
cd backend
bun install                    # Install dependencies
bun run dev                    # Start development server (hot reload)
bun run start                  # Start production server
bun run test                   # Run tests
bun run test:watch            # Run tests in watch mode
bun run lint                   # Run ESLint
bun run lint:fix              # Auto-fix linting issues
```

### Database Operations
```bash
cd backend
bun run db:generate           # Generate database migrations
bun run db:migrate            # Run database migrations
bun run db:push               # Push schema changes to database
bun run db:studio             # Open Drizzle Studio GUI
```

### Frontend Development
```bash
cd frontend
bun install                   # Install dependencies
bun run dev                   # Start Next.js dev server (port 3002)
bun run build                 # Build for production
bun run start                 # Start production server
bun run test                  # Run frontend tests
bun run test:watch           # Run tests in watch mode
bun run lint                  # Run ESLint
bun run type-check           # Run TypeScript type checking
```

### shadcn/ui Component Installation
```bash
cd frontend
bunx shadcn-ui@latest add button card input label textarea
bunx shadcn-ui@latest add form select checkbox radio-group
bunx shadcn-ui@latest add table dialog alert navigation-menu
```

## Environment Setup
- Backend runs on port 3000
- Frontend runs on port 3002
- PostgreSQL database required
- Environment files: `.env` (backend), `.env.local` (frontend)

## API Documentation
- Swagger UI: http://localhost:3000/api-docs
- Health Check: http://localhost:3000/health
- All endpoints documented with OpenAPI 3.0 specifications