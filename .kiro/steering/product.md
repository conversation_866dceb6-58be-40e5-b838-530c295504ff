# Church Code - Product Overview

Church Code is a multi-tenant church management system that enables multiple churches to register and manage their operations independently. Each church operates as a separate tenant with complete data isolation.

## Core Features

- **Multi-tenant architecture** - Multiple churches with isolated data
- **Church registration & onboarding** - Complete setup with admin user creation
- **Member management** - Admin-controlled member account creation and management
- **Branch management** - Support for multiple church locations/campuses
- **Event management** - Full event lifecycle with RSVP system and recurring events
- **Donation tracking** - Comprehensive financial management with reporting
- **Role-based access control** - Church-specific permissions and hierarchies
- **JWT authentication** - Secure authentication with refresh tokens

## Multi-Tenant Model

Churches are identified by unique slugs and operate completely independently:
- Each church has its own admin users, members, events, donations, and branches
- Data isolation is enforced at the database and API level
- Church-scoped API endpoints use pattern: `/api/churches/{slug}/resource`
- All operations require proper tenant validation and access control

## User Types

- **Super Admin** - Full church management privileges
- **Pastor/Elder** - Church administration and member management
- **Deacon** - Limited administrative functions
- **Member** - Basic access to church resources
- **Visitor** - Public access only

## Key Workflows

1. **Church Registration** - New churches register via onboarding flow
2. **Member Creation** - <PERSON><PERSON> create member accounts (no public registration)
3. **Event Management** - Create, manage, and track church events with RSVPs
4. **Financial Tracking** - Record and report donations with category management
5. **Branch Operations** - Manage multiple church locations and assignments