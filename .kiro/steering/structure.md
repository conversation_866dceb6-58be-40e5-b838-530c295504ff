# Church Code - Project Structure

## Repository Organization

```
church-code/
├── backend/                  # Express.js API server
├── frontend/                 # Next.js web application
├── .kiro/                   # Kiro AI assistant configuration
└── AGENTS.md                # Comprehensive development guidelines
```

## Backend Structure (`backend/`)

```
backend/
├── src/
│   ├── controllers/         # Route handlers (business logic)
│   ├── middleware/          # Authentication, tenant validation
│   ├── db/
│   │   ├── schema/         # Drizzle ORM schema definitions
│   │   └── migrations/     # Database migration files
│   ├── routes/             # API route definitions
│   ├── types/              # TypeScript type definitions
│   ├── utils/              # Utility functions and helpers
│   ├── config/             # Configuration files (Swagger, etc.)
│   └── __tests__/          # Test files
├── index.ts                # Application entry point
├── package.json            # Dependencies and scripts
├── tsconfig.json           # TypeScript configuration
├── drizzle.config.ts       # Database configuration
└── .env                    # Environment variables
```

## Frontend Structure (`frontend/`)

```
frontend/
├── src/
│   ├── app/                # Next.js App Router
│   │   ├── (public)/       # Public routes (auth, landing)
│   │   ├── [slug]/         # Church-specific routes
│   │   ├── globals.css     # Global Tailwind styles
│   │   └── layout.tsx      # Root layout component
│   ├── components/
│   │   ├── ui/             # shadcn/ui components (auto-generated)
│   │   ├── forms/          # Custom form components
│   │   ├── layout/         # Layout components (nav, sidebar)
│   │   ├── auth/           # Authentication components
│   │   └── features/       # Feature-specific components
│   ├── lib/
│   │   ├── api.ts          # API client functions
│   │   ├── auth.tsx        # Authentication utilities
│   │   ├── utils.ts        # Utility functions (cn helper)
│   │   └── validations.ts  # Zod schemas for forms
│   ├── hooks/              # Custom React hooks
│   ├── types/              # TypeScript type definitions
│   └── middleware.ts       # Next.js middleware for route protection
├── public/                 # Static assets
├── package.json            # Dependencies and scripts
├── next.config.ts          # Next.js configuration
├── tailwind.config.js      # Tailwind CSS configuration
└── components.json         # shadcn/ui configuration
```

## Key Architecture Patterns

### Multi-Tenant Routing
- **Backend**: `/api/churches/{slug}/resource` pattern
- **Frontend**: `/[slug]/dashboard` dynamic routing
- **Middleware**: Tenant validation on all church-scoped endpoints

### Database Schema Organization
- **Core entities**: churches, members, roles, branches
- **Feature entities**: events, donations, announcements
- **Support entities**: refresh tokens, onboarding sessions
- **Tenant isolation**: All entities include `churchId` foreign key

### API Endpoint Structure
```
/api/
├── auth/                   # Authentication endpoints
├── churches/               # Church management
│   └── {slug}/            # Church-scoped resources
│       ├── members/       # Member management
│       ├── events/        # Event management
│       ├── donations/     # Donation tracking
│       └── branches/      # Branch management
└── onboarding/            # Church registration flow
```

### Frontend Route Structure
```
/
├── (public)/              # Public routes
│   ├── login/            # Authentication
│   ├── register-church/  # Church registration
│   └── onboarding/       # Setup flow
└── [slug]/               # Church-specific routes
    ├── dashboard/        # Main dashboard
    ├── members/          # Member management
    ├── events/           # Event management
    ├── branches/         # Branch management
    └── analytics/        # Reporting
```

## File Naming Conventions

### Backend
- **Controllers**: `{resource}Controller.ts` (camelCase functions)
- **Types**: `{resource}.ts` (PascalCase interfaces)
- **Schema**: `{resource}s.ts` (plural, camelCase exports)
- **Tests**: `{resource}Controller.test.ts`

### Frontend
- **Components**: `PascalCase.tsx` for components
- **Pages**: `page.tsx` for Next.js routes
- **Layouts**: `layout.tsx` for Next.js layouts
- **Hooks**: `use{Name}.ts` for custom hooks
- **Utils**: `camelCase.ts` for utility functions

## Import Organization
1. External libraries (React, Next.js, etc.)
2. Internal utilities and types
3. Relative imports (components, etc.)

## Testing Structure
- **Backend**: Tests alongside source in `__tests__/` directories
- **Frontend**: Component tests in `__tests__/` subdirectories
- **Integration**: Multi-tenant scenarios and API integration tests