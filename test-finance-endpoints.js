#!/usr/bin/env node

const API_BASE = 'http://localhost:3000/api';

async function testFinanceEndpoints() {
  console.log('🧪 Testing Finance Endpoints...\n');

  try {
    // Test health endpoint first
    console.log('1. Testing health endpoint...');
    const healthResponse = await fetch(`${API_BASE.replace('/api', '')}/health`);
    if (healthResponse.ok) {
      console.log('✅ Health endpoint working');
    } else {
      console.log('❌ Health endpoint failed');
      return;
    }

    // Test currencies endpoint (public)
    console.log('\n2. Testing currencies endpoint...');
    const currenciesResponse = await fetch(`${API_BASE}/currencies`);
    if (currenciesResponse.ok) {
      const currencies = await currenciesResponse.json();
      console.log('✅ Currencies endpoint working');
      console.log(`   Found ${currencies.data?.currencies?.length || 0} currencies`);
    } else {
      console.log('❌ Currencies endpoint failed');
    }

    console.log('\n3. Testing finance endpoints (requires authentication)...');
    console.log('   Note: These endpoints require authentication, so they should return 401');
    
    // Test finance endpoints (should return 401 without auth)
    const financeEndpoints = [
      '/churches/test-church/finances',
      '/churches/test-church/finances/projects',
      '/churches/test-church/finances/categories',
      '/churches/test-church/payment-methods',
      '/churches/test-church/analytics/financial'
    ];

    for (const endpoint of financeEndpoints) {
      const response = await fetch(`${API_BASE}${endpoint}`);
      if (response.status === 401) {
        console.log(`✅ ${endpoint} - correctly requires authentication`);
      } else {
        console.log(`❌ ${endpoint} - unexpected status: ${response.status}`);
      }
    }

    console.log('\n🎉 Finance endpoints are properly configured!');
    console.log('\nNext steps:');
    console.log('1. Start the backend server: cd backend && bun run dev');
    console.log('2. Start the frontend server: cd frontend && bun run dev');
    console.log('3. Register a church and login to test the finance features');

  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
    console.log('\nMake sure the backend server is running on port 3000');
  }
}

testFinanceEndpoints();